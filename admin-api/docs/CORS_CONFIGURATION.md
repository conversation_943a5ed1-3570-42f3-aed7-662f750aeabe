# CORS 配置指南

## 概述

本文档介绍 XPay Admin API 的跨域资源共享（CORS）配置方法。

## 配置文件位置

CORS 配置位于 `manifest/config/config.yaml` 文件中的 `cors` 部分。

## 配置选项

### 基本配置示例

```yaml
# CORS 跨域配置
cors:
  allowOrigin: "*" # 允许的源，"*" 表示允许所有域名
  allowCredentials: "true" # 是否允许携带凭据
  exposeHeaders: "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type"
  maxAge: 86400 # 预检请求的缓存时间（秒）
  allowMethods: "GET, POST, PUT, DELETE, OPTIONS, PATCH"
  allowHeaders: "Origin, X-Requested-With, Content-Type, Accept, Authorization, X-Token"
```

### 生产环境配置示例

```yaml
# CORS 跨域配置 - 生产环境
cors:
  # 使用具体域名列表代替通配符
  allowDomain: "https://app.example.com,https://admin.example.com"
  allowCredentials: "true"
  exposeHeaders: "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type"
  maxAge: 86400
  allowMethods: "GET, POST, PUT, DELETE, OPTIONS, PATCH"
  allowHeaders: "Origin, X-Requested-With, Content-Type, Accept, Authorization, X-Token"
```

## 配置参数说明

| 参数 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| `allowOrigin` | string | 允许的源，可以是具体域名或 "*" | "*" |
| `allowDomain` | string | 允许的域名列表，逗号分隔 | - |
| `allowCredentials` | string | 是否允许携带凭据（cookies、authorization headers等） | "true" |
| `exposeHeaders` | string | 允许浏览器访问的响应头 | "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type" |
| `maxAge` | int | 预检请求结果的缓存时间（秒） | 86400 |
| `allowMethods` | string | 允许的 HTTP 方法 | "GET, POST, PUT, DELETE, OPTIONS, PATCH" |
| `allowHeaders` | string | 允许的请求头 | "Origin, X-Requested-With, Content-Type, Accept, Authorization, X-Token" |

## 注意事项

1. **安全性考虑**：
   - 生产环境不建议使用 `allowOrigin: "*"`
   - 应该明确指定允许的域名

2. **凭据和通配符**：
   - 当 `allowCredentials` 为 "true" 时，`allowOrigin` 不能是 "*"
   - 中间件会自动处理这种情况，使用请求的 Origin 头

3. **域名列表优先级**：
   - 如果同时设置了 `allowOrigin` 和 `allowDomain`，`allowDomain` 优先

4. **预检请求**：
   - OPTIONS 请求会自动处理并返回 200 状态码
   - 使用 `maxAge` 控制预检请求的缓存时间

## 常见场景配置

### 1. 开发环境（允许所有域名）

```yaml
cors:
  allowOrigin: "*"
  allowCredentials: "false" # 注意：使用 * 时不能为 true
```

### 2. 单域名生产环境

```yaml
cors:
  allowOrigin: "https://app.example.com"
  allowCredentials: "true"
```

### 3. 多域名生产环境

```yaml
cors:
  allowDomain: "https://app.example.com,https://admin.example.com,https://m.example.com"
  allowCredentials: "true"
```

### 4. 内部 API（限制特定域名）

```yaml
cors:
  allowDomain: "https://internal.company.com"
  allowCredentials: "true"
  allowMethods: "GET, POST" # 限制方法
```

## 测试 CORS 配置

使用 curl 测试 CORS 配置：

```bash
# 测试预检请求
curl -X OPTIONS http://localhost:7999/api/system/login \
  -H "Origin: https://example.com" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type" \
  -v

# 测试实际请求
curl -X POST http://localhost:7999/api/system/login \
  -H "Origin: https://example.com" \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test"}' \
  -v
```

## 故障排除

1. **浏览器报 CORS 错误**：
   - 检查配置文件中的域名是否正确
   - 确认包含了协议（http:// 或 https://）
   - 检查是否有拼写错误

2. **凭据无法发送**：
   - 确保 `allowCredentials` 设置为 "true"
   - 确保没有使用 `allowOrigin: "*"`

3. **自定义请求头被拒绝**：
   - 将自定义请求头添加到 `allowHeaders` 配置中

4. **预检请求失败**：
   - 检查 `allowMethods` 是否包含请求的方法
   - 确认 OPTIONS 方法被正确处理