# Consul 类型化配置读取示例

## 配置存储格式

在 Consul 中，配置以 JSON 格式存储，包含值和类型信息：

```json
{
  "value": "true",
  "value_type": "boolean"
}
```

## Go 客户端示例

### 1. 基础配置读取器

```go
package consulconfig

import (
    "context"
    "encoding/json"
    "fmt"
    
    "github.com/hashicorp/consul/api"
)

// ConfigValueType 配置值类型
type ConfigValueType string

const (
    TypeText     ConfigValueType = "text"
    TypePassword ConfigValueType = "password"
    TypeBoolean  ConfigValueType = "boolean"
    TypeNumber   ConfigValueType = "number"
    TypeJSON     ConfigValueType = "json"
    TypeMap      ConfigValueType = "map"
    TypeArray    ConfigValueType = "array"
)

// ConfigData 配置数据结构
type ConfigData struct {
    Value     string          `json:"value"`
    ValueType ConfigValueType `json:"value_type"`
}

// ConsulConfigClient Consul 配置客户端
type ConsulConfigClient struct {
    client *api.Client
    prefix string
}

// NewConsulConfigClient 创建新的配置客户端
func NewConsulConfigClient(address, token, prefix string) (*ConsulConfigClient, error) {
    config := api.DefaultConfig()
    config.Address = address
    if token != "" {
        config.Token = token
    }
    
    client, err := api.NewClient(config)
    if err != nil {
        return nil, err
    }
    
    return &ConsulConfigClient{
        client: client,
        prefix: prefix,
    }, nil
}

// GetConfigData 获取原始配置数据
func (c *ConsulConfigClient) GetConfigData(ctx context.Context, key string) (*ConfigData, error) {
    kv := c.client.KV()
    fullKey := fmt.Sprintf("%s/%s", c.prefix, key)
    
    pair, _, err := kv.Get(fullKey, nil)
    if err != nil {
        return nil, fmt.Errorf("获取配置失败: %w", err)
    }
    
    if pair == nil {
        return nil, fmt.Errorf("配置不存在: %s", key)
    }
    
    var configData ConfigData
    if err := json.Unmarshal(pair.Value, &configData); err != nil {
        // 兼容旧格式：如果不是 JSON，当作纯文本处理
        return &ConfigData{
            Value:     string(pair.Value),
            ValueType: TypeText,
        }, nil
    }
    
    return &configData, nil
}

// GetString 获取字符串配置
func (c *ConsulConfigClient) GetString(ctx context.Context, key string) (string, error) {
    data, err := c.GetConfigData(ctx, key)
    if err != nil {
        return "", err
    }
    return data.Value, nil
}

// GetBool 获取布尔配置
func (c *ConsulConfigClient) GetBool(ctx context.Context, key string) (bool, error) {
    data, err := c.GetConfigData(ctx, key)
    if err != nil {
        return false, err
    }
    
    switch data.ValueType {
    case TypeBoolean:
        return strconv.ParseBool(data.Value)
    default:
        // 尝试转换
        switch strings.ToLower(data.Value) {
        case "1", "true", "yes", "on":
            return true, nil
        case "0", "false", "no", "off":
            return false, nil
        default:
            return false, fmt.Errorf("无法转换为布尔值: %s", data.Value)
        }
    }
}

// GetInt 获取整数配置
func (c *ConsulConfigClient) GetInt(ctx context.Context, key string) (int, error) {
    data, err := c.GetConfigData(ctx, key)
    if err != nil {
        return 0, err
    }
    
    switch data.ValueType {
    case TypeNumber:
        // 使用 decimal 解析后转换为整数
        dec, err := decimal.NewFromString(data.Value)
        if err != nil {
            return 0, err
        }
        return int(dec.IntPart()), nil
    }
    
    // 尝试直接转换
    return strconv.Atoi(data.Value)
}

// GetDecimal 获取高精度数字配置（适用于金额等）
func (c *ConsulConfigClient) GetDecimal(ctx context.Context, key string) (decimal.Decimal, error) {
    data, err := c.GetConfigData(ctx, key)
    if err != nil {
        return decimal.Zero, err
    }
    
    // 不管什么类型，都尝试解析为 decimal
    return decimal.NewFromString(data.Value)
}

// GetMap 获取 Map 配置
func (c *ConsulConfigClient) GetMap(ctx context.Context, key string) (map[string]interface{}, error) {
    data, err := c.GetConfigData(ctx, key)
    if err != nil {
        return nil, err
    }
    
    if data.ValueType != TypeMap && data.ValueType != TypeJSON {
        return nil, fmt.Errorf("配置类型不是 Map: %s", data.ValueType)
    }
    
    var result map[string]interface{}
    if err := json.Unmarshal([]byte(data.Value), &result); err != nil {
        return nil, fmt.Errorf("解析 Map 失败: %w", err)
    }
    
    return result, nil
}

// GetTypedValue 获取类型化的值
func (c *ConsulConfigClient) GetTypedValue(ctx context.Context, key string) (interface{}, error) {
    data, err := c.GetConfigData(ctx, key)
    if err != nil {
        return nil, err
    }
    
    switch data.ValueType {
    case TypeBoolean:
        return strconv.ParseBool(data.Value)
    case TypeNumber:
        // 使用 decimal 保持高精度
        return decimal.NewFromString(data.Value)
    case TypeJSON, TypeMap, TypeArray:
        var result interface{}
        err := json.Unmarshal([]byte(data.Value), &result)
        return result, err
    default:
        return data.Value, nil
    }
}
```

### 2. 使用示例

```go
package main

import (
    "context"
    "fmt"
    "log"
    
    "github.com/shopspring/decimal"
)

func main() {
    // 创建配置客户端
    client, err := NewConsulConfigClient(
        "127.0.0.1:8500",
        "your-token",
        "xpay/config",
    )
    if err != nil {
        log.Fatal(err)
    }
    
    ctx := context.Background()
    
    // 获取布尔配置
    redPacketEnabled, err := client.GetBool(ctx, "red_packet_setting.state")
    if err != nil {
        log.Printf("获取红包开关失败: %v", err)
    } else {
        fmt.Printf("红包功能: %v\n", redPacketEnabled)
    }
    
    // 获取字符串配置
    botName, err := client.GetString(ctx, "telegram_bot_setting.name")
    if err != nil {
        log.Printf("获取机器人名称失败: %v", err)
    } else {
        fmt.Printf("机器人名称: %s\n", botName)
    }
    
    // 获取高精度数字配置（适用于金额）
    minAmount, err := client.GetDecimal(ctx, "payment.min_amount")
    if err != nil {
        log.Printf("获取最小金额失败: %v", err)
    } else {
        fmt.Printf("最小支付金额: %s\n", minAmount.String())
    }
    
    // 获取 Map 配置
    supportMap, err := client.GetMap(ctx, "telegram_bot_setting.support")
    if err != nil {
        log.Printf("获取支持客服失败: %v", err)
    } else {
        fmt.Printf("支持客服: %v\n", supportMap)
    }
    
    // 获取类型化的值（自动转换）
    value, err := client.GetTypedValue(ctx, "payment.fee_rate")
    if err != nil {
        log.Printf("获取配置失败: %v", err)
    } else {
        // 如果是 number 类型，返回的是 decimal.Decimal
        if decVal, ok := value.(decimal.Decimal); ok {
            fmt.Printf("手续费率: %s%%\n", decVal.Mul(decimal.NewFromInt(100)).String())
        }
    }
}
```

### 3. 带缓存的客户端

```go
type CachedConsulConfigClient struct {
    *ConsulConfigClient
    cache sync.Map
    ttl   time.Duration
}

type cacheItem struct {
    data      *ConfigData
    expiresAt time.Time
}

func (c *CachedConsulConfigClient) GetConfigData(ctx context.Context, key string) (*ConfigData, error) {
    // 检查缓存
    if cached, ok := c.cache.Load(key); ok {
        item := cached.(*cacheItem)
        if time.Now().Before(item.expiresAt) {
            return item.data, nil
        }
    }
    
    // 从 Consul 获取
    data, err := c.ConsulConfigClient.GetConfigData(ctx, key)
    if err != nil {
        return nil, err
    }
    
    // 更新缓存
    c.cache.Store(key, &cacheItem{
        data:      data,
        expiresAt: time.Now().Add(c.ttl),
    })
    
    return data, nil
}
```

## Python 客户端示例

```python
import json
import consul
from typing import Any, Optional, Dict

class ConsulConfigClient:
    def __init__(self, host='127.0.0.1', port=8500, token=None, prefix='xpay/config'):
        self.consul = consul.Consul(host=host, port=port, token=token)
        self.prefix = prefix
    
    def get_config_data(self, key: str) -> Dict[str, Any]:
        """获取配置数据"""
        full_key = f"{self.prefix}/{key}"
        index, data = self.consul.kv.get(full_key)
        
        if data is None:
            raise KeyError(f"配置不存在: {key}")
        
        try:
            # 尝试解析 JSON
            config_data = json.loads(data['Value'].decode('utf-8'))
            return config_data
        except json.JSONDecodeError:
            # 兼容旧格式
            return {
                'value': data['Value'].decode('utf-8'),
                'value_type': 'text'
            }
    
    def get_string(self, key: str) -> str:
        """获取字符串配置"""
        data = self.get_config_data(key)
        return data['value']
    
    def get_bool(self, key: str) -> bool:
        """获取布尔配置"""
        data = self.get_config_data(key)
        value = data['value']
        
        if data.get('value_type') == 'boolean':
            return value.lower() in ('true', '1', 'yes', 'on')
        
        # 尝试转换
        return value.lower() in ('true', '1', 'yes', 'on')
    
    def get_int(self, key: str) -> int:
        """获取整数配置"""
        data = self.get_config_data(key)
        value = data['value']
        
        if data.get('value_type') == 'number':
            # 可能是浮点数
            return int(float(value))
        
        return int(value)
    
    def get_map(self, key: str) -> Dict[str, Any]:
        """获取 Map 配置"""
        data = self.get_config_data(key)
        
        if data.get('value_type') not in ('map', 'json'):
            raise ValueError(f"配置类型不是 Map: {data.get('value_type')}")
        
        return json.loads(data['value'])
    
    def get_typed_value(self, key: str) -> Any:
        """获取类型化的值"""
        data = self.get_config_data(key)
        value_type = data.get('value_type', 'text')
        value = data['value']
        
        if value_type == 'boolean':
            return value.lower() in ('true', '1', 'yes', 'on')
        elif value_type == 'number':
            try:
                return int(value)
            except ValueError:
                return float(value)
        elif value_type in ('json', 'map', 'array'):
            return json.loads(value)
        else:
            return value

# 使用示例
if __name__ == "__main__":
    client = ConsulConfigClient(token='your-token')
    
    # 获取各种类型的配置
    red_packet_enabled = client.get_bool('red_packet_setting.state')
    print(f"红包功能: {red_packet_enabled}")
    
    bot_name = client.get_string('telegram_bot_setting.name')
    print(f"机器人名称: {bot_name}")
    
    support_map = client.get_map('telegram_bot_setting.support')
    print(f"支持客服: {support_map}")
    
    # 自动类型转换
    payment_state = client.get_typed_value('payment_setting.state')
    print(f"收款开关: {payment_state} (类型: {type(payment_state).__name__})")
```

## 配置示例

根据你提供的数据，在 Consul 中的存储格式如下：

```json
// xpay/config/red_packet_setting.state
{
  "value": "true",
  "value_type": "boolean"
}

// xpay/config/telegram_bot_setting.name
{
  "value": "aabwcbcc_bot",
  "value_type": "text"
}

// xpay/config/telegram_bot_setting.key
{
  "value": "456",
  "value_type": "password"
}

// xpay/config/telegram_bot_setting.support
{
  "value": "{\"1\":\"1\",\"2\":\"2\"}",
  "value_type": "map"
}

// xpay/config/payment.min_amount (金额示例)
{
  "value": "0.01",
  "value_type": "number"
}

// xpay/config/payment.fee_rate (费率示例)
{
  "value": "0.025",
  "value_type": "number"
}

// xpay/config/wallet.max_balance (大金额示例)
{
  "value": "999999999.99",
  "value_type": "number"
}
```

## 优势

1. **类型安全**：配置包含类型信息，避免类型转换错误
2. **向后兼容**：支持旧的纯文本格式
3. **自动转换**：根据类型自动转换为正确的数据类型
4. **易于扩展**：可以轻松添加新的数据类型
5. **跨语言支持**：JSON 格式在各种语言中都有良好支持
6. **高精度数值**：使用 `decimal.Decimal` 处理金额，避免浮点数精度问题

## 使用 Decimal 的好处

对于金融相关的配置（如金额、费率等），使用 `github.com/shopspring/decimal` 的优势：

1. **精度保证**：避免浮点数运算的精度损失
2. **大数支持**：支持任意精度的数值
3. **安全计算**：提供安全的算术运算方法
4. **字符串友好**：可以精确地与字符串互转

示例：
```go
// 配置中存储的费率
feeRate, _ := client.GetDecimal(ctx, "payment.fee_rate") // 0.025

// 计算手续费
amount := decimal.NewFromInt(1000) // 1000 元
fee := amount.Mul(feeRate)         // 25 元（精确计算）

// 格式化输出
fmt.Printf("金额: %s, 费率: %s%%, 手续费: %s\n", 
    amount.String(),                              // "1000"
    feeRate.Mul(decimal.NewFromInt(100)).String(), // "2.5"
    fee.String())                                 // "25"
```