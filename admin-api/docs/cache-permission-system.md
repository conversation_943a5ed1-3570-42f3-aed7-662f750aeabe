# 缓存权限系统使用说明

## 概述

本系统实现了基于Redis缓存的用户权限管理系统，包括用户信息缓存、权限缓存以及基于权限的菜单过滤功能。系统通过单例模式管理缓存，并与Casdoor权限系统集成。

## 核心组件

### 1. 缓存管理器 (`internal/cache/cache_manager.go`)

单例模式的缓存管理器，负责：
- 用户信息缓存
- 用户权限缓存
- API权限缓存
- 菜单权限缓存

#### 缓存键格式：
- 用户信息：`admin:user:{userId}:info`
- 用户权限：`admin:user:{userId}:permissions`
- API权限：`admin:user:{userId}:api_permissions`
- 菜单权限：`admin:user:{userId}:menu_permissions`

#### 缓存TTL：
- 用户信息：30分钟
- 权限信息：60分钟

### 2. 权限服务 (`internal/cache/permission_service.go`)

权限服务负责：
- 从Casdoor获取用户权限
- 解析和转换权限格式
- 缓存权限数据
- 权限检查功能

### 3. 中间件

#### 认证中间件 (`internal/middleware/auth.go`)
- 解析JWT Token
- 验证用户身份
- 设置用户上下文信息

#### 权限中间件 (`internal/middleware/permission.go`)
- API权限验证
- 菜单权限验证
- 基于缓存的快速权限检查

## 使用流程

### 1. 用户登录流程

1. 用户通过 `/api/system/login` 接口登录
2. 验证用户名密码
3. 生成JWT Token
4. **异步缓存用户信息和权限**：
   - 缓存用户基本信息
   - 从Casdoor获取用户权限
   - 解析并缓存API权限和菜单权限

```go
// 登录成功后，异步缓存用户信息和权限
go func(memberId int64, username string) {
    ctxBg := gctx.New()
    
    // 缓存用户基本信息
    cacheManager := cache.GetInstance()
    userInfo := &cache.UserInfo{
        ID:       memberId,
        Username: username,
        // ... 其他字段
    }
    
    if cacheErr := cacheManager.SetUserInfo(ctxBg, memberId, userInfo); cacheErr != nil {
        g.Log().Errorf(ctxBg, "缓存用户信息失败: userId=%d, error=%v", memberId, cacheErr)
    }
    
    // 缓存用户权限（从Casdoor获取）
    permissionService := cache.NewPermissionService()
    if permErr := permissionService.CacheUserPermissionsFromCasdoor(ctxBg, memberId, username); permErr != nil {
        g.Log().Errorf(ctxBg, "缓存用户权限失败: userId=%d, error=%v", memberId, permErr)
    }
}(adminMember.Id, adminMember.Username)
```

### 2. API访问权限验证

每个API请求都会经过权限中间件验证：

1. 提取用户ID
2. 构建API权限键：`{path}@{method}`
3. 从缓存检查权限
4. 允许或拒绝访问

```go
// 权限检查示例
apiPath := "/api/system/users"
method := "get"
permissionKey := "/api/system/users@get"

hasPermission, err := permissionService.HasAPIPermission(ctx, userId, apiPath, method)
if !hasPermission {
    // 返回403 Forbidden
}
```

### 3. 菜单权限过滤

获取用户可访问菜单时：

1. 从缓存获取用户菜单权限
2. 根据权限过滤菜单树
3. 返回过滤后的菜单结构

```go
// 菜单权限过滤
menuPermissions, err := cacheManager.GetUserMenuPermissions(ctx, userId)
filteredMenuTree := filterMenuTreeByPermissions(menuTree, menuPermissions)
```

## 权限格式说明

### Casdoor权限格式

从Casdoor获取的权限格式示例：
```json
{
  "name": "d6f9a0d",
  "displayName": "Menu 代理列表",
  "description": "Menu: 代理列表 (Path: /admin/agent/list)",
  "resourceType": "Custom",
  "resources": ["/admin/agent/list"],
  "actions": ["Visit"],
  "effect": "Allow"
}
```

### 缓存权限格式

#### API权限
```json
{
  "/api/system/users@get": true,
  "/api/system/users@post": true,
  "/api/system/users@put": false
}
```

#### 菜单权限
```json
{
  "/admin/users": true,
  "/admin/agents": true,
  "/admin/settings": false
}
```

## 缓存管理

### 手动清除缓存

```go
// 清除指定用户的所有缓存
cacheManager := cache.GetInstance()
err := cacheManager.ClearUserCache(ctx, userId)

// 异步清除缓存
cacheManager.ClearUserCacheAsync(userId)
```

### 刷新用户权限

```go
// 刷新用户权限（清除旧缓存并重新获取）
permissionService := cache.NewPermissionService()
err := permissionService.RefreshUserPermissions(ctx, userId, username)
```

## 配置说明

### Redis配置

确保Redis服务正常运行，系统使用GoFrame的默认Redis配置。

### Casdoor配置

确保Casdoor服务正常运行，并且权限数据格式正确：
- `resourceType` 应为 "Custom"
- `effect` 应为 "Allow"
- API权限的`resources`格式：`/api/path@method`
- 菜单权限的`resources`格式：`/admin/path`

## 错误处理

### 缓存失败处理

- 缓存读取失败时会降级到数据库查询
- 缓存写入失败不会影响主流程，只记录日志

### 权限检查失败处理

- 权限检查失败时默认拒绝访问
- 记录详细的错误日志便于调试

## 性能优化

### 缓存策略

- 用户信息缓存30分钟，权限缓存60分钟
- 登录时异步缓存，不影响登录响应时间
- 权限检查优先使用缓存，大幅提升性能

### 内存使用

- 使用Redis外部缓存，不占用应用内存
- 合理的TTL设置平衡性能与数据一致性

## 调试功能

### 获取用户权限信息

可以使用调试接口查看用户的权限信息：

```go
// 在需要调试的地方添加这个端点
func GetUserPermissionInfo(r *ghttp.Request) {
    // 实现在 internal/middleware/permission.go 中
}
```

## 注意事项

1. **数据一致性**：权限变更后需要手动清除相关用户的缓存
2. **Redis依赖**：系统依赖Redis，Redis不可用时权限系统会降级
3. **Casdoor集成**：确保Casdoor权限数据格式正确
4. **性能监控**：建议监控缓存命中率和响应时间

## 扩展功能

### 支持角色级缓存

可以扩展系统支持角色级权限缓存，减少单用户缓存的内存使用。

### 支持权限继承

可以实现菜单权限的继承机制，父菜单有权限时子菜单自动继承。

### 支持权限分组

可以按功能模块对权限进行分组管理，提高管理效率。