# GoFrame LEFT JOIN Soft Delete Fix

## Problem Description

When using GoFrame ORM with LEFT JOINs, the framework automatically adds `deleted_at IS NULL` conditions for ALL joined tables. This causes issues with LEFT JOIN semantics:

### Example SQL Generated by GoFrame:
```sql
WHERE (rpc.deleted_at IS NULL) 
AND claimer.deleted_at IS NULL 
AND claimer_first_agent.deleted_at IS NULL 
AND claimer_uba.deleted_at IS NULL
```

### The Issue:
- When a LEFT JOIN doesn't find a matching record (e.g., user has no backup account), all columns are NULL
- The condition `claimer_uba.deleted_at IS NULL` evaluates to FALSE (because NULL IS NULL is actually NULL in SQL, which is treated as FALSE in WHERE clause)
- This filters out the entire row, even though we want to include users without backup accounts

## Solution

Add explicit conditions that check if the LEFT JOINed record doesn't exist OR if it exists and isn't soft-deleted:

```go
// For optional relationships (agents, backup accounts)
m = m.Where("(creator_uba.user_id IS NULL OR creator_uba.deleted_at IS NULL)")

// For required relationships (main user, red packet)
m = m.WhereNull("creator.deleted_at")
```

## Implementation Details

### Files Modified:

1. **red_packets.go**
   - `ListAdminRedPacketsWithFullInfo`: Added proper soft delete handling
   - `GetAdminRedPacketDetail`: Already had proper handling
   - `GetRedPacketForUpdate`: Already had proper handling

2. **red_packet_claims.go**
   - `ListAdminRedPacketClaimsWithFullInfo`: Added proper soft delete handling

3. **red_packet_images.go**
   - `ListAdminRedPacketImagesWithFullInfo`: Added proper soft delete handling

4. **payment_requests.go**
   - `ListAdminPaymentRequestsWithFullInfo`: Added proper soft delete handling

### Pattern Applied:

```go
// Required relationships - user must exist and not be deleted
m = m.WhereNull("creator.deleted_at")

// Optional relationships - handle NULL case
// Agents (users might not have agents)
m = m.Where("(creator_first_agent.id IS NULL OR creator_first_agent.deleted_at IS NULL)")
m = m.Where("(creator_second_agent.id IS NULL OR creator_second_agent.deleted_at IS NULL)")
m = m.Where("(creator_third_agent.id IS NULL OR creator_third_agent.deleted_at IS NULL)")

// Backup accounts (users might not have telegram accounts)
m = m.Where("(creator_uba.user_id IS NULL OR creator_uba.deleted_at IS NULL)")

// Tokens (usually exist but handle for safety)
m = m.Where("(token.token_id IS NULL OR token.deleted_at IS NULL)")
```

## Results

After applying this fix:
- Users without backup accounts will appear in results with NULL Telegram fields
- Users without agents will appear in results with NULL agent fields
- Only actually soft-deleted records are filtered out
- LEFT JOIN semantics are preserved correctly

## Best Practices

1. **Identify Required vs Optional Relationships**
   - Required: Use `WhereNull("table.deleted_at")`
   - Optional: Use `Where("(table.key IS NULL OR table.deleted_at IS NULL)")`

2. **Always Consider LEFT JOIN Semantics**
   - LEFT JOIN means the right table might not have matching records
   - Don't filter out rows just because the JOIN didn't find a match

3. **Test with Data Variations**
   - Users with all relationships
   - Users without backup accounts
   - Users without agents
   - Mixed scenarios

## SQL Explanation

The condition `(creator_uba.user_id IS NULL OR creator_uba.deleted_at IS NULL)` works because:
- If no backup account exists: `creator_uba.user_id IS NULL` is TRUE → entire condition is TRUE
- If backup account exists and not deleted: `creator_uba.deleted_at IS NULL` is TRUE → entire condition is TRUE  
- If backup account exists and is deleted: both parts are FALSE → entire condition is FALSE (row filtered out)

This preserves the LEFT JOIN behavior while still filtering out soft-deleted records.