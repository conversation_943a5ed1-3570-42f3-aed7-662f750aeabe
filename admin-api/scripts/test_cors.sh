#!/bin/bash

# CORS 测试脚本
# 用于测试 XPay Admin API 的 CORS 配置

API_URL="http://localhost:7999/api/system/login"
ORIGIN="https://example.com"

echo "=== 测试 CORS 配置 ==="
echo "API URL: $API_URL"
echo "Origin: $ORIGIN"
echo ""

echo "1. 测试 OPTIONS 预检请求："
echo "--------------------------------"
curl -X OPTIONS "$API_URL" \
  -H "Origin: $ORIGIN" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type, Authorization" \
  -v 2>&1 | grep -E "(< HTTP|< Access-Control-)"

echo ""
echo ""

echo "2. 测试 POST 请求："
echo "--------------------------------"
curl -X POST "$API_URL" \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test123"}' \
  -v 2>&1 | grep -E "(< HTTP|< Access-Control-)"

echo ""
echo ""

echo "3. 测试不同的 Origin："
echo "--------------------------------"
ORIGINS=("http://localhost:3000" "https://app.xpay.com" "https://malicious.com")

for origin in "${ORIGINS[@]}"; do
    echo "Testing Origin: $origin"
    curl -X OPTIONS "$API_URL" \
      -H "Origin: $origin" \
      -H "Access-Control-Request-Method: GET" \
      -s -v 2>&1 | grep -E "< Access-Control-Allow-Origin" || echo "  No Access-Control-Allow-Origin header"
    echo ""
done

echo ""
echo "=== 测试完成 ==="
echo ""
echo "检查要点："
echo "1. Access-Control-Allow-Origin 是否正确返回"
echo "2. Access-Control-Allow-Methods 是否包含所需方法"
echo "3. Access-Control-Allow-Headers 是否包含所需请求头"
echo "4. Access-Control-Allow-Credentials 是否正确设置"
echo "5. OPTIONS 请求是否返回 200 状态码"