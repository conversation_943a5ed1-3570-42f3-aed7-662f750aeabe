#!/usr/bin/env python3
"""
配置模板生成器
读取 manifest/config/config.yaml 文件，生成模板文件和变量提取文件
"""

import yaml
import json
import re
import os
from pathlib import Path


def generate_var_name(path, key=""):
    """生成符合规范的变量名"""
    # 转换为大写，用下划线分隔
    if key:
        full_path = f"{path}.{key}" if path else key
    else:
        full_path = path
    
    # 将路径转换为大写变量名格式
    var_name = full_path.replace('.', '_').replace('[', '_').replace(']', '').upper()
    # 添加前缀
    return f"ADMIN_API_{var_name}"


def extract_all_config_values(data, path="", variables=None):
    """递归提取所有配置值作为变量"""
    if variables is None:
        variables = {}
    
    if isinstance(data, dict):
        for key, value in data.items():
            new_path = f"{path}.{key}" if path else key
            extract_all_config_values(value, new_path, variables)
    elif isinstance(data, list):
        for i, item in enumerate(data):
            new_path = f"{path}.{i}" if path else str(i)
            extract_all_config_values(item, new_path, variables)
    else:
        # 对于基本类型值，生成变量
        if data is not None and data != "":
            var_name = generate_var_name(path)
            variables[var_name] = data
    
    return variables


def replace_with_template_vars(data, path=""):
    """将配置值替换为模板变量"""
    if isinstance(data, dict):
        result = {}
        for key, value in data.items():
            new_path = f"{path}.{key}" if path else key
            result[key] = replace_with_template_vars(value, new_path)
        return result
    elif isinstance(data, list):
        result = []
        for i, item in enumerate(data):
            new_path = f"{path}.{i}" if path else str(i)
            result.append(replace_with_template_vars(item, new_path))
        return result
    else:
        # 对于基本类型值，替换为变量
        if data is not None and data != "":
            var_name = generate_var_name(path)
            # 根据原始值的类型决定是否加引号
            if isinstance(data, str):
                return f'"${{{var_name}}}"'
            else:
                # 数字、布尔值等不加引号
                return f"${{{var_name}}}"
        return data


def main():
    # 用户自定义变量值覆盖配置
    custom_variable_values = {
        # 在这里定义自定义的变量值，会覆盖从 config.yaml 中提取的值
        # 格式: "变量名": "自定义值"
        # 例如:
        # "ADMIN_API_TELEGRAM_BOTTOKEN": "your_custom_bot_token",
        # "ADMIN_API_DATABASE_DEFAULT_LINK": "your_custom_database_connection",
        # "ADMIN_API_REDIS_DEFAULT_PASS": "your_custom_redis_password",
        "ADMIN_API_DATABASE_DEFAULT_LINK": "mysql:root:root@tcp(mysql:3306)/xpayapi?loc=Local&parseTime=true&charset=utf8mb4&timeout=30s&readTimeout=30s&writeTimeout=30s",
        "ADMIN_API_DATABASE_MERCHANT_LINK": "mysql:root:root@tcp(mysql:3306)/merchant?loc=Local&parseTime=true&charset=utf8mb4&timeout=30s&readTimeout=30s&writeTimeout=30s",
        "ADMIN_API_CONSUL_ADDRESS": "consul:8500",
        "ADMIN_API_WALLETSAPI_BASEURL": "http://wallets:8080",
        "ADMIN_API_REDIS_DEFAULT_ADDRESS": "valkey:6379",
        "ADMIN_API_CASDOOR_SERVER_ENDPOINT": "http://casdoor:8000",
        "ADMIN_API_AGENT_CASDOOR_SERVER_ENDPOINT": "http://casdoor:8000",
        "ADMIN_API_MERCHANT_CASDOOR_SERVER_ENDPOINT": "http://casdoor:8000",

    }
    
    # 设置文件路径
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    config_path = project_root / "manifest" / "config" / "config.yaml"
    template_path = project_root / "manifest" / "config" / "config.yaml.template"
    variables_path = project_root / "config_variables.json"
    
    # 检查源文件是否存在
    if not config_path.exists():
        print(f"错误: 配置文件不存在: {config_path}")
        return 1
    
    try:
        # 读取配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        print(f"成功读取配置文件: {config_path}")
        
        # 提取所有配置值作为变量
        variables = extract_all_config_values(config_data)
        
        # 应用用户自定义的变量值覆盖
        for var_name, custom_value in custom_variable_values.items():
            if var_name in variables:
                print(f"覆盖变量 {var_name}: {variables[var_name]} -> {custom_value}")
                variables[var_name] = custom_value
            else:
                print(f"添加自定义变量 {var_name}: {custom_value}")
                variables[var_name] = custom_value
        
        # 生成模板数据
        template_data = replace_with_template_vars(config_data)
        
        # 写入模板文件
        with open(template_path, 'w', encoding='utf-8') as f:
            yaml_content = yaml.dump(template_data, default_flow_style=False, allow_unicode=True, sort_keys=False)
            # 修复双引号转义问题：将 '"${VAR}"' 替换为 "${VAR}"
            yaml_content = re.sub(r"'\"(\$\{[^}]+\})\"'", r'"\1"', yaml_content)
            f.write(yaml_content)
        
        print(f"成功生成模板文件: {template_path}")
        
        # 写入变量文件
        with open(variables_path, 'w', encoding='utf-8') as f:
            json.dump(variables, f, indent=2, ensure_ascii=False)
        
        print(f"成功生成变量文件: {variables_path}")
        print(f"提取了 {len(variables)} 个配置变量")
        
        return 0
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        return 1


if __name__ == "__main__":
    exit(main())