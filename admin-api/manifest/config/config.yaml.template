server:
  address: "${ADMIN_API_SERVER_ADDRESS}"
  openapiPath: "${ADMIN_API_SERVER_OPENAPIPATH}"
  swaggerPath: "${ADMIN_API_SERVER_SWAGGERPATH}"
  logPath: "${ADMIN_API_SERVER_LOGPATH}"
  dumpRouterMap: ${ADMIN_API_SERVER_DUMPROUTERMAP}
dev_mode: ${ADMIN_API_DEV_MODE}
i18n:
  path: "${ADMIN_API_I18N_PATH}"
  language: "${ADMIN_API_I18N_LANGUAGE}"
database:
  logger:
    path: "${ADMIN_API_DATABASE_LOGGER_PATH}"
    level: "${ADMIN_API_DATABASE_LOGGER_LEVEL}"
    stdout: ${ADMIN_API_DATABASE_LOGGER_STDOUT}
  default:
    link: "${ADMIN_API_DATABASE_DEFAULT_LINK}"
    debug: ${ADMIN_API_DATABASE_DEFAULT_DEBUG}
redis:
  default:
    address: "${ADMIN_API_REDIS_DEFAULT_ADDRESS}"
    db: ${ADMIN_API_REDIS_DEFAULT_DB}
    pass: "${ADMIN_API_REDIS_DEFAULT_PASS}"
    idleTimeout: "${ADMIN_API_REDIS_DEFAULT_IDLETIMEOUT}"
logger:
  path: "${ADMIN_API_LOGGER_PATH}"
  level: "${ADMIN_API_LOGGER_LEVEL}"
  stdout: ${ADMIN_API_LOGGER_STDOUT}
  rotateSize: "${ADMIN_API_LOGGER_ROTATESIZE}"
  rotateExpire: "${ADMIN_API_LOGGER_ROTATEEXPIRE}"
  format: "${ADMIN_API_LOGGER_FORMAT}"
health_check:
  port: ${ADMIN_API_HEALTH_CHECK_PORT}
minio:
  endpoint: "${ADMIN_API_MINIO_ENDPOINT}"
  accessKeyID: "${ADMIN_API_MINIO_ACCESSKEYID}"
  secretAccessKey: "${ADMIN_API_MINIO_SECRETACCESSKEY}"
  bucketName: "${ADMIN_API_MINIO_BUCKETNAME}"
  useSSL: ${ADMIN_API_MINIO_USESSL}
  publicURLPrefix: "${ADMIN_API_MINIO_PUBLICURLPREFIX}"
walletsApi:
  baseUrl: "${ADMIN_API_WALLETSAPI_BASEURL}"
certificate: |
  -----BEGIN CERTIFICATE-----
  MIIE3TCCAsWgAwIBAgIDAeJAMA0GCSqGSIb3DQEBCwUAMCgxDjAMBgNVBAoTBWFk
  bWluMRYwFAYDVQQDEw1jZXJ0LWJ1aWx0LWluMB4XDTI1MDUzMDA5MjkwNloXDTQ1
  MDUzMDA5MjkwNlowKDEOMAwGA1UEChMFYWRtaW4xFjAUBgNVBAMTDWNlcnQtYnVp
  bHQtaW4wggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQDbUQw5MjDVUhPU
  kqWH/l7S+XJfNgUCHL65Dul3BsJjD1bVOcjPD09XvEalfVcShZVNPXdojm0V2wcL
  cNDMgG7AlALss2/t/dt9Jd/SFZBmMlM+im99Ubi717ZSfCbews1eV4ItZLidkCUz
  +vW39uOnmn3Y816Bz816fPcZQNP0HkI9i1mx6pYLyfgqP6Pp3r2uy5E93JP0IZga
  Z8oiY9iWjWvLJ7/oCzqUtCu0GWjlXS5sE23dnHpcU41r2nSoIELI/kx+gigQ9mHN
  I7eynjZS+cG0Kr+PVpHkbIjHCmuWxNzKUW8kWgWsiEfLsL6sGHXh2K+d4AAf7Nnh
  JiC5h06lQtilBPSV7UPF8wzmkRtTg8N7GZZG2GLRJ5FTQTkklEJG68UIqS64XN5z
  DzliI7wX7bAVs/ShFNeDYtFmGuY+xZ0u0qCZm5dTQB+UyGSXT+xdQD4nTWCt2O8i
  xvzmPdlavM8e84HY4fmAAzstov/szT130Kfk+JBm3K34jl9gZ7Cxgq5oC4YXq0JD
  NZaj092Gsmg1iWyjt1cMtL7r+JwL0UG0IK49M2NjfvBGoc2V5ede0seui8Xi9jMg
  Hav9qraIxKVJqs/2Cr/1IHMsSwTNXlX3UnzMaDUUQJGggX2q2ucsn6gsyccmmaA4
  ctFN3ad0u0yJLUvytu8KrQbR6K2hzwIDAQABoxAwDjAMBgNVHRMBAf8EAjAAMA0G
  CSqGSIb3DQEBCwUAA4ICAQC2gC5QgWo22b5k8grINiUL3uXuCztB5wZB5dFIjIsi
  KrgtHsEEBh666UKFCW2FahudhbQAn5Ng2BWAZfBsG3iA8vx2Xv5NStTeDnDN/V8c
  td5O7QqgmALUvEMsO0gEfLLzJncASdmwLFHHg/zLYG/VCRdsK+ibcHcmmB0sVYVX
  ugtKlEQo1Pf8NRpGHG1X6YOmzpW3wk49mSuQrOogiFen82A141D/3H1PFXx/eZA3
  mPFG6/R8BApzs6pgipsI6J4dL50rP+5qqTflSOYohcbxMJ2MnsonGQUlvHm4rasj
  kekZJ2m1xSRT8CdZWr/jut/HbIO/UPwX8Z2CEw60B73YWAlCZbM/gVTPtucsJ5aq
  rfQMYaRY4wD2VAxKD47D96X1NBXCEkkE69fraeSdzL9m7UzdSdqt1SbBTOQvVi7j
  eWTPkMzmx61YJFiGCwEErOIYLRNlCSUKGGwZAna+9hKJkTHgDw0FZ0xS+S8EcnTp
  lirU8tq7acXCHKz5JQ7f7+0KMs7j3zHi7oaWVnwksF8RGl/DFBWdRn2iqR7oGZH5
  uCYNELUAAB7HU5nZzS5tvUbS+dOptLPOKhR39NORoqOWdMN15KY32jVsaugm+0mN
  9iMWOULg2VohsUj4Sa+2CNG3DyXTzkQIi4CsQJ/uMiRadwNtZhLwjX9Q8PCNw84i
  6w==
  -----END CERTIFICATE-----
casdoor_server:
  endpoint: "${ADMIN_API_CASDOOR_SERVER_ENDPOINT}"
  client_id: "${ADMIN_API_CASDOOR_SERVER_CLIENT_ID}"
  client_secret: "${ADMIN_API_CASDOOR_SERVER_CLIENT_SECRET}"
  organization: "${ADMIN_API_CASDOOR_SERVER_ORGANIZATION}"
  owner: "${ADMIN_API_CASDOOR_SERVER_OWNER}"
  application: "${ADMIN_API_CASDOOR_SERVER_APPLICATION}"
  frontend_url: "${ADMIN_API_CASDOOR_SERVER_FRONTEND_URL}"
  user: "${ADMIN_API_CASDOOR_SERVER_USER}"
  role: "${ADMIN_API_CASDOOR_SERVER_ROLE}"
  model: "${ADMIN_API_CASDOOR_SERVER_MODEL}"
agent_casdoor_server:
  endpoint: "${ADMIN_API_AGENT_CASDOOR_SERVER_ENDPOINT}"
  client_id: "${ADMIN_API_AGENT_CASDOOR_SERVER_CLIENT_ID}"
  client_secret: "${ADMIN_API_AGENT_CASDOOR_SERVER_CLIENT_SECRET}"
  organization: "${ADMIN_API_AGENT_CASDOOR_SERVER_ORGANIZATION}"
  owner: "${ADMIN_API_AGENT_CASDOOR_SERVER_OWNER}"
  application: "${ADMIN_API_AGENT_CASDOOR_SERVER_APPLICATION}"
  frontend_url: "${ADMIN_API_AGENT_CASDOOR_SERVER_FRONTEND_URL}"
  user: "${ADMIN_API_AGENT_CASDOOR_SERVER_USER}"
  role: "${ADMIN_API_AGENT_CASDOOR_SERVER_ROLE}"
  model: "${ADMIN_API_AGENT_CASDOOR_SERVER_MODEL}"
  default_avatar: "${ADMIN_API_AGENT_CASDOOR_SERVER_DEFAULT_AVATAR}"
merchant_casdoor_server:
  endpoint: "${ADMIN_API_MERCHANT_CASDOOR_SERVER_ENDPOINT}"
  client_id: "${ADMIN_API_MERCHANT_CASDOOR_SERVER_CLIENT_ID}"
  client_secret: "${ADMIN_API_MERCHANT_CASDOOR_SERVER_CLIENT_SECRET}"
  organization: "${ADMIN_API_MERCHANT_CASDOOR_SERVER_ORGANIZATION}"
  owner: "${ADMIN_API_MERCHANT_CASDOOR_SERVER_OWNER}"
  application: "${ADMIN_API_MERCHANT_CASDOOR_SERVER_APPLICATION}"
  frontend_url: "${ADMIN_API_MERCHANT_CASDOOR_SERVER_FRONTEND_URL}"
  user: "${ADMIN_API_MERCHANT_CASDOOR_SERVER_USER}"
  role: "${ADMIN_API_MERCHANT_CASDOOR_SERVER_ROLE}"
  model: "${ADMIN_API_MERCHANT_CASDOOR_SERVER_MODEL}"
  default_avatar: "${ADMIN_API_MERCHANT_CASDOOR_SERVER_DEFAULT_AVATAR}"
  delete_strategy: "${ADMIN_API_MERCHANT_CASDOOR_SERVER_DELETE_STRATEGY}"
consul:
  address: "${ADMIN_API_CONSUL_ADDRESS}"
  token: "${ADMIN_API_CONSUL_TOKEN}"
  config_prefix: "${ADMIN_API_CONSUL_CONFIG_PREFIX}"
cors:
  allowOrigin: "${ADMIN_API_CORS_ALLOWORIGIN}"
  allowCredentials: "${ADMIN_API_CORS_ALLOWCREDENTIALS}"
  exposeHeaders: "${ADMIN_API_CORS_EXPOSEHEADERS}"
  maxAge: ${ADMIN_API_CORS_MAXAGE}
  allowMethods: "${ADMIN_API_CORS_ALLOWMETHODS}"
  allowHeaders: "${ADMIN_API_CORS_ALLOWHEADERS}"
