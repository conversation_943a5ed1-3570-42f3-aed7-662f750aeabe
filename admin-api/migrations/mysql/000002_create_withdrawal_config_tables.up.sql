-- Migration: Create withdrawal configuration tables
-- Created: 2025-08-03
-- Description: Creates tables for withdrawal amount settings, approval settings, and fee settings

-- ============================================================================
-- Withdrawal Amount Settings Table
-- ============================================================================
CREATE TABLE IF NOT EXISTS withdrawal_amount_settings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    currency VARCHAR(20) NOT NULL COMMENT '币种符号 (如 USDT, BTC)',
    network VARCHAR(50) NOT NULL COMMENT '网络类型 (如 TRC20, ERC20)',
    min_amount DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '单笔最小提现金额',
    max_amount DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '单笔最大提现金额',
    status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态: 1-启用, 0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL DEFAULT NULL COMMENT '软删除时间',
    
    UNIQUE KEY uk_currency_network (currency, network, deleted_at),
    INDEX idx_currency (currency),
    INDEX idx_network (network),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提现金额设置表';

-- ============================================================================
-- Withdrawal Approval Settings Table
-- ============================================================================
CREATE TABLE IF NOT EXISTS withdrawal_approval_settings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    currency VARCHAR(20) NOT NULL COMMENT '币种符号 (如 USDT, BTC)',
    network VARCHAR(50) NOT NULL COMMENT '网络类型 (如 TRC20, ERC20)',
    auto_release_min DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '无需审核自动放币最小金额',
    auto_release_max DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '无需审核自动放币最大金额',
    approval_auto_min DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '审核确定后自动放币最小金额',
    approval_auto_max DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '审核确定后自动放币最大金额',
    approval_manual_min DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '审核确定后手动放币最小金额',
    approval_manual_max DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '审核确定后手动放币最大金额',
    status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态: 1-启用, 0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL DEFAULT NULL COMMENT '软删除时间',
    
    UNIQUE KEY uk_currency_network (currency, network, deleted_at),
    INDEX idx_currency (currency),
    INDEX idx_network (network),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提现审核设置表';

-- ============================================================================
-- Withdrawal Fee Settings Table
-- ============================================================================
CREATE TABLE IF NOT EXISTS withdrawal_fee_settings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    currency VARCHAR(20) NOT NULL COMMENT '币种符号 (如 USDT, BTC)',
    network VARCHAR(50) NOT NULL COMMENT '网络类型 (如 TRC20, ERC20)',
    amount_min DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '单笔提现金额范围最小值',
    amount_max DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '单笔提现金额范围最大值',
    fee_type ENUM('fixed', 'percent') NOT NULL DEFAULT 'fixed' COMMENT '手续费类型: fixed-固定金额, percent-百分比',
    fee_value DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '手续费值 (固定金额或百分比)',
    status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态: 1-启用, 0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL DEFAULT NULL COMMENT '软删除时间',
    
    UNIQUE KEY uk_currency_network_range (currency, network, amount_min, amount_max, deleted_at),
    INDEX idx_currency (currency),
    INDEX idx_network (network),
    INDEX idx_fee_type (fee_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提现手续费设置表';
