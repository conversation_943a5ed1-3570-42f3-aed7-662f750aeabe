package codes

import (
	"github.com/gogf/gf/v2/errors/gcode"
)

// 部门管理模块错误码 (3000-3099)
var (
	// CodeDeptNotFound 部门不存在
	CodeDeptNotFound = gcode.New(3001, "部门不存在", nil)
	// CodeDeptCodeExists 部门编码已存在
	CodeDeptCodeExists = gcode.New(3002, "部门编码已存在", nil)
	// CodeDeptHasChildren 部门存在子部门，无法删除
	CodeDeptHasChildren = gcode.New(3003, "部门存在子部门，无法删除", nil)
	// CodeDeptParentNotFound 指定的上级部门不存在
	CodeDeptParentNotFound = gcode.New(3004, "指定的上级部门不存在", nil)
	// CodeDeptCannotSetParentToChild 不能将上级部门设置为自身或其子孙部门
	CodeDeptCannotSetParentToChild = gcode.New(3005, "不能将上级部门设置为自身或其子孙部门", nil)
	// CodeDeptInvalidType 无效的部门类型
	CodeDeptInvalidType = gcode.New(3006, "无效的部门类型", nil)
)
