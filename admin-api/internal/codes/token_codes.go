package codes

import "github.com/gogf/gf/v2/errors/gcode"

var (
	// 代币管理 (3000-3999)
	CodeTokenNotFound              = gcode.New(3001, "代币信息不存在", nil)
	CodeTokenNetworkSymbolExists   = gcode.New(3002, "该网络下代币符号已存在", nil)
	CodeTokenNetworkContractExists = gcode.New(3003, "该网络下合约地址已存在", nil)
	CodeTokenCreateFailed          = gcode.New(3004, "代币创建失败", nil)
	CodeTokenUpdateFailed          = gcode.New(3005, "代币更新失败", nil)
	CodeTokenDeleteFailed          = gcode.New(3006, "代币删除失败", nil)
	CodeTokenInvalidAmountLimit    = gcode.New(3007, "无效的金额限制(最小值不能大于最大值且需为有效数字或-1)", nil)
	CodeTokenInvalidFeeAmount      = gcode.New(3008, "无效的手续费金额(需为非负数字)", nil)
)
