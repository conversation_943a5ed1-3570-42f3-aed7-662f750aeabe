package codes

import (
	"github.com/gogf/gf/v2/errors/gcode"
)

// Config Management Module Error Codes (4100-4199)
var (
	// --- Category Errors (4100-4119) ---
	CodeCategoryNotFound    = gcode.New(4100, "配置分类不存在", nil)
	CodeCategoryKeyExists   = gcode.New(4101, "配置分类 Key 已存在", nil)
	CodeCategoryHasItems    = gcode.New(4102, "配置分类下存在配置项，无法删除", nil) // If not using cascade delete
	CodeCategoryKeyInvalid  = gcode.New(4103, "配置分类 Key 格式无效 (只能包含小写字母、数字和下划线)", nil)
	CodeCategoryNameInvalid = gcode.New(4104, "配置分类名称无效", nil) // Example if needed

	// --- Item Errors (4120-4139) ---
	CodeItemNotFound           = gcode.New(4120, "配置项不存在", nil)
	CodeItemKeyExists          = gcode.New(4121, "配置项 Key 已存在", nil)
	CodeItemKeyFormatError     = gcode.New(4122, "配置项 Key 格式错误 (必须以 'categoryKey.' 开头)", nil)
	CodeItemValueTypeError     = gcode.New(4123, "配置项值与类型不匹配", nil)
	CodeItemInvalidJSON        = gcode.New(4124, "配置项值不是有效的 JSON 格式", nil)
	CodeItemCategoryIDRequired = gcode.New(4125, "所属分类 ID 不能为空", nil)
	CodeItemKeyRequired        = gcode.New(4126, "配置项 Key 不能为空", nil)
	CodeItemValueTypeRequired  = gcode.New(4127, "配置项值类型不能为空", nil)
	CodeItemCategoryNotFound   = gcode.New(4128, "配置项所属的分类不存在", nil) // Added based on doc
)
