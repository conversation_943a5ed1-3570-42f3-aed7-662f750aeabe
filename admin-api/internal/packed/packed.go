package packed

import (
	"admin-api/internal/codes"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// 业务错误码范围
const (
	// 业务错误码范围开始
	BusinessErrorCodeStart = 1000
	// 业务错误码范围结束
	BusinessErrorCodeEnd = 9999
)

// ResponseMiddleware 自定义响应中间件
func ResponseMiddleware(r *ghttp.Request) {
	r.Middleware.Next()

	// 检查是否是文件下载类型的响应（CSV、Excel等）
	contentType := r.Response.Header().Get("Content-Type")
	if strings.Contains(contentType, "text/csv") ||
		strings.Contains(contentType, "application/octet-stream") ||
		strings.Contains(contentType, "application/vnd.ms-excel") {
		return
	}

	// 如果已经有返回内容，不再处理
	if r.Response.BufferLength() > 0 {
		return
	}

	var (
		err = r.GetError()
		res = r.GetHandlerResponse()
	)

	// --- 添加日志开始 ---
	if err != nil {
		g.Log().Warningf(r.GetCtx(), "[DEBUG] ResponseMiddleware received error: %#v", err) // 记录原始错误对象
		g.Log().Warningf(r.GetCtx(), "[DEBUG] Type of error: %T", err)                      // 记录错误类型
		extractedCode := gerror.Code(err)
		g.Log().Warningf(r.GetCtx(), "[DEBUG] Result of gerror.Code(err): %#v", extractedCode) // 记录 gerror.Code 的结果
	}
	// --- 添加日志结束 ---

	if err != nil {
		// 错误响应处理
		code := 1
		msg := err.Error()
		errorMessage := err.Error()

		// 如果是 gerror 类型的错误，则提取错误码
		if e := gerror.Code(err); e != nil {
			code = e.Code()
			msg = e.Message()
		} else {
			// 非自定义错误使用通用错误码
			code = codes.CodeError.Code()
		}

		// 对常见错误进行特殊处理，提供更友好的错误信息
		if code == codes.CodeInternalError.Code() {
			// 检查是否是 JSON 解析错误
			if strings.Contains(errorMessage, "unexpected end of JSON input") {
				msg = "用户信息已过期，请重新登录"
				code = codes.CodeUserInfoExpired.Code()
			} else if strings.Contains(errorMessage, "反序列化") {
				msg = "缓存数据错误，请重新登录"
				code = codes.CodeUserCacheError.Code()
			}
		}

		// 区分业务错误和系统错误
		// 业务错误只记录简单信息，系统错误记录完整堆栈
		if code >= BusinessErrorCodeStart && code <= BusinessErrorCodeEnd {
			// 业务错误，只记录简单日志
			g.Log().Info(r.GetCtx(), "业务错误:", msg, "错误码:", code)
		} else {
			// 系统错误，记录完整堆栈
			g.Log().Error(r.GetCtx(), err)
		}

		r.Response.WriteJson(g.Map{
			"code":          code,         // 错误码
			"message":       msg,          // 通用错误信息
			"error_message": errorMessage, // 详细错误信息
			"data":          nil,          // 无数据
		})
		return
	}

	// 成功响应处理
	r.Response.WriteJson(g.Map{
		"code":          codes.CodeSuccess.Code(),    // 成功码
		"message":       codes.CodeSuccess.Message(), // 成功提示
		"error_message": "",                          // 无错误信息
		"data":          res,                         // 返回数据
	})
}
