package config

import (
	"sync/atomic"
	"time"
)

// Metrics 配置系统指标
type Metrics struct {
	// 配置读取次数
	ReadCount atomic.Int64
	// 配置读取失败次数
	ReadFailureCount atomic.Int64
	// Consul 同步成功次数
	ConsulSyncSuccess atomic.Int64
	// Consul 同步失败次数
	ConsulSyncFailure atomic.Int64
	// 最后同步时间
	LastSyncTime atomic.Value // time.Time
}

var metrics = &Metrics{}

// GetMetrics 获取配置系统指标
func GetMetrics() *Metrics {
	return metrics
}

// GetMetricsMap 获取指标的 map 格式（用于监控导出）
func GetMetricsMap() map[string]interface{} {
	lastSync := metrics.LastSyncTime.Load()
	lastSyncTime := ""
	if t, ok := lastSync.(time.Time); ok {
		lastSyncTime = t.Format(time.RFC3339)
	}

	return map[string]interface{}{
		"read_count":          metrics.ReadCount.Load(),
		"read_failure_count":  metrics.ReadFailureCount.Load(),
		"consul_sync_success": metrics.ConsulSyncSuccess.Load(),
		"consul_sync_failure": metrics.ConsulSyncFailure.Load(),
		"last_sync_time":      lastSyncTime,
	}
}
