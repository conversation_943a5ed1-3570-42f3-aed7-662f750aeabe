package config

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/hashicorp/consul/api"
)

// ConsulSync Consul 配置同步器
type ConsulSync struct {
	client *api.Client
	prefix string // Consul KV 前缀
}

// 全局 Consul 同步器实例
var consulSync *ConsulSync

// InitConsulSync 初始化 Consul 同步器
func InitConsulSync(ctx context.Context) error {
	// 从配置文件读取 Consul 配置
	address := g.Cfg().MustGet(ctx, "consul.address", "127.0.0.1:8500").String()
	token := g.Cfg().MustGet(ctx, "consul.token", "").String()
	prefix := g.Cfg().MustGet(ctx, "consul.config_prefix", "xpay/config").String()

	// 创建 Consul 客户端配置
	config := api.DefaultConfig()
	config.Address = address
	if token != "" {
		config.Token = token
	}

	// 创建 Consul 客户端
	client, err := api.NewClient(config)
	if err != nil {
		return fmt.Errorf("创建 Consul 客户端失败: %w", err)
	}

	// 测试连接和权限
	kv := client.KV()
	testKey := fmt.Sprintf("%s/_health_check", prefix)
	testPair := &api.KVPair{
		Key:   testKey,
		Value: []byte("test"),
	}

	_, err = kv.Put(testPair, nil)
	if err != nil {
		g.Log().Errorf(ctx, "Consul 连接测试失败: %v", err)
		panic(err)
	}

	// 清理测试数据
	kv.Delete(testKey, nil)

	consulSync = &ConsulSync{
		client: client,
		prefix: prefix,
	}

	g.Log().Infof(ctx, "Consul 同步器初始化成功: address=%s, prefix=%s", address, prefix)
	return nil
}

// SyncToConsul 同步单个配置到 Consul
func (cs *ConsulSync) SyncToConsul(ctx context.Context, key, value string) error {
	if cs == nil {
		return fmt.Errorf("Consul 同步器未初始化")
	}

	kv := cs.client.KV()

	// 构造完整的 key 路径
	fullKey := fmt.Sprintf("%s/%s", cs.prefix, key)

	// 写入 Consul
	p := &api.KVPair{
		Key:   fullKey,
		Value: []byte(value),
	}

	_, err := kv.Put(p, nil)
	if err != nil {
		return fmt.Errorf("写入 Consul 失败: key=%s, error=%w", fullKey, err)
	}

	g.Log().Debugf(ctx, "配置已同步到 Consul: key=%s", fullKey)
	return nil
}

// SyncConfigData 同步配置数据到 Consul（JSON格式）
func (cs *ConsulSync) SyncConfigData(ctx context.Context, key string, data *ConfigData) error {
	if cs == nil {
		return fmt.Errorf("consul 同步器未初始化")
	}

	// 将配置数据序列化为 JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化配置数据失败: %w", err)
	}

	// 存储 JSON 数据
	return cs.SyncToConsul(ctx, key, string(jsonData))
}

// DeleteFromConsul 从 Consul 删除配置
func (cs *ConsulSync) DeleteFromConsul(ctx context.Context, key string) error {
	if cs == nil {
		return fmt.Errorf("Consul 同步器未初始化")
	}

	kv := cs.client.KV()

	// 构造完整的 key 路径
	fullKey := fmt.Sprintf("%s/%s", cs.prefix, key)

	// 从 Consul 删除
	_, err := kv.Delete(fullKey, nil)
	if err != nil {
		return fmt.Errorf("从 Consul 删除失败: key=%s, error=%w", fullKey, err)
	}

	g.Log().Debugf(ctx, "配置已从 Consul 删除: key=%s", fullKey)
	return nil
}

// SyncCategoryToConsul 同步整个分类到 Consul
func (cs *ConsulSync) SyncCategoryToConsul(ctx context.Context, category string, data map[string]interface{}) error {
	if cs == nil {
		return fmt.Errorf("Consul 同步器未初始化")
	}

	// 将分类数据序列化为 JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化分类数据失败: %w", err)
	}

	// 存储为分类级别的 JSON
	categoryKey := fmt.Sprintf("category/%s", category)
	return cs.SyncToConsul(ctx, categoryKey, string(jsonData))
}

// GetConsulSync 获取全局 Consul 同步器实例
func GetConsulSync() *ConsulSync {
	return consulSync
}
