package config

import (
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/os/glog"
)

// OnConfigItemCreated 配置项创建后的钩子函数
func OnConfigItemCreated(ctx context.Context, item *entity.AdminConfigItems) {
	glog.Debug(ctx, "配置项创建，更新缓存:", item.Key)
	UpdateCache(ctx, item)
}

// OnConfigItemUpdated 配置项更新后的钩子函数
func OnConfigItemUpdated(ctx context.Context, item *entity.AdminConfigItems) {
	glog.Debug(ctx, "配置项更新，更新缓存:", item.Key)
	UpdateCache(ctx, item)
}

// OnConfigItemDeleted 配置项删除后的钩子函数
func OnConfigItemDeleted(ctx context.Context, key string) {
	glog.Debug(ctx, "配置项删除，从缓存移除:", key)
	RemoveFromCache(ctx, key)
}

// OnConfigItemsDeleted 多个配置项删除后的钩子函数
func OnConfigItemsDeleted(ctx context.Context, keys []string) {
	glog.Debug(ctx, "多个配置项删除，从缓存移除:", keys)
	for _, key := range keys {
		RemoveFromCache(ctx, key)
	}
}

// OnConfigCategoryDeleted 配置分类删除后的钩子函数
func OnConfigCategoryDeleted(ctx context.Context, categoryKey string) {
	glog.Debug(ctx, "配置分类删除，刷新缓存:", categoryKey)
	// 简单起见，直接刷新整个缓存
	RefreshCache(ctx)
}
