package config

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
)

// Validator 配置验证器接口
type Validator interface {
	Validate(ctx context.Context, key, value string) error
}

// ValidatorFunc 验证函数类型
type ValidatorFunc func(ctx context.Context, key, value string) error

// Validate 实现 Validator 接口
func (f ValidatorFunc) Validate(ctx context.Context, key, value string) error {
	return f(ctx, key, value)
}

// 内置验证器
var (
	// IntValidator 整数验证器
	IntValidator = ValidatorFunc(func(ctx context.Context, key, value string) error {
		_, err := strconv.Atoi(value)
		if err != nil {
			return fmt.Errorf("配置 %s 必须是整数", key)
		}
		return nil
	})

	// BoolValidator 布尔验证器
	BoolValidator = ValidatorFunc(func(ctx context.Context, key, value string) error {
		_, err := strconv.ParseBool(value)
		if err != nil {
			return fmt.Errorf("配置 %s 必须是布尔值 (true/false)", key)
		}
		return nil
	})

	// URLValidator URL验证器
	URLValidator = ValidatorFunc(func(ctx context.Context, key, value string) error {
		urlRegex := regexp.MustCompile(`^https?://[^\s]+$`)
		if !urlRegex.MatchString(value) {
			return fmt.Errorf("配置 %s 必须是有效的URL", key)
		}
		return nil
	})
)

// RangeValidator 创建范围验证器
func RangeValidator(min, max int) Validator {
	return ValidatorFunc(func(ctx context.Context, key, value string) error {
		n, err := strconv.Atoi(value)
		if err != nil {
			return fmt.Errorf("配置 %s 必须是整数", key)
		}
		if n < min || n > max {
			return fmt.Errorf("配置 %s 必须在 %d 到 %d 之间", key, min, max)
		}
		return nil
	})
}

// RegexValidator 创建正则验证器
func RegexValidator(pattern string) Validator {
	re := regexp.MustCompile(pattern)
	return ValidatorFunc(func(ctx context.Context, key, value string) error {
		if !re.MatchString(value) {
			return fmt.Errorf("配置 %s 格式不正确", key)
		}
		return nil
	})
}

// 配置验证规则注册表
var validators = make(map[string]Validator)

// RegisterValidator 注册配置验证器
func RegisterValidator(keyPattern string, validator Validator) {
	validators[keyPattern] = validator
}

// ValidateConfig 验证配置值
func ValidateConfig(ctx context.Context, key, value string) error {
	// 精确匹配
	if validator, ok := validators[key]; ok {
		return validator.Validate(ctx, key, value)
	}

	// 模式匹配 (支持通配符)
	for pattern, validator := range validators {
		if matchPattern(pattern, key) {
			return validator.Validate(ctx, key, value)
		}
	}

	return nil
}

// matchPattern 简单的模式匹配
func matchPattern(pattern, key string) bool {
	if pattern == "*" {
		return true
	}
	// 支持 "prefix.*" 格式
	if len(pattern) > 2 && pattern[len(pattern)-2:] == ".*" {
		prefix := pattern[:len(pattern)-2]
		return len(key) >= len(prefix) && key[:len(prefix)] == prefix
	}
	return pattern == key
}

// 使用示例：
// func init() {
//     // 注册验证规则
//     config.RegisterValidator("api.max_retry", config.RangeValidator(1, 10))
//     config.RegisterValidator("api.timeout", config.RangeValidator(1000, 60000))
//     config.RegisterValidator("app.debug", config.BoolValidator)
//     config.RegisterValidator("webhook.url", config.URLValidator)
// }
