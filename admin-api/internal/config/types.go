package config

import (
	"encoding/json"
	"strconv"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/shopspring/decimal"
)

// ConfigValueType 配置值类型
type ConfigValueType string

const (
	TypeText     ConfigValueType = "text"
	TypePassword ConfigValueType = "password"
	TypeBoolean  ConfigValueType = "boolean"
	TypeNumber   ConfigValueType = "number"
	TypeJSON     ConfigValueType = "json"
	TypeMap      ConfigValueType = "map"
	TypeArray    ConfigValueType = "array"
)

// ConfigData 存储在 Consul 中的配置数据结构
type ConfigData struct {
	Value     string          `json:"value"`
	ValueType ConfigValueType `json:"value_type"`
}

// ParseConfigValue 解析配置值并返回正确的类型
func ParseConfigValue(data *ConfigData) (interface{}, error) {
	if data == nil {
		return nil, gerror.New("配置数据为空")
	}

	switch data.ValueType {
	case TypeBoolean:
		return strconv.ParseBool(data.Value)

	case TypeNumber:
		// 使用 decimal 解析，保证精度
		dec, err := decimal.NewFromString(data.Value)
		if err != nil {
			return nil, gerror.Wrapf(err, "解析数字失败: %s", data.Value)
		}
		return dec, nil

	case TypeJSON, TypeMap, TypeArray:
		var result interface{}
		err := json.Unmarshal([]byte(data.Value), &result)
		return result, err

	case TypeText, TypePassword:
		fallthrough
	default:
		return data.Value, nil
	}
}

// GetStringValue 获取字符串值（所有类型都可以转为字符串）
func GetStringValue(data *ConfigData) string {
	if data == nil {
		return ""
	}
	return data.Value
}

// GetBoolValue 获取布尔值
func GetBoolValue(data *ConfigData) (bool, error) {
	if data == nil {
		return false, gerror.New("配置数据为空")
	}

	if data.ValueType == TypeBoolean {
		return strconv.ParseBool(data.Value)
	}

	// 尝试从其他类型转换
	switch data.Value {
	case "1", "true", "True", "TRUE", "yes", "Yes", "YES", "on", "On", "ON":
		return true, nil
	case "0", "false", "False", "FALSE", "no", "No", "NO", "off", "Off", "OFF":
		return false, nil
	default:
		return false, gerror.Newf("无法将 %s 转换为布尔值", data.Value)
	}
}

// GetIntValue 获取整数值
func GetIntValue(data *ConfigData) (int, error) {
	if data == nil {
		return 0, gerror.New("配置数据为空")
	}

	if data.ValueType == TypeNumber {
		// 优先尝试解析为整数
		if intVal, err := strconv.Atoi(data.Value); err == nil {
			return intVal, nil
		}
		// 如果是浮点数，转换为整数
		if floatVal, err := strconv.ParseFloat(data.Value, 64); err == nil {
			return int(floatVal), nil
		}
	}

	// 尝试从字符串转换
	return strconv.Atoi(data.Value)
}

// GetMapValue 获取 Map 值
func GetMapValue(data *ConfigData) (map[string]interface{}, error) {
	if data == nil {
		return nil, gerror.New("配置数据为空")
	}

	var result map[string]interface{}

	if data.ValueType == TypeMap || data.ValueType == TypeJSON {
		err := json.Unmarshal([]byte(data.Value), &result)
		return result, err
	}

	return nil, gerror.Newf("配置类型 %s 不是 Map", data.ValueType)
}

// GetDecimalValue 获取 Decimal 值（高精度数字）
func GetDecimalValue(data *ConfigData) (decimal.Decimal, error) {
	if data == nil {
		return decimal.Zero, gerror.New("配置数据为空")
	}

	// 不管什么类型，都尝试解析为 decimal
	dec, err := decimal.NewFromString(data.Value)
	if err != nil {
		return decimal.Zero, gerror.Wrapf(err, "无法解析为 Decimal: %s", data.Value)
	}

	return dec, nil
}

// GetFloat64Value 获取浮点数值
func GetFloat64Value(data *ConfigData) (float64, error) {
	if data == nil {
		return 0, gerror.New("配置数据为空")
	}

	if data.ValueType == TypeNumber {
		dec, err := decimal.NewFromString(data.Value)
		if err != nil {
			return 0, gerror.Wrapf(err, "解析数字失败: %s", data.Value)
		}
		return dec.InexactFloat64(), nil
	}

	// 尝试直接解析
	return strconv.ParseFloat(data.Value, 64)
}

// FormatDecimalValue 格式化 Decimal 值为字符串
func FormatDecimalValue(value decimal.Decimal) string {
	return value.String()
}
