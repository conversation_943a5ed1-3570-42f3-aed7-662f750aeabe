package casdoor

import (
	"admin-api/internal/model/entity"
	"admin-api/utility/totp"
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

type MerchantSyncService struct {
	client         *casdoorsdk.Client
	defaultAvatar  string
	deleteStrategy string // 删除策略: "soft" - 软删除(禁用), "hard" - 硬删除(真正删除)
}

// NewMerchantSyncService 创建商户同步服务
func NewMerchantSyncService(ctx context.Context) (*MerchantSyncService, error) {
	client, err := GetMerchantCasdoorClient(ctx)
	if err != nil {
		return nil, err
	}

	// 获取默认头像配置
	defaultAvatar := g.Cfg().MustGet(ctx, "merchant_casdoor_server.default_avatar").String()
	if defaultAvatar == "" {
		defaultAvatar = "https://cdn.casbin.org/img/casbin.svg" // 默认值
	}

	// 获取删除策略配置
	deleteStrategy := g.Cfg().MustGet(ctx, "merchant_casdoor_server.delete_strategy").String()
	if deleteStrategy == "" {
		deleteStrategy = "hard" // 默认为软删除
	}

	return &MerchantSyncService{
		client:         client,
		defaultAvatar:  defaultAvatar,
		deleteStrategy: deleteStrategy,
	}, nil
}

// SyncAddMerchant 同步添加商户到 Casdoor，返回生成的 TOTP Secret 和恢复码
func (s *MerchantSyncService) SyncAddMerchant(ctx context.Context, merchant *entity.Merchants, userAccount string, userEmail string, password string) (totpSecret string, recoveryCodes []string, err error) {
	// 生成 TOTP Secret
	totpSecret, err = totp.GenerateTOTPSecret()
	if err != nil {
		g.Log().Errorf(ctx, "生成 TOTP Secret 失败: %v", err)
		return "", nil, fmt.Errorf("生成 TOTP Secret 失败: %w", err)
	}

	// 生成恢复码
	recoveryCodes = totp.GenerateRecoveryCodes()

	// 调用带 MFA 的方法
	err = s.SyncAddMerchantWithMFA(ctx, merchant, userAccount, userEmail, password, totpSecret, recoveryCodes)
	if err != nil {
		return "", nil, err
	}

	return totpSecret, recoveryCodes, nil
}

// SyncAddMerchantWithMFA 同步添加商户到 Casdoor，使用预生成的 TOTP Secret 和恢复码
func (s *MerchantSyncService) SyncAddMerchantWithMFA(ctx context.Context, merchant *entity.Merchants, userAccount string, userEmail string, password string, totpSecret string, recoveryCodes []string) error {
	user := &casdoorsdk.User{
		Owner:             s.client.OrganizationName,
		SignupApplication: s.client.ApplicationName,
		Name:              userAccount, // 使用 users.account 作为用户名
		Email:             userEmail,
		DisplayName:       merchant.MerchantName, // 使用商户名称作为显示名
		Password:          password,
		Type:              "normal-user",
		Avatar:            s.defaultAvatar,
		IsForbidden:       merchant.Status == 0, // 状态为0时禁用
		CreatedTime:       gtime.Now().Format("Y-m-d H:i:s"),
		TotpSecret:        totpSecret,    // 预设 TOTP Secret
		RecoveryCodes:     recoveryCodes, // 预设恢复码
		MfaPhoneEnabled:   false,         // 默认不启用手机 MFA
		MfaEmailEnabled:   false,         // 默认不启用邮箱 MFA

		// 使用 Properties 存储额外信息
		Properties: map[string]string{
			"merchantId":   fmt.Sprintf("%d", merchant.MerchantId),
			"status":       fmt.Sprintf("%d", merchant.Status),
			"businessName": merchant.BusinessName,
			"websiteUrl":   merchant.WebsiteUrl,
			"notes":        merchant.Notes,
		},
	}

	// 分配商户角色
	merchantRole := g.Cfg().MustGet(ctx, "merchant_casdoor_server.role").String()
	if merchantRole != "" {
		// 从配置中解析组织名和角色名
		user.Roles = []*casdoorsdk.Role{
			{
				Owner: s.client.OrganizationName,
				Name:  getResourceName(merchantRole), // 提取角色名
			},
		}
	}

	_, err := s.client.AddUser(user)
	if err != nil {
		g.Log().Errorf(ctx, "同步添加商户到 Casdoor 失败: %v", err)
		return err
	}

	g.Log().Infof(ctx, "成功同步商户 %s 到 Casdoor (TOTP Secret: %s)", userAccount, totpSecret)
	return nil
}

// SyncUpdateMerchant 同步更新商户信息到 Casdoor
func (s *MerchantSyncService) SyncUpdateMerchant(ctx context.Context, merchant *entity.Merchants, userAccount string, userEmail string) error {
	// 先获取 Casdoor 中的用户信息
	user, err := s.client.GetUser(userAccount)
	if err != nil {
		return fmt.Errorf("获取 Casdoor 用户失败: %w", err)
	}

	if user == nil {
		// 用户不存在，可能需要创建
		return fmt.Errorf("Casdoor 中不存在用户: %s", userAccount)
	}

	// 更新字段
	user.Email = userEmail
	user.DisplayName = merchant.MerchantName
	user.IsForbidden = merchant.Status == 0 // 状态为0时禁用

	// 更新 Properties
	if user.Properties == nil {
		user.Properties = make(map[string]string)
	}
	user.Properties["lastUpdated"] = gtime.Now().Format("Y-m-d H:i:s")
	user.Properties["status"] = fmt.Sprintf("%d", merchant.Status)
	user.Properties["businessName"] = merchant.BusinessName
	user.Properties["websiteUrl"] = merchant.WebsiteUrl
	user.Properties["notes"] = merchant.Notes

	// 只更新特定字段
	columns := []string{"email", "displayName", "isForbidden", "properties"}

	_, err = s.client.UpdateUserForColumns(user, columns)
	if err != nil {
		g.Log().Errorf(ctx, "同步更新商户到 Casdoor 失败: %v", err)
		return err
	}

	g.Log().Infof(ctx, "成功更新商户 %s 到 Casdoor", userAccount)
	return nil
}

// SyncUpdatePassword 同步更新密码到 Casdoor
func (s *MerchantSyncService) SyncUpdatePassword(ctx context.Context, userAccount string, newPassword string) error {
	// Casdoor 的 SetPassword 方法不需要旧密码
	_, err := s.client.SetPassword(s.client.OrganizationName, userAccount, "", newPassword)
	if err != nil {
		g.Log().Errorf(context.Background(), "同步更新商户密码到 Casdoor 失败: %v", err)
		return err
	}
	return nil
}

// SyncDeleteMerchant 同步删除商户（根据配置决定软删除或硬删除）
func (s *MerchantSyncService) SyncDeleteMerchant(ctx context.Context, userAccount string) error {
	user, err := s.client.GetUser(userAccount)
	if err != nil {
		return fmt.Errorf("获取 Casdoor 用户失败: %w", err)
	}

	if user == nil {
		// 用户不存在，直接返回成功
		g.Log().Infof(ctx, "用户 %s 在 Casdoor 中不存在，跳过删除", userAccount)
		return nil
	}

	// 根据配置决定删除策略
	if s.deleteStrategy == "hard" {
		// 硬删除：真正删除用户
		return s.hardDeleteUser(ctx, userAccount)
	} else {
		// 软删除：禁用用户
		return s.softDeleteUser(ctx, user, userAccount)
	}
}

// softDeleteUser 软删除用户（禁用）
func (s *MerchantSyncService) softDeleteUser(ctx context.Context, user *casdoorsdk.User, userAccount string) error {
	// 禁用用户而不是删除
	user.IsForbidden = true

	// 更新 Properties 记录删除时间
	if user.Properties == nil {
		user.Properties = make(map[string]string)
	}
	user.Properties["deletedAt"] = gtime.Now().Format("Y-m-d H:i:s")

	columns := []string{"isForbidden", "properties"}
	_, err := s.client.UpdateUserForColumns(user, columns)
	if err != nil {
		g.Log().Errorf(ctx, "禁用 Casdoor 用户失败: %v", err)
		return fmt.Errorf("禁用 Casdoor 用户失败: %w", err)
	}

	g.Log().Infof(ctx, "成功禁用 Casdoor 用户 %s", userAccount)
	return nil
}

// hardDeleteUser 硬删除用户（真正删除）
func (s *MerchantSyncService) hardDeleteUser(ctx context.Context, userAccount string) error {
	// 先删除用户的MFA设置
	if err := s.DeleteMFA(ctx, userAccount); err != nil {
		g.Log().Warningf(ctx, "删除用户 %s 的MFA设置失败: %v", userAccount, err)
		// 继续删除用户，不因MFA删除失败而中断
	}

	// 删除用户
	success, err := s.client.DeleteUser(&casdoorsdk.User{
		Owner: s.client.OrganizationName,
		Name:  userAccount,
	})
	if err != nil {
		g.Log().Errorf(ctx, "硬删除 Casdoor 用户失败: %v", err)
		return fmt.Errorf("硬删除 Casdoor 用户失败: %w", err)
	}
	if !success {
		g.Log().Errorf(ctx, "硬删除 Casdoor 用户失败: success=false")
		return fmt.Errorf("硬删除 Casdoor 用户失败: success=false")
	}

	g.Log().Infof(ctx, "成功硬删除 Casdoor 用户 %s", userAccount)
	return nil
}

// ForceHardDeleteMerchant 强制硬删除商户（忽略配置，直接删除）
func (s *MerchantSyncService) ForceHardDeleteMerchant(ctx context.Context, userAccount string) error {
	return s.hardDeleteUser(ctx, userAccount)
}

// DeleteMFA 调用 Casdoor 的 delete-mfa API 端点
func (s *MerchantSyncService) DeleteMFA(ctx context.Context, userAccount string) error {
	// 构建请求参数
	params := map[string]string{
		"owner": s.client.OrganizationName,
		"name":  userAccount,
	}

	// 转换为 JSON
	paramBytes, err := json.Marshal(params)
	if err != nil {
		return fmt.Errorf("序列化参数失败: %w", err)
	}

	// 调用 delete-mfa API
	response, err := s.client.DoPost("delete-mfa", nil, paramBytes, false, false)
	if err != nil {
		// 检查错误信息是否表示用户不存在
		errStr := err.Error()
		errLower := strings.ToLower(errStr)
		if strings.Contains(errLower, "doesn't exist") ||
			strings.Contains(errLower, "does not exist") ||
			strings.Contains(errLower, "not found") ||
			strings.Contains(errLower, "用户不存在") {
			g.Log().Warningf(ctx, "用户 %s 在 Casdoor 中不存在，跳过删除 MFA", userAccount)
			return nil
		}
		g.Log().Errorf(ctx, "调用 delete-mfa API 失败: %v", err)
		return fmt.Errorf("删除 MFA 失败: %w", err)
	}

	if response.Status != "ok" {
		// 如果用户不存在，这不是错误 - 没有什么需要删除的
		msgLower := strings.ToLower(response.Msg)
		if strings.Contains(msgLower, "doesn't exist") ||
			strings.Contains(msgLower, "does not exist") ||
			strings.Contains(msgLower, "not found") ||
			strings.Contains(msgLower, "用户不存在") {
			g.Log().Warningf(ctx, "用户 %s 在 Casdoor 中不存在，跳过删除 MFA", userAccount)
			return nil
		}
		return fmt.Errorf("删除 MFA 失败: %s", response.Msg)
	}

	g.Log().Infof(ctx, "成功通过 delete-mfa API 删除用户 %s 的 MFA", userAccount)
	return nil
}

// BatchSyncMerchants 批量同步商户（用于数据迁移）
func (s *MerchantSyncService) BatchSyncMerchants(ctx context.Context, merchants []*entity.Merchants) (int, int, error) {
	successCount := 0
	failCount := 0

	for _, merchant := range merchants {
		// 检查用户是否已存在（使用商户名称）
		existingUser, _ := s.client.GetUser(merchant.MerchantName)
		if existingUser != nil {
			g.Log().Infof(ctx, "用户 %s 已存在，跳过", merchant.MerchantName)
			successCount++
			continue
		}

		// 使用临时密码，需要用户首次登录时修改
		tempPassword := fmt.Sprintf("Temp_%s_2024!", merchant.MerchantName)

		// 使用商户自己的邮箱
		_, _, err := s.SyncAddMerchant(ctx, merchant, merchant.MerchantName, merchant.Email, tempPassword)
		if err != nil {
			g.Log().Errorf(ctx, "同步商户 %s 失败: %v", merchant.MerchantName, err)
			failCount++
			continue
		}
		successCount++
	}

	g.Log().Infof(ctx, "批量同步完成: 成功 %d, 失败 %d", successCount, failCount)
	if failCount > 0 {
		return successCount, failCount, fmt.Errorf("批量同步部分失败")
	}
	return successCount, failCount, nil
}

// CheckUserExists 检查用户是否在Casdoor中存在
func (s *MerchantSyncService) CheckUserExists(ctx context.Context, userAccount string) (bool, error) {
	user, err := s.client.GetUser(userAccount)
	if err != nil {
		// 如果是网络错误或其他系统错误，返回错误
		return false, fmt.Errorf("检查Casdoor用户存在性失败: %w", err)
	}
	return user != nil, nil
}

// CheckEmailExists 检查邮箱是否在Casdoor中存在
func (s *MerchantSyncService) CheckEmailExists(ctx context.Context, email string) (bool, error) {
	// 尝试通过邮箱查找用户（如果Casdoor支持的话）
	// 注意：这里使用GetUsers()然后过滤，在生产环境中可能需要优化
	// 如果Casdoor SDK支持按邮箱查询，应该使用更高效的方法
	users, err := s.client.GetUsers()
	if err != nil {
		return false, fmt.Errorf("获取Casdoor用户列表失败: %w", err)
	}

	for _, user := range users {
		if user.Email == email {
			return true, nil
		}
	}
	return false, nil
}

// HealthCheck 健康检查
func (s *MerchantSyncService) HealthCheck(ctx context.Context) error {
	// 测试连接到 Casdoor
	_, err := s.client.GetUsers()
	if err != nil {
		return fmt.Errorf("Casdoor 连接失败: %w", err)
	}
	return nil
}

// GetUserMFAInfo 获取用户的 MFA 信息（TOTP Secret 和恢复码）
func (s *MerchantSyncService) GetUserMFAInfo(ctx context.Context, userAccount string) (totpSecret string, recoveryCodes []string, err error) {
	user, err := s.client.GetUser(userAccount)
	if err != nil {
		return "", nil, fmt.Errorf("获取用户信息失败: %w", err)
	}

	if user == nil {
		return "", nil, fmt.Errorf("用户不存在: %s", userAccount)
	}

	return user.TotpSecret, user.RecoveryCodes, nil
}

// SetUserMFAInfo 设置用户的 MFA 信息
func (s *MerchantSyncService) SetUserMFAInfo(ctx context.Context, userAccount string, totpSecret string, recoveryCodes []string) error {
	user, err := s.client.GetUser(userAccount)
	if err != nil {
		return fmt.Errorf("获取用户信息失败: %w", err)
	}

	if user == nil {
		return fmt.Errorf("用户不存在: %s", userAccount)
	}

	// 更新 MFA 信息
	user.TotpSecret = totpSecret
	user.RecoveryCodes = recoveryCodes

	// 只更新 MFA 相关字段
	columns := []string{"totpSecret", "recoveryCodes"}

	_, err = s.client.UpdateUserForColumns(user, columns)
	if err != nil {
		return fmt.Errorf("更新用户 MFA 信息失败: %w", err)
	}

	g.Log().Infof(ctx, "成功更新用户 %s 的 MFA 信息", userAccount)
	return nil
}
