package casdoor

import (
	"context"
	"fmt"

	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/gogf/gf/v2/frame/g"
)

func initAuthConfig() {
	casdoorsdk.InitConfig(
		GlobalConfig.Server.Endpoint,
		GlobalConfig.Server.ClientID,
		GlobalConfig.Server.ClientSecret,
		GlobalConfig.Certificate,
		GlobalConfig.Server.Organization,
		GlobalConfig.Server.Application,
	)
}

// InitCasdoor 初始化Casdoor配置和SDK
func InitCasdoor(ctx context.Context) {
	if err := LoadConfig(ctx); err != nil {
		panic(fmt.Errorf("failed to load casdoor config: %w", err))
	}
	initAuthConfig()

	// 获取所有权限
	permissions, err := casdoorsdk.GetPermissions()
	if err != nil {
		g.Log().Errorf(ctx, "获取Casdoor权限列表失败: %v", err)
	} else {
		g.Log().Infof(ctx, "已获取 %d 个Casdoor权限", len(permissions))
	}
	// g.Log().Info(ctx, " Casdoor权限: %+v", permissions)

	g.Log().Info(ctx, "Casdoor初始化完成")

	// for _, perm := range permissions {
	// 	casdoorsdk.DeletePermission(perm)
	// }

	// // // // // 直接将permissions作为参数传递给同步函数
	SyncRoutesWithCasdoor(ctx, permissions)
	// // g.Log().Info(ctx, "Casdoor路由同步完成")

	// // // // // // 重新获取最新权限列表（包含刚刚添加的路由权限）
	// permissions, _ = casdoorsdk.GetPermissions()
	SyncMenusWithCasdoor(ctx, permissions)
	// g.Log().Info(ctx, "Casdoor菜单同步完成")
}
