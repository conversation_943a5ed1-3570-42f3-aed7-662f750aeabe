// Copyright 2023 The Casdoor Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package casdoor

import (
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

type ServerConfig struct {
	Endpoint       string `yaml:"endpoint"`
	ClientID       string `yaml:"client_id"`
	ClientSecret   string `yaml:"client_secret"`
	Organization   string `yaml:"organization"`
	Application    string `yaml:"application"`
	FrontendURL    string `yaml:"frontend_url"`
	User           string `yaml:"user"`
	Role           string `yaml:"role"`
	Model          string `yaml:"model"`
	Owner          string `yaml:"owner"`
	DefaultAvatar  string `yaml:"default_avatar"`  // 默认头像URL
	DeleteStrategy string `yaml:"delete_strategy"` // 删除策略: "soft" - 软删除(禁用), "hard" - 硬删除(真正删除)
}

type Config struct {
	Certificate string       `yaml:"certificate"`
	Server      ServerConfig `yaml:"server"`
}

var GlobalConfig *Config

func LoadConfig(ctx context.Context) error {
	// 初始化 GlobalConfig
	GlobalConfig = &Config{}

	// 使用 GoFrame 的配置管理系统读取 casdoor 配置
	err := g.Cfg().MustGet(ctx, "casdoor_server").Scan(&GlobalConfig.Server)
	if err != nil {
		return fmt.Errorf("failed to load casdoor server configuration: %w", err)
	}
	// 读取证书配置
	if err := g.Cfg().MustGet(ctx, "certificate").Scan(&GlobalConfig.Certificate); err != nil {
		return fmt.Errorf("failed to load certificate configuration: %w", err)
	}
	// 检查必需的配置项是否存在
	if GlobalConfig.Server.Endpoint == "" {
		return fmt.Errorf("casdoor server endpoint is required")
	}
	if GlobalConfig.Server.ClientID == "" {
		return fmt.Errorf("casdoor server client_id is required")
	}
	if GlobalConfig.Server.ClientSecret == "" {
		return fmt.Errorf("casdoor server client_secret is required")
	}
	if GlobalConfig.Server.Organization == "" {
		return fmt.Errorf("casdoor server organization is required")
	}
	if GlobalConfig.Server.Application == "" {
		return fmt.Errorf("casdoor server application is required")
	}
	if GlobalConfig.Server.FrontendURL == "" {
		return fmt.Errorf("casdoor server frontend_url is required")
	}
	// 检查证书配置是否存在
	if GlobalConfig.Certificate == "" {
		return fmt.Errorf("certificate is required")
	}
	// 检查用户配置是否存在
	if GlobalConfig.Server.User == "" {
		return fmt.Errorf("user is required")
	}
	// 检查角色配置是否存在
	if GlobalConfig.Server.Role == "" {
		return fmt.Errorf("role is required")
	}

	// 读取证书配置
	GlobalConfig.Certificate = g.Cfg().MustGet(ctx, "certificate").String()

	return nil
}
