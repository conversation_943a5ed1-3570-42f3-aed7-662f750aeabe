package casdoor

import (
	"context"
	"sync"

	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

var (
	merchantClient     *casdoorsdk.Client
	merchantClientOnce sync.Once
)

// GetMerchantCasdoorClient 获取商户专用的 Casdoor 客户端（单例）
func GetMerchantCasdoorClient(ctx context.Context) (*casdoorsdk.Client, error) {
	var err error
	merchantClientOnce.Do(func() {
		config := &ServerConfig{}
		err = g.Cfg().MustGet(ctx, "merchant_casdoor_server").Scan(config)
		if err != nil {
			g.Log().Errorf(ctx, "加载商户 Casdoor 配置失败: %v", err)
			return
		}

		// 获取证书配置
		certificate := g.Cfg().MustGet(ctx, "certificate").String()
		if certificate == "" {
			g.Log().Error(ctx, "证书配置为空")
			return
		}

		// 创建商户专用的 Casdoor 客户端
		merchantClient = casdoorsdk.NewClient(
			config.Endpoint,
			config.ClientID,
			config.ClientSecret,
			certificate,
			config.Organization,
			config.Application,
		)

		g.Log().Infof(ctx, "商户 Casdoor 客户端初始化成功 - Organization: %s, Application: %s",
			config.Organization, config.Application)
	})

	if err != nil {
		return nil, err
	}

	if merchantClient == nil {
		return nil, gerror.New("商户 Casdoor 客户端初始化失败")
	}

	return merchantClient, nil
}
