package constants

// DEPRECATED: This file contains legacy fund operations constants.
// All new code should use the fund_operations_v2.go system with FundOperationType enums.
// These constants are kept for backward compatibility only and will be removed in a future version.
//
// Migration Guide:
// - Use constants.FundOpRedPacketCreate instead of BizPrefixRedPacketCreate
// - Use descriptor.FormatBasicDescription() instead of DescTpl* templates
// - See fund_operations_v2.go for the new unified system

// DEPRECATED: BusinessID prefix constants (use FundOperationType.GetBusinessIDPrefix() instead)
const (
	// DEPRECATED: Use constants.FundOpRedPacketCreate.GetBusinessIDPrefix()
	BizPrefixRedPacketCreate = "rp_create"
	// DEPRECATED: Use constants.FundOpRedPacketClaim.GetBusinessIDPrefix()
	BizPrefixRedPacketClaim = "rp_claim"
	// DEPRECATED: Use constants.FundOpRedPacketCancel.GetBusinessIDPrefix()
	BizPrefixRedPacketCancel = "rp_cancel"
	// DEPRECATED: Use constants.FundOpRedPacketExpire.GetBusinessIDPrefix()
	BizPrefixRedPacketExpire = "rp_expire"

	// DEPRECATED: Use constants.FundOpTransferOut.GetBusinessIDPrefix()
	BizPrefixTransferOut = "transfer_out"
	// DEPRECATED: Use constants.FundOpTransferIn.GetBusinessIDPrefix()
	BizPrefixTransferIn = "transfer_in"
	// DEPRECATED: Use constants.FundOpTransferExpire.GetBusinessIDPrefix()
	BizPrefixTransferExpire = "transfer_expire"
	// DEPRECATED: Use constants.FundOpTransferCollect.GetBusinessIDPrefix()
	BizPrefixTransferCollect = "transfer_collect"

	// DEPRECATED: Use constants.FundOpWithdrawReq.GetBusinessIDPrefix()
	BizPrefixWithdrawReq = "withdraw_req"
	// DEPRECATED: Use constants.FundOpWithdrawRefund.GetBusinessIDPrefix()
	BizPrefixWithdrawRefund = "withdraw_refund"

	// DEPRECATED: Use constants.FundOpSwapOut.GetBusinessIDPrefix()
	BizPrefixSwapOut = "swap_out"
	// DEPRECATED: Use constants.FundOpSwapIn.GetBusinessIDPrefix()
	BizPrefixSwapIn = "swap_in"

	// DEPRECATED: Use constants.FundOpPaymentOut.GetBusinessIDPrefix()
	BizPrefixPaymentOut = "payment_out"
	// DEPRECATED: Use constants.FundOpPaymentIn.GetBusinessIDPrefix()
	BizPrefixPaymentIn = "payment_in"

	// DEPRECATED: Use constants.FundOpAdminAdd.GetBusinessIDPrefix()
	BizPrefixAdminAdd = "admin_add"
	// DEPRECATED: Use constants.FundOpAdminDeduct.GetBusinessIDPrefix()
	BizPrefixAdminDeduct = "admin_deduct"

	// DEPRECATED: Use constants.FundOpSystemAdjust.GetBusinessIDPrefix()
	BizPrefixSystemAdjust = "system_adjust"
	// DEPRECATED: Use constants.FundOpCommission.GetBusinessIDPrefix()
	BizPrefixCommission = "commission"
	// DEPRECATED: Use constants.FundOpReferral.GetBusinessIDPrefix()
	BizPrefixReferral = "referral"
)

// DEPRECATED: Description templates for fund operations (Chinese)
// Use FundOperationType.FormatDescription() or utils.NewFundOperationDescriptor() instead
const (
	// DEPRECATED: Use constants.FundOpTransferOut.FormatDescriptionWithTarget()
	DescTplTransferOut = "转账给 %s: %s %s"
	// DEPRECATED: Use constants.FundOpTransferIn.FormatDescription()
	DescTplTransferIn = "收到转账: %s %s"

	// DEPRECATED: Use constants.FundOpRedPacketCreate.FormatDescription()
	DescTplRedPacketCreate = "创建红包 %s"
	// DEPRECATED: Use constants.FundOpRedPacketClaim.FormatDescription()
	DescTplRedPacketClaim = "领取红包: %s %s"
	// DEPRECATED: Use constants.FundOpRedPacketCancel.FormatDescription()
	DescTplRedPacketCancel = "取消红包退款 %s"
	// DEPRECATED: Use constants.FundOpRedPacketExpire.FormatDescription()
	DescTplRedPacketExpire = "红包过期退款 %s"

	// DEPRECATED: Use constants.FundOpWithdrawReq.FormatDescriptionWithTarget()
	DescTplWithdraw = "提现到 %s: %s %s"
	// DEPRECATED: Use constants.FundOpWithdrawRefund.FormatDescription()
	DescTplWithdrawRefund = "提现失败退款: %s %s"

	// DEPRECATED: Use constants.FundOpSwapOut.FormatDescription()
	DescTplSwapOut = "兑换扣除: %s %s"
	// DEPRECATED: Use constants.FundOpSwapIn.FormatDescription()
	DescTplSwapIn = "兑换获得: %s %s"
	// DEPRECATED: Use utils.NewFundOperationDescriptor().FormatSwapDescription()
	DescTplSwapFull = "兑换 %s %s 为 %s %s"

	// DEPRECATED: Use constants.FundOpPaymentOut.FormatDescriptionWithTarget()
	DescTplPaymentOut = "支付给 %s: %s %s"
	// DEPRECATED: Use constants.FundOpPaymentIn.FormatDescription()
	DescTplPaymentIn = "收到付款: %s %s"

	// DEPRECATED: Use constants.FundOpAdminAdd.FormatDescription()
	DescTplAdminAdd = "系统增加: %s %s"
	// DEPRECATED: Use constants.FundOpAdminDeduct.FormatDescription()
	DescTplAdminDeduct = "系统扣除: %s %s"

	// DEPRECATED: Use constants.FundOpCommission.FormatDescription()
	DescTplCommission = "佣金收入: %s %s"
	// DEPRECATED: Use constants.FundOpReferral.FormatDescription()
	DescTplReferral = "推荐奖励: %s %s"
	// DEPRECATED: Use constants.FundOpSystemAdjust.FormatDescription()
	DescTplSystemAdjust = "系统调整: %s %s"

	// DEPRECATED: Use utils.NewFundOperationDescriptor().FormatDescriptionWithTargetAndMemo()
	DescTplTransferOutWithMemo = "转账给 %s: %s %s - %s" // 收款人、金额、币种、备注
	// DEPRECATED: Use utils.NewFundOperationDescriptor().FormatDescriptionWithMemo()
	DescTplTransferInWithMemo = "收到转账: %s %s - %s" // 金额、币种、备注

	// DEPRECATED: Use utils.NewFundOperationDescriptor() with custom template
	DescTplDemoFunds = "新用户演示资金: %s %s"

	// DEPRECATED: Use utils.NewFundOperationDescriptor() with custom template
	DescTplWithdrawRefundDetail = "提现失败退款 - %s: %s %s" // 币种名称、金额、币种

	// DEPRECATED: Use utils.NewFundOperationDescriptor().FormatPaymentRequestDescription()
	DescTplPaymentRequestPay = "支付收款请求 #%d: %s %s" // 请求ID、金额、币种
	// DEPRECATED: Use utils.NewFundOperationDescriptor().FormatPaymentRequestDescription()
	DescTplPaymentRequestReceive = "收到支付 #%d: %s %s" // 请求ID、金额、币种

	// DEPRECATED: Use utils.NewFundOperationDescriptor().FormatDescriptionWithMemo()
	DescTplTransferCollectOut = "转账扣款: %s %s - %s" // 金额、币种、备注
	// DEPRECATED: Use utils.NewFundOperationDescriptor().FormatDescriptionWithMemo()
	DescTplTransferCollectIn = "收到转账: %s %s - %s" // 金额、币种、备注
)

// DEPRECATED: Description templates for fund operations (English)
// Use FundOperationType.FormatDescription("en", amount, symbol) or utils.NewFundOperationDescriptor("en") instead
const (
	// DEPRECATED: Use constants.FundOpTransferOut.FormatDescriptionWithTarget("en", target, amount, symbol)
	DescTplTransferOutEN = "Transfer to %s: %s %s"
	// DEPRECATED: Use constants.FundOpTransferIn.FormatDescription("en", amount, symbol)
	DescTplTransferInEN = "Received transfer: %s %s"

	// DEPRECATED: Use constants.FundOpRedPacketCreate.FormatDescription("en", amount, symbol)
	DescTplRedPacketCreateEN = "Create red packet %s"
	// DEPRECATED: Use constants.FundOpRedPacketClaim.FormatDescription("en", amount, symbol)
	DescTplRedPacketClaimEN = "Claim red packet: %s %s"
	// DEPRECATED: Use constants.FundOpRedPacketCancel.FormatDescription("en", amount, symbol)
	DescTplRedPacketCancelEN = "Red packet cancel refund %s"
	// DEPRECATED: Use constants.FundOpRedPacketExpire.FormatDescription("en", amount, symbol)
	DescTplRedPacketExpireEN = "Red packet expiry refund %s"

	// DEPRECATED: Use constants.FundOpWithdrawReq.FormatDescriptionWithTarget("en", target, amount, symbol)
	DescTplWithdrawEN = "Withdraw to %s: %s %s"
	// DEPRECATED: Use constants.FundOpWithdrawRefund.FormatDescription("en", amount, symbol)
	DescTplWithdrawRefundEN = "Withdrawal refund: %s %s"

	// DEPRECATED: Use constants.FundOpSwapOut.FormatDescription("en", amount, symbol)
	DescTplSwapOutEN = "Swap deduct: %s %s"
	// DEPRECATED: Use constants.FundOpSwapIn.FormatDescription("en", amount, symbol)
	DescTplSwapInEN = "Swap receive: %s %s"
	// DEPRECATED: Use utils.NewFundOperationDescriptor("en").FormatSwapDescription()
	DescTplSwapFullEN = "Swap %s %s for %s %s"

	// DEPRECATED: Use constants.FundOpPaymentOut.FormatDescriptionWithTarget("en", target, amount, symbol)
	DescTplPaymentOutEN = "Pay to %s: %s %s"
	// DEPRECATED: Use constants.FundOpPaymentIn.FormatDescription("en", amount, symbol)
	DescTplPaymentInEN = "Received payment: %s %s"

	// DEPRECATED: Use constants.FundOpAdminAdd.FormatDescription("en", amount, symbol)
	DescTplAdminAddEN = "Admin add: %s %s"
	// DEPRECATED: Use constants.FundOpAdminDeduct.FormatDescription("en", amount, symbol)
	DescTplAdminDeductEN = "Admin deduct: %s %s"

	// DEPRECATED: Use constants.FundOpCommission.FormatDescription("en", amount, symbol)
	DescTplCommissionEN = "Commission income: %s %s"
	// DEPRECATED: Use constants.FundOpReferral.FormatDescription("en", amount, symbol)
	DescTplReferralEN = "Referral bonus: %s %s"
	// DEPRECATED: Use constants.FundOpSystemAdjust.FormatDescription("en", amount, symbol)
	DescTplSystemAdjustEN = "System adjustment: %s %s"

	// DEPRECATED: Use utils.NewFundOperationDescriptor("en").FormatDescriptionWithTargetAndMemo()
	DescTplTransferOutWithMemoEN = "Transfer to %s: %s %s - %s" // recipient, amount, symbol, memo
	// DEPRECATED: Use utils.NewFundOperationDescriptor("en").FormatDescriptionWithMemo()
	DescTplTransferInWithMemoEN = "Received transfer: %s %s - %s" // amount, symbol, memo

	// DEPRECATED: Use utils.NewFundOperationDescriptor("en") with custom template
	DescTplDemoFundsEN = "New user demo funds: %s %s"

	// DEPRECATED: Use utils.NewFundOperationDescriptor("en") with custom template
	DescTplWithdrawRefundDetailEN = "Withdrawal refund - %s: %s %s" // symbol name, amount, symbol

	// DEPRECATED: Use utils.NewFundOperationDescriptor("en").FormatPaymentRequestDescription()
	DescTplPaymentRequestPayEN = "Pay request #%d: %s %s" // request ID, amount, symbol
	// DEPRECATED: Use utils.NewFundOperationDescriptor("en").FormatPaymentRequestDescription()
	DescTplPaymentRequestReceiveEN = "Received payment #%d: %s %s" // request ID, amount, symbol

	// DEPRECATED: Use utils.NewFundOperationDescriptor("en").FormatDescriptionWithMemo()
	DescTplTransferCollectOutEN = "Transfer debit: %s %s - %s" // amount, symbol, memo
	// DEPRECATED: Use utils.NewFundOperationDescriptor("en").FormatDescriptionWithMemo()
	DescTplTransferCollectInEN = "Transfer received: %s %s - %s" // amount, symbol, memo
)

// ============================================================================
// MIGRATION NOTICE
// ============================================================================
//
// This file contains DEPRECATED constants that are kept for backward compatibility only.
// All new development should use the fund_operations_v2.go system.
//
// Migration completed: All existing code has been migrated to use the new system.
// These constants will be removed in a future version.
//
// For new code, use:
// 1. FundOperationType enums (e.g., constants.FundOpTransferOut)
// 2. utils.NewFundOperationDescriptor() for description formatting
// 3. FundOperationType.GetBusinessIDPrefix() for business ID generation
//
// See fund_operations_v2.go and utils/fund_operations_v2.go for the new API.
// ============================================================================
