package utility

import (
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ParseDateRange 解析日期范围字符串并生成查询条件
// dateRange: 格式为 "2025-01-01,2025-01-31" 的日期范围字符串
// fieldName: 数据库字段名，默认为 "created_at"
// 返回值: 可以直接用于数据库查询的条件map，如果解析失败则返回nil
func ParseDateRange(dateRange string, fieldName ...string) g.Map {
	if dateRange == "" {
		return nil
	}

	// 确定字段名
	dbField := "created_at"
	if len(fieldName) > 0 && fieldName[0] != "" {
		dbField = fieldName[0]
	}

	// 解析日期范围
	dates := strings.Split(dateRange, ",")
	if len(dates) != 2 {
		return nil
	}

	// 构造开始和结束时间
	startTimeStr := strings.TrimSpace(dates[0]) + " 00:00:00"
	endTimeStr := strings.TrimSpace(dates[1]) + " 23:59:59"

	// 转换为时间对象
	startTime := gtime.NewFromStr(startTimeStr)
	endTime := gtime.NewFromStr(endTimeStr)

	// 验证时间是否有效
	if startTime.IsZero() || endTime.IsZero() {
		return nil
	}

	// 验证开始时间不能晚于结束时间
	if startTime.After(endTime) {
		return nil
	}

	// 返回查询条件
	return g.Map{
		dbField + " BETWEEN ? AND ?": g.Slice{startTime, endTime},
	}
}

// AddDateRangeCondition 将DateRange条件添加到现有的查询条件中
// condition: 现有的查询条件map
// dateRange: 日期范围字符串
// fieldName: 数据库字段名，默认为 "created_at"
func AddDateRangeCondition(condition g.Map, dateRange string, fieldName ...string) {
	if condition == nil {
		return
	}

	dateCondition := ParseDateRange(dateRange, fieldName...)
	if dateCondition != nil {
		// 将日期条件合并到现有条件中
		for k, v := range dateCondition {
			condition[k] = v
		}
	}
}

// ParseDateRange 解析日期范围字符串并生成查询条件
// dateRange: 格式为 "2025-01-01,2025-01-31" 的日期范围字符串
// fieldName: 数据库字段名，默认为 "created_at"
// 返回值: 可以直接用于数据库查询的条件map，如果解析失败则返回nil
// func ParseDateRange(dateRange string, fieldName ...string) g.Map {
// 	if dateRange == "" {
// 		return nil
// 	}

// 	// 确定字段名
// 	dbField := "created_at"
// 	if len(fieldName) > 0 && fieldName[0] != "" {
// 		dbField = fieldName[0]
// 	}

// 	// 解析日期范围
// 	dates := strings.Split(dateRange, ",")
// 	if len(dates) != 2 {
// 		return nil
// 	}

// 	// 构造开始和结束时间
// 	startTimeStr := strings.TrimSpace(dates[0]) + " 00:00:00"
// 	endTimeStr := strings.TrimSpace(dates[1]) + " 23:59:59"

// 	// 转换为时间对象
// 	startTime := gtime.NewFromStr(startTimeStr)
// 	endTime := gtime.NewFromStr(endTimeStr)

// 	// 验证时间是否有效
// 	if startTime.IsZero() || endTime.IsZero() {
// 		return nil
// 	}

// 	// 验证开始时间不能晚于结束时间
// 	if startTime.After(endTime) {
// 		return nil
// 	}

// 	// 返回查询条件
// 	return g.Map{
// 		dbField + " BETWEEN ? AND ?": g.Slice{startTime, endTime},
// 	}
// }

// AddDateRangeCondition 将DateRange条件添加到现有的查询条件中
// condition: 现有的查询条件map
// dateRange: 日期范围字符串
// fieldName: 数据库字段名，默认为 "created_at"
// func AddDateRangeCondition(condition g.Map, dateRange string, fieldName ...string) {
// 	if condition == nil {
// 		return
// 	}

// 	dateCondition := ParseDateRange(dateRange, fieldName...)
// 	if dateCondition != nil {
// 		// 将日期条件合并到现有条件中
// 		for k, v := range dateCondition {
// 			condition[k] = v
// 		}
// 	}
// }

// AddFlexibleDateRangeCondition 添加灵活的日期范围条件，支持多种格式
// condition: 现有的查询条件map
// dateRange: 日期范围字符串，格式：2025-01-01,2025-01-31
// createdAtArray: 日期范围数组，格式：[开始时间, 结束时间]
// fieldName: 数据库字段名，默认为 "created_at"
func AddFlexibleDateRangeCondition(condition g.Map, dateRange string, createdAtArray []string, fieldName ...string) {
	if condition == nil {
		return
	}

	// 确定字段名
	dbField := "created_at"
	if len(fieldName) > 0 && fieldName[0] != "" {
		dbField = fieldName[0]
	}

	var dateCondition g.Map

	// 优先使用createdAt数组参数
	if len(createdAtArray) >= 2 && createdAtArray[0] != "" && createdAtArray[1] != "" {
		dateCondition = ParseDateRangeFromArray(createdAtArray, dbField)
	} else if dateRange != "" {
		// 如果没有数组参数，使用字符串参数
		dateCondition = ParseDateRange(dateRange, dbField)
	}

	// 将日期条件合并到现有条件中
	if dateCondition != nil {
		for k, v := range dateCondition {
			condition[k] = v
		}
	}
}

// ParseDateRangeFromArray 解析日期范围数组并生成查询条件
// createdAtArray: 日期范围数组，格式：[开始时间, 结束时间]
// fieldName: 数据库字段名
// 返回值: 可以直接用于数据库查询的条件map，如果解析失败则返回nil
func ParseDateRangeFromArray(createdAtArray []string, fieldName string) g.Map {
	if len(createdAtArray) < 2 {
		return nil
	}

	startTimeStr := strings.TrimSpace(createdAtArray[0])
	endTimeStr := strings.TrimSpace(createdAtArray[1])

	if startTimeStr == "" || endTimeStr == "" {
		return nil
	}

	// 处理不同的时间格式
	startTime := parseFlexibleTime(startTimeStr)
	endTime := parseFlexibleTime(endTimeStr)

	// 验证时间是否有效
	if startTime.IsZero() || endTime.IsZero() {
		return nil
	}

	// 验证开始时间不能晚于结束时间
	if startTime.After(endTime) {
		return nil
	}

	// 返回查询条件
	return g.Map{
		fieldName + " BETWEEN ? AND ?": g.Slice{startTime, endTime},
	}
}

// parseFlexibleTime 解析灵活的时间格式
// 支持格式：
// - 2024-04-12 00:00:25
// - 2024-04-12+00:00:25
// - 2024-04-12T00:00:25
// - 2024-04-12
func parseFlexibleTime(timeStr string) *gtime.Time {
	// 替换加号为空格（处理URL编码的时间）
	timeStr = strings.ReplaceAll(timeStr, "+", " ")

	// 尝试多种时间格式
	formats := []string{
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05",
		"2006-01-02 15:04",
		"2006-01-02T15:04",
		"2006-01-02",
	}

	for _, format := range formats {
		if t := gtime.NewFromStrFormat(timeStr, format); !t.IsZero() {
			return t
		}
	}

	// 如果都不匹配，尝试gtime的默认解析
	return gtime.NewFromStr(timeStr)
}
