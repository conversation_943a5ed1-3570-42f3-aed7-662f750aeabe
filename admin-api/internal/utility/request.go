package utility

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// GetRequestIP 获取请求IP地址
func GetRequestIP(ctx context.Context) string {
	// 从请求上下文中获取客户端IP
	if r := g.RequestFromCtx(ctx); r != nil {
		// 优先获取 X-Forwarded-For 头
		if forwardedFor := r.Header.Get("X-Forwarded-For"); forwardedFor != "" {
			return forwardedFor
		}
		// 其次获取 X-Real-IP 头
		if realIP := r.Header.Get("X-Real-IP"); realIP != "" {
			return realIP
		}
		// 最后使用 RemoteAddr
		return r.GetClientIp()
	}
	// 默认返回本地地址
	return "127.0.0.1"
}

// GetCurrentMerchantID 从请求上下文中获取当前商户ID
func GetCurrentMerchantID(ctx context.Context) uint64 {
	if r := g.RequestFromCtx(ctx); r != nil {
		return r.GetCtxVar("merchantId").Uint64()
	}
	return 0
}
