package utility

import (
	"github.com/gogf/gf/v2/database/gdb"
)

// AddAgentSearchConditions 添加三级代理查询条件（通用函数）
// 支持按代理名称模糊搜索，需要在调用前已经left join了相关代理表
func AddAgentSearchConditions(model *gdb.Model, firstAgent, secondAgent, thirdAgent string) *gdb.Model {
	if firstAgent != "" {
		model = model.WhereLike("first_agent.username", "%"+firstAgent+"%")
	}
	if secondAgent != "" {
		model = model.WhereLike("second_agent.username", "%"+secondAgent+"%")
	}
	if thirdAgent != "" {
		model = model.WhereLike("third_agent.username", "%"+thirdAgent+"%")
	}
	return model
}

// AddTelegramSearchConditions 添加telegram查询条件（通用函数）
// 支持按telegram信息模糊搜索，需要在调用前已经left join了user_backup_accounts表
func AddTelegramSearchConditions(model *gdb.Model, telegramId, telegramUsername, firstName string) *gdb.Model {
	if telegramId != "" {
		model = model.WhereLike("uba.telegram_id", "%"+telegramId+"%")
	}
	if telegramUsername != "" {
		model = model.WhereLike("uba.telegram_username", "%"+telegramUsername+"%")
	}
	if firstName != "" {
		model = model.WhereLike("uba.first_name", "%"+firstName+"%")
	}
	return model
}

// AddAgentAndTelegramJoins 添加代理和telegram表的关联查询（通用函数）
// 一次性添加所有必要的表关联，避免重复代码
func AddAgentAndTelegramJoins(model *gdb.Model, usersTable, userBackupAccountsTable string) *gdb.Model {
	// 关联三级代理表 - 使用agents表而不是users表
	model = model.LeftJoin("agents first_agent", "u.first_id = first_agent.agent_id")
	model = model.LeftJoin("agents second_agent", "u.second_id = second_agent.agent_id")
	model = model.LeftJoin("agents third_agent", "u.third_id = third_agent.agent_id")

	// 关联主备份账户表（is_master=1）
	model = model.LeftJoin(userBackupAccountsTable+" uba", "u.id = uba.user_id AND uba.is_master = 1")

	return model
}

// GetAgentAndTelegramFields 获取代理和telegram相关字段（通用函数）
// 返回查询时需要select的字段列表
func GetAgentAndTelegramFields() []string {
	return []string{
		"first_agent.username as first_agent_name",
		"second_agent.username as second_agent_name",
		"third_agent.username as third_agent_name",
		"uba.telegram_id",
		"uba.telegram_username",
		"uba.first_name",
	}
}
