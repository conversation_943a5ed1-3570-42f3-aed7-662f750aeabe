package consts

// User Account Type Constants
const (
	AccountTypeUnknown  = 0 // 未知/未定义类型
	AccountTypeUser     = 1 // 普通用户
	AccountTypeMerchant = 2 // 商户
	AccountTypeAgent    = 3 // 代理
)

// Account Type Name Mapping
var AccountTypeMap = map[int]string{
	AccountTypeUnknown:  "未知类型",
	AccountTypeUser:     "普通用户",
	AccountTypeMerchant: "商户",
	AccountTypeAgent:    "代理",
}

// IsValidAccountType checks if the given account type is valid
func IsValidAccountType(accountType int) bool {
	_, ok := AccountTypeMap[accountType]
	return ok
}

// GetAccountTypeName returns the name for the given account type
func GetAccountTypeName(accountType int) string {
	return AccountTypeMap[accountType]
}

// User Status Constants
const (
	UserStatusActive  = 0 // 活跃状态
	UserStatusStopped = 1 // 已暂停
)

// User Status Name Mapping
var UserStatusMap = map[int]string{
	UserStatusActive:  "活跃",
	UserStatusStopped: "已暂停",
}

// IsValidUserStatus checks if the given user status is valid
func IsValidUserStatus(status int) bool {
	_, ok := UserStatusMap[status]
	return ok
}

// GetUserStatusName returns the name for the given user status
func GetUserStatusName(status int) string {
	return UserStatusMap[status]
}
