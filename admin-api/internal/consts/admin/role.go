package dept // 包名保持与同目录下的 dept.go, menu.go 一致

// 数据范围常量
const (
	DataScopeAll          = 1 // 全部数据权限
	DataScopeDept         = 2 // 本部门数据权限
	DataScopeDeptAndBelow = 3 // 本部门及以下数据权限
	DataScopeSelf         = 4 // 仅本人数据权限
	DataScopeCustom       = 5 // 自定义数据权限
)

// IsValidDataScope 检查数据范围值是否有效
func IsValidDataScope(scope int) bool {
	switch scope {
	case DataScopeAll, DataScopeDept, DataScopeDeptAndBelow, DataScopeSelf, DataScopeCustom:
		return true
	default:
		return false
	}
}
