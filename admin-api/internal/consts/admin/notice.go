package dept // 包名保持与同目录下的 dept.go, menu.go, role.go 一致

// 公告类型常量
const (
	NoticeTypeNotification   = 1 // 通知公告
	NoticeTypePrivateMessage = 2 // 私信
)

// 公告状态常量
const (
	NoticeStatusDraft     = 0 // 草稿
	NoticeStatusPublished = 1 // 已发布
	NoticeStatusDisabled  = 2 // 停用 (或下架)
)

// 公告标签常量 (示例)
const (
	NoticeTagNormal    = 0 // 普通
	NoticeTagImportant = 1 // 重要
	NoticeTagActivity  = 2 // 活动
)

// TypeMap 公告类型映射
var NoticeTypeMap = map[int]string{
	NoticeTypeNotification:   "通知公告",
	NoticeTypePrivateMessage: "私信",
}

// StatusMap 公告状态映射
var NoticeStatusMap = map[int]string{
	NoticeStatusDraft:     "草稿",
	NoticeStatusPublished: "已发布",
	NoticeStatusDisabled:  "停用",
}

// TagMap 公告标签映射
var NoticeTagMap = map[int]string{
	NoticeTagNormal:    "普通",
	NoticeTagImportant: "重要",
	NoticeTagActivity:  "活动",
}

// IsValidNoticeType 检查公告类型是否有效
func IsValidNoticeType(noticeType int) bool {
	_, ok := NoticeTypeMap[noticeType]
	return ok
}

// IsValidNoticeStatus 检查公告状态是否有效
func IsValidNoticeStatus(status int) bool {
	_, ok := NoticeStatusMap[status]
	return ok
}

// IsValidNoticeTag 检查公告标签是否有效
func IsValidNoticeTag(tag int) bool {
	_, ok := NoticeTagMap[tag]
	return ok
}
