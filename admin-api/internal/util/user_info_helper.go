package util

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// UserAgentInfo 用户代理层级信息
type UserAgentInfo struct {
	FirstAgentName  string `json:"firstAgentName" orm:"first_agent_name"`
	SecondAgentName string `json:"secondAgentName" orm:"second_agent_name"`
	ThirdAgentName  string `json:"thirdAgentName" orm:"third_agent_name"`
}

// UserTelegramInfo 用户 Telegram 信息
type UserTelegramInfo struct {
	TelegramId       string `json:"telegramId" orm:"telegram_id"`
	TelegramUsername string `json:"telegramUsername" orm:"telegram_username"`
	FirstName        string `json:"firstName" orm:"first_name"`
}

// UserCompleteInfo 用户完整信息（代理层级 + Telegram）
type UserCompleteInfo struct {
	UserAgentInfo
	UserTelegramInfo
}

// UserInfoHelper 用户信息查询助手
type UserInfoHelper struct {
	db gdb.DB
}

// NewUserInfoHelper 创建用户信息查询助手
func NewUserInfoHelper(db gdb.DB) *UserInfoHelper {
	return &UserInfoHelper{db: db}
}

// GetUserAgentInfo 获取单个用户的代理层级信息
func (h *UserInfoHelper) GetUserAgentInfo(ctx context.Context, userId int64) (*UserAgentInfo, error) {
	var result UserAgentInfo

	err := h.db.Model("users u").
		LeftJoin("agents first_agent", "u.first_id = first_agent.agent_id").
		LeftJoin("agents second_agent", "u.second_id = second_agent.agent_id").
		LeftJoin("agents third_agent", "u.third_id = third_agent.agent_id").
		Fields(
			"first_agent.username as first_agent_name",
			"second_agent.username as second_agent_name",
			"third_agent.username as third_agent_name",
		).
		Where("u.id", userId).
		Scan(&result)

	if err != nil {
		return nil, gerror.Wrapf(err, "获取用户代理信息失败, userId: %d", userId)
	}

	return &result, nil
}

// GetUserTelegramInfo 获取单个用户的 Telegram 信息
func (h *UserInfoHelper) GetUserTelegramInfo(ctx context.Context, userId int64) (*UserTelegramInfo, error) {
	var result UserTelegramInfo

	err := h.db.Model("users u").
		LeftJoin("user_backup_accounts uba", "u.id = uba.user_id AND uba.is_master = 1").
		Fields(
			"uba.telegram_id as telegram_id",
			"uba.telegram_username as telegram_username",
			"uba.first_name as first_name",
		).
		Where("u.id", userId).
		Scan(&result)

	if err != nil {
		return nil, gerror.Wrapf(err, "获取用户Telegram信息失败, userId: %d", userId)
	}

	return &result, nil
}

// GetUserCompleteInfo 获取单个用户的完整信息（代理层级 + Telegram）
func (h *UserInfoHelper) GetUserCompleteInfo(ctx context.Context, userId int64) (*UserCompleteInfo, error) {
	var result UserCompleteInfo

	err := h.db.Model("users u").
		LeftJoin("agents first_agent", "u.first_id = first_agent.agent_id").
		LeftJoin("agents second_agent", "u.second_id = second_agent.agent_id").
		LeftJoin("agents third_agent", "u.third_id = third_agent.agent_id").
		LeftJoin("user_backup_accounts uba", "u.id = uba.user_id AND uba.is_master = 1").
		Fields(
			"first_agent.username as first_agent_name",
			"second_agent.username as second_agent_name",
			"third_agent.username as third_agent_name",
			"uba.telegram_id as telegram_id",
			"uba.telegram_username as telegram_username",
			"uba.first_name as first_name",
		).
		Where("u.id", userId).
		Scan(&result)

	if err != nil {
		return nil, gerror.Wrapf(err, "获取用户完整信息失败, userId: %d", userId)
	}

	return &result, nil
}

// BatchGetUserCompleteInfo 批量获取用户完整信息
func (h *UserInfoHelper) BatchGetUserCompleteInfo(ctx context.Context, userIds []int64) (map[int64]*UserCompleteInfo, error) {
	if len(userIds) == 0 {
		return make(map[int64]*UserCompleteInfo), nil
	}

	type userInfoWithId struct {
		UserId int64 `json:"userId" orm:"user_id"`
		UserCompleteInfo
	}

	var results []userInfoWithId

	err := h.db.Model("users u").
		LeftJoin("agents first_agent", "u.first_id = first_agent.agent_id").
		LeftJoin("agents second_agent", "u.second_id = second_agent.agent_id").
		LeftJoin("agents third_agent", "u.third_id = third_agent.agent_id").
		LeftJoin("user_backup_accounts uba", "u.id = uba.user_id AND uba.is_master = 1").
		Fields(
			"u.id as user_id",
			"first_agent.username as first_agent_name",
			"second_agent.username as second_agent_name",
			"third_agent.username as third_agent_name",
			"uba.telegram_id as telegram_id",
			"uba.telegram_username as telegram_username",
			"uba.first_name as first_name",
		).
		WhereIn("u.id", userIds).
		Scan(&results)

	if err != nil {
		return nil, gerror.Wrap(err, "批量获取用户完整信息失败")
	}

	// 转换为 map
	resultMap := make(map[int64]*UserCompleteInfo, len(results))
	for _, item := range results {
		resultMap[item.UserId] = &UserCompleteInfo{
			UserAgentInfo: UserAgentInfo{
				FirstAgentName:  item.FirstAgentName,
				SecondAgentName: item.SecondAgentName,
				ThirdAgentName:  item.ThirdAgentName,
			},
			UserTelegramInfo: UserTelegramInfo{
				TelegramId:       item.TelegramId,
				TelegramUsername: item.TelegramUsername,
				FirstName:        item.FirstName,
			},
		}
	}

	// 确保所有请求的用户ID都有对应的记录（即使是空值）
	for _, userId := range userIds {
		if _, exists := resultMap[userId]; !exists {
			resultMap[userId] = &UserCompleteInfo{}
		}
	}

	return resultMap, nil
}

// AddUserInfoJoins 为查询添加用户信息关联（代理层级 + Telegram）
// userAlias: 用户表的别名，如 "sender", "receiver"
// fieldPrefix: 字段前缀，如 "sender_", "receiver_"
func (h *UserInfoHelper) AddUserInfoJoins(query *gdb.Model, userAlias, fieldPrefix string) *gdb.Model {
	// 添加代理层级关联 - 使用agents表
	query = query.
		LeftJoin(userAlias+"_first_agent agents", userAlias+".first_id = "+userAlias+"_first_agent.agent_id").
		LeftJoin(userAlias+"_second_agent agents", userAlias+".second_id = "+userAlias+"_second_agent.agent_id").
		LeftJoin(userAlias+"_third_agent agents", userAlias+".third_id = "+userAlias+"_third_agent.agent_id").
		LeftJoin(userAlias+"_uba user_backup_accounts", userAlias+".id = "+userAlias+"_uba.user_id AND "+userAlias+"_uba.is_master = 1")

	return query
}

// GetUserInfoFields 获取用户信息字段列表
// fieldPrefix: 字段前缀，如 "sender_", "receiver_"
func (h *UserInfoHelper) GetUserInfoFields(userAlias, fieldPrefix string) []string {
	return []string{
		userAlias + "_first_agent.username as " + fieldPrefix + "first_agent_name",
		userAlias + "_second_agent.username as " + fieldPrefix + "second_agent_name",
		userAlias + "_third_agent.username as " + fieldPrefix + "third_agent_name",
		userAlias + "_uba.telegram_id as " + fieldPrefix + "telegram_id",
		userAlias + "_uba.telegram_username as " + fieldPrefix + "telegram_username",
		userAlias + "_uba.first_name as " + fieldPrefix + "first_name",
	}
}

// GetDefaultUserInfoHelper 获取默认的用户信息查询助手
func GetDefaultUserInfoHelper() *UserInfoHelper {
	return NewUserInfoHelper(g.DB())
}

// QueryPattern 查询模式枚举
type QueryPattern int

const (
	// SingleUser 单用户模式（如钱包、充值、提现等）
	SingleUser QueryPattern = iota
	// DualUser 双用户模式（如转账、收款请求、推荐佣金等）
	DualUser
	// BackupAccount 备份账户模式（主用户和备用用户）
	BackupAccount
)

// BuildQueryWithUserInfo 构建包含用户信息的查询
func BuildQueryWithUserInfo(baseQuery *gdb.Model, pattern QueryPattern, config QueryConfig) *gdb.Model {
	helper := GetDefaultUserInfoHelper()

	switch pattern {
	case SingleUser:
		return buildSingleUserQuery(baseQuery, helper, config)
	case DualUser:
		return buildDualUserQuery(baseQuery, helper, config)
	case BackupAccount:
		return buildBackupAccountQuery(baseQuery, helper, config)
	default:
		return baseQuery
	}
}

// QueryConfig 查询配置
type QueryConfig struct {
	// 主用户表别名和字段前缀
	PrimaryUserAlias   string
	PrimaryFieldPrefix string

	// 次用户表别名和字段前缀（双用户模式使用）
	SecondaryUserAlias   string
	SecondaryFieldPrefix string

	// 是否包含代理信息
	IncludeAgentInfo bool
	// 是否包含 Telegram 信息
	IncludeTelegramInfo bool
}

// buildSingleUserQuery 构建单用户查询
func buildSingleUserQuery(baseQuery *gdb.Model, helper *UserInfoHelper, config QueryConfig) *gdb.Model {
	if config.IncludeAgentInfo {
		baseQuery = baseQuery.
			LeftJoin(config.PrimaryUserAlias+"_first_agent agents", config.PrimaryUserAlias+".first_id = "+config.PrimaryUserAlias+"_first_agent.agent_id").
			LeftJoin(config.PrimaryUserAlias+"_second_agent agents", config.PrimaryUserAlias+".second_id = "+config.PrimaryUserAlias+"_second_agent.agent_id").
			LeftJoin(config.PrimaryUserAlias+"_third_agent agents", config.PrimaryUserAlias+".third_id = "+config.PrimaryUserAlias+"_third_agent.agent_id")
	}

	if config.IncludeTelegramInfo {
		baseQuery = baseQuery.
			LeftJoin(config.PrimaryUserAlias+"_uba user_backup_accounts", config.PrimaryUserAlias+".id = "+config.PrimaryUserAlias+"_uba.user_id AND "+config.PrimaryUserAlias+"_uba.is_master = 1")
	}

	return baseQuery
}

// buildDualUserQuery 构建双用户查询
func buildDualUserQuery(baseQuery *gdb.Model, helper *UserInfoHelper, config QueryConfig) *gdb.Model {
	// 添加主用户信息
	baseQuery = buildSingleUserQuery(baseQuery, helper, QueryConfig{
		PrimaryUserAlias:    config.PrimaryUserAlias,
		PrimaryFieldPrefix:  config.PrimaryFieldPrefix,
		IncludeAgentInfo:    config.IncludeAgentInfo,
		IncludeTelegramInfo: config.IncludeTelegramInfo,
	})

	// 添加次用户信息
	baseQuery = buildSingleUserQuery(baseQuery, helper, QueryConfig{
		PrimaryUserAlias:    config.SecondaryUserAlias,
		PrimaryFieldPrefix:  config.SecondaryFieldPrefix,
		IncludeAgentInfo:    config.IncludeAgentInfo,
		IncludeTelegramInfo: config.IncludeTelegramInfo,
	})

	return baseQuery
}

// buildBackupAccountQuery 构建备份账户查询
func buildBackupAccountQuery(baseQuery *gdb.Model, helper *UserInfoHelper, config QueryConfig) *gdb.Model {
	// 备份账户模式只需要主用户的信息
	return buildSingleUserQuery(baseQuery, helper, config)
}

// GetUserInfoFieldsForPattern 根据查询模式获取字段列表
func GetUserInfoFieldsForPattern(pattern QueryPattern, config QueryConfig) []string {
	var fields []string

	switch pattern {
	case SingleUser:
		if config.IncludeAgentInfo {
			fields = append(fields,
				config.PrimaryUserAlias+"_first_agent.username as "+config.PrimaryFieldPrefix+"first_agent_name",
				config.PrimaryUserAlias+"_second_agent.username as "+config.PrimaryFieldPrefix+"second_agent_name",
				config.PrimaryUserAlias+"_third_agent.username as "+config.PrimaryFieldPrefix+"third_agent_name",
			)
		}
		if config.IncludeTelegramInfo {
			fields = append(fields,
				config.PrimaryUserAlias+"_uba.telegram_id as "+config.PrimaryFieldPrefix+"telegram_id",
				config.PrimaryUserAlias+"_uba.telegram_username as "+config.PrimaryFieldPrefix+"telegram_username",
				config.PrimaryUserAlias+"_uba.first_name as "+config.PrimaryFieldPrefix+"first_name",
			)
		}
	case DualUser:
		// 主用户字段
		primaryFields := GetUserInfoFieldsForPattern(SingleUser, QueryConfig{
			PrimaryUserAlias:    config.PrimaryUserAlias,
			PrimaryFieldPrefix:  config.PrimaryFieldPrefix,
			IncludeAgentInfo:    config.IncludeAgentInfo,
			IncludeTelegramInfo: config.IncludeTelegramInfo,
		})
		// 次用户字段
		secondaryFields := GetUserInfoFieldsForPattern(SingleUser, QueryConfig{
			PrimaryUserAlias:    config.SecondaryUserAlias,
			PrimaryFieldPrefix:  config.SecondaryFieldPrefix,
			IncludeAgentInfo:    config.IncludeAgentInfo,
			IncludeTelegramInfo: config.IncludeTelegramInfo,
		})
		fields = append(primaryFields, secondaryFields...)
	case BackupAccount:
		fields = GetUserInfoFieldsForPattern(SingleUser, config)
	}

	return fields
}
