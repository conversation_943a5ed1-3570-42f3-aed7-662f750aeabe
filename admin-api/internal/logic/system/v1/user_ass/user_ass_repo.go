package user_ass

import (
	"context"
	"database/sql"
	"strings"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/user_ass"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gtime"
)

// userAssRepository implements the IUserAssRepository interface.
type userAssRepository struct{}

// NewUserAssRepository creates and returns a new instance of userAssRepository.
func NewUserAssRepository() user_ass.IUserAssRepository {
	return &userAssRepository{}
}

// List retrieves a paginated list of user associations with user information
func (r *userAssRepository) List(ctx context.Context, req *v1.GetBackupAccountsReq) ([]*user_ass.UserAssWithUserInfo, int, error) {
	// Build base query with joins to get user information
	m := dao.UserAss.Ctx(ctx).
		LeftJoin("users u", "user_ass.b_user_id = u.id").
		LeftJoin("user_backup_accounts uba", "u.id = uba.user_id").
		LeftJoin("users main_user", "user_ass.a_user_id = main_user.id").
		LeftJoin("user_backup_accounts main_uba", "main_user.id = main_uba.user_id AND main_uba.is_master = 1").
		WhereNull("user_ass.deleted_at").
		WhereNull("u.deleted_at").
		WhereNull("uba.deleted_at")

	// Apply filters
	if req.TelegramUsername != "" {
		m = m.WhereLike("uba.telegram_username", "%"+req.TelegramUsername+"%")
	}

	if req.TelegramId > 0 {
		m = m.Where("uba.telegram_id", req.TelegramId)
	}

	if req.UserId > 0 {
		m = m.Where("user_ass.a_user_id", req.UserId)
	}

	if req.DateRange != "" {
		parts := strings.Split(req.DateRange, ",")
		if len(parts) == 2 {
			startTime := gtime.NewFromStr(parts[0] + " 00:00:00")
			endTime := gtime.NewFromStr(parts[1] + " 23:59:59")
			if startTime != nil && endTime != nil {
				m = m.WhereBetween("user_ass.created_at", startTime, endTime)
			}
		}
	}

	// Count total records
	total, err := m.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "Failed to count user associations")
	}

	// Apply pagination
	if req.Page > 0 && req.PageSize > 0 {
		m = m.Page(req.Page, req.PageSize)
	}

	// Select fields and order
	m = m.Fields(
		"user_ass.*",
		"uba.telegram_username",
		"uba.telegram_id",
		"main_uba.telegram_id as main_telegram_id",
		"main_uba.telegram_username as main_telegram_username",
		"main_uba.first_name as main_first_name",
	).OrderDesc("user_ass.created_at")

	// Execute query
	var results []gdb.Record
	if err := m.Scan(&results); err != nil {
		return nil, 0, gerror.Wrap(err, "Failed to retrieve user associations")
	}

	// Convert to response format
	list := make([]*user_ass.UserAssWithUserInfo, 0, len(results))
	for _, record := range results {
		item := &user_ass.UserAssWithUserInfo{
			UserAss: &entity.UserAss{
				Id:         record["id"].Uint64(),
				AUserId:    record["a_user_id"].Int64(),
				BUserId:    record["b_user_id"].Int64(),
				VerifiedAt: record["verified_at"].GTime(),
				CreatedAt:  record["created_at"].GTime(),
				UpdatedAt:  record["updated_at"].GTime(),
				DeletedAt:  record["deleted_at"].GTime(),
			},
			TelegramUsername:     record["telegram_username"].String(),
			TelegramId:           record["telegram_id"].Int64(),
			MainTelegramId:       record["main_telegram_id"].String(),
			MainTelegramUsername: record["main_telegram_username"].String(),
			MainFirstName:        record["main_first_name"].String(),
		}
		list = append(list, item)
	}

	return list, total, nil
}

// ListByAUserID retrieves backup accounts for a specific A user
func (r *userAssRepository) ListByAUserID(ctx context.Context, aUserId int64, req *v1.GetUserBackupAccountsReq) ([]*user_ass.UserAssWithUserInfo, int, error) {
	// Build base query
	m := dao.UserAss.Ctx(ctx).
		LeftJoin("users u", "user_ass.b_user_id = u.id").
		LeftJoin("user_backup_accounts uba", "u.id = uba.user_id").
		LeftJoin("users main_user", "user_ass.a_user_id = main_user.id").
		LeftJoin("user_backup_accounts main_uba", "main_user.id = main_uba.user_id AND main_uba.is_master = 1").
		Where("user_ass.a_user_id", aUserId).
		WhereNull("user_ass.deleted_at").
		WhereNull("u.deleted_at").
		WhereNull("uba.deleted_at")

	// Apply filters
	if req.TelegramUsername != "" {
		m = m.WhereLike("uba.telegram_username", "%"+req.TelegramUsername+"%")
	}

	if req.TelegramId > 0 {
		m = m.Where("uba.telegram_id", req.TelegramId)
	}

	if req.DateRange != "" {
		parts := strings.Split(req.DateRange, ",")
		if len(parts) == 2 {
			startTime := gtime.NewFromStr(parts[0] + " 00:00:00")
			endTime := gtime.NewFromStr(parts[1] + " 23:59:59")
			if startTime != nil && endTime != nil {
				m = m.WhereBetween("user_ass.created_at", startTime, endTime)
			}
		}
	}

	// Count total
	total, err := m.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "Failed to count user associations for user")
	}

	// Apply pagination
	if req.Page > 0 && req.PageSize > 0 {
		m = m.Page(req.Page, req.PageSize)
	}

	// Select and order
	m = m.Fields(
		"user_ass.*",
		"uba.telegram_username",
		"uba.telegram_id",
		"main_uba.telegram_id as main_telegram_id",
		"main_uba.telegram_username as main_telegram_username",
		"main_uba.first_name as main_first_name",
	).OrderDesc("user_ass.created_at")

	// Execute query
	var results []gdb.Record
	if err := m.Scan(&results); err != nil {
		return nil, 0, gerror.Wrap(err, "Failed to retrieve user associations for user")
	}

	// Convert to response format
	list := make([]*user_ass.UserAssWithUserInfo, 0, len(results))
	for _, record := range results {
		item := &user_ass.UserAssWithUserInfo{
			UserAss: &entity.UserAss{
				Id:         record["id"].Uint64(),
				AUserId:    record["a_user_id"].Int64(),
				BUserId:    record["b_user_id"].Int64(),
				VerifiedAt: record["verified_at"].GTime(),
				CreatedAt:  record["created_at"].GTime(),
				UpdatedAt:  record["updated_at"].GTime(),
				DeletedAt:  record["deleted_at"].GTime(),
			},
			TelegramUsername:     record["telegram_username"].String(),
			TelegramId:           record["telegram_id"].Int64(),
			MainTelegramId:       record["main_telegram_id"].String(),
			MainTelegramUsername: record["main_telegram_username"].String(),
			MainFirstName:        record["main_first_name"].String(),
		}
		list = append(list, item)
	}

	return list, total, nil
}

// Create adds a new user association
func (r *userAssRepository) Create(ctx context.Context, aUserId, bUserId int64) (int64, error) {
	now := gtime.Now()
	userAss := &entity.UserAss{
		AUserId:   aUserId,
		BUserId:   bUserId,
		CreatedAt: now,
		UpdatedAt: now,
	}

	result, err := dao.UserAss.Ctx(ctx).Data(userAss).Insert()
	if err != nil {
		return 0, gerror.Wrap(err, "Failed to create user association")
	}

	newId, err := result.LastInsertId()
	if err != nil {
		return 0, gerror.Wrap(err, "Failed to get last insert ID for user association")
	}

	return newId, nil
}

// GetByID retrieves a user association by ID
func (r *userAssRepository) GetByID(ctx context.Context, id int64) (*entity.UserAss, error) {
	var userAss *entity.UserAss
	err := dao.UserAss.Ctx(ctx).
		Where(dao.UserAss.Columns().Id, id).
		WhereNull(dao.UserAss.Columns().DeletedAt).
		Scan(&userAss)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, gerror.Wrapf(err, "Failed to get user association by ID: %d", id)
	}

	return userAss, nil
}

// DeleteByID deletes a user association by ID
func (r *userAssRepository) DeleteByID(ctx context.Context, id int64) error {
	_, err := dao.UserAss.Ctx(ctx).
		Where(dao.UserAss.Columns().Id, id).
		Delete()

	if err != nil {
		return gerror.Wrapf(err, "Failed to delete user association ID: %d", id)
	}

	return nil
}

// CheckAssociationExists checks if association exists between two users (A -> B)
func (r *userAssRepository) CheckAssociationExists(ctx context.Context, aUserId, bUserId int64) (bool, error) {
	count, err := dao.UserAss.Ctx(ctx).
		Where(dao.UserAss.Columns().AUserId, aUserId).
		Where(dao.UserAss.Columns().BUserId, bUserId).
		WhereNull(dao.UserAss.Columns().DeletedAt).
		Count()

	if err != nil {
		return false, gerror.Wrap(err, "Failed to check association existence")
	}

	return count > 0, nil
}

// CheckReverseAssociationExists checks if reverse association exists (B -> A)
func (r *userAssRepository) CheckReverseAssociationExists(ctx context.Context, aUserId, bUserId int64) (bool, error) {
	count, err := dao.UserAss.Ctx(ctx).
		Where(dao.UserAss.Columns().AUserId, bUserId).
		Where(dao.UserAss.Columns().BUserId, aUserId).
		WhereNull(dao.UserAss.Columns().DeletedAt).
		Count()

	if err != nil {
		return false, gerror.Wrap(err, "Failed to check reverse association existence")
	}

	return count > 0, nil
}

// UpdateVerifiedStatus updates the verification status of a user association
func (r *userAssRepository) UpdateVerifiedStatus(ctx context.Context, id int64, verified bool) error {
	data := map[string]any{
		"updated_at": gtime.Now(),
	}

	if verified {
		data["verified_at"] = gtime.Now()
	} else {
		data["verified_at"] = nil
	}

	_, err := dao.UserAss.Ctx(ctx).
		Data(data).
		Where(dao.UserAss.Columns().Id, id).
		WhereNull(dao.UserAss.Columns().DeletedAt).
		Update()

	if err != nil {
		return gerror.Wrapf(err, "Failed to update verification status for user association ID: %d", id)
	}

	return nil
}
