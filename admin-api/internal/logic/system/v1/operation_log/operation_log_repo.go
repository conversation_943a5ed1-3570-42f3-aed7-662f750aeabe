package operation_log

import (
	"context"
	"database/sql"

	// "strings" // Removed unused import
	"admin-api/internal/codes"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/operation_log" // Import the interface package

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

type operationLogRepository struct{}

// NewOperationLogRepository creates and returns a new instance of IOperationLogRepository.
func NewOperationLogRepository() operation_log.IOperationLogRepository {
	return &operationLogRepository{}
}

// List retrieves a paginated list of operation logs based on the provided conditions.
func (r *operationLogRepository) List(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.OperationLog, total int, err error) {
	m := dao.OperationLog.Ctx(ctx)
	if len(condition) > 0 {
		m = m.Where(condition)
	}

	total, err = m.Count()
	if err != nil || total == 0 {
		if err != nil && err != sql.ErrNoRows {
			return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "获取操作日志总数失败")
		}
		return []*entity.OperationLog{}, 0, nil
	}

	list = make([]*entity.OperationLog, 0)
	// Apply sorting, default to descending creation time
	err = m.Page(page, pageSize).OrderDesc(dao.OperationLog.Columns().CreatedAt).Scan(&list)
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询操作日志列表失败")
	}
	return list, total, nil
}

// GetByID retrieves a single operation log entry by its ID.
func (r *operationLogRepository) GetByID(ctx context.Context, id int64) (*entity.OperationLog, error) {
	var log *entity.OperationLog
	err := dao.OperationLog.Ctx(ctx).Where(dao.OperationLog.Columns().Id, id).Scan(&log)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCodef(codes.CodeOperationLogNotFound, "操作日志不存在 (ID: %d)", id)
		}
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询操作日志失败 (ID: %d)", id)
	}
	return log, nil
}

// ParseTimeRange function moved to utility/timeutil package
