package v1

import (
	"context"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/dao" // Keep dao for Merchants query for now
	"admin-api/internal/model/entity"
	"admin-api/internal/utility"
	"math" // Import math for Ceil

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g" // 重新添加g导入，因为我们需要使用g.Map
	"github.com/gogf/gf/v2/os/gtime"
)

// GetApiKeyList 获取API密钥列表
func (s *sSystemLogic) GetApiKeyList(ctx context.Context, req *v1.GetApiKeyListReq) (res *v1.GetApiKeyListRes, err error) {
	res = &v1.GetApiKeyListRes{}    // Initialize response
	res.Page = common.PageResponse{ // Initialize embedded struct fields
		CurrentPage: req.Page,
		PageSize:    req.PageSize,
	}
	res.Data = make([]*v1.ApiKeyInfoType, 0) // Initialize data slice

	// 处理时间范围查询 - 使用 DateRange 统一处理
	// Note: This will be applied after other conditions

	// 构建查询条件
	condition := g.Map{}
	if req.ApiKeyId > 0 {
		condition["ak.api_key_id"] = req.ApiKeyId
	}
	if req.MerchantId != nil && *req.MerchantId > 0 {
		condition["ak.merchant_id"] = *req.MerchantId
	}
	if req.MerchantName != "" {
		condition["m.merchant_name LIKE"] = "%" + req.MerchantName + "%"
	}
	if req.ApiKey != "" {
		condition["ak.api_key LIKE"] = "%" + req.ApiKey + "%"
	}
	if req.Label != "" {
		condition["ak.label LIKE"] = "%" + req.Label + "%"
	}
	if req.Status != "" {
		condition["ak.status"] = req.Status
	}
	if req.Scopes != "" {
		condition["ak.scopes LIKE"] = "%" + req.Scopes + "%"
	}
	if req.IpWhitelist != "" {
		condition["ak.ip_whitelist LIKE"] = "%" + req.IpWhitelist + "%"
	}

	// 使用 DateRange 统一处理时间范围查询
	utility.AddDateRangeCondition(condition, req.DateRange, "ak.created_at")
	// Handle ExpiresAt
	if req.ExpiresAt != "" {
		expiresTime := gtime.NewFromStr(req.ExpiresAt + " 00:00:00")
		if expiresTime != nil {
			condition["ak.expires_at >="] = expiresTime
			condition["ak.expires_at <="] = gtime.NewFromStr(req.ExpiresAt + " 23:59:59")
		}
	}
	// Handle LastUsedAt
	if req.LastUsedAt != "" {
		lastUsedTime := gtime.NewFromStr(req.LastUsedAt + " 00:00:00")
		if lastUsedTime != nil {
			condition["ak.last_used_at >="] = lastUsedTime
			condition["ak.last_used_at <="] = gtime.NewFromStr(req.LastUsedAt + " 23:59:59")
		}
	}

	// 查询API密钥列表 using repository
	list, total, err := s.apiKeyRepo.List(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, err
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = int(math.Ceil(float64(total) / float64(req.PageSize)))

	// 如果没有数据，直接返回
	if len(list) == 0 {
		return res, nil
	}

	// 收集商户ID
	merchantIds := make([]uint, 0, len(list))
	for _, apiKey := range list {
		merchantIds = append(merchantIds, apiKey.MerchantId)
	}

	// 批量查询商户信息
	merchantMap := make(map[uint]*entity.Merchants)
	if len(merchantIds) > 0 {
		var merchants []*entity.Merchants
		err = dao.Merchants.Ctx(ctx).WhereIn(dao.Merchants.Columns().MerchantId, merchantIds).WhereNull(dao.Merchants.Columns().DeletedAt).Scan(&merchants)
		if err != nil {
			return nil, gerror.Wrap(err, "查询商户信息失败")
		}

		for _, merchant := range merchants {
			merchantMap[uint(merchant.MerchantId)] = merchant
		}
	}

	// 组装返回数据
	for _, apiKey := range list {
		apiKeyInfo := &v1.ApiKeyInfoType{
			ApiKeyId:    apiKey.ApiKeyId,
			MerchantId:  apiKey.MerchantId,
			ApiKey:      apiKey.ApiKey,
			Label:       apiKey.Label,
			Status:      apiKey.Status,
			Scopes:      apiKey.Scopes,
			IpWhitelist: apiKey.IpWhitelist,
			ExpiresAt:   apiKey.ExpiresAt,
			LastUsedAt:  apiKey.LastUsedAt,
			CreatedAt:   apiKey.CreatedAt,
		}

		// 添加商户名称
		if merchant, ok := merchantMap[apiKey.MerchantId]; ok {
			apiKeyInfo.MerchantName = merchant.MerchantName
		}

		res.Data = append(res.Data, apiKeyInfo)
	}

	// 如果是导出，则直接返回数据
	if req.Export {
		// 导出逻辑可以在这里实现
		// 例如：生成Excel文件并返回下载链接
	}

	return res, nil
}

// UpdateApiKey 更新API密钥
func (s *sSystemLogic) UpdateApiKey(ctx context.Context, req *v1.UpdateApiKeyReq) (res *v1.UpdateApiKeyRes, err error) {
	res = &v1.UpdateApiKeyRes{Success: false} // Initialize response

	// 检查API密钥是否存在 using repository
	_, err = s.apiKeyRepo.GetByID(ctx, req.ApiKeyId)
	if err != nil {
		// Wrap or check specific error code if repo returns it
		if gerror.Code(err) == codes.CodeApiKeyNotFound {
			return nil, gerror.NewCode(codes.CodeApiKeyNotFound) // Return consistent not found error
		}
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询API密钥失败")
	}

	// 构建更新数据
	updateData := g.Map{
		"label":        req.Label,
		"scopes":       req.Scopes,
		"ip_whitelist": req.IpWhitelist,
		"expires_at":   req.ExpiresAt,
	}

	// 执行更新 using repository
	err = s.apiKeyRepo.Update(ctx, req.ApiKeyId, updateData)
	if err != nil {
		return nil, err
	}

	res.Success = true
	return res, nil
}

// DeleteApiKey 删除API密钥
func (s *sSystemLogic) DeleteApiKey(ctx context.Context, req *v1.DeleteApiKeyReq) (res *v1.DeleteApiKeyRes, err error) {
	res = &v1.DeleteApiKeyRes{Success: false} // Initialize response

	// 执行删除 using repository
	err = s.apiKeyRepo.DeleteSoft(ctx, req.ApiKeyIds)
	if err != nil {
		return nil, err
	}

	res.Success = true
	return res, nil
}

// UpdateApiKeyStatus 更新API密钥状态
func (s *sSystemLogic) UpdateApiKeyStatus(ctx context.Context, req *v1.UpdateApiKeyStatusReq) (res *v1.UpdateApiKeyStatusRes, err error) {
	res = &v1.UpdateApiKeyStatusRes{Success: false} // Initialize response

	// 检查API密钥是否存在 using repository
	_, err = s.apiKeyRepo.GetByID(ctx, req.ApiKeyId)
	if err != nil {
		// Wrap or check specific error code if repo returns it
		if gerror.Code(err) == codes.CodeApiKeyNotFound {
			return nil, gerror.NewCode(codes.CodeApiKeyNotFound) // Return consistent not found error
		}
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询API密钥失败")
	}

	// 检查状态值是否有效
	if req.Status != "active" && req.Status != "revoked" && req.Status != "expired" {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的状态值")
	}

	// 更新状态 using repository
	err = s.apiKeyRepo.UpdateStatus(ctx, req.ApiKeyId, req.Status)
	if err != nil {
		return nil, err
	}

	res.Success = true
	return res, nil
}
