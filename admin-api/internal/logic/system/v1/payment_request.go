package v1

import (
	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/consts"
	"admin-api/internal/dao"
	"admin-api/utility/excel"
	"context"
	"math"
	"strings"

	"admin-api/internal/model/entity" // Still needed for GetPaymentRequestDetail response embedding

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// ListPaymentRequests 获取收款请求列表 (后台管理)
func (s *sSystemLogic) ListPaymentRequests(ctx context.Context, req *v1.ListPaymentRequestReq) (res *v1.ListPaymentRequestRes, err error) {
	// 初始化返回结果
	res = &v1.ListPaymentRequestRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
			TotalSize:   0,
			TotalPage:   0,
		},
		Data: make([]*v1.PaymentRequestListItem, 0),
	}

	// 构建查询条件
	condition := g.Map{}

	// 添加筛选条件
	if req.RequestId != 0 {
		condition["pr.request_id"] = req.RequestId
	}
	if req.RequesterUsername != "" {
		condition["requester.username LIKE"] = "%" + req.RequesterUsername + "%"
	}
	if req.PayerUsername != "" {
		condition["payer.username LIKE"] = "%" + req.PayerUsername + "%"
	}
	if req.RequesterAccount != "" {
		condition["requester.username LIKE"] = "%" + req.RequesterAccount + "%"
	}
	if req.PayerAccount != "" {
		condition["payer.username LIKE"] = "%" + req.PayerAccount + "%"
	}
	if req.TokenSymbol != "" {
		condition["token.symbol LIKE"] = "%" + req.TokenSymbol + "%"
	}
	if req.Status != nil {
		condition["pr.status"] = *req.Status
	}

	// 处理日期范围
	if req.DateRange != "" {
		dateRange := strings.Split(req.DateRange, ",")
		if len(dateRange) == 2 {
			condition["pr.created_at >="] = dateRange[0] + " 00:00:00"
			condition["pr.created_at <="] = dateRange[1] + " 23:59:59"
		}
	}

	// 新增：三级代理模糊查询
	if req.FirstAgentName != "" {
		condition["requester_first_agent.username LIKE"] = "%" + req.FirstAgentName + "%"
	}
	if req.SecondAgentName != "" {
		condition["requester_second_agent.username LIKE"] = "%" + req.SecondAgentName + "%"
	}
	if req.ThirdAgentName != "" {
		condition["requester_third_agent.username LIKE"] = "%" + req.ThirdAgentName + "%"
	}

	// 新增：telegram信息查询
	if req.TelegramId != "" {
		condition["requester_uba.telegram_id LIKE"] = "%" + req.TelegramId + "%"
	}
	if req.TelegramUsername != "" {
		condition["requester_uba.telegram_username LIKE"] = "%" + req.TelegramUsername + "%"
	}
	if req.FirstName != "" {
		condition["requester_uba.first_name LIKE"] = "%" + req.FirstName + "%"
	}

	// 如果是导出
	if req.Export == 1 {
		// 导出时不进行分页限制
		list, _, err := dao.PaymentRequests.ListAdminPaymentRequestsWithFullInfo(ctx, 1, 9999999, condition)
		if err != nil {
			return nil, gerror.Wrap(err, "导出查询收款请求列表失败")
		}

		// 转换为导出格式
		exportData := make([]interface{}, len(list))
		for i, item := range list {
			createdAt := ""
			if item.CreatedAt != nil {
				createdAt = item.CreatedAt.String()
			}
			expiresAt := ""
			if item.ExpiresAt != nil {
				expiresAt = item.ExpiresAt.String()
			}

			exportData[i] = struct {
				RequestId             int64  `json:"requestId" excel:"收款请求ID"`
				RequesterUsername     string `json:"requesterUsername" excel:"发起者用户名"`
				RequesterAccount      string `json:"requesterAccount" excel:"发起者账号"`
				PayerUsername         string `json:"payerUsername" excel:"付款人用户名"`
				PayerAccount          string `json:"payerAccount" excel:"付款人账号"`
				TokenSymbol           string `json:"tokenSymbol" excel:"代币符号"`
				Amount                string `json:"amount" excel:"金额"`
				Memo                  string `json:"memo" excel:"备注"`
				StatusText            string `json:"statusText" excel:"状态"`
				CreatedAt             string `json:"createdAt" excel:"创建时间"`
				ExpiresAt             string `json:"expiresAt" excel:"过期时间"`
				FirstAgentName        string `json:"firstAgentName" excel:"发起者一级代理"`
				SecondAgentName       string `json:"secondAgentName" excel:"发起者二级代理"`
				ThirdAgentName        string `json:"thirdAgentName" excel:"发起者三级代理"`
				TelegramId            string `json:"telegramId" excel:"发起者Telegram ID"`
				TelegramUsername      string `json:"telegramUsername" excel:"发起者Telegram用户名"`
				FirstName             string `json:"firstName" excel:"发起者真实姓名"`
				PayerFirstAgentName   string `json:"payerFirstAgentName" excel:"付款人一级代理"`
				PayerSecondAgentName  string `json:"payerSecondAgentName" excel:"付款人二级代理"`
				PayerThirdAgentName   string `json:"payerThirdAgentName" excel:"付款人三级代理"`
				PayerTelegramId       string `json:"payerTelegramId" excel:"付款人Telegram ID"`
				PayerTelegramUsername string `json:"payerTelegramUsername" excel:"付款人Telegram用户名"`
				PayerFirstName        string `json:"payerFirstName" excel:"付款人真实姓名"`
			}{
				RequestId:             item.RequestId,
				RequesterUsername:     item.RequesterUsername,
				RequesterAccount:      item.RequesterAccount,
				PayerUsername:         item.PayerUsername,
				PayerAccount:          item.PayerAccount,
				TokenSymbol:           item.TokenSymbol,
				Amount:                item.Amount.String(),
				Memo:                  item.Memo,
				StatusText:            consts.PaymentRequestStatusText[item.Status],
				CreatedAt:             createdAt,
				ExpiresAt:             expiresAt,
				FirstAgentName:        item.FirstAgentName,
				SecondAgentName:       item.SecondAgentName,
				ThirdAgentName:        item.ThirdAgentName,
				TelegramId:            item.TelegramId,
				TelegramUsername:      item.TelegramUsername,
				FirstName:             item.FirstName,
				PayerFirstAgentName:   item.PayerFirstAgentName,
				PayerSecondAgentName:  item.PayerSecondAgentName,
				PayerThirdAgentName:   item.PayerThirdAgentName,
				PayerTelegramId:       item.PayerTelegramId,
				PayerTelegramUsername: item.PayerTelegramUsername,
				PayerFirstName:        item.PayerFirstName,
			}
		}

		// 定义Excel表头
		excelTags := []string{} // excel.ExportByStructs 会自动从 struct tag 读取

		// 调用Excel导出工具
		return res, excel.ExportByStructs(ctx, excelTags, exportData, "收款请求", "收款请求列表")
	}

	// 查询分页数据
	list, total, err := dao.PaymentRequests.ListAdminPaymentRequestsWithFullInfo(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, gerror.Wrap(err, "查询收款请求列表失败")
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = int(math.Ceil(float64(total) / float64(req.PageSize)))

	// 转换DAO结果到API响应格式
	res.Data = make([]*v1.PaymentRequestListItem, len(list))
	for i, item := range list {
		createdAt := ""
		if item.CreatedAt != nil {
			createdAt = item.CreatedAt.String()
		}
		expiresAt := ""
		if item.ExpiresAt != nil {
			expiresAt = item.ExpiresAt.String()
		}

		res.Data[i] = &v1.PaymentRequestListItem{
			RequestId:             item.RequestId,
			RequesterUserId:       item.RequesterUserId,
			RequesterUsername:     item.RequesterUsername,
			RequesterAccount:      item.RequesterAccount,
			PayerUserId:           item.PayerUserId,
			PayerUsername:         item.PayerUsername,
			PayerAccount:          item.PayerAccount,
			TokenId:               item.TokenId,
			TokenSymbol:           item.TokenSymbol,
			TokenName:             item.TokenName,
			Amount:                item.Amount.String(),
			Memo:                  item.Memo,
			Status:                item.Status,
			StatusText:            consts.PaymentRequestStatusText[item.Status],
			CreatedAt:             createdAt,
			ExpiresAt:             expiresAt,
			FirstAgentName:        item.FirstAgentName,
			SecondAgentName:       item.SecondAgentName,
			ThirdAgentName:        item.ThirdAgentName,
			TelegramId:            item.TelegramId,
			TelegramUsername:      item.TelegramUsername,
			FirstName:             item.FirstName,
			PayerFirstAgentName:   item.PayerFirstAgentName,
			PayerSecondAgentName:  item.PayerSecondAgentName,
			PayerThirdAgentName:   item.PayerThirdAgentName,
			PayerTelegramId:       item.PayerTelegramId,
			PayerTelegramUsername: item.PayerTelegramUsername,
			PayerFirstName:        item.PayerFirstName,
		}
	}

	return res, nil
}

// GetPaymentRequestDetail 获取收款请求详情 (后台管理)
func (s *sSystemLogic) GetPaymentRequestDetail(ctx context.Context, req *v1.GetPaymentRequestDetailReq) (res *v1.GetPaymentRequestDetailRes, err error) {
	res = &v1.GetPaymentRequestDetailRes{}

	// 调用 Repository 获取详情 DTO
	detailDTO, err := s.paymentRequestRepo.GetDetailByID(ctx, req.RequestId)
	if err != nil {
		// Repository 层会处理 gerror.Wrap
		return nil, err // 直接返回错误
	}
	if detailDTO == nil {
		// Repository 层约定未找到返回 nil, nil
		return nil, gerror.NewCodef(codes.CodePaymentRequestNotFound, "收款请求不存在, RequestId: %d", req.RequestId)
	}

	// 将 DTO 转换为 API 响应结构
	detail := &v1.PaymentRequestDetail{}
	// 转换基础实体部分
	if detailDTO.PaymentRequests != nil {
		detail.PaymentRequests = detailDTO.PaymentRequests
	} else {
		// 如果基础实体部分为 nil，需要处理，或者确保 repo 层总是返回非 nil 的基础实体部分
		detail.PaymentRequests = &entity.PaymentRequests{} // 初始化以避免 nil panic
		g.Log().Warningf(ctx, "GetPaymentRequestDetail: Repository 返回的 DTO 中 PaymentRequests 部分为 nil, RequestId: %d", req.RequestId)
	}

	// 填充其他字段从 DTO
	detail.RequesterUsername = detailDTO.RequesterUsername
	detail.PayerUsername = detailDTO.PayerUsername
	detail.TokenSymbol = detailDTO.TokenSymbol
	detail.TokenName = detailDTO.TokenName
	detail.TransactionId = detailDTO.TransactionId
	detail.TransactionStatus = detailDTO.TransactionStatus
	detail.TransactionTime = detailDTO.TransactionTime // DTO 中已是 string

	// 填充状态文本 (添加类型转换)
	detail.StatusText = consts.PaymentRequestStatusText[int(detail.PaymentRequests.Status)]

	res.Data = detail

	return res, nil
}

// UpdatePaymentRequestStatus 更新收款请求状态 (后台管理)
func (s *sSystemLogic) UpdatePaymentRequestStatus(ctx context.Context, req *v1.UpdatePaymentRequestStatusReq) (res *v1.UpdatePaymentRequestStatusRes, err error) {
	res = &v1.UpdatePaymentRequestStatusRes{}

	// 1. 检查请求是否存在 (使用 Repository)
	existingRequest, err := s.paymentRequestRepo.GetByID(ctx, req.RequestId)
	if err != nil {
		// Repository 层会处理 gerror.Wrap
		return nil, err // 直接返回错误
	}
	if existingRequest == nil {
		// Repository 层约定未找到返回 nil, nil
		return nil, gerror.NewCodef(codes.CodePaymentRequestNotFound, "收款请求不存在, RequestId: %d", req.RequestId)
	}

	// 2. 更新状态 (使用 Repository)
	// 注意：API 层的校验器已限制 targetStatus 只能是 3 或 4
	// Repository 的 UpdateStatus 目前只更新状态，不处理 remark
	err = s.paymentRequestRepo.UpdateStatus(ctx, req.RequestId, req.TargetStatus)
	if err != nil {
		// Repository 层会处理 gerror.Wrap
		return nil, err // 直接返回错误
	}
	// 如果需要更新 remark，需要扩展 Repository 接口和实现，或在此处单独处理
	// 例如:
	// if req.Remark != "" {
	//     _, errUpdateRemark := dao.PaymentRequests.Ctx(ctx).Data(g.Map{"remark": req.Remark}).WherePri(req.RequestId).Update()
	//     if errUpdateRemark != nil {
	//         g.Log().Warningf(ctx, "更新收款请求备注失败 (状态已更新), RequestId: %d, Remark: %s, Error: %v", req.RequestId, req.Remark, errUpdateRemark)
	//         // 可以选择是否将此警告作为错误返回
	//     }
	// }

	res.Success = true
	return res, nil
}
