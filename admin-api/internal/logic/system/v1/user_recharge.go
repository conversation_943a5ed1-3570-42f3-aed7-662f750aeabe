package v1

import (
	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/dao"
	"admin-api/utility/excel"
	"context"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// ListUserRecharges 获取用户充值记录列表
func (s *sSystemLogic) ListUserRecharges(ctx context.Context, req *v1.ListUserRechargesReq) (res *v1.ListUserRechargesRes, err error) {
	// 初始化返回结果，避免空指针
	res = &v1.ListUserRechargesRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
			TotalSize:   0,
			TotalPage:   0,
		},
		Data: make([]*v1.UserRechargeListItem, 0),
	}

	// 参数校验
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	condition := g.Map{}

	// 用户条件
	if req.UserId > 0 {
		condition["ur.user_id"] = req.UserId
	}
	if req.Account != "" {
		condition["u.username LIKE"] = "%" + req.Account + "%"
	}
	if req.TokenId > 0 {
		condition["ur.token_id"] = req.TokenId
	}
	if req.Symbol != "" {
		condition["ur.name LIKE"] = "%" + req.Symbol + "%"
	}
	if req.Chan != "" {
		condition["ur.chan"] = req.Chan
	}
	if req.FromAddress != "" {
		condition["ur.from_address LIKE"] = "%" + req.FromAddress + "%"
	}
	if req.ToAddress != "" {
		condition["ur.to_address LIKE"] = "%" + req.ToAddress + "%"
	}
	if req.TxHash != "" {
		condition["ur.tx_hash"] = req.TxHash
	}
	if req.State > 0 {
		condition["ur.state"] = req.State
	}

	// 日期范围条件
	if req.DateRange != "" {
		dates := strings.Split(req.DateRange, ",")
		if len(dates) == 2 {
			startDate := strings.TrimSpace(dates[0])
			endDate := strings.TrimSpace(dates[1])
			if startDate != "" && endDate != "" {
				condition["ur.created_at BETWEEN ? AND ?"] = g.Slice{startDate + " 00:00:00", endDate + " 23:59:59"}
			}
		}
	}

	// 代理查询条件
	if req.FirstAgentName != "" {
		condition["u_first_agent.username LIKE"] = "%" + req.FirstAgentName + "%"
	}
	if req.SecondAgentName != "" {
		condition["u_second_agent.username LIKE"] = "%" + req.SecondAgentName + "%"
	}
	if req.ThirdAgentName != "" {
		condition["u_third_agent.username LIKE"] = "%" + req.ThirdAgentName + "%"
	}

	// Telegram查询条件
	if req.TelegramId != "" {
		condition["u_uba.telegram_id LIKE"] = "%" + req.TelegramId + "%"
	}
	if req.TelegramUsername != "" {
		condition["u_uba.telegram_username LIKE"] = "%" + req.TelegramUsername + "%"
	}
	if req.FirstName != "" {
		condition["u_uba.first_name LIKE"] = "%" + req.FirstName + "%"
	}

	// 处理导出
	if req.Export == 1 {
		// 导出时不进行分页限制
		list, _, err := dao.UserRecharges.ListAdminUserRechargesWithFullInfo(ctx, 1, 9999999, condition)
		if err != nil {
			return nil, gerror.Wrap(err, "导出查询失败")
		}

		// 转换为导出格式
		exportData := make([]interface{}, len(list))
		for i, item := range list {
			createdAt := ""
			if item.CreatedAt != nil {
				createdAt = item.CreatedAt.String()
			}
			completedAt := ""
			if item.CompletedAt != nil {
				completedAt = item.CompletedAt.String()
			}
			updatedAt := ""
			if item.UpdatedAt != nil {
				updatedAt = item.UpdatedAt.String()
			}

			// 状态文本
			stateText := ""
			switch item.State {
			case 1:
				stateText = "待确认"
			case 2:
				stateText = "已完成"
			case 3:
				stateText = "已撤销"
			default:
				stateText = "未知"
			}

			// 计算处理时长
			duration := ""
			if item.CompletedAt != nil && !item.CompletedAt.IsZero() && item.CreatedAt != nil {
				dur := item.CompletedAt.Time.Sub(item.CreatedAt.Time)
				hours := int(dur.Hours())
				minutes := int(dur.Minutes()) % 60
				if hours > 0 {
					duration = fmt.Sprintf("%d小时%d分钟", hours, minutes)
				} else {
					duration = fmt.Sprintf("%d分钟", minutes)
				}
			}

			exportData[i] = struct {
				UserRechargesId      uint   `json:"userRechargesId" excel:"充值记录ID"`
				UserId               uint64 `json:"userId" excel:"用户ID"`
				Account              string `json:"account" excel:"用户账号"`
				Name                 string `json:"name" excel:"币种名称"`
				Chan                 string `json:"chan" excel:"链名称"`
				TokenContractAddress string `json:"tokenContractAddress" excel:"代币合约地址"`
				FromAddress          string `json:"fromAddress" excel:"来源地址"`
				ToAddress            string `json:"toAddress" excel:"充值地址"`
				TxHash               string `json:"txHash" excel:"交易哈希"`
				Amount               string `json:"amount" excel:"充值金额"`
				StateText            string `json:"stateText" excel:"状态"`
				Confirmations        uint   `json:"confirmations" excel:"确认数"`
				Error                string `json:"error" excel:"错误信息"`
				Duration             string `json:"duration" excel:"处理时长"`
				CreatedAt            string `json:"createdAt" excel:"创建时间"`
				CompletedAt          string `json:"completedAt" excel:"完成时间"`
				UpdatedAt            string `json:"updatedAt" excel:"更新时间"`
				FirstAgentName       string `json:"firstAgentName" excel:"一级代理"`
				SecondAgentName      string `json:"secondAgentName" excel:"二级代理"`
				ThirdAgentName       string `json:"thirdAgentName" excel:"三级代理"`
				TelegramId           string `json:"telegramId" excel:"Telegram ID"`
				TelegramUsername     string `json:"telegramUsername" excel:"Telegram用户名"`
				FirstName            string `json:"firstName" excel:"真实姓名"`
			}{
				UserRechargesId:      item.UserRechargesId,
				UserId:               item.UserId,
				Account:              item.Account,
				Name:                 item.Name,
				Chan:                 item.Chan,
				TokenContractAddress: item.TokenContractAddress,
				FromAddress:          item.FromAddress,
				ToAddress:            item.ToAddress,
				TxHash:               item.TxHash,
				Amount:               item.Amount.String(),
				StateText:            stateText,
				Confirmations:        item.Confirmations,
				Error:                item.Error,
				Duration:             duration,
				CreatedAt:            createdAt,
				CompletedAt:          completedAt,
				UpdatedAt:            updatedAt,
				FirstAgentName:       item.FirstAgentName,
				SecondAgentName:      item.SecondAgentName,
				ThirdAgentName:       item.ThirdAgentName,
				TelegramId:           item.TelegramId,
				TelegramUsername:     item.TelegramUsername,
				FirstName:            item.FirstName,
			}
		}

		// 定义Excel表头
		excelTags := []string{} // excel.ExportByStructs 会自动从 struct tag 读取

		// 调用Excel导出工具
		err = excel.ExportByStructs(ctx, excelTags, exportData, "用户充值记录", "用户充值记录列表")
		return nil, err
	}

	// 查询分页数据
	list, total, err := dao.UserRecharges.ListAdminUserRechargesWithFullInfo(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询用户充值记录列表失败")
	}

	// 设置分页信息
	res.Page.TotalSize = total
	if req.PageSize > 0 {
		res.Page.TotalPage = (total + req.PageSize - 1) / req.PageSize
	}

	// 转换为API响应格式
	res.Data = make([]*v1.UserRechargeListItem, len(list))
	for i, item := range list {
		// 格式化时间
		createdAt := ""
		if item.CreatedAt != nil {
			createdAt = item.CreatedAt.String()
		}
		completedAt := ""
		if item.CompletedAt != nil && !item.CompletedAt.IsZero() {
			completedAt = item.CompletedAt.String()
		}
		updatedAt := ""
		if item.UpdatedAt != nil {
			updatedAt = item.UpdatedAt.String()
		}

		// 状态文本
		stateText := ""
		switch item.State {
		case 1:
			stateText = "待确认"
		case 2:
			stateText = "已完成"
		case 3:
			stateText = "已撤销"
		default:
			stateText = "未知"
		}

		// 计算处理时长
		duration := ""
		if item.CompletedAt != nil && !item.CompletedAt.IsZero() && item.CreatedAt != nil {
			dur := item.CompletedAt.Time.Sub(item.CreatedAt.Time)
			hours := int(dur.Hours())
			minutes := int(dur.Minutes()) % 60
			if hours > 0 {
				duration = fmt.Sprintf("%d小时%d分钟", hours, minutes)
			} else {
				duration = fmt.Sprintf("%d分钟", minutes)
			}
		}

		// 是否已确认
		isConfirmed := item.Confirmations >= 6

		res.Data[i] = &v1.UserRechargeListItem{
			UserRechargesId:      item.UserRechargesId,
			UserId:               item.UserId,
			Account:              item.Account,
			TokenId:              item.TokenId,
			Name:                 item.Name,
			Chan:                 item.Chan,
			TokenContractAddress: item.TokenContractAddress,
			FromAddress:          item.FromAddress,
			ToAddress:            item.ToAddress,
			TxHash:               item.TxHash,
			Amount:               item.Amount.String(),
			State:                item.State,
			StateText:            stateText,
			Confirmations:        item.Confirmations,
			Error:                item.Error,
			CreatedAt:            createdAt,
			CompletedAt:          completedAt,
			UpdatedAt:            updatedAt,
			Duration:             duration,
			IsConfirmed:          isConfirmed,
			NotificationSent:     item.NotificationSent,
			NotificationSentAt:   item.NotificationSentAt,
			FailureReason:        item.FailureReason,
			FirstAgentName:       item.FirstAgentName,
			SecondAgentName:      item.SecondAgentName,
			ThirdAgentName:       item.ThirdAgentName,
			TelegramId:           item.TelegramId,
			TelegramUsername:     item.TelegramUsername,
			FirstName:            item.FirstName,
		}
	}

	return res, nil
}
