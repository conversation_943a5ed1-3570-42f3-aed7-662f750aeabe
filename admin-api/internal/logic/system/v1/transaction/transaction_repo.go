package transaction

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/dao"
	"admin-api/internal/model"                      // Import model for TransactionAdminInfo
	"admin-api/internal/service/system/transaction" // Import the interface package
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// transactionRepository 实现了 ITransactionRepository 接口
type transactionRepository struct{}

// NewTransactionRepository 创建一个新的 transactionRepository 实例
func NewTransactionRepository() transaction.ITransactionRepository {
	return &transactionRepository{}
}

// ListAdmin 获取后台交易记录列表
func (r *transactionRepository) ListAdmin(ctx context.Context, page, pageSize int, condition g.Map) (list []*model.TransactionAdminInfo, total int, err error) {
	// 使用 DAO 中已有的关联查询方法
	// 注意：DAO 中的 ListAdminTransactions 可能返回的是 map[string]interface{} 或特定结构体
	// 需要确认 dao.Transactions.ListAdminTransactions 的返回类型
	// 假设它返回 []*model.TransactionAdminInfo, total, error
	daoList, total, err := dao.Transactions.ListAdminTransactions(ctx, page, pageSize, condition)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "transactionRepository.ListAdmin: 调用 DAO 查询失败")
	}

	// 如果 DAO 返回的是 []*model.TransactionAdminInfo，则直接赋值
	list = daoList

	// 如果 DAO 返回的是其他类型（例如 map），则需要转换
	// list = make([]*model.TransactionAdminInfo, 0, len(daoList))
	// if err = gconv.Structs(daoList, &list); err != nil {
	// 	 return nil, 0, gerror.Wrap(err, "transactionRepository.ListAdmin: 转换 DAO 结果到 DTO 失败")
	// }

	// DTO 结构与 DAO 返回的 model 结构字段一致，无需额外处理 (假设)

	return list, total, nil
}

// ListAdminWithAgentInfo 获取后台交易记录列表（带代理和telegram信息）
// Deprecated: 此方法已废弃，请在 logic 层直接调用 dao.Transactions.ListAdminTransactionsWithFullInfo
func (r *transactionRepository) ListAdminWithAgentInfo(ctx context.Context, req *v1.ListAdminTransactionsReq) (list []*model.TransactionAdminInfo, total int, err error) {
	// 保留此方法以兼容旧代码，但实际上已不再使用
	// 如果有其他模块调用此方法，应该逐步迁移到直接调用 DAO
	return make([]*model.TransactionAdminInfo, 0), 0, nil
}
