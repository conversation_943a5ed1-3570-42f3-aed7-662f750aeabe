package role

import (
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/role" // Import the interface package
	"context"
	"database/sql"
	"errors"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// roleRepository 实现了 IRoleRepository 接口
type roleRepository struct{}

// NewRoleRepository 创建一个新的 roleRepository 实例
func NewRoleRepository() role.IRoleRepository {
	return &roleRepository{}
}

// List 获取角色列表
func (r *roleRepository) List(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.AdminRole, total int, err error) {
	m := dao.AdminRole.Ctx(ctx).Where(condition).WhereNull(dao.AdminRole.Columns().DeletedAt)

	total, err = m.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "roleRepository.List: 获取角色总数失败")
	}
	if total == 0 {
		return make([]*entity.AdminRole, 0), 0, nil
	}

	err = m.Page(page, pageSize).OrderAsc(dao.AdminRole.Columns().Sort).OrderAsc(dao.AdminRole.Columns().Id).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "roleRepository.List: 查询角色列表失败")
	}
	return list, total, nil
}

// GetByID 根据 ID 获取角色实体
func (r *roleRepository) GetByID(ctx context.Context, id int64) (*entity.AdminRole, error) {
	var entity *entity.AdminRole
	err := dao.AdminRole.Ctx(ctx).Where(dao.AdminRole.Columns().Id, id).WhereNull(dao.AdminRole.Columns().DeletedAt).Scan(&entity)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // Not found
		}
		return nil, gerror.Wrapf(err, "roleRepository.GetByID: 查询角色失败, ID: %d", id)
	}
	return entity, nil
}

// GetByIDs 根据 ID 列表获取角色实体列表
func (r *roleRepository) GetByIDs(ctx context.Context, ids []int64) ([]*entity.AdminRole, error) {
	var list []*entity.AdminRole
	if len(ids) == 0 {
		return list, nil
	}
	err := dao.AdminRole.Ctx(ctx).WhereIn(dao.AdminRole.Columns().Id, ids).WhereNull(dao.AdminRole.Columns().DeletedAt).Scan(&list)
	if err != nil {
		return nil, gerror.Wrapf(err, "roleRepository.GetByIDs: 查询角色列表失败, IDs: %v", ids)
	}
	return list, nil
}

// Create 创建角色
func (r *roleRepository) Create(ctx context.Context, data *do.AdminRole) (id int64, err error) {
	data.CreatedAt = gtime.Now()
	data.UpdatedAt = gtime.Now()
	res, err := dao.AdminRole.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return 0, gerror.Wrap(err, "roleRepository.Create: 创建角色失败")
	}
	id, err = res.LastInsertId()
	if err != nil {
		return 0, gerror.Wrap(err, "roleRepository.Create: 获取新角色ID失败")
	}
	return id, nil
}

// Update 更新角色基础信息 (不包括权限相关)
func (r *roleRepository) Update(ctx context.Context, id int64, data g.Map) error {
	data[dao.AdminRole.Columns().UpdatedAt] = gtime.Now() // 确保更新时间
	_, err := dao.AdminRole.Ctx(ctx).Data(data).Where(dao.AdminRole.Columns().Id, id).Update()
	if err != nil {
		return gerror.Wrapf(err, "roleRepository.Update: 更新角色失败, ID: %d", id)
	}
	return nil
}

// UpdateDataScope 更新角色数据范围
func (r *roleRepository) UpdateDataScope(ctx context.Context, id int64, dataScope int, customDeptJson []byte) error {
	updateData := g.Map{
		dao.AdminRole.Columns().DataScope:  dataScope,
		dao.AdminRole.Columns().UpdatedAt:  gtime.Now(),
		dao.AdminRole.Columns().CustomDept: nil, // 默认为 NULL
	}
	if dataScope == 5 && customDeptJson != nil { // 假设 5 是自定义
		updateData[dao.AdminRole.Columns().CustomDept] = customDeptJson
	}

	_, err := dao.AdminRole.Ctx(ctx).Data(updateData).Where(dao.AdminRole.Columns().Id, id).Update()
	if err != nil {
		return gerror.Wrapf(err, "roleRepository.UpdateDataScope: 更新角色数据范围失败, ID: %d", id)
	}
	return nil
}

// DeleteSoft 软删除角色
func (r *roleRepository) DeleteSoft(ctx context.Context, ids []int64) error {
	_, err := dao.AdminRole.Ctx(ctx).Data(do.AdminRole{DeletedAt: gtime.Now()}).WhereIn(dao.AdminRole.Columns().Id, ids).Update()
	if err != nil {
		return gerror.Wrapf(err, "roleRepository.DeleteSoft: 软删除角色失败, IDs: %v", ids)
	}
	return nil
}

// IsKeyExist 检查 Key 是否存在 (排除指定 ID)
func (r *roleRepository) IsKeyExist(ctx context.Context, key string, excludeId ...int64) (bool, error) {
	m := dao.AdminRole.Ctx(ctx).Where(dao.AdminRole.Columns().Key, key).WhereNull(dao.AdminRole.Columns().DeletedAt)
	if len(excludeId) > 0 {
		m = m.WhereNotIn(dao.AdminRole.Columns().Id, excludeId)
	}
	count, err := m.Count()
	if err != nil {
		return false, gerror.Wrapf(err, "roleRepository.IsKeyExist: 检查角色 Key 失败, Key: %s", key)
	}
	return count > 0, nil
}

// GetRoleMapByIds 根据 ID 列表获取 ID -> Role 实体的映射
func (r *roleRepository) GetRoleMapByIds(ctx context.Context, ids []int64) (map[int64]*entity.AdminRole, error) {
	roleMap := make(map[int64]*entity.AdminRole)
	if len(ids) == 0 {
		return roleMap, nil
	}
	list, err := r.GetByIDs(ctx, ids) // Reuse GetByIDs
	if err != nil {
		return nil, err // GetByIDs already wraps the error
	}
	for _, role := range list {
		roleMap[role.Id] = role
	}
	return roleMap, nil
}

// CountByIDs 根据 ID 列表计算角色数量
func (r *roleRepository) CountByIDs(ctx context.Context, ids []int64) (int, error) {
	if len(ids) == 0 {
		return 0, nil
	}
	count, err := dao.AdminRole.Ctx(ctx).WhereIn(dao.AdminRole.Columns().Id, ids).WhereNull(dao.AdminRole.Columns().DeletedAt).Count()
	if err != nil {
		return 0, gerror.Wrapf(err, "roleRepository.CountByIDs: 查询角色数量失败, IDs: %v", ids)
	}
	return count, nil
}

// GetRolesByIDs 根据 ID 列表获取角色实体列表 (兼容 member.go 调用)
func (r *roleRepository) GetRolesByIDs(ctx context.Context, ids []int64) ([]*entity.AdminRole, error) {
	// 直接复用 GetByIDs 方法
	return r.GetByIDs(ctx, ids)
}
