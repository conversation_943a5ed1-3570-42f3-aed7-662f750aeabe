package v1

import (
	"context"
	"fmt"
	"math"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/utility/excel"
)

// ListAdminRedPackets 获取红包列表
func (s *sSystemLogic) ListAdminRedPackets(ctx context.Context, req *v1.ListAdminRedPacketsReq) (res *v1.ListAdminRedPacketsRes, err error) {
	// 初始化返回结构
	res = &v1.ListAdminRedPacketsRes{
		List: make([]*v1.RedPacketAdminInfoType, 0),
		Page: &common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
	}

	// 构建查询条件
	condition := g.Map{}

	// 按创建者ID筛选
	if req.CreatorUserId > 0 {
		condition["rp.creator_user_id"] = req.CreatorUserId
	}

	// 按代币ID筛选
	if req.TokenId > 0 {
		condition["rp.token_id"] = req.TokenId
	}

	// 按状态筛选
	if req.Status != "" {
		condition["rp.status"] = req.Status
	}

	// 按红包类型筛选
	if req.Type != "" {
		condition["rp.type"] = req.Type
	}

	// 按发送人账号筛选
	if req.SenderAccount != "" {
		condition["creator.account LIKE ?"] = "%" + req.SenderAccount + "%"
	}

	// 按发送人昵称筛选
	if req.SenderNickname != "" {
		condition["rp.creator_username LIKE ?"] = "%" + req.SenderNickname + "%"
	}

	// 按代币符号筛选
	if req.Symbol != "" {
		condition["rp.symbol"] = req.Symbol
	}

	// 按红包UUID模糊搜索
	if req.Uuid != "" {
		condition["rp.uuid LIKE"] = "%" + req.Uuid + "%"
	}

	// 处理日期范围
	if req.DateRange != "" {
		dateRange := strings.Split(req.DateRange, ",")
		if len(dateRange) == 2 {
			condition["rp.created_at >="] = dateRange[0] + " 00:00:00"
			condition["rp.created_at <="] = dateRange[1] + " 23:59:59"
		}
	}

	// 发送方代理查询条件
	if req.FirstAgentName != "" {
		condition["creator_first_agent.username LIKE"] = "%" + req.FirstAgentName + "%"
	}
	if req.SecondAgentName != "" {
		condition["creator_second_agent.username LIKE"] = "%" + req.SecondAgentName + "%"
	}
	if req.ThirdAgentName != "" {
		condition["creator_third_agent.username LIKE"] = "%" + req.ThirdAgentName + "%"
	}

	// 发送方Telegram查询条件
	if req.TelegramId != "" {
		condition["creator_uba.telegram_id LIKE"] = "%" + req.TelegramId + "%"
	}
	if req.TelegramUsername != "" {
		condition["creator_uba.telegram_username LIKE"] = "%" + req.TelegramUsername + "%"
	}
	if req.FirstName != "" {
		condition["creator_uba.first_name LIKE"] = "%" + req.FirstName + "%"
	}

	// 处理导出
	if req.Export {
		// 直接调用DAO方法获取完整信息
		list, _, err := dao.RedPackets.ListAdminRedPacketsWithFullInfo(ctx, 1, 10000, condition)
		if err != nil {
			return nil, gerror.Wrap(err, "导出查询红包列表失败")
		}

		// 导出Excel
		header := []string{"红包ID", "红包UUID", "创建者ID", "创建者用户名", "代币", "红包类型", "总金额", "总数量", "剩余金额", "剩余数量", "红包状态", "留言", "创建时间", "过期时间"}
		rows := make([][]interface{}, 0, len(list))

		for _, item := range list {
			// 格式化数据
			createdAt := ""
			expiresAt := ""
			if item.CreatedAt != nil {
				createdAt = item.CreatedAt.Format("Y-m-d H:i:s")
			}
			if item.ExpiresAt != nil {
				expiresAt = item.ExpiresAt.Format("Y-m-d H:i:s")
			}

			// 组装数据行
			row := []interface{}{
				item.RedPacketId,
				item.Uuid,
				item.CreatorUserId,
				item.CreatorUsername,
				item.TokenSymbol,
				item.Type,
				item.TotalAmount.String(),
				item.TotalQuantity,
				item.RemainingAmount.String(),
				item.RemainingQuantity,
				item.Status,
				item.Memo,
				createdAt,
				expiresAt,
			}
			rows = append(rows, row)
		}

		// 生成Excel文件
		fileName := fmt.Sprintf("红包列表_%s", gtime.Now().Format("YmdHis"))
		return nil, excel.ExportByStructs(ctx, header, rows, fileName, "红包列表")
	}

	// 直接调用DAO方法进行分页查询
	list, total, err := dao.RedPackets.ListAdminRedPacketsWithFullInfo(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, gerror.Wrap(err, "查询红包列表失败")
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = int(math.Ceil(float64(total) / float64(req.PageSize)))

	// 转换DAO结果到API响应格式
	res.List = make([]*v1.RedPacketAdminInfoType, len(list))
	for i, item := range list {
		res.List[i] = &v1.RedPacketAdminInfoType{
			// 嵌入的 RedPackets 字段
			RedPackets: entity.RedPackets{
				RedPacketId:       item.RedPacketId,
				Uuid:              item.Uuid,
				SenderUserId:      uint64(item.CreatorUserId),
				CreatorUsername:   item.CreatorUsername,
				TokenId:           item.TokenId,
				Symbol:            item.Symbol,
				Type:              item.Type,
				TotalAmount:       item.TotalAmount,
				Quantity:          item.TotalQuantity,
				RemainingAmount:   item.RemainingAmount,
				RemainingQuantity: item.RemainingQuantity,
				Status:            item.Status,
				Memo:              item.Memo,
				RedPacketImagesId: item.ImageId,
				ThumbUrl:          item.ImageUrl,
				MessageId:         item.MessageId,
				ExpiresAt:         item.ExpiresAt,
				CreatedAt:         item.CreatedAt,
			},
			// 其他字段
			TokenLogo:        item.TokenLogo,
			FirstAgentName:   item.FirstAgentName,
			SecondAgentName:  item.SecondAgentName,
			ThirdAgentName:   item.ThirdAgentName,
			TelegramId:       item.TelegramId,
			TelegramUsername: item.TelegramUsername,
			FirstName:        item.FirstName,
		}
	}

	return res, nil
}

// GetAdminRedPacketDetail 获取红包详情
func (s *sSystemLogic) GetAdminRedPacketDetail(ctx context.Context, req *v1.GetAdminRedPacketDetailReq) (res *v1.GetAdminRedPacketDetailRes, err error) {
	// 初始化返回结构
	// Initialize response struct directly
	res = &v1.GetAdminRedPacketDetailRes{
		RedPacket: nil,
		Claims:    make([]*v1.RedPacketClaimAdminInfoType, 0),
	}

	// TODO: 需要实现直接DAO查询方法
	// 暂时返回空数据
	return res, nil

}

// CancelRedPacket 取消红包
func (s *sSystemLogic) CancelRedPacket(ctx context.Context, req *v1.CancelRedPacketReq) (res *v1.CancelRedPacketRes, err error) {
	res = &v1.CancelRedPacketRes{
		Success: false,
	}

	// TODO: 需要实现取消红包功能，包括事务处理、状态检查、金额退还等
	// 暂时返回失败
	res.Success = false
	return res, nil
}

// ListAdminRedPacketClaims 获取红包领取记录列表
func (s *sSystemLogic) ListAdminRedPacketClaims(ctx context.Context, req *v1.ListAdminRedPacketClaimsReq) (res *v1.ListAdminRedPacketClaimsRes, err error) {
	// 初始化返回结构
	res = &v1.ListAdminRedPacketClaimsRes{
		List: make([]*v1.RedPacketClaimAdminInfoType, 0),
		Page: &common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
	}

	// 构建查询条件
	condition := g.Map{}

	// 按红包ID筛选
	if req.RedPacketId > 0 {
		condition["rpc.red_packet_id"] = req.RedPacketId
	}

	// 按红包UUID模糊搜索
	if req.RedPacketUuid != "" {
		condition["rp.uuid LIKE"] = "%" + req.RedPacketUuid + "%"
	}

	// 按领取者ID筛选
	if req.ClaimerUserId > 0 {
		condition["rpc.claimer_user_id"] = req.ClaimerUserId
	}

	// 按发送方用户ID筛选
	if req.SenderUserId > 0 {
		condition["rpc.sender_user_id"] = req.SenderUserId
	}

	// 按接收方用户ID筛选
	if req.ReceiverUserId > 0 {
		condition["rpc.receiver_user_id"] = req.ReceiverUserId
	}

	// 按状态筛选
	if req.Status != "" {
		condition["rpc.status"] = req.Status
	}

	// 按代币符号筛选
	if req.Symbol != "" {
		condition["rp.symbol"] = req.Symbol
	}

	// 按红包类型筛选
	if req.RedPacketType != "" {
		condition["rp.type"] = req.RedPacketType
	}

	// 按发送方用户名筛选
	if req.SenderUsername != "" {
		condition["rpc.sender_username LIKE ?"] = "%" + req.SenderUsername + "%"
	}

	// 按接收方用户名筛选
	if req.ReceiverUsername != "" {
		condition["rpc.receiver_username LIKE ?"] = "%" + req.ReceiverUsername + "%"
	}

	// 按领取者用户名筛选
	if req.ClaimerUsername != "" {
		condition["claimer.account LIKE ?"] = "%" + req.ClaimerUsername + "%"
	}

	// 按金额范围筛选
	if req.MinAmount > 0 {
		condition["rpc.claimed_amount >="] = req.MinAmount
	}
	if req.MaxAmount > 0 {
		condition["rpc.claimed_amount <="] = req.MaxAmount
	}

	// 处理日期范围
	if req.DateRange != "" {
		dateRange := strings.Split(req.DateRange, ",")
		if len(dateRange) == 2 {
			condition["rpc.claimed_at >="] = dateRange[0] + " 00:00:00"
			condition["rpc.claimed_at <="] = dateRange[1] + " 23:59:59"
		}
	}

	// 领取方代理查询条件（与返回字段一致）
	if req.FirstAgentName != "" {
		condition["claimer_first_agent.username LIKE"] = "%" + req.FirstAgentName + "%"
	}
	if req.SecondAgentName != "" {
		condition["claimer_second_agent.username LIKE"] = "%" + req.SecondAgentName + "%"
	}
	if req.ThirdAgentName != "" {
		condition["claimer_third_agent.username LIKE"] = "%" + req.ThirdAgentName + "%"
	}

	// 领取方Telegram查询条件（与返回字段一致）
	if req.TelegramId != "" {
		condition["claimer_uba.telegram_id LIKE"] = "%" + req.TelegramId + "%"
	}
	if req.TelegramUsername != "" {
		condition["claimer_uba.telegram_username LIKE"] = "%" + req.TelegramUsername + "%"
	}
	if req.FirstName != "" {
		condition["claimer_uba.first_name LIKE"] = "%" + req.FirstName + "%"
	}

	// 发送方代理查询条件（与返回字段一致）
	if req.SenderFirstAgentName != "" {
		condition["sender_first_agent.username LIKE"] = "%" + req.SenderFirstAgentName + "%"
	}
	if req.SenderSecondAgentName != "" {
		condition["sender_second_agent.username LIKE"] = "%" + req.SenderSecondAgentName + "%"
	}
	if req.SenderThirdAgentName != "" {
		condition["sender_third_agent.username LIKE"] = "%" + req.SenderThirdAgentName + "%"
	}

	// 发送方Telegram查询条件（与返回字段一致）
	if req.SenderTelegramId != "" {
		condition["sender_uba.telegram_id LIKE"] = "%" + req.SenderTelegramId + "%"
	}
	if req.SenderTelegramUsername != "" {
		condition["sender_uba.telegram_username LIKE"] = "%" + req.SenderTelegramUsername + "%"
	}
	if req.SenderFirstName != "" {
		condition["sender_uba.first_name LIKE"] = "%" + req.SenderFirstName + "%"
	}

	// 处理导出
	if req.Export {
		// 直接调用DAO方法获取完整信息
		list, _, err := dao.RedPacketClaims.ListAdminRedPacketClaimsWithFullInfo(ctx, 1, 10000, condition)
		if err != nil {
			return nil, gerror.Wrap(err, "导出查询红包领取记录失败")
		}

		// 转换为导出格式
		exportData := make([]interface{}, len(list))
		for i, item := range list {
			createdAt := ""
			if item.CreatedAt != nil {
				createdAt = item.CreatedAt.String()
			}

			exportData[i] = struct {
				ClaimId         int64  `json:"claimId" excel:"领取记录ID"`
				RedPacketId     int64  `json:"redPacketId" excel:"红包ID"`
				RedPacketUuid   string `json:"redPacketUuid" excel:"红包UUID"`
				ClaimerUserId   int64  `json:"claimerUserId" excel:"领取者ID"`
				ClaimerUsername string `json:"claimerUsername" excel:"领取者用户名"`
				ClaimedAmount   string `json:"claimedAmount" excel:"领取金额"`
				TokenSymbol     string `json:"tokenSymbol" excel:"代币符号"`
				RedPacketType   string `json:"redPacketType" excel:"红包类型"`
				RedPacketMemo   string `json:"redPacketMemo" excel:"红包留言"`
				CreatedAt       string `json:"createdAt" excel:"领取时间"`
				TransactionId   int64  `json:"transactionId" excel:"交易ID"`
				SenderUserId    int64  `json:"senderUserId" excel:"发送方ID"`
				SenderUsername  string `json:"senderUsername" excel:"发送方用户名"`
				// 领取方代理和Telegram信息
				FirstAgentName   string `json:"firstAgentName" excel:"领取方一级代理"`
				SecondAgentName  string `json:"secondAgentName" excel:"领取方二级代理"`
				ThirdAgentName   string `json:"thirdAgentName" excel:"领取方三级代理"`
				TelegramId       string `json:"telegramId" excel:"领取方Telegram ID"`
				TelegramUsername string `json:"telegramUsername" excel:"领取方Telegram用户名"`
				FirstName        string `json:"firstName" excel:"领取方真实姓名"`
				// 发送方代理和Telegram信息
				SenderFirstAgentName   string `json:"senderFirstAgentName" excel:"发送方一级代理"`
				SenderSecondAgentName  string `json:"senderSecondAgentName" excel:"发送方二级代理"`
				SenderThirdAgentName   string `json:"senderThirdAgentName" excel:"发送方三级代理"`
				SenderTelegramId       string `json:"senderTelegramId" excel:"发送方Telegram ID"`
				SenderTelegramUsername string `json:"senderTelegramUsername" excel:"发送方Telegram用户名"`
				SenderFirstName        string `json:"senderFirstName" excel:"发送方真实姓名"`
			}{
				ClaimId:                item.ClaimId,
				RedPacketId:            item.RedPacketId,
				RedPacketUuid:          item.RedPacketUuid,
				ClaimerUserId:          item.ClaimerUserId,
				ClaimerUsername:        item.ClaimerUsername,
				ClaimedAmount:          item.ClaimedAmount.String(),
				TokenSymbol:            item.TokenSymbol,
				RedPacketType:          item.RedPacketType,
				RedPacketMemo:          item.RedPacketMemo,
				CreatedAt:              createdAt,
				TransactionId:          item.TransactionId,
				SenderUserId:           item.SenderUserId,
				SenderUsername:         item.SenderUsername,
				FirstAgentName:         item.FirstAgentName,
				SecondAgentName:        item.SecondAgentName,
				ThirdAgentName:         item.ThirdAgentName,
				TelegramId:             item.TelegramId,
				TelegramUsername:       item.TelegramUsername,
				FirstName:              item.FirstName,
				SenderFirstAgentName:   item.SenderFirstAgentName,
				SenderSecondAgentName:  item.SenderSecondAgentName,
				SenderThirdAgentName:   item.SenderThirdAgentName,
				SenderTelegramId:       item.SenderTelegramId,
				SenderTelegramUsername: item.SenderTelegramUsername,
				SenderFirstName:        item.SenderFirstName,
			}
		}

		// 定义Excel表头
		excelTags := []string{} // excel.ExportByStructs 会自动从 struct tag 读取

		// 调用Excel导出工具
		return res, excel.ExportByStructs(ctx, excelTags, exportData, "红包领取记录", "红包领取记录列表")
	}

	// 直接调用DAO方法进行分页查询
	list, total, err := dao.RedPacketClaims.ListAdminRedPacketClaimsWithFullInfo(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, gerror.Wrap(err, "查询红包领取记录列表失败")
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = int(math.Ceil(float64(total) / float64(req.PageSize)))

	// 转换DAO结果到API响应格式
	res.List = make([]*v1.RedPacketClaimAdminInfoType, len(list))
	for i, item := range list {
		res.List[i] = &v1.RedPacketClaimAdminInfoType{
			// 嵌入的 RedPacketClaims 字段
			RedPacketClaims: entity.RedPacketClaims{
				ClaimId:          item.ClaimId,
				RedPacketId:      item.RedPacketId,
				ClaimerUserId:    item.ClaimerUserId,
				Amount:           item.ClaimedAmount,
				TransactionId:    item.TransactionId,
				ClaimedAt:        item.CreatedAt,
				Status:           item.Status,
				Symbol:           item.Symbol,
				SenderUserId:     item.ClaimSenderUserId,
				ReceiverUserId:   item.ClaimReceiverUserId,
				SenderUsername:   item.ClaimSenderUsername,
				ReceiverUsername: item.ClaimReceiverUsername,
			},
			// 其他字段
			ClaimerUsername: item.ClaimerUsername,
			RedPacketMemo:   item.RedPacketMemo,
			TokenSymbol:     item.TokenSymbol,
			TokenId:         item.TokenId,
			TokenName:       item.TokenName,
			RedPacketType:   item.RedPacketType,
			CreatedAt:       item.CreatedAt,
			// 领取方代理和Telegram信息
			FirstAgentName:   item.FirstAgentName,
			SecondAgentName:  item.SecondAgentName,
			ThirdAgentName:   item.ThirdAgentName,
			TelegramId:       item.TelegramId,
			TelegramUsername: item.TelegramUsername,
			FirstName:        item.FirstName,
			// 发送方代理和Telegram信息
			SenderFirstAgentName:   item.SenderFirstAgentName,
			SenderSecondAgentName:  item.SenderSecondAgentName,
			SenderThirdAgentName:   item.SenderThirdAgentName,
			SenderTelegramId:       item.SenderTelegramId,
			SenderTelegramUsername: item.SenderTelegramUsername,
			SenderFirstName:        item.SenderFirstName,
			// 红包UUID
			RedPacketUuid: item.RedPacketUuid,
		}
	}

	return res, nil
}
