package v1

import (
	"encoding/json"

	"github.com/gogf/gf/v2/errors/gerror"
)

// serializeRecoveryCodes 将恢复码数组序列化为 JSON 字符串
func serializeRecoveryCodes(codes []string) (string, error) {
	if len(codes) == 0 {
		return "[]", nil
	}
	data, err := json.Marshal(codes)
	if err != nil {
		return "", gerror.Wrap(err, "序列化恢复码失败")
	}
	return string(data), nil
}

// deserializeRecoveryCodes 将 JSON 字符串反序列化为恢复码数组
func deserializeRecoveryCodes(jsonStr string) ([]string, error) {
	if jsonStr == "" || jsonStr == "[]" {
		return []string{}, nil
	}
	var codes []string
	err := json.Unmarshal([]byte(jsonStr), &codes)
	if err != nil {
		return nil, gerror.Wrap(err, "反序列化恢复码失败")
	}
	return codes, nil
}
