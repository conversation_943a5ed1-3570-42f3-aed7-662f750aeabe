package v1

import (
	"context"
	"strings"

	"github.com/gogf/gf/v2/frame/g"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"

	// "admin-api/internal/dao" // Removed direct DAO dependency
	"admin-api/utility/excel"

	"github.com/gogf/gf/v2/errors/gerror" // Re-add gerror import
)

// GetReferralCommissionList 获取佣金记录列表
func (s *sSystemLogic) GetReferralCommissionList(ctx context.Context, req *v1.GetReferralCommissionListReq) (res *v1.GetReferralCommissionListRes, err error) { // Changed receiver name 'l' to 's'
	// 初始化返回结果，避免空指针
	// Initialize response struct directly
	res = &v1.GetReferralCommissionListRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
			TotalSize:   0,
			TotalPage:   0,
		},
		Data: make([]*v1.ReferralCommissionListItem, 0),
	}

	// 构建查询条件
	condition := g.Map{}

	// 按用户ID筛选
	if req.ReferrerId > 0 {
		condition["referrer_id"] = req.ReferrerId
	}

	// 按被推荐人ID筛选
	if req.InviteeId > 0 {
		condition["invitee_id"] = req.InviteeId
	}

	// 按状态筛选
	if req.Status != "" {
		condition["status"] = req.Status
	}

	// 按代币ID筛选
	if req.TokenId > 0 {
		condition["token_id"] = req.TokenId
	}

	// 按日期范围筛选
	if req.DateRange != "" {
		dateRangeArr := strings.Split(req.DateRange, ",")
		if len(dateRangeArr) == 2 {
			startTime := dateRangeArr[0] + " 00:00:00"
			endTime := dateRangeArr[1] + " 23:59:59"
			// Use string literals for column names in range conditions
			condition["created_at >="] = startTime
			condition["created_at <="] = endTime
		}
	}

	// 处理代理层级搜索
	if req.FirstAgentName != "" {
		condition["referrer_first_agent.username LIKE"] = "%" + req.FirstAgentName + "%"
	}
	if req.SecondAgentName != "" {
		condition["referrer_second_agent.username LIKE"] = "%" + req.SecondAgentName + "%"
	}
	if req.ThirdAgentName != "" {
		condition["referrer_third_agent.username LIKE"] = "%" + req.ThirdAgentName + "%"
	}

	// 处理Telegram信息搜索
	if req.TelegramId != "" {
		condition["referrer_uba.telegram_id LIKE"] = "%" + req.TelegramId + "%"
	}
	if req.TelegramUsername != "" {
		condition["referrer_uba.telegram_username LIKE"] = "%" + req.TelegramUsername + "%"
	}
	if req.FirstName != "" {
		condition["referrer_uba.first_name LIKE"] = "%" + req.FirstName + "%"
	}

	// 如果是导出，查询所有数据
	if req.Export == 1 {
		// 导出时不进行分页限制
		// Use repository to get the list for export
		list, _, err := s.referralCommissionRepo.List(ctx, 1, 9999999, condition) // Use 's' receiver
		if err != nil {
			return nil, gerror.Wrap(err, "导出查询佣金记录失败")
		}

		// 导出Excel
		// 定义Excel表头
		excelTags := []string{
			"佣金ID",
			"交易ID",
			"推荐人ID",
			"被推荐人ID",
			"推荐层级",
			"佣金金额",
			"佣金比率",
			"代币ID",
			"状态",
			"创建时间",
		}

		// 准备导出数据
		exportData := make([]interface{}, len(list))
		for i, v := range list {
			// 状态显示转换
			status := "未知"
			switch v.Status {
			case "pending":
				status = "待发放"
			case "paid":
				status = "已发放"
			case "cancelled":
				status = "已取消"
			}

			exportData[i] = struct {
				CommissionId     int64   `json:"commission_id"`
				TransactionId    int64   `json:"transaction_id"`
				ReferrerId       int64   `json:"referrer_id"`
				InviteeId        int64   `json:"invitee_id"`
				Level            int     `json:"level"`
				CommissionAmount float64 `json:"commission_amount"`
				CommissionRate   float64 `json:"commission_rate"`
				TokenId          int     `json:"token_id"`
				Status           string  `json:"status"`
				CreatedAt        string  `json:"created_at"`
			}{
				CommissionId:     v.ReferralCommissions.CommissionId,
				TransactionId:    v.ReferralCommissions.TransactionId,
				ReferrerId:       v.ReferralCommissions.ReferrerId,
				InviteeId:        v.ReferralCommissions.InviteeId,
				Level:            v.ReferralCommissions.Level,
				CommissionAmount: v.ReferralCommissions.CommissionAmount.InexactFloat64(),
				CommissionRate:   v.ReferralCommissions.CommissionRate.InexactFloat64(),
				TokenId:          v.ReferralCommissions.TokenId,
				Status:           status,
				CreatedAt:        v.ReferralCommissions.CreatedAt.Format("Y-m-d H:i:s"),
			}
		}

		// 调用Excel导出工具
		return res, excel.ExportByStructs(ctx, excelTags, exportData, "佣金记录", "佣金记录列表")
	}

	// 不是导出，进行正常分页查询
	// 分页查询
	// Use repository to get the paginated list
	list, total, err := s.referralCommissionRepo.List(ctx, req.Page, req.PageSize, condition) // Use 's' receiver
	if err != nil {
		return nil, gerror.Wrap(err, "查询佣金记录列表失败")
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = (total + req.PageSize - 1) / req.PageSize

	// 转换为响应结构
	// Convert ReferralCommissionWithUserInfoDTO to v1.ReferralCommissionListItem
	items := make([]*v1.ReferralCommissionListItem, len(list))
	for i, commission := range list {
		items[i] = &v1.ReferralCommissionListItem{
			ReferralCommissions: commission.ReferralCommissions,
			// 用户信息
			ReferrerUsername: commission.ReferrerUsername,
			ReferrerAccount:  commission.ReferrerAccount,
			InviteeUsername:  commission.InviteeUsername,
			InviteeAccount:   commission.InviteeAccount,
			TokenSymbol:      commission.TokenSymbol,
			// 推荐人代理信息
			ReferrerFirstAgentName:  commission.ReferrerFirstAgentName,
			ReferrerSecondAgentName: commission.ReferrerSecondAgentName,
			ReferrerThirdAgentName:  commission.ReferrerThirdAgentName,
			// 推荐人Telegram信息
			ReferrerTelegramId:       commission.ReferrerTelegramId,
			ReferrerTelegramUsername: commission.ReferrerTelegramUsername,
			ReferrerFirstName:        commission.ReferrerFirstName,
			// 被推荐人代理信息
			InviteeFirstAgentName:  commission.InviteeFirstAgentName,
			InviteeSecondAgentName: commission.InviteeSecondAgentName,
			InviteeThirdAgentName:  commission.InviteeThirdAgentName,
			// 被推荐人Telegram信息
			InviteeTelegramId:       commission.InviteeTelegramId,
			InviteeTelegramUsername: commission.InviteeTelegramUsername,
			InviteeFirstName:        commission.InviteeFirstName,
		}
	}
	res.Data = items

	return res, nil
}
