package v1

import (
	"context"
	"fmt"
	"strings"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/utility"
	"admin-api/utility/csv"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// GetMyTransactions 获取我的资金记录列表
func (s *sSystemLogic) GetMyTransactions(ctx context.Context, req *v1.GetMyTransactionsReq) (res *v1.GetMyTransactionsRes, err error) {
	res = &v1.GetMyTransactionsRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*v1.MerchantTransactionInfoType, 0),
	}

	// 构建查询条件
	condition := g.Map{}

	// 如果提供了商户ID，则筛选特定商户
	if req.MerchantId != nil && *req.MerchantId > 0 {
		condition["merchant_id"] = *req.MerchantId
	}

	// Apply additional filters
	s.applyTransactionFilters(condition, req)

	// 如果是导出
	if req.Export {
		// 导出时不分页，查询所有符合条件的数据
		list, _, err := dao.MerchantTransactions.List(ctx, 1, 9999999, condition)
		if err != nil {
			return nil, gerror.Wrap(err, "导出查询资金记录失败")
		}

		if len(list) == 0 {
			return res, nil // 没有数据可导出
		}

		// 获取商户ID列表以批量查询商户名称
		merchantIds := make([]uint64, 0)
		merchantIdMap := make(map[uint64]bool)
		for _, transaction := range list {
			merchantId := uint64(transaction.MerchantId)
			if !merchantIdMap[merchantId] {
				merchantIds = append(merchantIds, merchantId)
				merchantIdMap[merchantId] = true
			}
		}

		// 批量查询商户信息
		merchantNameMap := make(map[uint64]string)
		if len(merchantIds) > 0 {
			var merchants []entity.Merchants
			err = dao.Merchants.Ctx(ctx).WhereIn("merchant_id", merchantIds).Scan(&merchants)
			if err != nil {
				g.Log().Errorf(ctx, "查询商户信息失败: %v", err)
			} else {
				for _, merchant := range merchants {
					merchantNameMap[merchant.MerchantId] = merchant.MerchantName
				}
			}
		}

		// 准备导出数据
		exportData := make([]interface{}, 0, len(list))
		for _, transaction := range list {
			// 准备导出结构体
			exportItem := struct {
				TransactionId      uint64 `json:"transactionId" excel:"交易ID"`
				MerchantId         uint64 `json:"merchantId" excel:"商户ID"`
				MerchantName       string `json:"merchantName" excel:"商户名称"`
				TransactionChannel string `json:"transactionChannel" excel:"交易方式"`
				Type               string `json:"type" excel:"交易类型"`
				TokenSymbol        string `json:"tokenSymbol" excel:"币种"`
				Direction          string `json:"direction" excel:"资金方向"`
				WalletType         string `json:"walletType" excel:"钱包类型"`
				Amount        string `json:"amount" excel:"交易金额"`
				BalanceBefore string `json:"balanceBefore" excel:"交易前余额"`
				BalanceAfter  string `json:"balanceAfter" excel:"交易后余额"`
				Status        string `json:"status" excel:"交易状态"`
				Memo          string `json:"memo" excel:"交易备注"`
				BusinessId    string `json:"businessId" excel:"业务ID"`
				RequestSource string `json:"requestSource" excel:"请求来源"`
				FeeAmount     string `json:"feeAmount" excel:"手续费"`
				CreatedAt     string `json:"createdAt" excel:"创建时间"`
				ProcessedAt   string `json:"processedAt" excel:"处理时间"`
			}{
				TransactionId:      transaction.TransactionId,
				MerchantId:         uint64(transaction.MerchantId),
				MerchantName:       merchantNameMap[uint64(transaction.MerchantId)],
				TransactionChannel: s.getTransactionChannelText(transaction.TransactionChannel),
				Type:               s.getTransactionTypeText(transaction.Type),
				TokenSymbol:        transaction.Symbol,
				Direction:          s.getDirectionText(transaction.Direction),
				WalletType:         s.getWalletTypeText(transaction.WalletType),
				Amount:        transaction.Amount.String(),
				BalanceBefore: transaction.BalanceBefore.String(),
				BalanceAfter:  transaction.BalanceAfter.String(),
				Status:        s.getTransactionStatusText(transaction.Status),
				Memo:          transaction.Memo,
				BusinessId:    transaction.BusinessId,
				RequestSource: transaction.RequestSource,
				FeeAmount:     transaction.FeeAmount.String(),
				CreatedAt:     transaction.CreatedAt.Format("Y-m-d H:i:s"),
			}
			if transaction.ProcessedAt != nil {
				exportItem.ProcessedAt = transaction.ProcessedAt.Format("Y-m-d H:i:s")
			}
			exportData = append(exportData, exportItem)
		}

		// 定义Excel表头
		excelTags := []string{
			"交易ID", "商户ID", "商户名称", "交易方式", "交易类型", "币种", "资金方向", "钱包类型", "交易金额",
			"交易前余额", "交易后余额", "交易状态", "交易备注", "业务ID",
			"请求来源", "手续费", "创建时间", "处理时间",
		}

		// 调用CSV导出工具
		fileName := "merchant_transactions"
		if req.MerchantId != nil && *req.MerchantId > 0 {
			fileName = fmt.Sprintf("merchant_transactions_%d", *req.MerchantId)
		}
		return res, csv.ExportByStructs(ctx, excelTags, exportData, fileName, "资金记录")
	}

	// 正常查询
	list, total, err := dao.MerchantTransactions.List(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, gerror.Wrap(err, "查询资金记录失败")
	}

	// 设置分页信息
	res.Page.TotalSize = int(total)
	res.Page.TotalPage = int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	// 获取商户ID列表以批量查询商户名称
	merchantIds := make([]uint64, 0)
	merchantIdMap := make(map[uint64]bool)
	for _, transaction := range list {
		merchantId := uint64(transaction.MerchantId)
		if !merchantIdMap[merchantId] {
			merchantIds = append(merchantIds, merchantId)
			merchantIdMap[merchantId] = true
		}
	}

	// 批量查询商户信息
	merchantNameMap := make(map[uint64]string)
	if len(merchantIds) > 0 {
		var merchants []entity.Merchants
		err = dao.Merchants.Ctx(ctx).WhereIn("merchant_id", merchantIds).Scan(&merchants)
		if err != nil {
			g.Log().Errorf(ctx, "查询商户信息失败: %v", err)
		} else {
			for _, merchant := range merchants {
				merchantNameMap[merchant.MerchantId] = merchant.MerchantName
			}
		}
	}

	// 转换数据格式
	for _, transaction := range list {
		transactionInfo := &v1.MerchantTransactionInfoType{
			TransactionId:          transaction.TransactionId,
			MerchantId:             uint64(transaction.MerchantId),
			MerchantName:           merchantNameMap[uint64(transaction.MerchantId)],
			TransactionChannel:     transaction.TransactionChannel,
			TransactionChannelText: s.getTransactionChannelText(transaction.TransactionChannel),
			Type:                   transaction.Type,
			TypeText:               s.getTransactionTypeText(transaction.Type),
			WalletType:             transaction.WalletType,
			WalletTypeText:         s.getWalletTypeText(transaction.WalletType),
			Direction:              transaction.Direction,
			DirectionText:          s.getDirectionText(transaction.Direction),
			Amount:            transaction.Amount,
			BalanceBefore:     transaction.BalanceBefore,
			BalanceAfter:      transaction.BalanceAfter,
			Symbol:            transaction.Symbol,
			Status:            transaction.Status,
			StatusText:        s.getTransactionStatusText(transaction.Status),
			Memo:              transaction.Memo,
			CreatedAt:         transaction.CreatedAt,
			ProcessedAt:       transaction.ProcessedAt,
			RelatedEntityType: transaction.RelatedEntityType,
			RelatedEntityId:   transaction.RelatedEntityId,
			BusinessId:        transaction.BusinessId,
			RequestSource:     transaction.RequestSource,
			RequestReference:  transaction.RequestReference,
			RequestAmount:     transaction.RequestAmount,
			FeeAmount:         transaction.FeeAmount,
		}
		res.Data = append(res.Data, transactionInfo)
	}

	return res, nil
}

// GetMyTransactionDetail 获取我的资金记录详情
func (s *sSystemLogic) GetMyTransactionDetail(ctx context.Context, req *v1.GetMyTransactionDetailReq) (res *v1.GetMyTransactionDetailRes, err error) {
	// 查询交易记录
	var transaction *entity.MerchantTransactions
	err = dao.MerchantTransactions.Ctx(ctx).Where("transaction_id", req.TransactionId).Scan(&transaction)
	if err != nil {
		return nil, gerror.Wrap(err, "查询资金记录详情失败")
	}

	if transaction == nil {
		return nil, gerror.NewCode(codes.CodeNotFound, "资金记录不存在")
	}

	// 构建响应数据
	res = &v1.GetMyTransactionDetailRes{
		Data: &v1.MerchantTransactionDetailType{
			TransactionId:          transaction.TransactionId,
			MerchantId:             transaction.MerchantId,
			TokenId:                transaction.TokenId,
			TransactionChannel:     transaction.TransactionChannel,
			TransactionChannelText: s.getTransactionChannelText(transaction.TransactionChannel),
			Type:                   transaction.Type,
			TypeText:               s.getTransactionTypeText(transaction.Type),
			WalletType:             transaction.WalletType,
			WalletTypeText:         s.getWalletTypeText(transaction.WalletType),
			Direction:              transaction.Direction,
			DirectionText:          s.getDirectionText(transaction.Direction),
			Amount:               transaction.Amount,
			BalanceBefore:        transaction.BalanceBefore,
			BalanceAfter:         transaction.BalanceAfter,
			Symbol:               transaction.Symbol,
			Status:               transaction.Status,
			StatusText:           s.getTransactionStatusText(transaction.Status),
			Memo:                 transaction.Memo,
			RelatedTransactionId: transaction.RelatedTransactionId,
			RelatedEntityId:      transaction.RelatedEntityId,
			RelatedEntityType:    transaction.RelatedEntityType,
			BusinessId:           transaction.BusinessId,
			RequestAmount:        transaction.RequestAmount,
			RequestReference:     transaction.RequestReference,
			RequestSource:        transaction.RequestSource,
			RequestIp:            transaction.RequestIp,
			RequestUserAgent:     transaction.RequestUserAgent,
			FeeAmount:            transaction.FeeAmount,
			FeeType:              transaction.FeeType,
			ExchangeRate:         transaction.ExchangeRate,
			TargetUserId:         transaction.TargetUserId,
			TargetUsername:       transaction.TargetUsername,
			CreatedAt:            transaction.CreatedAt,
			UpdatedAt:            transaction.UpdatedAt,
			RequestTimestamp:     transaction.RequestTimestamp,
			ProcessedAt:          transaction.ProcessedAt,
		},
	}

	return res, nil
}

// GetMyTransactionStats 获取我的资金统计信息
func (s *sSystemLogic) GetMyTransactionStats(ctx context.Context, req *v1.GetMyTransactionStatsReq) (res *v1.GetMyTransactionStatsRes, err error) {
	// 如果没有指定商户ID，则统计所有商户的数据
	var merchantId uint64
	if req.MerchantId != nil && *req.MerchantId > 0 {
		merchantId = *req.MerchantId
	}

	// 获取基础统计信息
	stats, err := dao.MerchantTransactions.GetStatsByMerchantId(ctx, merchantId, req.TokenSymbol, req.DateRange)
	if err != nil {
		return nil, gerror.Wrap(err, "获取资金统计失败")
	}

	// 计算成功率
	var successRate string = "0%"
	if stats.TotalTransactions > 0 {
		rate := float64(stats.SuccessTransactions) / float64(stats.TotalTransactions) * 100
		successRate = fmt.Sprintf("%.2f%%", rate)
	}

	// 计算净变动
	totalAmountIn, _ := decimal.NewFromString(stats.TotalAmountIn)
	totalAmountOut, _ := decimal.NewFromString(stats.TotalAmountOut)
	netAmount := totalAmountIn.Sub(totalAmountOut)
	totalFeeAmount, _ := decimal.NewFromString(stats.TotalFeeAmount)

	// 构建统计数据
	statsData := &v1.TransactionStatsType{
		TotalTransactions:   stats.TotalTransactions,
		SuccessTransactions: stats.SuccessTransactions,
		FailedTransactions:  stats.FailedTransactions,
		SuccessRate:         successRate,
		TotalAmountIn:       totalAmountIn,
		TotalAmountOut:      totalAmountOut,
		NetAmount:           netAmount,
		TotalFeeAmount:      totalFeeAmount,
		TypeStats:           make(map[string]*v1.TransactionTypeStatsType),
		DateRange:           req.DateRange,
		LastUpdated:         nil, // 可以设置为当前时间
	}

	// 获取按类型统计
	typeStats, err := dao.MerchantTransactions.GetTypeStatsByMerchantId(ctx, merchantId, req.TokenSymbol)
	if err == nil {
		for _, typeStat := range typeStats {
			typeKey := typeStat["type"].(string)
			count := typeStat["count"].(int64)
			successCount := typeStat["success_count"].(int64)
			failedCount := typeStat["failed_count"].(int64)
			totalAmount, _ := decimal.NewFromString(typeStat["total_amount"].(string))

			statsData.TypeStats[typeKey] = &v1.TransactionTypeStatsType{
				Type:         typeKey,
				TypeText:     s.getTransactionTypeText(typeKey),
				Count:        count,
				TotalAmount:  totalAmount,
				SuccessCount: successCount,
				FailedCount:  failedCount,
			}
		}
	}

	// 如果查询所有币种，获取按币种统计
	if req.TokenSymbol == "" {
		tokenStats, err := dao.MerchantTransactions.GetTokenStatsByMerchantId(ctx, merchantId)
		if err == nil {
			statsData.TokenStats = make(map[string]*v1.TransactionTokenStatsType)
			for _, tokenStat := range tokenStats {
				tokenSymbol := tokenStat["token_symbol"].(string)
				count := tokenStat["count"].(int64)
				totalAmountIn, _ := decimal.NewFromString(tokenStat["total_amount_in"].(string))
				totalAmountOut, _ := decimal.NewFromString(tokenStat["total_amount_out"].(string))
				netAmount := totalAmountIn.Sub(totalAmountOut)

				statsData.TokenStats[tokenSymbol] = &v1.TransactionTokenStatsType{
					TokenSymbol:    tokenSymbol,
					Count:          count,
					TotalAmountIn:  totalAmountIn,
					TotalAmountOut: totalAmountOut,
					NetAmount:      netAmount,
				}
			}
		}
	}

	res = &v1.GetMyTransactionStatsRes{
		Data: statsData,
	}

	return res, nil
}

// applyTransactionFilters 应用筛选条件
func (s *sSystemLogic) applyTransactionFilters(condition g.Map, req *v1.GetMyTransactionsReq) {
	// 代币符号筛选
	if req.TokenSymbol != "" {
		condition["symbol"] = strings.ToUpper(req.TokenSymbol)
	}

	// 交易类型筛选
	if req.Type != "" {
		condition["type"] = req.Type
	}

	// 交易方式筛选
	if req.TransactionChannel != "" {
		condition["transaction_channel"] = req.TransactionChannel
	}

	// 资金方向筛选
	if req.Direction != "" {
		condition["direction"] = req.Direction
	}

	// 钱包类型筛选
	if req.WalletType != "" {
		condition["wallet_type"] = req.WalletType
	}

	// 交易状态筛选
	if req.Status != nil {
		condition["status"] = *req.Status
	}

	// 业务ID搜索
	if req.BusinessId != "" {
		condition["business_id LIKE ?"] = "%" + req.BusinessId + "%"
	}

	// 关联实体类型筛选
	if req.RelatedEntityType != "" {
		condition["related_entity_type"] = req.RelatedEntityType
	}

	// 金额范围筛选
	if req.AmountMin != "" {
		if minAmount, parseErr := decimal.NewFromString(req.AmountMin); parseErr == nil {
			condition["amount >= ?"] = minAmount
		}
	}
	if req.AmountMax != "" {
		if maxAmount, parseErr := decimal.NewFromString(req.AmountMax); parseErr == nil {
			condition["amount <= ?"] = maxAmount
		}
	}

	// 使用灵活的日期范围处理，支持dateRange和createdAt数组两种格式
	utility.AddFlexibleDateRangeCondition(condition, req.DateRange, req.CreatedAt)
}

// getTransactionTypeText 获取交易类型文本描述
func (s *sSystemLogic) getTransactionTypeText(transactionType string) string {
	switch transactionType {
	case "deposit":
		return "充值"
	case "withdrawal":
		return "提现"
	case "transfer":
		return "转账"
	case "red_packet":
		return "红包"
	case "payment":
		return "支付"
	case "commission":
		return "佣金"
	case "system_adjust":
		return "系统调账"
	case "freeze":
		return "冻结"
	case "unfreeze":
		return "解冻"
	case "withdraw_cancel":
		return "提现撤销"
	case "withdraw_prepare":
		return "提现准备"
	case "withdraw_complete":
		return "提现完成"
	default:
		return "其他"
	}
}

// getWalletTypeText 获取钱包类型文本描述
func (s *sSystemLogic) getWalletTypeText(walletType string) string {
	switch walletType {
	case "available":
		return "可用余额"
	case "frozen":
		return "冻结余额"
	default:
		return "未知类型"
	}
}

// getDirectionText 获取资金方向文本描述
func (s *sSystemLogic) getDirectionText(direction string) string {
	switch direction {
	case "in":
		return "入账"
	case "out":
		return "出账"
	default:
		return "未知方向"
	}
}

// getTransactionChannelText 获取交易方式文本描述
func (s *sSystemLogic) getTransactionChannelText(channel string) string {
	switch channel {
	case "auth":
		return "授权支付"
	case "okpay":
		return "OKPay渠道"
	case "address":
		return "地址渠道"
	default:
		return "未知方式"
	}
}

// getTransactionStatusText 获取交易状态文本描述
func (s *sSystemLogic) getTransactionStatusText(status uint) string {
	switch status {
	case 1:
		return "成功"
	case 0:
		return "失败"
	default:
		return "未知状态"
	}
}

// getCurrentMerchantId 获取当前商户ID
func (s *sSystemLogic) getCurrentMerchantId(ctx context.Context) uint64 {
	if r := g.RequestFromCtx(ctx); r != nil {
		return r.GetCtxVar("merchantId").Uint64()
	}
	return 0
}
