package login_log

import (
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/login_log"
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

type loginLogRepository struct{}

// NewLoginLogRepository creates and returns a new instance of ILoginLogRepository.
func NewLoginLogRepository() login_log.ILoginLogRepository {
	return &loginLogRepository{}
}

// Create inserts a new login log entry into the database.
func (r *loginLogRepository) Create(ctx context.Context, log *entity.LoginLog) error {
	// Convert entity to do
	var logDo *do.LoginLog
	if err := gconv.Struct(log, &logDo); err != nil {
		return err
	}
	_, err := dao.LoginLog.Ctx(ctx).Data(logDo).Insert()
	return err
}

// List retrieves a paginated list of login logs based on the provided conditions.
func (r *loginLogRepository) List(ctx context.Context, page, pageSize int, condition g.Map) (list []*login_log.LoginLogWithUserInfo, total int, err error) {
	// Build base query with joins to get user information
	m := dao.LoginLog.Ctx(ctx).
		LeftJoin("users u", "login_log.member_id = u.id").
		LeftJoin("users first_agent", "u.first_id = first_agent.id").
		LeftJoin("users second_agent", "u.second_id = second_agent.id").
		LeftJoin("users third_agent", "u.third_id = third_agent.id").
		LeftJoin("user_backup_accounts uba", "u.id = uba.user_id AND uba.is_master = 1").
		Where(condition)

	total, err = m.Count()
	if err != nil || total == 0 {
		return nil, 0, err
	}

	// Select fields including user information
	m = m.Fields(
		"login_log.*",
		"u.account as user_account",
		"first_agent.username as first_agent_name",
		"second_agent.username as second_agent_name",
		"third_agent.username as third_agent_name",
		"uba.telegram_id as telegram_id",
		"uba.telegram_username as telegram_username",
		"uba.first_name as first_name",
	)

	var results []gdb.Record
	err = m.Page(page, pageSize).Order("login_log.id DESC").Scan(&results)
	if err != nil {
		return nil, 0, err
	}

	// Convert results to LoginLogWithUserInfo
	list = make([]*login_log.LoginLogWithUserInfo, 0, len(results))
	for _, record := range results {
		loginLogEntity := &entity.LoginLog{}
		err = gconv.Struct(record, loginLogEntity)
		if err != nil {
			return nil, 0, err
		}

		// Create LoginLogWithUserInfo with additional fields
		loginLogWithUserInfo := &login_log.LoginLogWithUserInfo{
			LoginLog:         loginLogEntity,
			UserAccount:      record["user_account"].String(),
			FirstAgentName:   record["first_agent_name"].String(),
			SecondAgentName:  record["second_agent_name"].String(),
			ThirdAgentName:   record["third_agent_name"].String(),
			TelegramId:       record["telegram_id"].String(),
			TelegramUsername: record["telegram_username"].String(),
			FirstName:        record["first_name"].String(),
		}

		list = append(list, loginLogWithUserInfo)
	}

	return list, total, err
}

// GetByID retrieves a single login log entry by its ID.
func (r *loginLogRepository) GetByID(ctx context.Context, id int64) (*entity.LoginLog, error) {
	var logEntity *entity.LoginLog
	err := dao.LoginLog.Ctx(ctx).Where(dao.LoginLog.Columns().Id, id).Scan(&logEntity)
	return logEntity, err
}
