package user_recharge

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/user_recharge"
	"admin-api/internal/utility"
	"context"
	"database/sql"
	"fmt"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// userRechargeRepository implements the IUserRechargeRepository interface.
type userRechargeRepository struct{}

// NewUserRechargeRepository creates and returns a new instance of userRechargeRepository.
func NewUserRechargeRepository() user_recharge.IUserRechargeRepository {
	return &userRechargeRepository{}
}

// ListWithAgentInfo retrieves a paginated list of user recharges with agent and telegram info.
func (r *userRechargeRepository) ListWithAgentInfo(ctx context.Context, req *v1.ListUserRechargesReq) (list []*v1.UserRechargeListItem, total int, err error) {
	// Initialize empty list to avoid nil response
	list = make([]*v1.UserRechargeListItem, 0)

	// Build query model
	model := dao.UserRecharges.Ctx(ctx).As("ur").
		LeftJoin(dao.Users.Table()+" u", "ur."+dao.UserRecharges.Columns().UserId+" = u."+dao.Users.Columns().Id)

	// 添加代理和telegram表的关联查询
	model = utility.AddAgentAndTelegramJoins(model, dao.Users.Table(), dao.UserBackupAccounts.Table())

	// Build conditions - 复制原有条件逻辑
	if req.UserId > 0 {
		model = model.Where("ur."+dao.UserRecharges.Columns().UserId, req.UserId)
	}
	if req.Account != "" {
		model = model.Where("u."+dao.Users.Columns().Account, req.Account)
	}
	if req.TokenId > 0 {
		model = model.Where("ur."+dao.UserRecharges.Columns().TokenId, req.TokenId)
	}
	if req.Symbol != "" {
		model = model.WhereLike("ur."+dao.UserRecharges.Columns().Name, "%"+req.Symbol+"%")
	}
	if req.Chan != "" {
		model = model.Where("ur."+dao.UserRecharges.Columns().Chan, req.Chan)
	}
	if req.FromAddress != "" {
		model = model.Where("ur."+dao.UserRecharges.Columns().FromAddress, req.FromAddress)
	}
	if req.ToAddress != "" {
		model = model.Where("ur."+dao.UserRecharges.Columns().ToAddress, req.ToAddress)
	}
	if req.TxHash != "" {
		model = model.Where("ur."+dao.UserRecharges.Columns().TxHash, req.TxHash)
	}
	if req.State > 0 {
		model = model.Where("ur."+dao.UserRecharges.Columns().State, req.State)
	}

	// Handle date range - 使用统一的DateRange处理工具
	condition := g.Map{}
	utility.AddDateRangeCondition(condition, req.DateRange, "ur.created_at")
	for key, value := range condition {
		model = model.Where(key, value)
	}

	// 新增：三级代理模糊查询
	model = utility.AddAgentSearchConditions(model, req.FirstAgentName, req.SecondAgentName, req.ThirdAgentName)

	// 新增：telegram查询条件
	model = utility.AddTelegramSearchConditions(model, req.TelegramId, req.TelegramUsername, req.FirstName)

	// Count total before pagination
	total, err = model.Count()
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询用户充值记录总数失败")
	}

	if total == 0 {
		return list, 0, nil
	}

	// Select fields for the result
	baseFields := []string{
		"ur.user_recharges_id",
		"ur.user_id",
		"ur.token_id",
		"ur.name",
		"ur.chan",
		"ur.token_contract_address",
		"ur.from_address",
		"ur.to_address",
		"ur.tx_hash",
		"ur.amount",
		"ur.state",
		"ur.confirmations",
		"ur.error",
		"ur.notification_sent",
		"ur.notification_sent_at",
		"ur.failure_reason",
		"ur.created_at",
		"ur.completed_at",
		"ur.updated_at",
		"u.account",
	}

	// 添加代理和telegram字段
	agentAndTelegramFields := utility.GetAgentAndTelegramFields()
	allFields := append(baseFields, agentAndTelegramFields...)

	// 转换为interface{}类型
	fieldInterfaces := make([]interface{}, len(allFields))
	for i, field := range allFields {
		fieldInterfaces[i] = field
	}

	model = model.Fields(fieldInterfaces...)

	// Apply pagination if not exporting
	if req.Export == 0 {
		model = model.Page(req.Page, req.PageSize)
	}

	// Order by creation time descending
	model = model.OrderDesc("ur.created_at")

	// Execute the query
	var result []struct {
		UserRechargesId      uint        `json:"user_recharges_id"`
		UserId               uint64      `json:"user_id"`
		TokenId              uint        `json:"token_id"`
		Name                 string      `json:"name"`
		Chan                 string      `json:"chan"`
		TokenContractAddress string      `json:"token_contract_address"`
		FromAddress          string      `json:"from_address"`
		ToAddress            string      `json:"to_address"`
		TxHash               string      `json:"tx_hash"`
		Amount               string      `json:"amount"`
		State                uint        `json:"state"`
		Confirmations        uint        `json:"confirmations"`
		Error                string      `json:"error"`
		NotificationSent     uint        `json:"notification_sent"`
		NotificationSentAt   *gtime.Time `json:"notification_sent_at"`
		FailureReason        string      `json:"failure_reason"`
		CreatedAt            *gtime.Time `json:"created_at"`
		CompletedAt          *gtime.Time `json:"completed_at"`
		UpdatedAt            *gtime.Time `json:"updated_at"`
		Account              string      `json:"account"`

		// 新增：代理信息
		FirstAgentName  string `json:"first_agent_name"`
		SecondAgentName string `json:"second_agent_name"`
		ThirdAgentName  string `json:"third_agent_name"`

		// 新增：telegram信息
		TelegramId       string `json:"telegram_id"`
		TelegramUsername string `json:"telegram_username"`
		FirstName        string `json:"first_name"`
	}

	err = model.Scan(&result)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询用户充值记录列表失败")
	}

	// Convert result to response format
	for _, item := range result {
		listItem := &v1.UserRechargeListItem{
			UserRechargesId:      item.UserRechargesId,
			UserId:               item.UserId,
			Account:              item.Account,
			TokenId:              item.TokenId,
			Name:                 item.Name,
			Chan:                 item.Chan,
			TokenContractAddress: item.TokenContractAddress,
			FromAddress:          item.FromAddress,
			ToAddress:            item.ToAddress,
			TxHash:               item.TxHash,
			Amount:               item.Amount,
			State:                item.State,
			Confirmations:        item.Confirmations,
			Error:                item.Error,
			NotificationSent:     item.NotificationSent,
			NotificationSentAt:   item.NotificationSentAt,
			FailureReason:        item.FailureReason,

			// 新增：代理信息
			FirstAgentName:  item.FirstAgentName,
			SecondAgentName: item.SecondAgentName,
			ThirdAgentName:  item.ThirdAgentName,

			// 新增：telegram信息
			TelegramId:       item.TelegramId,
			TelegramUsername: item.TelegramUsername,
			FirstName:        item.FirstName,
		}

		// Format timestamps
		if item.CreatedAt != nil {
			listItem.CreatedAt = item.CreatedAt.Format("2006-01-02 15:04:05")
		}
		if item.CompletedAt != nil && !item.CompletedAt.IsZero() {
			listItem.CompletedAt = item.CompletedAt.Format("2006-01-02 15:04:05")
		}
		if item.UpdatedAt != nil {
			listItem.UpdatedAt = item.UpdatedAt.Format("2006-01-02 15:04:05")
		}

		// Add state text
		switch item.State {
		case 1:
			listItem.StateText = "待确认"
		case 2:
			listItem.StateText = "已完成"
		case 3:
			listItem.StateText = "已撤销"
		default:
			listItem.StateText = "未知"
		}

		// Calculate duration if completed
		if item.CompletedAt != nil && !item.CompletedAt.IsZero() && item.CreatedAt != nil {
			duration := item.CompletedAt.Time.Sub(item.CreatedAt.Time)
			hours := int(duration.Hours())
			minutes := int(duration.Minutes()) % 60
			if hours > 0 {
				listItem.Duration = fmt.Sprintf("%d小时%d分钟", hours, minutes)
			} else {
				listItem.Duration = fmt.Sprintf("%d分钟", minutes)
			}
		}

		// Check if confirmed
		listItem.IsConfirmed = item.Confirmations >= 6

		list = append(list, listItem)
	}

	return list, total, nil
}

// List retrieves a paginated list of user recharges based on the provided criteria (legacy method).
func (r *userRechargeRepository) List(ctx context.Context, req *v1.ListUserRechargesReq) (list []*entity.UserRecharges, total int, err error) {
	// Initialize empty list to avoid nil response
	list = make([]*entity.UserRecharges, 0)

	// Build query model
	model := dao.UserRecharges.Ctx(ctx).As("ur").
		LeftJoin(dao.Users.Table()+" u", "ur."+dao.UserRecharges.Columns().UserId+" = u."+dao.Users.Columns().Id)

	// 添加代理和telegram表的关联查询
	model = utility.AddAgentAndTelegramJoins(model, dao.Users.Table(), dao.UserBackupAccounts.Table())

	// Build conditions
	if req.UserId > 0 {
		model = model.Where("ur."+dao.UserRecharges.Columns().UserId, req.UserId)
	}

	if req.Account != "" {
		model = model.Where("u."+dao.Users.Columns().Account, req.Account)
	}

	if req.TokenId > 0 {
		model = model.Where("ur."+dao.UserRecharges.Columns().TokenId, req.TokenId)
	}

	// API field is Symbol (json:"name"), maps to Name in UserRecharges entity for the token's name/symbol
	// The request field is req.Symbol for filtering by token symbol.
	if req.Symbol != "" {
		model = model.WhereLike("ur."+dao.UserRecharges.Columns().Name, "%"+req.Symbol+"%")
	}

	if req.Chan != "" {
		model = model.Where("ur."+dao.UserRecharges.Columns().Chan, req.Chan)
	}

	if req.FromAddress != "" {
		model = model.Where("ur."+dao.UserRecharges.Columns().FromAddress, req.FromAddress)
	}

	if req.ToAddress != "" {
		model = model.Where("ur."+dao.UserRecharges.Columns().ToAddress, req.ToAddress)
	}

	if req.TxHash != "" {
		model = model.Where("ur."+dao.UserRecharges.Columns().TxHash, req.TxHash)
	}

	if req.State > 0 {
		model = model.Where("ur."+dao.UserRecharges.Columns().State, req.State)
	}

	// Handle date range if provided - 使用统一的DateRange处理工具
	condition := g.Map{}
	utility.AddDateRangeCondition(condition, req.DateRange, "ur.created_at")
	for key, value := range condition {
		model = model.Where(key, value)
	}

	// 新增：三级代理模糊查询
	model = utility.AddAgentSearchConditions(model, req.FirstAgentName, req.SecondAgentName, req.ThirdAgentName)

	// 新增：telegram查询条件
	model = utility.AddTelegramSearchConditions(model, req.TelegramId, req.TelegramUsername, req.FirstName)

	// Count total before pagination
	total, err = model.Count()
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询用户充值记录总数失败")
	}

	// Apply pagination if not exporting
	if req.Export == 0 {
		model = model.Page(req.Page, req.PageSize)
	}

	// Order by creation time descending (newest first)
	model = model.OrderDesc("ur." + dao.UserRecharges.Columns().CreatedAt)

	// Execute query
	err = model.Fields("ur.*").Scan(&list) // Explicitly select fields from ur to avoid ambiguity
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询用户充值记录失败")
	}

	return list, total, nil
}

// GetByID retrieves a single user recharge by its ID.
func (r *userRechargeRepository) GetByID(ctx context.Context, id uint) (*entity.UserRecharges, error) {
	var recharge *entity.UserRecharges
	err := dao.UserRecharges.Ctx(ctx).Where(dao.UserRecharges.Columns().UserRechargesId, id).Scan(&recharge)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCodef(codes.CodeUserRechargeNotFound, "充值记录不存在 (ID: %d)", id)
		}
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询充值记录失败 (ID: %d)", id)
	}
	return recharge, nil
}

// Export exports user recharges data based on the provided criteria.
func (r *userRechargeRepository) Export(ctx context.Context, req *v1.ListUserRechargesReq) ([]*entity.UserRecharges, error) {
	// Set export flag to ensure we get all records
	req.Export = 1
	list, _, err := r.List(ctx, req)
	return list, err
}
