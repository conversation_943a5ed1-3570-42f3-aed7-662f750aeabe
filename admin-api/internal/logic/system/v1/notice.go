package v1

import (
	"context"
	// "encoding/json" // 移除未使用的导入
	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/utility"
	"fmt"

	// "admin-api/internal/consts" // 移除未使用的导入
	deptconsts "admin-api/internal/consts/admin" // 导入包含常量定义的包
	// "admin-api/internal/dao" // Removed unused import
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"

	// "admin-api/internal/service" // 移除未使用的导入

	"github.com/gogf/gf/v2/container/gset"
	// "github.com/gogf/gf/v2/database/gdb" // 移除未使用的导入
	// "github.com/gogf/gf/v2/encoding/gjson" // 移除未使用的导入
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// --- Admin 端 Logic ---

// GetAdminNoticeList 获取公告列表(管理端)
func (s *sSystemLogic) GetAdminNoticeList(ctx context.Context, req *v1.GetAdminNoticeListReq) (res *v1.GetAdminNoticeListRes, err error) {
	res = &v1.GetAdminNoticeListRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*entity.AdminNotice, 0),
	}

	condition := g.Map{}
	if req.Title != "" {
		condition["title LIKE ?"] = "%" + req.Title + "%" // Use string key
	}
	if req.Type != nil {
		condition["type"] = *req.Type // Use string key
	}
	if req.Tag != nil {
		condition["tag"] = *req.Tag // Use string key
	}
	if req.Status != nil {
		condition["status"] = *req.Status // Use string key
	}
	if req.CreatedBy != nil {
		condition["created_by"] = *req.CreatedBy // Use string key
	}

	// 使用统一的DateRange处理工具
	utility.AddDateRangeCondition(condition, req.DateRange)

	list, total, err := s.noticeRepo.ListAdmin(ctx, req.Page, req.PageSize, condition) // Use noticeRepo
	if err != nil {
		return nil, err
	}

	res.Page.TotalSize = total
	res.Page.TotalPage = common.CalculateTotalPage(total, req.PageSize)
	res.Data = list

	return res, nil
}

// GetAdminNotice 获取公告详情(管理端)
func (s *sSystemLogic) GetAdminNotice(ctx context.Context, req *v1.GetAdminNoticeReq) (res *v1.GetAdminNoticeRes, err error) {
	notice, err := s.noticeRepo.GetByID(ctx, req.Id) // Use noticeRepo
	if err != nil {
		return nil, err // GetById 内部已处理 CodeNoticeNotFound
	}
	res = &v1.GetAdminNoticeRes{
		Data: notice,
	}
	return res, nil
}

// AddAdminNotice 新增公告(管理端)
func (s *sSystemLogic) AddAdminNotice(ctx context.Context, req *v1.AddAdminNoticeReq) (res *v1.AddAdminNoticeRes, err error) {
	// 1. 校验类型和状态
	if !deptconsts.IsValidNoticeType(int(req.Type)) {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的公告类型")
	}
	if !deptconsts.IsValidNoticeStatus(req.Status) {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的公告状态")
	}
	if req.Tag != 0 && !deptconsts.IsValidNoticeTag(req.Tag) { // 允许 tag 为 0 (普通)
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的公告标签")
	}

	// 2. 如果是私信，校验接收者不能为空
	var receiverIds []int64
	if req.Type == deptconsts.NoticeTypePrivateMessage {
		if req.Receiver == nil || req.Receiver.IsNil() {
			return nil, gerror.NewCode(codes.CodeNoticeReceiverEmpty)
		}
		if err = req.Receiver.Scan(&receiverIds); err != nil {
			return nil, gerror.NewCodef(codes.CodeInvalidParameter, "接收者列表格式错误: %v", err)
		}
		if len(receiverIds) == 0 {
			return nil, gerror.NewCode(codes.CodeNoticeReceiverEmpty)
		}
		// 可选：校验 receiverIds 中的用户 ID 是否都有效
	}

	// 3. 获取当前用户ID作为创建者
	userIdVar := ctx.Value("userId") // 改为小写
	if userIdVar == nil {
		return nil, gerror.NewCode(codes.CodeUnauthorized, "无法获取创建者信息")
	}
	createdBy := gconv.Int64(userIdVar)

	// 4. 准备数据
	noticeDo := &do.AdminNotice{}
	if err = gconv.Struct(req, noticeDo); err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "结构体转换失败")
	}
	noticeDo.CreatedBy = createdBy
	noticeDo.UpdatedBy = createdBy // 新增时，更新者也是创建者

	// 5. 执行插入
	lastInsertId, err := s.noticeRepo.Create(ctx, noticeDo) // Use noticeRepo
	if err != nil {
		return nil, err
	}

	res = &v1.AddAdminNoticeRes{
		Id: lastInsertId,
	}
	return res, nil
}

// EditAdminNotice 编辑公告(管理端)
func (s *sSystemLogic) EditAdminNotice(ctx context.Context, req *v1.EditAdminNoticeReq) (res *v1.EditAdminNoticeRes, err error) {
	// 1. 校验公告是否存在 (Use Repository)
	_, err = s.noticeRepo.GetByID(ctx, req.Id)
	if err != nil {
		return nil, err // Repository handles NotFound
	}

	// 2. 校验类型和状态
	if !deptconsts.IsValidNoticeType(int(req.Type)) {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的公告类型")
	}
	if !deptconsts.IsValidNoticeStatus(req.Status) {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的公告状态")
	}
	if req.Tag != 0 && !deptconsts.IsValidNoticeTag(req.Tag) {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的公告标签")
	}

	// 3. 如果是私信，校验接收者不能为空
	var receiverIds []int64
	if req.Type == deptconsts.NoticeTypePrivateMessage {
		if req.Receiver == nil || req.Receiver.IsNil() {
			return nil, gerror.NewCode(codes.CodeNoticeReceiverEmpty)
		}
		if err = req.Receiver.Scan(&receiverIds); err != nil {
			return nil, gerror.NewCodef(codes.CodeInvalidParameter, "接收者列表格式错误: %v", err)
		}
		if len(receiverIds) == 0 {
			return nil, gerror.NewCode(codes.CodeNoticeReceiverEmpty)
		}
	}

	// 4. 获取当前用户ID作为修改者
	userIdVar := ctx.Value("userId")
	if userIdVar == nil {
		return nil, gerror.NewCode(codes.CodeUnauthorized, "无法获取修改者信息")
	}
	updatedBy := gconv.Int64(userIdVar)

	// 5. 准备更新数据 (使用 g.Map 避免更新 CreatedBy 等字段)
	updateData := g.Map{
		// Use string keys for g.Map
		"title":      req.Title,
		"type":       req.Type,
		"tag":        req.Tag,
		"content":    req.Content,
		"receiver":   req.Receiver, // Pass *gjson.Json directly
		"remark":     req.Remark,
		"sort":       req.Sort,
		"status":     req.Status,
		"updated_by": updatedBy,
	}

	// 6. 执行更新 (Use Repository)
	err = s.noticeRepo.Update(ctx, req.Id, updateData)
	if err != nil {
		return nil, err // Repository method wraps error
	}

	res = &v1.EditAdminNoticeRes{}
	return res, nil
}

// DeleteAdminNotice 删除公告(管理端) - 软删除
func (s *sSystemLogic) DeleteAdminNotice(ctx context.Context, req *v1.DeleteAdminNoticeReq) (res *v1.DeleteAdminNoticeRes, err error) {
	if len(req.Ids) == 0 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "请选择要删除的公告")
	}

	// 获取当前用户ID作为操作者
	userIdVar := ctx.Value("userId")
	if userIdVar == nil {
		return nil, gerror.NewCode(codes.CodeUnauthorized, "无法获取操作者信息")
	}
	userId := gconv.Int64(userIdVar)

	// Use Repository for soft delete
	err = s.noticeRepo.Delete(ctx, req.Ids, userId)
	if err != nil {
		return nil, err // Repository method wraps error
	}

	res = &v1.DeleteAdminNoticeRes{}
	return res, nil
}

// GetMemberListForNotice 获取用户列表供选择器使用(管理端)
func (s *sSystemLogic) GetMemberListForNotice(ctx context.Context, req *v1.GetMemberListForNoticeReq) (res *v1.GetMemberListForNoticeRes, err error) {
	res = &v1.GetMemberListForNoticeRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*v1.UserInfo, 0),
	}

	condition := g.Map{}
	if req.Username != "" {
		condition["username"] = req.Username // key 与 DAO 中约定的保持一致
	}
	if req.DeptId != nil {
		condition["deptId"] = req.DeptId
	}
	if req.RoleId != nil {
		condition["roleId"] = req.RoleId
	}
	if req.PostId != nil {
		condition["postId"] = req.PostId
	}

	// Use memberRepo.ListForNotice
	listUserInfo, total, err := s.memberRepo.ListForNotice(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, err // Repository method wraps error
	}

	res.Page.TotalSize = total
	res.Page.TotalPage = common.CalculateTotalPage(total, req.PageSize)
	// 类型转换 []*dao.UserInfoForNotice -> []*v1.UserInfo
	// No need for gconv.Structs as ListForNotice returns []*v1.UserInfo
	res.Data = listUserInfo

	return res, nil
}

// GetAdminNoticeReadStatus 获取公告已读/未读用户列表(管理端)
func (s *sSystemLogic) GetAdminNoticeReadStatus(ctx context.Context, req *v1.GetAdminNoticeReadStatusReq) (res *v1.GetAdminNoticeReadStatusRes, err error) {
	res = &v1.GetAdminNoticeReadStatusRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*v1.UserInfo, 0),
	}

	// 1. 获取公告信息 (Use Repository)
	notice, err := s.noticeRepo.GetByID(ctx, req.Id)
	if err != nil {
		return nil, err // Repository handles NotFound
	}

	// 2. 获取已读用户 ID 列表 (Use Repository)
	readMemberIds, err := s.noticeReadRepo.GetReadMemberIDs(ctx, req.Id)
	if err != nil {
		return nil, err // Repository method wraps error
	}
	readMemberIdSet := gset.NewFrom(gconv.Interfaces(readMemberIds))

	// 3. 确定目标用户 ID 列表
	var targetMemberIds []int64
	if notice.Type == deptconsts.NoticeTypeNotification { // 通知公告，目标是所有用户
		allMemberIds, err := s.memberRepo.GetAllActiveUserIDs(ctx) // Use memberRepo
		if err != nil {
			return nil, err // Repository method wraps error
		}
		targetMemberIds = allMemberIds
	} else if notice.Type == deptconsts.NoticeTypePrivateMessage { // 私信，目标是 receiver
		if notice.Receiver == nil || notice.Receiver.IsNil() {
			// 私信没有接收者，理论上不应该发生，返回空列表
			return res, nil
		}
		if err = notice.Receiver.Scan(&targetMemberIds); err != nil {
			return nil, gerror.NewCodef(codes.CodeInternalError, "解析私信接收者失败: %v", err)
		}
	} else {
		// 未知类型，返回空
		return res, nil
	}

	if len(targetMemberIds) == 0 {
		return res, nil // 没有目标用户
	}

	// 4. 根据请求的 readStatus 筛选用户 ID
	var finalUserIds []int64
	if req.ReadStatus == nil { // 查询所有相关用户
		finalUserIds = targetMemberIds
	} else if *req.ReadStatus == 1 { // 查询已读用户
		for _, targetId := range targetMemberIds {
			if readMemberIdSet.Contains(targetId) {
				finalUserIds = append(finalUserIds, targetId)
			}
		}
	} else { // 查询未读用户
		for _, targetId := range targetMemberIds {
			if !readMemberIdSet.Contains(targetId) {
				finalUserIds = append(finalUserIds, targetId)
			}
		}
	}

	if len(finalUserIds) == 0 {
		return res, nil // 没有符合条件的用户
	}

	// 5. 构建查询用户详情的条件
	userCondition := g.Map{
		"m.id IN (?)": finalUserIds, // Use string key with alias
	}
	if req.Username != "" {
		userCondition["username"] = req.Username // 复用 GetMemberListForNotice 的 key
	}
	if req.DeptId != nil {
		userCondition["deptId"] = req.DeptId
	}
	// 注意：这里如果需要按角色、岗位筛选，逻辑会更复杂，需要 JOIN

	// 6. 查询用户信息（分页）
	//    复用 GetMemberListForNotice，但传入筛选后的用户 ID 列表
	//    需要修改 GetMemberListForNotice 以支持按 ID 列表筛选，或者新建一个方法
	//    临时方案：直接查询，不复用 GetMemberListForNotice
	var userList []*v1.UserInfo
	// Use memberRepo.ListForNotice with added ID filter
	userCondition[fmt.Sprintf("%s IN (?)", "id")] = finalUserIds // Assuming 'id' is the primary key field name in the condition map expected by ListForNotice
	userList, total, err := s.memberRepo.ListForNotice(ctx, req.Page, req.PageSize, userCondition)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询用户信息失败")
	}
	if total == 0 {
		return res, nil
	}

	res.Page.TotalSize = total
	res.Page.TotalPage = common.CalculateTotalPage(total, req.PageSize)
	res.Data = userList

	return res, nil
}

// --- User 端 Logic ---

// GetMyNoticeList 获取我的公告列表(用户端)
func (s *sSystemLogic) GetMyNoticeList(ctx context.Context, req *v1.GetMyNoticeListReq) (res *v1.GetMyNoticeListRes, err error) {
	res = &v1.GetMyNoticeListRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*v1.NoticeListItem, 0),
	}

	// 1. 获取当前用户 ID
	userIdVar := ctx.Value("userId")
	if userIdVar == nil {
		return nil, gerror.NewCode(codes.CodeUnauthorized, "无法获取用户信息")
	}
	userId := gconv.Int64(userIdVar)

	// 2. 构建查询条件
	condition := g.Map{}
	if req.Type != nil {
		condition["type"] = *req.Type // Use string key
	}
	if req.Tag != nil {
		condition["tag"] = *req.Tag // Use string key
	}

	// 3. 查询用户应接收的公告列表 (Use Repository)
	// Note: ListUser now handles filtering by type/tag and receiver logic
	list, err := s.noticeRepo.ListUser(ctx, userId, condition)
	if err != nil {
		return nil, err // Repository method wraps error
	}
	// Check list length instead of total, as total is not returned by ListUser
	if len(list) == 0 {
		return res, nil
	}

	// 4. 查询这些公告的阅读状态
	noticeIds := make([]int64, len(list))
	for i, notice := range list {
		noticeIds[i] = notice.Id
	}
	readStatusMap, err := s.noticeReadRepo.GetReadStatusMap(ctx, userId, noticeIds) // Use noticeReadRepo
	if err != nil {
		// Log error but continue
		g.Log().Errorf(ctx, "查询用户 %d 的公告阅读状态失败: %v", userId, err)
		readStatusMap = make(map[int64]*entity.AdminNoticeRead) // Use empty map to continue
	}

	// 5. 组装结果，并根据 isRead 筛选 (如果需要)
	resultList := make([]*v1.NoticeListItem, 0, len(list))
	for _, notice := range list {
		item := &v1.NoticeListItem{
			AdminNotice: notice,
			IsRead:      false,
			ReadClicks:  0,
		}
		if readRecord, ok := readStatusMap[notice.Id]; ok {
			item.IsRead = true
			item.ReadClicks = readRecord.Clicks
		}

		// 根据 isRead 参数筛选
		if req.IsRead != nil {
			shouldRead := *req.IsRead == 1
			if item.IsRead == shouldRead {
				resultList = append(resultList, item)
			}
		} else {
			resultList = append(resultList, item)
		}
	}

	// 注意：如果在这里根据 isRead 筛选，totalSize 和 totalPage 需要重新计算或调整
	// 更优的做法是在数据库层面处理 isRead 筛选，但这会使查询更复杂
	// 暂时返回筛选后的列表，分页信息可能不完全准确
	// Recalculate total based on the filtered resultList length if isRead filter applied
	filteredTotal := len(resultList)
	res.Page.TotalSize = filteredTotal
	res.Page.TotalPage = common.CalculateTotalPage(filteredTotal, req.PageSize)
	// Apply pagination to the filtered list in memory (less efficient but simpler for now)
	startIndex := (req.Page - 1) * req.PageSize
	endIndex := startIndex + req.PageSize
	if startIndex < 0 {
		startIndex = 0
	}
	if startIndex >= filteredTotal {
		resultList = []*v1.NoticeListItem{} // Page out of bounds
	} else {
		if endIndex > filteredTotal {
			endIndex = filteredTotal
		}
		resultList = resultList[startIndex:endIndex]
	}
	res.Data = resultList

	return res, nil
}

// MarkNoticeRead 标记公告已读(用户端)
func (s *sSystemLogic) MarkNoticeRead(ctx context.Context, req *v1.MarkNoticeReadReq) (res *v1.MarkNoticeReadRes, err error) {
	// 1. 获取当前用户 ID
	userIdVar := ctx.Value("userId")
	if userIdVar == nil {
		return nil, gerror.NewCode(codes.CodeUnauthorized, "无法获取用户信息")
	}
	userId := gconv.Int64(userIdVar)

	// 2. 查询公告是否存在且已发布 (Use Repository)
	notice, err := s.noticeRepo.GetByID(ctx, req.NoticeId)
	if err != nil {
		return nil, err // Repository handles NotFound
	}
	if notice.Status != deptconsts.NoticeStatusPublished {
		return nil, gerror.NewCode(codes.CodeNoticeNotFound, "公告未发布或已停用")
	}

	// 3. 检查用户是否有权阅读此公告
	canRead := false
	if notice.Type == deptconsts.NoticeTypeNotification { // 通知公告所有人都可读
		canRead = true
	} else if notice.Type == deptconsts.NoticeTypePrivateMessage { // 私信需要检查 receiver
		if notice.Receiver != nil && !notice.Receiver.IsNil() {
			var receiverIds []int64
			if err = notice.Receiver.Scan(&receiverIds); err == nil {
				for _, receiverId := range receiverIds {
					if receiverId == userId {
						canRead = true
						break
					}
				}
			} else {
				g.Log().Errorf(ctx, "解析私信 %d 的接收者失败: %v", req.NoticeId, err)
			}
		}
	}
	if !canRead {
		return nil, gerror.NewCode(codes.CodeNoticeCannotRead)
	}

	// 4. 标记已读或增加点击次数 (Use Repository)
	err = s.noticeReadRepo.MarkReadOrIncrementClick(ctx, userId, req.NoticeId)
	if err != nil {
		return nil, err // Repository method wraps error
	}

	res = &v1.MarkNoticeReadRes{}
	return res, nil
}

// GetMyUnreadNoticeCount 获取我的未读公告数(用户端)
func (s *sSystemLogic) GetMyUnreadNoticeCount(ctx context.Context, req *v1.GetMyUnreadNoticeCountReq) (res *v1.GetMyUnreadNoticeCountRes, err error) {
	// 1. 获取当前用户 ID
	userIdVar := ctx.Value("userId")
	if userIdVar == nil {
		return nil, gerror.NewCode(codes.CodeUnauthorized, "无法获取用户信息")
	}
	userId := gconv.Int64(userIdVar)

	// 2. 调用 Repository 方法计算未读数量
	// Note: The repo implementation is a placeholder and might be inaccurate.
	// Consider moving this logic entirely to the service/logic layer if complex.
	count, err := s.noticeRepo.GetUserUnreadNoticeCount(ctx, userId)
	if err != nil {
		return nil, err // Repository method wraps error
	}

	res = &v1.GetMyUnreadNoticeCountRes{
		Count: count,
	}
	return res, nil
}
