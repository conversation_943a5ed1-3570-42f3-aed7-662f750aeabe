package v1

import (
	"context"
	"fmt"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/utility"
	"admin-api/utility/csv"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// GetMyDeposits 获取我的充值记录列表
func (s *sSystemLogic) GetMyDeposits(ctx context.Context, req *v1.GetMyDepositsReq) (res *v1.GetMyDepositsRes, err error) {
	res = &v1.GetMyDepositsRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*v1.MerchantDepositInfoType, 0),
	}

	// 构建查询条件
	condition := g.Map{}

	// 如果提供了商户ID，则筛选特定商户
	if req.MerchantId != nil && *req.MerchantId > 0 {
		condition["merchant_id"] = *req.MerchantId
	}

	// Apply additional filters
	if req.TokenId != nil {
		condition["token_id"] = *req.TokenId
	}
	if req.TokenName != "" {
		condition["name LIKE ?"] = "%" + req.TokenName + "%"
	}
	if req.State != nil {
		condition["state"] = *req.State
	}
	if req.TxHash != "" {
		condition["tx_hash LIKE ?"] = "%" + req.TxHash + "%"
	}

	// 使用灵活的日期范围处理，支持dateRange和createdAt数组两种格式
	utility.AddFlexibleDateRangeCondition(condition, req.DateRange, req.CreatedAt)

	// 如果是导出
	if req.Export {
		// 导出时不分页，查询所有符合条件的数据
		list, _, err := dao.MerchantDeposits.List(ctx, 1, 9999999, condition)
		if err != nil {
			return nil, gerror.Wrap(err, "导出查询充值记录失败")
		}

		if len(list) == 0 {
			return res, nil // 没有数据可导出
		}

		// 获取商户ID列表以批量查询商户名称
		merchantIds := make([]uint64, 0)
		merchantIdMap := make(map[uint64]bool)
		for _, deposit := range list {
			if !merchantIdMap[deposit.MerchantId] {
				merchantIds = append(merchantIds, deposit.MerchantId)
				merchantIdMap[deposit.MerchantId] = true
			}
		}

		// 批量查询商户信息
		merchantNameMap := make(map[uint64]string)
		if len(merchantIds) > 0 {
			var merchants []entity.Merchants
			err = dao.Merchants.Ctx(ctx).WhereIn("merchant_id", merchantIds).Scan(&merchants)
			if err != nil {
				g.Log().Errorf(ctx, "查询商户信息失败: %v", err)
			} else {
				for _, merchant := range merchants {
					merchantNameMap[merchant.MerchantId] = merchant.MerchantName
				}
			}
		}

		// 准备导出数据
		exportData := make([]interface{}, 0, len(list))
		for _, deposit := range list {
			// 状态文本
			stateText := s.getDepositStateText(deposit.State)

			// 准备导出结构体
			exportItem := struct {
				RechargesId   int64  `json:"rechargesId" excel:"充值记录ID"`
				MerchantId    uint64 `json:"merchantId" excel:"商户ID"`
				MerchantName  string `json:"merchantName" excel:"商户名称"`
				TokenName     string `json:"tokenName" excel:"币种名称"`
				Amount        string `json:"amount" excel:"充值数量"`
				FromAddress   string `json:"fromAddress" excel:"来源地址"`
				ToAddress     string `json:"toAddress" excel:"目标地址"`
				TxHash        string `json:"txHash" excel:"交易哈希"`
				State         string `json:"state" excel:"状态"`
				FailureReason string `json:"failureReason" excel:"失败原因"`
				CreatedAt     string `json:"createdAt" excel:"创建时间"`
				CompletedAt   string `json:"completedAt" excel:"完成时间"`
			}{
				RechargesId:   int64(deposit.RechargesId),
				MerchantId:    deposit.MerchantId,
				MerchantName:  merchantNameMap[deposit.MerchantId],
				TokenName:     deposit.Name,
				Amount:        deposit.Amount.String(),
				FromAddress:   deposit.FromAddress,
				ToAddress:     deposit.ToAddress,
				TxHash:        deposit.TxHash,
				State:         stateText,
				FailureReason: deposit.FailureReason,
				CreatedAt:     deposit.CreatedAt.Format("Y-m-d H:i:s"),
			}
			if deposit.CompletedAt != nil {
				exportItem.CompletedAt = deposit.CompletedAt.Format("Y-m-d H:i:s")
			}
			exportData = append(exportData, exportItem)
		}

		// 定义Excel表头
		excelTags := []string{
			"充值记录ID", "商户ID", "商户名称", "币种名称", "充值数量", "来源地址", "目标地址",
			"交易哈希", "状态", "失败原因", "创建时间", "完成时间",
		}

		// 调用CSV导出工具
		fileName := "merchant_deposits"
		if req.MerchantId != nil && *req.MerchantId > 0 {
			fileName = fmt.Sprintf("merchant_deposits_%d", *req.MerchantId)
		}
		// 导出CSV后直接返回nil，避免ResponseMiddleware处理
		err = csv.ExportByStructs(ctx, excelTags, exportData, fileName, "充值记录")
		return nil, err
	}

	// 正常查询
	list, total, err := dao.MerchantDeposits.List(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, gerror.Wrap(err, "查询充值记录失败")
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = (total + req.PageSize - 1) / req.PageSize

	// 获取商户ID列表以批量查询商户名称
	merchantIds := make([]uint64, 0)
	merchantIdMap := make(map[uint64]bool)
	for _, deposit := range list {
		if !merchantIdMap[deposit.MerchantId] {
			merchantIds = append(merchantIds, deposit.MerchantId)
			merchantIdMap[deposit.MerchantId] = true
		}
	}

	// 批量查询商户信息
	merchantNameMap := make(map[uint64]string)
	if len(merchantIds) > 0 {
		var merchants []entity.Merchants
		err = dao.Merchants.Ctx(ctx).WhereIn("merchant_id", merchantIds).Scan(&merchants)
		if err != nil {
			g.Log().Errorf(ctx, "查询商户信息失败: %v", err)
		} else {
			for _, merchant := range merchants {
				merchantNameMap[merchant.MerchantId] = merchant.MerchantName
			}
		}
	}

	// 转换数据格式
	for _, deposit := range list {
		depositInfo := &v1.MerchantDepositInfoType{
			RechargesId:          deposit.RechargesId,
			MerchantId:           deposit.MerchantId,
			MerchantName:         merchantNameMap[deposit.MerchantId],
			TokenId:              deposit.TokenId,
			TokenName:            deposit.Name,
			Chan:                 deposit.Chan,
			TokenContractAddress: deposit.TokenContractAddress,
			FromAddress:          deposit.FromAddress,
			ToAddress:            deposit.ToAddress,
			TxHash:               deposit.TxHash,
			Amount:               deposit.Amount,
			State:                deposit.State,
			StateText:            s.getDepositStateText(deposit.State),
			FailureReason:        deposit.FailureReason,
			Confirmations:        deposit.Confirmations,
			CreatedAt:            deposit.CreatedAt,
			CompletedAt:          deposit.CompletedAt,
			NotificationSent:     deposit.NotificationSent,
			NotificationSentAt:   deposit.NotificationSentAt,
		}
		res.Data = append(res.Data, depositInfo)
	}

	return res, nil
}

// GetMyDepositDetail 获取我的充值记录详情
func (s *sSystemLogic) GetMyDepositDetail(ctx context.Context, req *v1.GetMyDepositDetailReq) (res *v1.GetMyDepositDetailRes, err error) {
	// 查询充值记录
	var deposit *entity.MerchantDeposits
	err = dao.MerchantDeposits.Ctx(ctx).Where("recharges_id", req.RechargesId).Scan(&deposit)
	if err != nil {
		return nil, gerror.Wrap(err, "查询充值记录详情失败")
	}

	if deposit == nil {
		return nil, gerror.NewCode(codes.CodeNotFound, "充值记录不存在")
	}

	// 构建响应数据
	res = &v1.GetMyDepositDetailRes{
		Data: &v1.MerchantDepositDetailType{
			MerchantDeposits: *deposit,
			StateText:        s.getDepositStateText(deposit.State),
		},
	}

	return res, nil
}

// getDepositStateText 获取充值状态文本描述
func (s *sSystemLogic) getDepositStateText(state uint) string {
	switch state {
	case 1:
		return "待确认/处理中"
	case 2:
		return "已完成/已入账"
	default:
		return "未知状态"
	}
}
