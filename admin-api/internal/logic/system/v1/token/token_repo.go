package token

import (
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/token" // Import the interface package
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// tokenRepository 实现了 ITokenRepository 接口
type tokenRepository struct{}

// NewTokenRepository 创建一个新的 tokenRepository 实例
func NewTokenRepository() token.ITokenRepository {
	return &tokenRepository{}
}

// List 获取代币列表
func (r *tokenRepository) List(ctx context.Context, page, pageSize int, condition g.Map, orderBy, orderDirection string) (list []*entity.Tokens, total int, err error) {
	m := dao.Tokens.Ctx(ctx).Where(condition).WhereNull(dao.Tokens.Columns().DeletedAt)

	total, err = m.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "tokenRepository.List: 获取代币总数失败")
	}
	if total == 0 {
		return make([]*entity.Tokens, 0), 0, nil
	}

	// Apply sorting
	if orderDirection == "desc" {
		m = m.OrderDesc(orderBy)
	} else {
		m = m.OrderAsc(orderBy)
	}

	err = m.Page(page, pageSize).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "tokenRepository.List: 查询代币列表失败")
	}
	return list, total, nil
}

// GetByID 根据 ID 获取代币实体
func (r *tokenRepository) GetByID(ctx context.Context, tokenId uint) (*entity.Tokens, error) {
	var entity *entity.Tokens
	err := dao.Tokens.Ctx(ctx).Where(dao.Tokens.Columns().TokenId, tokenId).WhereNull(dao.Tokens.Columns().DeletedAt).Scan(&entity)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // Not found
		}
		return nil, gerror.Wrapf(err, "tokenRepository.GetByID: 查询代币失败, ID: %d", tokenId)
	}
	return entity, nil
}

// Create 创建代币
func (r *tokenRepository) Create(ctx context.Context, data *do.Tokens) (id int64, err error) {
	data.CreatedAt = gtime.Now()
	data.UpdatedAt = gtime.Now()
	res, err := dao.Tokens.Ctx(ctx).Data(data).Insert()
	if err != nil {
		// TODO: Handle specific unique constraint errors if DAO doesn't
		return 0, gerror.Wrap(err, "tokenRepository.Create: 创建代币失败")
	}
	id, err = res.LastInsertId()
	if err != nil {
		return 0, gerror.Wrap(err, "tokenRepository.Create: 获取新代币ID失败")
	}
	return id, nil
}

// Update 更新代币信息 (部分更新)
func (r *tokenRepository) Update(ctx context.Context, tokenId uint, data g.Map) error {
	if len(data) == 0 {
		return nil // No fields to update
	}
	data[dao.Tokens.Columns().UpdatedAt] = gtime.Now() // Ensure update time is set
	_, err := dao.Tokens.Ctx(ctx).Data(data).Where(dao.Tokens.Columns().TokenId, tokenId).Update()
	if err != nil {
		// TODO: Handle specific unique constraint errors if DAO doesn't
		return gerror.Wrapf(err, "tokenRepository.Update: 更新代币失败, ID: %d", tokenId)
	}
	return nil
}

// DeleteSoft 软删除代币
func (r *tokenRepository) DeleteSoft(ctx context.Context, tokenId uint) error {
	_, err := dao.Tokens.Ctx(ctx).Data(do.Tokens{DeletedAt: gtime.Now()}).Where(dao.Tokens.Columns().TokenId, tokenId).Update()
	if err != nil {
		// Check if it's a "not found" error after update attempt (0 rows affected)
		// Or handle it in the DAO layer
		return gerror.Wrapf(err, "tokenRepository.DeleteSoft: 软删除代币失败, ID: %d", tokenId)
	}
	// Consider checking affected rows if DAO doesn't return specific not found error
	return nil
}

// CheckExistence 检查指定条件下代币是否存在 (排除指定 ID)
func (r *tokenRepository) CheckExistence(ctx context.Context, network string, value string, field string, excludeId ...uint) (bool, error) {
	// Validate field to prevent SQL injection if field comes directly from user input
	var columnName string
	switch field {
	case dao.Tokens.Columns().Symbol:
		columnName = dao.Tokens.Columns().Symbol
	case dao.Tokens.Columns().ContractAddress:
		columnName = dao.Tokens.Columns().ContractAddress
	default:
		return false, gerror.New(fmt.Sprintf("tokenRepository.CheckExistence: 不支持的检查字段: %s", field))
	}

	m := dao.Tokens.Ctx(ctx).
		Where(dao.Tokens.Columns().Network, network).
		Where(columnName, value).
		WhereNull(dao.Tokens.Columns().DeletedAt)

	if len(excludeId) > 0 && excludeId[0] > 0 {
		m = m.WhereNot(dao.Tokens.Columns().TokenId, excludeId[0])
	}

	count, err := m.Count()
	if err != nil {
		return false, gerror.Wrapf(err, "tokenRepository.CheckExistence: 检查代币存在性失败 (Network: %s, Field: %s, Value: %s)", network, field, value)
	}
	return count > 0, nil
}
