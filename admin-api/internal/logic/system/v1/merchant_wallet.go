package v1

import (
	"context"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/utility"
	"admin-api/utility/excel"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/guid"
	"github.com/shopspring/decimal"
)

// GetMerchantAssets 获取商户资产概览
func (s *sSystemLogic) GetMerchantAssets(ctx context.Context, req *v1.GetMerchantAssetsReq) (res *v1.GetMerchantAssetsRes, err error) {
	// 验证商户是否存在
	merchant, err := dao.Merchants.Ctx(ctx).Where("merchant_id", req.MerchantId).WhereNull("deleted_at").One()
	if err != nil {
		return nil, gerror.Wrap(err, "查询商户信息失败")
	}
	if merchant.IsEmpty() {
		return nil, gerror.NewCode(codes.MerchantNotFound)
	}

	// 获取商户资产汇总
	wallets, err := dao.MerchantWallets.GetMerchantAssetsSummary(ctx, req.MerchantId)
	if err != nil {
		return nil, err
	}

	// 组装返回数据
	assets := make([]*v1.MerchantAssetItem, 0, len(wallets))
	for _, wallet := range wallets {
		// 格式化余额显示
		formattedAvailable := s.formatDecimalBalance(wallet.AvailableBalance)
		formattedFrozen := s.formatDecimalBalance(wallet.FrozenBalance)
		total := wallet.AvailableBalance.Add(wallet.FrozenBalance)
		formattedTotal := s.formatDecimalBalance(total)

		asset := &v1.MerchantAssetItem{
			Symbol:                    wallet.Symbol,
			AvailableBalance:          wallet.AvailableBalance.IntPart(),
			FrozenBalance:             wallet.FrozenBalance.IntPart(),
			FormattedAvailableBalance: formattedAvailable,
			FormattedFrozenBalance:    formattedFrozen,
			FormattedTotalBalance:     formattedTotal,
			DecimalPlaces:             wallet.DecimalPlaces,
			LastUpdated:               wallet.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
		assets = append(assets, asset)
	}

	return &v1.GetMerchantAssetsRes{
		MerchantId:   req.MerchantId,
		MerchantName: merchant["merchant_name"].String(),
		Assets:       assets,
		TotalAssets:  len(assets),
	}, nil
}

// GetMerchantWallets 获取商户钱包列表
func (s *sSystemLogic) GetMerchantWallets(ctx context.Context, req *v1.GetMerchantWalletsReq) (res *v1.GetMerchantWalletsRes, err error) {
	// 构建查询条件
	condition := g.Map{}
	if req.MerchantId != nil {
		condition["merchant_id"] = *req.MerchantId
	}
	if req.Symbol != "" {
		condition["symbol LIKE"] = "%" + req.Symbol + "%"
	}
	if req.HasBalance != nil && *req.HasBalance {
		// 修正语法错误：直接在condition Map中添加条件
		condition["(available_balance > 0 OR frozen_balance > 0)"] = ""
	}

	// 使用 DateRange 统一处理时间范围查询
	utility.AddDateRangeCondition(condition, req.DateRange)

	// 如果是导出，则直接返回数据
	if req.Export {
		list, _, err := dao.MerchantWallets.GetMerchantWalletsList(ctx, 1, 9999999, condition)
		if err != nil {
			return nil, gerror.Wrap(err, "导出查询商户钱包列表失败")
		}

		// 准备导出数据
		exportData := make([]interface{}, 0, len(list))
		for _, wallet := range list {
			// 获取商户名称
			merchantName := s.getMerchantNameById(ctx, wallet.MerchantId)

			exportItem := map[string]interface{}{
				"商户ID":   wallet.MerchantId,
				"商户名称":   merchantName,
				"钱包ID":   wallet.WalletId,
				"代币符号":   wallet.Symbol,
				"可用余额":   s.formatDecimalBalance(wallet.AvailableBalance),
				"冻结余额":   s.formatDecimalBalance(wallet.FrozenBalance),
				"总余额":    s.formatDecimalBalance(wallet.AvailableBalance.Add(wallet.FrozenBalance)),
				"小数位数":   wallet.DecimalPlaces,
				"创建时间":   wallet.CreatedAt.Format("2006-01-02 15:04:05"),
				"更新时间":   wallet.UpdatedAt.Format("2006-01-02 15:04:05"),
			}
			exportData = append(exportData, exportItem)
		}

		// 调用Excel导出工具
		return &v1.GetMerchantWalletsRes{}, excel.ExportByStructs(ctx, []string{}, exportData, "商户钱包列表", "商户钱包列表")
	}

	// 查询商户钱包列表
	list, total, err := dao.MerchantWallets.GetMerchantWalletsList(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, err
	}

	// 组装返回数据
	walletList := make([]*v1.MerchantWalletListItem, 0, len(list))
	for _, wallet := range list {
		// 获取商户名称
		merchantName := s.getMerchantNameById(ctx, wallet.MerchantId)

		walletItem := &v1.MerchantWalletListItem{
			MerchantWallets:           wallet,
			MerchantName:              merchantName,
			FormattedAvailableBalance: s.formatDecimalBalance(wallet.AvailableBalance),
			FormattedFrozenBalance:    s.formatDecimalBalance(wallet.FrozenBalance),
			FormattedTotalBalance:     s.formatDecimalBalance(wallet.AvailableBalance.Add(wallet.FrozenBalance)),
		}
		walletList = append(walletList, walletItem)
	}

	return &v1.GetMerchantWalletsRes{
		Page: common.PageResponse{
			PageSize:  req.PageSize,
			TotalSize: total,
		},
		Data: walletList,
	}, nil
}

// AdjustMerchantBalance 调整商户余额
func (s *sSystemLogic) AdjustMerchantBalance(ctx context.Context, req *v1.AdjustMerchantBalanceReq) (res *v1.AdjustMerchantBalanceRes, err error) {
	// 验证商户是否存在
	merchant, err := dao.Merchants.Ctx(ctx).Where("merchant_id", req.MerchantId).WhereNull("deleted_at").One()
	if err != nil {
		return nil, gerror.Wrap(err, "查询商户信息失败")
	}
	if merchant.IsEmpty() {
		return nil, gerror.NewCode(codes.MerchantNotFound)
	}

	// 验证调整金额
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return nil, gerror.NewCode(codes.InvalidAmount, "调整金额必须大于0")
	}

	// 在事务中执行调整操作
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 获取或创建商户钱包
		wallet, err := dao.MerchantWallets.GetMerchantWallet(ctx, req.MerchantId, req.Symbol)
		if err != nil {
			return gerror.Wrap(err, "查询商户钱包失败")
		}
		
		// 如果钱包不存在，创建一个新钱包
		if wallet == nil {
			// 暂时简单实现，直接返回错误
			return gerror.NewCode(codes.WalletNotFound, "钱包不存在，请先创建钱包")
		}

		// 计算调整前余额
		var balanceBefore, balanceAfter decimal.Decimal
		var newAvailable, newFrozen decimal.Decimal

		if req.WalletType == "available" {
			balanceBefore = wallet.AvailableBalance
			
			// 添加调试日志
			g.Log().Debugf(ctx, "调整前: 原始余额=%s, 小数位=%d", 
				wallet.AvailableBalance.String(), wallet.DecimalPlaces)
			g.Log().Debugf(ctx, "调整金额: %s, 类型: %s", req.Amount.String(), req.Type)
			
			if req.Type == "increase" {
				balanceAfter = balanceBefore.Add(req.Amount)
			} else {
				// 验证余额是否充足
				if balanceBefore.LessThan(req.Amount) {
					return gerror.NewCode(codes.CodeInsufficientFunds, "可用余额不足")
				}
				balanceAfter = balanceBefore.Sub(req.Amount)
			}
			
			g.Log().Debugf(ctx, "调整后: 计算后余额=%s", balanceAfter.String())
			
			newAvailable = balanceAfter
			newFrozen = wallet.FrozenBalance
		} else {
			balanceBefore = wallet.FrozenBalance
			if req.Type == "increase" {
				balanceAfter = balanceBefore.Add(req.Amount)
			} else {
				// 验证余额是否充足
				if balanceBefore.LessThan(req.Amount) {
					return gerror.NewCode(codes.CodeInsufficientFunds, "冻结余额不足")
				}
				balanceAfter = balanceBefore.Sub(req.Amount)
			}
			newFrozen = balanceAfter
			newAvailable = wallet.AvailableBalance
		}

		// 更新钱包余额
		err = dao.MerchantWallets.UpdateMerchantWalletBalance(ctx, tx, wallet.WalletId, newAvailable, newFrozen)
		if err != nil {
			return gerror.Wrap(err, "更新钱包余额失败")
		}

		// 记录交易记录
		businessId := guid.S()
		direction := "in"
		if req.Type == "decrease" {
			direction = "out"
		}

		transaction := &entity.MerchantTransactions{
			MerchantId:         uint(req.MerchantId),
			TokenId:            0, // 暂时设为0，后续可以从tokens表查询
			Type:               "system_adjust",
			WalletType:         req.WalletType,
			Direction:          direction,
			Amount:             req.Amount,
			BalanceBefore:      balanceBefore,
			BalanceAfter:       balanceAfter,
			RelatedEntityType:  "admin_adjust",
			Status:             1, // 成功
			Memo:               req.Reason,
			Symbol:             req.Symbol,
			BusinessId:         businessId,
			RequestReference:   req.Reference,
			RequestSource:      "admin",
		}

		// 插入交易记录
		transactionId, err := dao.MerchantTransactions.Ctx(ctx).TX(tx).Data(transaction).InsertAndGetId()
		if err != nil {
			return gerror.Wrap(err, "插入交易记录失败")
		}

		res = &v1.AdjustMerchantBalanceRes{
			Success:                true,
			TransactionId:          uint64(transactionId),
			BalanceBefore:          balanceBefore,
			BalanceAfter:           balanceAfter,
			FormattedBalanceBefore: s.formatDecimalBalance(balanceBefore),
			FormattedBalanceAfter:  s.formatDecimalBalance(balanceAfter),
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return res, nil
}

// GetMerchantWalletDetail 获取商户钱包详情
func (s *sSystemLogic) GetMerchantWalletDetail(ctx context.Context, req *v1.GetMerchantWalletDetailReq) (res *v1.GetMerchantWalletDetailRes, err error) {
	// 验证商户是否存在
	merchant, err := dao.Merchants.Ctx(ctx).Where("merchant_id", req.MerchantId).WhereNull("deleted_at").One()
	if err != nil {
		return nil, gerror.Wrap(err, "查询商户信息失败")
	}
	if merchant.IsEmpty() {
		return nil, gerror.NewCode(codes.MerchantNotFound)
	}

	// 获取钱包详情
	wallet, err := dao.MerchantWallets.GetMerchantWallet(ctx, req.MerchantId, req.Symbol)
	if err != nil {
		return nil, err
	}
	if wallet == nil {
		return nil, gerror.NewCode(codes.WalletNotFound, "钱包不存在")
	}

	// 格式化余额显示
	formattedAvailable := s.formatDecimalBalance(wallet.AvailableBalance)
	formattedFrozen := s.formatDecimalBalance(wallet.FrozenBalance)
	total := wallet.AvailableBalance.Add(wallet.FrozenBalance)
	formattedTotal := s.formatDecimalBalance(total)

	return &v1.GetMerchantWalletDetailRes{
		MerchantAssetItem: &v1.MerchantAssetItem{
			Symbol:                    wallet.Symbol,
			AvailableBalance:          wallet.AvailableBalance.IntPart(),
			FrozenBalance:             wallet.FrozenBalance.IntPart(),
			FormattedAvailableBalance: formattedAvailable,
			FormattedFrozenBalance:    formattedFrozen,
			FormattedTotalBalance:     formattedTotal,
			DecimalPlaces:             wallet.DecimalPlaces,
			LastUpdated:               wallet.UpdatedAt.Format("2006-01-02 15:04:05"),
		},
		MerchantId:   req.MerchantId,
		MerchantName: merchant["merchant_name"].String(),
		WalletId:     int64(wallet.WalletId),
		CreatedAt:    wallet.CreatedAt.Format("2006-01-02 15:04:05"),
	}, nil
}

// 工具方法

// formatDecimalBalance 格式化decimal余额显示
func (s *sSystemLogic) formatDecimalBalance(balance decimal.Decimal) string {
	return balance.String()
}

// getMerchantNameById 根据商户ID获取商户名称
func (s *sSystemLogic) getMerchantNameById(ctx context.Context, merchantId uint) string {
	merchant, err := dao.Merchants.Ctx(ctx).Where("merchant_id", merchantId).WhereNull("deleted_at").One()
	if err != nil || merchant.IsEmpty() {
		return "未知商户"
	}
	return merchant["merchant_name"].String()
}