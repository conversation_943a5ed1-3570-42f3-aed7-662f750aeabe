package payment_request

import (
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/payment_request" // 引入接口定义包
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

// paymentRequestRepository 实现了 IPaymentRequestRepository 接口
type paymentRequestRepository struct {
	// 嵌入 DAO 以便直接调用其方法，如果 DAO 方法足够原子化
	// 或者在这里只声明依赖，然后在 New 函数中初始化
}

// NewPaymentRequestRepository 创建一个新的 paymentRequestRepository 实例
func NewPaymentRequestRepository() payment_request.IPaymentRequestRepository {
	return &paymentRequestRepository{}
}

// List 获取收款请求列表 (包含关联信息) - 临时返回空数组，等待重构
func (r *paymentRequestRepository) List(ctx context.Context, page, pageSize int, condition g.Map) (list []*payment_request.PaymentRequestListItemDTO, total int, err error) {
	// 临时返回空数组
	return make([]*payment_request.PaymentRequestListItemDTO, 0), 0, nil
}

// GetByID 根据 ID 获取原始收款请求实体
func (r *paymentRequestRepository) GetByID(ctx context.Context, requestId int64) (*entity.PaymentRequests, error) {
	paymentRequest, err := dao.PaymentRequests.GetPaymentRequestById(ctx, requestId)
	if err != nil {
		// DAO 层已经包装了错误，这里可以直接返回
		return nil, err
	}
	// DAO 层约定未找到返回 nil, nil，这里保持一致
	return paymentRequest, nil
}

// GetDetailByID 根据 ID 获取收款请求详情 (包含关联信息)
func (r *paymentRequestRepository) GetDetailByID(ctx context.Context, requestId int64) (*payment_request.PaymentRequestDetailDTO, error) {
	// 使用 DAO 中已有的关联查询方法
	detailMap, err := dao.PaymentRequests.GetPaymentRequestWithDetails(ctx, requestId)
	if err != nil {
		// DAO 层已经包装了错误
		return nil, gerror.Wrap(err, "查询收款请求详情失败")
	}
	// DAO 层约定未找到返回 nil, nil
	if detailMap == nil {
		return nil, nil
	}

	// 将 map 转换为 DTO
	var detailDTO payment_request.PaymentRequestDetailDTO
	// 先转换基础实体部分
	var baseEntity entity.PaymentRequests
	if err = gconv.Struct(detailMap, &baseEntity); err != nil {
		return nil, gerror.Wrap(err, "paymentRequestRepository.GetDetailByID: 转换基础实体失败")
	}
	detailDTO.PaymentRequests = &baseEntity

	// 手动填充关联字段和处理时间格式
	// 优先使用payment_requests表中的username字段，如果为空则使用users表中的account字段
	if gconv.String(detailMap["requester_username"]) != "" {
		detailDTO.RequesterUsername = gconv.String(detailMap["requester_username"])
	} else {
		detailDTO.RequesterUsername = gconv.String(detailMap["requester_account"])
	}

	// 直接从users表获取account字段用于requesterAccount
	detailDTO.RequesterAccount = gconv.String(detailMap["requester_account"])

	if gconv.String(detailMap["payer_username"]) != "" {
		detailDTO.PayerUsername = gconv.String(detailMap["payer_username"])
	} else {
		detailDTO.PayerUsername = gconv.String(detailMap["payer_account"])
	}

	// 直接从users表获取account字段用于payerAccount
	detailDTO.PayerAccount = gconv.String(detailMap["payer_account"])

	detailDTO.TokenSymbol = gconv.String(detailMap["token_symbol"])
	detailDTO.TokenName = gconv.String(detailMap["token_name"])
	detailDTO.TransactionId = gconv.Int64(detailMap["transaction_id"])       // 注意处理 NULL
	detailDTO.TransactionStatus = gconv.Int(detailMap["transaction_status"]) // 注意处理 NULL

	// 映射发起者代理信息
	detailDTO.RequesterFirstAgentName = gconv.String(detailMap["requester_first_agent_name"])
	detailDTO.RequesterSecondAgentName = gconv.String(detailMap["requester_second_agent_name"])
	detailDTO.RequesterThirdAgentName = gconv.String(detailMap["requester_third_agent_name"])

	// 映射发起者Telegram信息
	detailDTO.RequesterTelegramId = gconv.String(detailMap["requester_telegram_id"])
	detailDTO.RequesterTelegramUsername = gconv.String(detailMap["requester_telegram_username"])
	detailDTO.RequesterFirstName = gconv.String(detailMap["requester_first_name"])

	// 映射付款人代理信息
	detailDTO.PayerFirstAgentName = gconv.String(detailMap["payer_first_agent_name"])
	detailDTO.PayerSecondAgentName = gconv.String(detailMap["payer_second_agent_name"])
	detailDTO.PayerThirdAgentName = gconv.String(detailMap["payer_third_agent_name"])

	// 映射付款人Telegram信息
	detailDTO.PayerTelegramId = gconv.String(detailMap["payer_telegram_id"])
	detailDTO.PayerTelegramUsername = gconv.String(detailMap["payer_telegram_username"])
	detailDTO.PayerFirstName = gconv.String(detailMap["payer_first_name"])

	// 处理交易时间
	if txTime, ok := detailMap["transaction_time"].(*gtime.Time); ok && txTime != nil {
		detailDTO.TransactionTime = txTime.String()
	} else if txTimeStr, ok := detailMap["transaction_time"].(string); ok {
		detailDTO.TransactionTime = txTimeStr
	} else {
		detailDTO.TransactionTime = "" // 处理 NULL 情况
	}

	// 填充 StatusText (这部分逻辑应该在 Service 层处理，但暂时放在这里)
	// detailDTO.StatusText = r.getStatusText(detailDTO.Status)

	return &detailDTO, nil
}

// UpdateStatus 更新收款请求状态
func (r *paymentRequestRepository) UpdateStatus(ctx context.Context, requestId int64, status int) error {
	// 直接调用 DAO 的更新方法
	err := dao.PaymentRequests.UpdatePaymentRequestStatus(ctx, requestId, status)
	if err != nil {
		// DAO 层已包装错误
		return err
	}
	return nil
}

// // getStatusText 是一个辅助函数，用于将状态码转换为文本描述
// // 注意：这个函数更适合放在 Service 层或 Util 层
// func (r *paymentRequestRepository) getStatusText(status int) string {
// 	switch status {
// 	case 1: // consts.PaymentRequestStatusPending:
// 		return "待支付"
// 	case 2: // consts.PaymentRequestStatusPaid:
// 		return "已支付"
// 	case 3: // consts.PaymentRequestStatusExpired:
// 		return "已过期"
// 	case 4: // consts.PaymentRequestStatusCancelled:
// 		return "已取消"
// 	default:
// 		return "未知状态"
// 	}
// }
