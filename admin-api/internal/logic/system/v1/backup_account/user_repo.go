package backup_account

import (
	"admin-api/internal/codes" // Add import for error codes
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/backup_account" // Import the interface package
	"context"
	"database/sql"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"  // Add for g.Map
	"github.com/gogf/gf/v2/os/gtime" // Add for UpdatedAt
)

// userRepository implements the IUserRepository interface defined in backup_account service.
type userRepository struct{}

// NewUserRepository creates and returns a new instance of userRepository.
func NewUserRepository() backup_account.IUserRepository {
	return &userRepository{}
}

// GetByID retrieves a user by their ID.
func (r *userRepository) GetByID(ctx context.Context, id uint) (*entity.Users, error) {
	var user *entity.Users
	err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().Id, id).Scan(&user)
	if err != nil {
		if err == sql.ErrNoRows {
			// Return a specific error code for not found
			return nil, gerror.NewCodef(codes.CodeUserNotFound, "用户不存在 (ID: %d)", id)
		}
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询用户失败 (ID: %d)", id)
	}
	// No need to check for nil after Scan if no error and not ErrNoRows
	return user, nil
}

// GetByIDs retrieves multiple users by their IDs, returning a map.
func (r *userRepository) GetByIDs(ctx context.Context, ids []uint) (map[uint]*entity.Users, error) {
	userMap := make(map[uint]*entity.Users)
	if len(ids) == 0 {
		return userMap, nil
	}
	var users []*entity.Users
	err := dao.Users.Ctx(ctx).WhereIn(dao.Users.Columns().Id, ids).Scan(&users)
	if err != nil {
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "批量查询用户失败 (IDs: %v)", ids)
	}
	for _, user := range users {
		if user != nil { // Add nil check for safety
			userMap[uint(user.Id)] = user // Convert uint64 to uint for map key
		}
	}
	return userMap, nil
}

// UpdatePassword updates the user's password hash.
func (r *userRepository) UpdatePassword(ctx context.Context, id uint, passwordHash string) error {
	_, err := dao.Users.Ctx(ctx).Data(g.Map{
		dao.Users.Columns().Password:  passwordHash,
		dao.Users.Columns().UpdatedAt: gtime.Now(),
	}).Where(dao.Users.Columns().Id, id).Update()
	if err != nil {
		return gerror.WrapCodef(codes.CodeInternalError, err, "更新用户密码失败 (ID: %d)", id)
	}
	return nil
}

// ResetGoogleSecret resets the user's Google Authenticator secret.
func (r *userRepository) ResetGoogleSecret(ctx context.Context, id uint) error {
	_, err := dao.Users.Ctx(ctx).Data(g.Map{
		dao.Users.Columns().Google2FaSecret:  nil, // Set secret to nil or empty string
		dao.Users.Columns().Google2FaEnabled: 0,   // Disable 2FA
		dao.Users.Columns().UpdatedAt:        gtime.Now(),
	}).Where(dao.Users.Columns().Id, id).Update()
	if err != nil {
		return gerror.WrapCodef(codes.CodeInternalError, err, "重置用户 Google Secret 失败 (ID: %d)", id)
	}
	return nil
}

// ExistsByID checks if a user exists by ID.
func (r *userRepository) ExistsByID(ctx context.Context, id uint) (bool, error) {
	count, err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().Id, id).Count()
	if err != nil {
		return false, gerror.WrapCodef(codes.CodeInternalError, err, "检查用户是否存在失败 (ID: %d)", id)
	}
	return count > 0, nil
}
