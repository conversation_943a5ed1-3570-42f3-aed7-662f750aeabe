package backup_account

import (
	v1 "admin-api/api/system/v1" // Import v1 for request struct usage
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/backup_account" // Import the interface package
	"context"
	"database/sql"
	"strings"

	// "github.com/gogf/gf/v2/database/gdb" // Removed as unused
	"github.com/gogf/gf/v2/errors/gerror"

	// "github.com/gogf/gf/v2/frame/g" // Removed as unused
	"github.com/gogf/gf/v2/os/gtime"
)

// backupAccountRepository implements the IBackupAccountRepository interface.
type backupAccountRepository struct{}

// NewBackupAccountRepository creates and returns a new instance of backupAccountRepository.
func NewBackupAccountRepository() backup_account.IBackupAccountRepository {
	return &backupAccountRepository{}
}

// ListByUserID retrieves a paginated list of backup accounts for a specific user.
func (r *backupAccountRepository) ListByUserID(ctx context.Context, userId int64, req *v1.GetUserBackupAccountsReq) ([]*entity.UserBackupAccounts, int, error) {
	m := dao.UserBackupAccounts.Ctx(ctx).
		Where(dao.UserBackupAccounts.Columns().UserId, userId).
		WhereNull(dao.UserBackupAccounts.Columns().DeletedAt)

		// Apply filters from request
	if req.TelegramUsername != "" {
		m = m.WhereLike(dao.UserBackupAccounts.Columns().TelegramUsername, "%"+req.TelegramUsername+"%") // Use TelegramUsername instead of AccountValue
	}

	if req.TelegramId > 0 {
		m = m.Where(dao.UserBackupAccounts.Columns().TelegramId, req.TelegramId)
	}

	// Note: IsVerified filtering removed as part of verification system removal

	if req.DateRange != "" {
		parts := strings.Split(req.DateRange, ",")
		if len(parts) == 2 {
			startTime := gtime.NewFromStr(parts[0] + " 00:00:00")
			endTime := gtime.NewFromStr(parts[1] + " 23:59:59")
			if startTime != nil && endTime != nil {
				m = m.WhereBetween(dao.UserBackupAccounts.Columns().CreatedAt, startTime, endTime)
			}
		}
	}

	total, err := m.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "Failed to count backup accounts")
	}

	if req.Page > 0 && req.PageSize > 0 {
		m = m.Page(req.Page, req.PageSize)
	}
	// Add default sorting if needed, e.g., by creation time
	m = m.OrderDesc(dao.UserBackupAccounts.Columns().CreatedAt)

	var results []*entity.UserBackupAccounts
	if err := m.Scan(&results); err != nil {
		return nil, 0, gerror.Wrap(err, "Failed to retrieve backup accounts")
	}

	return results, total, nil
}

// Create adds a new backup account record.
func (r *backupAccountRepository) Create(ctx context.Context, account *entity.UserBackupAccounts) (int64, error) {
	// Ensure CreatedAt and UpdatedAt are set if not already
	now := gtime.Now()
	if account.CreatedAt == nil {
		account.CreatedAt = now
	}
	if account.UpdatedAt == nil {
		account.UpdatedAt = now
	}

	result, err := dao.UserBackupAccounts.Ctx(ctx).Data(account).Insert()
	if err != nil {
		return 0, gerror.Wrap(err, "Failed to create backup account")
	}
	newId, err := result.LastInsertId()
	if err != nil {
		return 0, gerror.Wrap(err, "Failed to get last insert ID for backup account")
	}
	return newId, nil
}

// GetByID retrieves a single backup account by its ID.
func (r *backupAccountRepository) GetByID(ctx context.Context, id int64) (*entity.UserBackupAccounts, error) {
	var account *entity.UserBackupAccounts
	err := dao.UserBackupAccounts.Ctx(ctx).
		Where(dao.UserBackupAccounts.Columns().BackupAccountId, id).
		WhereNull(dao.UserBackupAccounts.Columns().DeletedAt).
		Scan(&account) // Correct column name: BackupAccountId
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // Not found is not an error here
		}
		return nil, gerror.Wrapf(err, "Failed to get backup account by ID: %d", id)
	}
	if account == nil {
		return nil, nil // Ensure nil is returned if Scan succeeds but result is nil
	}
	return account, nil
}

// UpdateVerifiedStatus updates the verification status of a backup account.
func (r *backupAccountRepository) UpdateVerifiedStatus(ctx context.Context, id int64, isVerified int) error {
	// If isVerified is 1, set VerifiedAt to current time, otherwise set to null
	data := &do.UserBackupAccounts{
		UpdatedAt: gtime.Now(),
	}

	if isVerified == 1 {
		data.VerifiedAt = gtime.Now()
	} else {
		// We need to set VerifiedAt to null, but do.UserBackupAccounts doesn't support nil directly
		// We'll use Data map approach instead
		_, err := dao.UserBackupAccounts.Ctx(ctx).
			Data(map[string]interface{}{"verified_at": nil, "updated_at": gtime.Now()}).
			Where(dao.UserBackupAccounts.Columns().BackupAccountId, id).
			Update()
		return err
	}

	_, err := dao.UserBackupAccounts.Ctx(ctx).
		Data(data).
		Where(dao.UserBackupAccounts.Columns().BackupAccountId, id).
		Update()

	if err != nil {
		return gerror.Wrapf(err, "Failed to update verification status for backup account ID: %d", id)
	}
	return nil
}

// DeleteByID deletes a backup account by its ID. (Hard delete)
func (r *backupAccountRepository) DeleteByID(ctx context.Context, id int64) error {
	_, err := dao.UserBackupAccounts.Ctx(ctx).Where(dao.UserBackupAccounts.Columns().BackupAccountId, id).Delete() // Correct column name: BackupAccountId
	if err != nil {
		return gerror.Wrapf(err, "Failed to delete backup account ID: %d", id)
	}
	return nil
}

// FindByAccount checks if a specific account (TelegramUsername) already exists for any user.
func (r *backupAccountRepository) FindByAccount(ctx context.Context, telegramUsername string) (*entity.UserBackupAccounts, error) {
	var acc *entity.UserBackupAccounts
	err := dao.UserBackupAccounts.Ctx(ctx).
		Where(dao.UserBackupAccounts.Columns().TelegramUsername, telegramUsername).
		WhereNull(dao.UserBackupAccounts.Columns().DeletedAt).
		Scan(&acc)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, gerror.Wrapf(err, "Failed to find backup account with username '%s'", telegramUsername)
	}
	return acc, nil
}

// FindByUserIDAndAccount checks if a specific account exists for a specific user.
func (r *backupAccountRepository) FindByUserIDAndAccount(ctx context.Context, userId int64, telegramUsername string) (*entity.UserBackupAccounts, error) {
	var acc *entity.UserBackupAccounts
	err := dao.UserBackupAccounts.Ctx(ctx).
		Where(dao.UserBackupAccounts.Columns().UserId, userId).
		Where(dao.UserBackupAccounts.Columns().TelegramUsername, telegramUsername).
		WhereNull(dao.UserBackupAccounts.Columns().DeletedAt).
		Scan(&acc)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, gerror.Wrapf(err, "Failed to find backup account for user %d with username '%s'", userId, telegramUsername)
	}
	return acc, nil
}

// FindByTelegramId checks if a specific TelegramId already exists.
func (r *backupAccountRepository) FindByTelegramId(ctx context.Context, telegramId int64) (*entity.UserBackupAccounts, error) {
	var acc *entity.UserBackupAccounts
	err := dao.UserBackupAccounts.Ctx(ctx).
		Where(dao.UserBackupAccounts.Columns().TelegramId, telegramId).
		WhereNull(dao.UserBackupAccounts.Columns().DeletedAt).
		Scan(&acc)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, gerror.Wrapf(err, "Failed to find backup account with TelegramId '%d'", telegramId)
	}
	return acc, nil
}

// List retrieves a paginated list of all backup accounts with optional filtering.
func (r *backupAccountRepository) List(ctx context.Context, req *v1.GetBackupAccountsReq) ([]*entity.UserBackupAccounts, int, error) {
	m := dao.UserBackupAccounts.Ctx(ctx).
		WhereNull(dao.UserBackupAccounts.Columns().DeletedAt)

	// Apply filters from request
	if req.TelegramUsername != "" {
		m = m.WhereLike(dao.UserBackupAccounts.Columns().TelegramUsername, "%"+req.TelegramUsername+"%")
	}

	if req.TelegramId > 0 {
		m = m.Where(dao.UserBackupAccounts.Columns().TelegramId, req.TelegramId)
	}

	if req.UserId > 0 {
		m = m.Where(dao.UserBackupAccounts.Columns().UserId, req.UserId)
	}

	// Note: IsVerified filtering removed as part of verification system removal

	if req.DateRange != "" {
		parts := strings.Split(req.DateRange, ",")
		if len(parts) == 2 {
			startTime := gtime.NewFromStr(parts[0] + " 00:00:00")
			endTime := gtime.NewFromStr(parts[1] + " 23:59:59")
			if startTime != nil && endTime != nil {
				m = m.WhereBetween(dao.UserBackupAccounts.Columns().CreatedAt, startTime, endTime)
			}
		}
	}

	total, err := m.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "Failed to count backup accounts")
	}

	if req.Page > 0 && req.PageSize > 0 {
		m = m.Page(req.Page, req.PageSize)
	}

	// Add default sorting by creation time
	m = m.OrderDesc(dao.UserBackupAccounts.Columns().CreatedAt)

	var results []*entity.UserBackupAccounts
	if err := m.Scan(&results); err != nil {
		return nil, 0, gerror.Wrap(err, "Failed to retrieve backup accounts")
	}

	return results, total, nil
}
