package dashboard

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/consts"
	"admin-api/internal/dao"
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// dashboardRepository implements the IDashboardRepository interface.
type dashboardRepository struct{}

// NewDashboardRepository creates a new dashboard repository instance.
func NewDashboardRepository() *dashboardRepository {
	return &dashboardRepository{}
}

// GetUserStats retrieves user-related statistics.
func (r *dashboardRepository) GetUserStats(ctx context.Context) (*v1.UserStats, error) {
	stats := &v1.UserStats{}

	// Get total user count
	totalCount, err := dao.Users.Ctx(ctx).WhereNull(dao.Users.Columns().DeletedAt).Count()
	if err != nil {
		return nil, gerror.Wrap(err, "获取用户总数失败")
	}
	stats.TotalCount = totalCount

	// Get today's login count
	today := gtime.Now().Format("Y-m-d")
	todayStart := today + " 00:00:00"
	todayEnd := today + " 23:59:59"

	todayLoginCount, err := dao.Users.Ctx(ctx).
		WhereNull(dao.Users.Columns().DeletedAt).
		Where(dao.Users.Columns().LastLoginTime+" BETWEEN ? AND ?", todayStart, todayEnd).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "获取今日登录用户数失败")
	}
	stats.TodayLoginCount = todayLoginCount

	// Get today's registration count
	todayRegCount, err := dao.Users.Ctx(ctx).
		WhereNull(dao.Users.Columns().DeletedAt).
		Where(dao.Users.Columns().CreatedAt+" BETWEEN ? AND ?", todayStart, todayEnd).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "获取今日注册用户数失败")
	}
	stats.TodayRegCount = todayRegCount

	return stats, nil
}

// GetDepositStats retrieves token deposit statistics.
func (r *dashboardRepository) GetDepositStats(ctx context.Context) (*v1.TokenAmountStats, error) {
	stats := &v1.TokenAmountStats{
		Total: make([]v1.TokenAmount, 0),
		Today: make([]v1.TokenAmount, 0),
	}

	// Get total deposit amount by token
	totalSql := `
		SELECT name, SUM(amount) as amount
		FROM user_recharges
		WHERE state = 2
		GROUP BY name
	`
	err := g.DB().Ctx(ctx).Raw(totalSql).Scan(&stats.Total)
	if err != nil {
		return nil, gerror.Wrap(err, "获取充值累计金额失败")
	}

	// Get today's deposit amount by token
	today := gtime.Now().Format("Y-m-d")
	todaySql := `
		SELECT name, SUM(amount) as amount
		FROM user_recharges
		WHERE state = 2
		AND DATE(created_at) = ?
		GROUP BY name
	`
	err = g.DB().Ctx(ctx).Raw(todaySql, today).Scan(&stats.Today)
	if err != nil {
		return nil, gerror.Wrap(err, "获取今日充值金额失败")
	}

	return stats, nil
}

// GetWithdrawalStats retrieves token withdrawal statistics.
func (r *dashboardRepository) GetWithdrawalStats(ctx context.Context) (*v1.WithdrawalStats, error) {
	stats := &v1.WithdrawalStats{
		TokenAmountStats: v1.TokenAmountStats{
			Total: make([]v1.TokenAmount, 0),
			Today: make([]v1.TokenAmount, 0),
		},
		PendingReview: make([]v1.TokenAmount, 0),
	}

	// Get total withdrawal amount by token
	totalSql := `
		SELECT name, SUM(amount) as amount
		FROM user_withdraws
		WHERE (state = 4 or state = 1 or state = 2)
		GROUP BY name
	`
	err := g.DB().Ctx(ctx).Raw(totalSql).Scan(&stats.Total)
	if err != nil {
		return nil, gerror.Wrap(err, "获取提现累计金额失败")
	}

	// Get today's withdrawal amount by token
	today := gtime.Now().Format("Y-m-d")
	todaySql := `
		SELECT name, SUM(amount) as amount
		FROM user_withdraws
		WHERE (state = 4 or state = 1 or state = 2)
		AND DATE(created_at) = ?
		GROUP BY name
	`
	err = g.DB().Ctx(ctx).Raw(todaySql, today).Scan(&stats.Today)
	if err != nil {
		return nil, gerror.Wrap(err, "获取今日提现金额失败")
	}

	// Get pending review withdrawal amount by token
	pendingSql := `
		SELECT name, SUM(amount) as amount
		FROM user_withdraws
		WHERE (state = 1 or state = 2)
		GROUP BY name
	`
	err = g.DB().Ctx(ctx).Raw(pendingSql).Scan(&stats.PendingReview)
	if err != nil {
		return nil, gerror.Wrap(err, "获取待审核提现金额失败")
	}

	return stats, nil
}

// GetTransferStats retrieves transfer statistics.
func (r *dashboardRepository) GetTransferStats(ctx context.Context) (*v1.TransferStats, error) {
	stats := &v1.TransferStats{
		TokenAmountStats: v1.TokenAmountStats{
			Total: make([]v1.TokenAmount, 0),
			Today: make([]v1.TokenAmount, 0),
		},
		PendingCollection: make([]v1.TokenAmount, 0),
	}

	// Get total transfer amount by token with decimal conversion
	totalSql := `
		SELECT 
			t.symbol as name, 
			SUM(tr.amount) / POWER(10, t.decimals) as amount
		FROM transfers tr
		INNER JOIN tokens t ON tr.symbol = t.symbol
		WHERE (tr.status = 'completed' or tr.status = 'pending_collection')
		GROUP BY t.symbol, t.decimals
	`
	err := g.DB().Ctx(ctx).Raw(totalSql).Scan(&stats.Total)
	if err != nil {
		return nil, gerror.Wrap(err, "获取转账累计金额失败")
	}

	// Get today's transfer amount by token with decimal conversion
	today := gtime.Now().Format("Y-m-d")
	todaySql := `
		SELECT 
			t.symbol as name, 
			SUM(tr.amount) / POWER(10, t.decimals) as amount
		FROM transfers tr
		INNER JOIN tokens t ON tr.symbol = t.symbol
		WHERE (tr.status = 'completed' or tr.status = 'pending_collection')
		AND DATE(tr.created_at) = ?
		GROUP BY t.symbol, t.decimals
	`
	err = g.DB().Ctx(ctx).Raw(todaySql, today).Scan(&stats.Today)
	if err != nil {
		return nil, gerror.Wrap(err, "获取今日转账金额失败")
	}

	// Get pending collection transfer amount by token with decimal conversion
	pendingSql := `
		SELECT 
			t.symbol as name, 
			SUM(tr.amount) / POWER(10, t.decimals) as amount
		FROM transfers tr
		INNER JOIN tokens t ON tr.symbol = t.symbol
		WHERE tr.status = 'pending_collection'
		GROUP BY t.symbol, t.decimals
	`
	err = g.DB().Ctx(ctx).Raw(pendingSql).Scan(&stats.PendingCollection)
	if err != nil {
		return nil, gerror.Wrap(err, "获取待领取转账金额失败")
	}

	return stats, nil
}

// GetPaymentStats retrieves payment statistics.
func (r *dashboardRepository) GetPaymentStats(ctx context.Context) (*v1.TokenAmountStats, error) {
	stats := &v1.TokenAmountStats{
		Total: make([]v1.TokenAmount, 0),
		Today: make([]v1.TokenAmount, 0),
	}

	// Get total payment amount by token
	totalSql := `
		SELECT t.symbol as name, SUM(pr.amount) as amount
		FROM payment_requests pr
		LEFT JOIN tokens t ON pr.token_id = t.token_id
		WHERE pr.status = ? 
		GROUP BY t.symbol
	`
	err := g.DB().Ctx(ctx).Raw(totalSql, consts.PaymentRequestStatusPaid).Scan(&stats.Total)
	if err != nil {
		return nil, gerror.Wrap(err, "获取收款累计金额失败")
	}

	// Get today's payment amount by token
	today := gtime.Now().Format("Y-m-d")
	todaySql := `
		SELECT t.symbol as name, SUM(pr.amount) as amount
		FROM payment_requests pr
		LEFT JOIN tokens t ON pr.token_id = t.token_id
		WHERE pr.status = ?
		AND DATE(pr.created_at) = ?
		GROUP BY t.symbol
	`
	err = g.DB().Ctx(ctx).Raw(todaySql, consts.PaymentRequestStatusPaid, today).Scan(&stats.Today)
	if err != nil {
		return nil, gerror.Wrap(err, "获取今日收款金额失败")
	}

	return stats, nil
}

// GetRedPacketStats retrieves red packet statistics by token.
func (r *dashboardRepository) GetRedPacketStats(ctx context.Context) (*v1.RedPacketStats, error) {
	stats := &v1.RedPacketStats{
		Total:             make([]v1.TokenAmount, 0),
		Today:             make([]v1.TokenAmount, 0),
		PendingCollection: make([]v1.TokenAmount, 0),
	}

	// Get total red packet amount by token
	totalSql := `
		SELECT symbol as name, SUM(total_amount) as amount
		FROM red_packets
		WHERE deleted_at IS NULL
		GROUP BY symbol
	`
	err := g.DB().Ctx(ctx).Raw(totalSql).Scan(&stats.Total)
	if err != nil {
		return nil, gerror.Wrap(err, "获取红包累计金额失败")
	}

	// Get today's red packet amount by token
	today := gtime.Now().Format("Y-m-d")
	todaySql := `
		SELECT symbol as name, SUM(total_amount) as amount
		FROM red_packets
		WHERE deleted_at IS NULL
		AND DATE(created_at) = ?
		GROUP BY symbol
	`
	err = g.DB().Ctx(ctx).Raw(todaySql, today).Scan(&stats.Today)
	if err != nil {
		return nil, gerror.Wrap(err, "获取今日红包金额失败")
	}

	// Get pending collection red packet amount by token
	pendingSql := `
		SELECT symbol as name, SUM(remaining_amount) as amount
		FROM red_packets
		WHERE deleted_at IS NULL
		AND status = 'active'
		GROUP BY symbol
	`
	err = g.DB().Ctx(ctx).Raw(pendingSql).Scan(&stats.PendingCollection)
	if err != nil {
		return nil, gerror.Wrap(err, "获取待领取红包金额失败")
	}

	return stats, nil
}
