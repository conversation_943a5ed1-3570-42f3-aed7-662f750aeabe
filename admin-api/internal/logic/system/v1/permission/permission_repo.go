package permission

import (
	"admin-api/internal/codes"
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/permission" // Import the interface package
	"context"
	"database/sql"
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

type permissionRepository struct{}

// NewPermissionRepository creates and returns a new instance of IPermissionRepository.
func NewPermissionRepository() permission.IPermissionRepository {
	return &permissionRepository{}
}

// List retrieves a flat list of admin permissions based on conditions.
func (r *permissionRepository) List(ctx context.Context, condition g.Map) ([]*entity.AdminPermissions, error) {
	m := dao.AdminPermissions.Ctx(ctx).WhereNull(dao.AdminPermissions.Columns().DeletedAt)
	if len(condition) > 0 {
		m = m.Where(condition)
	}
	m = m.OrderAsc(dao.AdminPermissions.Columns().Sort).OrderAsc(dao.AdminPermissions.Columns().Id)

	var permissions []*entity.AdminPermissions
	err := m.Scan(&permissions)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询权限列表失败")
	}
	return permissions, nil
}

// ListPaginated retrieves a paginated list of admin permissions based on conditions.
func (r *permissionRepository) ListPaginated(ctx context.Context, page, pageSize int, condition g.Map) ([]*entity.AdminPermissions, int, error) {
	m := dao.AdminPermissions.Ctx(ctx).WhereNull(dao.AdminPermissions.Columns().DeletedAt)
	if len(condition) > 0 {
		m = m.Where(condition)
	}

	total, err := m.Count()
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "获取权限总数失败")
	}

	if total == 0 {
		return make([]*entity.AdminPermissions, 0), 0, nil
	}

	m = m.OrderAsc(dao.AdminPermissions.Columns().Sort).OrderAsc(dao.AdminPermissions.Columns().Id)

	var permissions []*entity.AdminPermissions
	err = m.Page(page, pageSize).Scan(&permissions)
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询权限列表失败")
	}

	return permissions, total, nil
}

// Create inserts a new permission record (without handling Tree/Level) within a transaction.
func (r *permissionRepository) Create(ctx context.Context, tx gdb.TX, permissionDo *do.AdminPermissions) (id int64, err error) {
	if permissionDo.CreatedAt == nil {
		permissionDo.CreatedAt = gtime.Now()
	}
	if permissionDo.UpdatedAt == nil {
		permissionDo.UpdatedAt = gtime.Now()
	}
	// Use the provided transaction if tx is not nil, otherwise use Ctx(ctx)
	model := dao.AdminPermissions.Ctx(ctx)
	if tx != nil {
		model = model.TX(tx)
	}
	result, err := model.Data(permissionDo).Insert()
	if err != nil {
		g.Log().Errorf(ctx, "创建权限失败: %s 具体请求数据: %+v", err.Error(), permissionDo)
		// TODO: Consider adding a specific error code like CodePermissionCreateFailed
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "创建权限失败")
	}
	id, err = result.LastInsertId()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "获取新权限ID失败")
	}
	return id, nil
}

// Update updates permission fields within a transaction.
func (r *permissionRepository) Update(ctx context.Context, tx gdb.TX, id int64, data g.Map) error {
	if len(data) == 0 {
		return nil
	}
	data[dao.AdminPermissions.Columns().UpdatedAt] = gtime.Now()

	model := dao.AdminPermissions.Ctx(ctx)
	if tx != nil {
		model = model.TX(tx)
	}
	_, err := model.Data(data).
		Where(dao.AdminPermissions.Columns().Id, id).
		Update()
	if err != nil {
		// TODO: Consider adding a specific error code like CodePermissionUpdateFailed
		return gerror.WrapCodef(codes.CodeInternalError, err, "更新权限失败 (ID: %d)", id)
	}
	return nil
}

// Delete performs a soft delete on a permission within a transaction.
func (r *permissionRepository) Delete(ctx context.Context, tx gdb.TX, id int64) error {
	model := dao.AdminPermissions.Ctx(ctx)
	if tx != nil {
		model = model.TX(tx)
	}
	_, err := model.Data(g.Map{dao.AdminPermissions.Columns().DeletedAt: gtime.Now()}).
		Where(dao.AdminPermissions.Columns().Id, id).
		WhereNull(dao.AdminPermissions.Columns().DeletedAt).
		Update()
	if err != nil && err != sql.ErrNoRows {
		// TODO: Consider adding a specific error code like CodePermissionDeleteFailed
		return gerror.WrapCodef(codes.CodeInternalError, err, "软删除权限失败 (ID: %d)", id)
	}
	return nil
}

// GetByID retrieves a single admin permission by ID, optionally within a transaction.
func (r *permissionRepository) GetByID(ctx context.Context, tx gdb.TX, id int64) (*entity.AdminPermissions, error) {
	if id <= 0 {
		return nil, gerror.NewCodef(codes.CodeInvalidParameter, "无效的权限ID: %d", id)
	}

	var permission *entity.AdminPermissions
	model := dao.AdminPermissions.Ctx(ctx)
	if tx != nil {
		model = model.TX(tx)
	}
	err := model.
		Where(dao.AdminPermissions.Columns().Id, id).
		WhereNull(dao.AdminPermissions.Columns().DeletedAt).
		Scan(&permission)
	if err != nil {
		if err == sql.ErrNoRows {
			// TODO: Consider adding a specific error code like CodePermissionNotFound
			return nil, gerror.NewCodef(codes.CodeNotFound, "权限不存在 (ID: %d)", id) // Using generic NotFound for now
		}
		g.Log().Errorf(ctx, "查询权限失败 (ID: %d): %+v", id, err)
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询权限失败 (ID: %d)", id)
	}
	if permission == nil {
		g.Log().Warningf(ctx, "查询到空的权限 (ID: %d)", id)
		// TODO: Consider adding a specific error code like CodePermissionNotFound
		return nil, gerror.NewCodef(codes.CodeNotFound, "权限不存在 (ID: %d)", id) // Using generic NotFound for now
	}
	return permission, nil
}

// ExistsByKey checks if a permission with the same key exists, excluding a specific ID, optionally within a transaction.
func (r *permissionRepository) ExistsByKey(ctx context.Context, tx gdb.TX, key string, excludeId ...int64) (bool, error) {
	model := dao.AdminPermissions.Ctx(ctx)
	if tx != nil {
		model = model.TX(tx)
	}
	m := model.
		Where(dao.AdminPermissions.Columns().Key, key).
		WhereNull(dao.AdminPermissions.Columns().DeletedAt)
	if len(excludeId) > 0 {
		m = m.WhereNot(dao.AdminPermissions.Columns().Id, excludeId[0])
	}
	count, err := m.Count()
	if err != nil {
		// TODO: Consider adding a specific error code like CodePermissionKeyCheckFailed
		return false, gerror.WrapCodef(codes.CodeInternalError, err, "检查权限标识Key是否存在失败 (Key: %s)", key)
	}
	return count > 0, nil
}

// ExistsByNameAndPid checks if a permission with the same name exists under the same parent, excluding a specific ID, optionally within a transaction.
func (r *permissionRepository) ExistsByNameAndPid(ctx context.Context, tx gdb.TX, name string, pid int64, excludeId ...int64) (bool, error) {
	model := dao.AdminPermissions.Ctx(ctx)
	if tx != nil {
		model = model.TX(tx)
	}
	m := model.
		Where(dao.AdminPermissions.Columns().Name, name).
		Where(dao.AdminPermissions.Columns().ParentKey, pid). // Assuming ParentKey stores pid as string if pid is int64
		WhereNull(dao.AdminPermissions.Columns().DeletedAt)
	if len(excludeId) > 0 {
		m = m.WhereNot(dao.AdminPermissions.Columns().Id, excludeId[0])
	}
	count, err := m.Count()
	if err != nil {
		// TODO: Consider adding a specific error code like CodePermissionNameCheckFailed
		return false, gerror.WrapCodef(codes.CodeInternalError, err, "检查同级权限名称是否存在失败 (Name: %s, Pid: %d)", name, pid)
	}
	return count > 0, nil
}

// HasDirectChildren checks if a permission has any direct children.
func (r *permissionRepository) HasDirectChildren(ctx context.Context, id int64) (bool, error) {
	count, err := dao.AdminPermissions.Ctx(ctx).
		Where(dao.AdminPermissions.Columns().ParentKey, id).
		WhereNull(dao.AdminPermissions.Columns().DeletedAt).
		Count()
	if err != nil {
		// TODO: Consider adding a specific error code like CodePermissionChildCheckFailed
		return false, gerror.WrapCodef(codes.CodeInternalError, err, "检查子权限是否存在失败 (ID: %d)", id)
	}
	return count > 0, nil
}

// FindDescendantsByTree finds all descendants of a permission based on the tree path prefix.
func (r *permissionRepository) FindDescendantsByTree(ctx context.Context, treePrefix string) ([]*entity.AdminPermissions, error) {
	// Ensure the prefix format is correct, e.g., ",0,1,"
	if !strings.HasPrefix(treePrefix, ",") || !strings.HasSuffix(treePrefix, ",") {
		// Or handle this error more gracefully depending on requirements
		g.Log().Warningf(ctx, "FindDescendantsByTree called with potentially incorrect treePrefix format: %s", treePrefix)
		// return nil, gerror.Newf("Invalid treePrefix format: %s", treePrefix)
	}

	var descendants []*entity.AdminPermissions
	// Use WhereLike for prefix matching, e.g., Tree LIKE '%,0,1,%'
	// Exclude the node itself by ensuring the found tree path is longer
	err := dao.AdminPermissions.Ctx(ctx).
		WhereLike(dao.AdminPermissions.Columns().Tree, treePrefix+"%"). // Find paths starting with the prefix
		Where("LENGTH(tree) > ?", len(treePrefix)).                     // Ensure it's a descendant, using LENGTH function
		WhereNull(dao.AdminPermissions.Columns().DeletedAt).
		Scan(&descendants)

	if err != nil {
		// TODO: Consider adding a specific error code like CodePermissionDescendantFindFailed
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询子孙权限失败 (TreePrefix: %s)", treePrefix)
	}
	return descendants, nil
}

// BatchUpdateLevelTree updates the level and tree path for multiple permissions within a transaction.
func (r *permissionRepository) BatchUpdateLevelTree(ctx context.Context, tx gdb.TX, updates map[int64]g.Map) error {
	if len(updates) == 0 {
		return nil
	}
	for id, data := range updates {
		// Ensure 'level' and 'tree' keys exist and have correct types if needed,
		// although g.Map is flexible. Add validation if strict typing is required.
		data[dao.AdminPermissions.Columns().UpdatedAt] = gtime.Now()

		modelInLoop := dao.AdminPermissions.Ctx(ctx)
		if tx != nil {
			modelInLoop = modelInLoop.TX(tx)
		}
		_, err := modelInLoop.Data(data).
			Where(dao.AdminPermissions.Columns().Id, id).
			Update()
		if err != nil {
			// TODO: Consider adding a specific error code like CodePermissionBatchUpdateFailed
			return gerror.WrapCodef(codes.CodeInternalError, err, "批量更新权限 Level/Tree 失败 (ID: %d)", id)
		}
	}
	return nil
}

// GetByKey retrieves a single admin permission by Key, optionally within a transaction.
func (r *permissionRepository) GetByKey(ctx context.Context, tx gdb.TX, key string) (*entity.AdminPermissions, error) {
	if key == "" {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "权限 Key 无效")
	}

	var permission *entity.AdminPermissions
	model := dao.AdminPermissions.Ctx(ctx)
	if tx != nil {
		model = model.TX(tx)
	}
	err := model.
		Where(dao.AdminPermissions.Columns().Key, key).
		WhereNull(dao.AdminPermissions.Columns().DeletedAt).
		Scan(&permission)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCodef(codes.CodeNotFound, "权限不存在 (Key: %s)", key)
		}
		g.Log().Errorf(ctx, "查询权限失败 (Key: %s): %+v", key, err)
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询权限失败 (Key: %s)", key)
	}
	if permission == nil {
		g.Log().Debugf(ctx, "查询到空的权限 (Key: %s)", key)
		return nil, gerror.NewCodef(codes.CodeNotFound, "权限不存在 (Key: %s)", key)
	}
	return permission, nil
}

// FindByType retrieves all permissions of a specific type.
func (r *permissionRepository) FindByType(ctx context.Context, tx gdb.TX, permType string) ([]*entity.AdminPermissions, error) {
	model := dao.AdminPermissions.Ctx(ctx)
	if tx != nil {
		model = model.TX(tx)
	}

	var permissions []*entity.AdminPermissions
	err := model.
		Where(dao.AdminPermissions.Columns().Type, permType).
		WhereNull(dao.AdminPermissions.Columns().DeletedAt).
		OrderAsc(dao.AdminPermissions.Columns().Sort).
		OrderAsc(dao.AdminPermissions.Columns().Id).
		Scan(&permissions)

	if err != nil {
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询指定类型的权限失败 (Type: %s)", permType)
	}

	return permissions, nil
}

// FindByKeyPrefix retrieves all permissions with keys starting with the given prefix.
func (r *permissionRepository) FindByKeyPrefix(ctx context.Context, tx gdb.TX, keyPrefix string) ([]*entity.AdminPermissions, error) {
	model := dao.AdminPermissions.Ctx(ctx)
	if tx != nil {
		model = model.TX(tx)
	}

	var permissions []*entity.AdminPermissions
	err := model.
		WhereLike(dao.AdminPermissions.Columns().Key, keyPrefix+"%").
		WhereNull(dao.AdminPermissions.Columns().DeletedAt).
		OrderAsc(dao.AdminPermissions.Columns().Sort).
		OrderAsc(dao.AdminPermissions.Columns().Id).
		Scan(&permissions)

	if err != nil {
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询指定前缀的权限失败 (Prefix: %s)", keyPrefix)
	}

	return permissions, nil
}
