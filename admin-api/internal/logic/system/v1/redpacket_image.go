package v1

import (
	"context"
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/utility/excel"
)

// ReviewRedPacketImage 审核红包封面图片
func (s *sSystemLogic) ReviewRedPacketImage(ctx context.Context, req *v1.ReviewRedPacketImageReq) (res *v1.ReviewRedPacketImageRes, err error) {
	res = &v1.ReviewRedPacketImageRes{}
	// 使用数据库事务确保数据一致性
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 获取红包图片信息
		var image *do.RedPacketImages
		err = dao.RedPacketImages.Ctx(ctx).Where("red_packet_images_id", req.RedPacketImagesId).Scan(&image)
		if err != nil {
			return gerror.Wrapf(err, "获取红包图片信息失败, ID: %d", req.RedPacketImagesId)
		}

		if image == nil {
			return gerror.NewCodef(codes.CodeNotFound, "红包图片不存在, ID: %d", req.RedPacketImagesId)
		}

		// // 检查当前状态是否为待审核
		// if image.Status != "pending_review" {
		// 	return gerror.New("红包图片状态不是待审核状态")
		// }

		// 构建更新数据
		updateData := g.Map{
			"status":     req.Status,
			"updated_at": gtime.Now(),
		}

		// 如果是拒绝，需要填充拒绝原因
		if req.Status == "fail" {
			updateData["refuse_reason_zh"] = req.RefuseReasonZh
			updateData["refuse_reason_en"] = req.RefuseReasonEn
		} else {
			// 如果是通过，清空拒绝原因
			updateData["refuse_reason_zh"] = ""
			updateData["refuse_reason_en"] = ""
		}

		// 更新红包图片状态
		_, err = dao.RedPacketImages.Ctx(ctx).
			Where("red_packet_images_id", req.RedPacketImagesId).
			Where("status", "pending_review"). // 乐观锁，确保状态未被其他操作修改
			Data(updateData).
			Update()
		if err != nil {
			return gerror.Wrapf(err, "更新红包图片状态失败, ID: %d", req.RedPacketImagesId)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	res.Success = true
	return res, nil
}

// ListRedPacketImages 获取红包封面图片列表
func (s *sSystemLogic) ListRedPacketImages(ctx context.Context, req *v1.ListRedPacketImagesReq) (res *v1.ListRedPacketImagesRes, err error) {
	// 初始化返回结果
	res = &v1.ListRedPacketImagesRes{
		Page: &common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
			TotalSize:   0,
			TotalPage:   0,
		},
		List: make([]*v1.RedPacketImageInfoType, 0),
	}

	// 构建查询条件
	condition := g.Map{}

	// 添加筛选条件
	if req.Status != "" {
		condition["rpi.status"] = req.Status
	}
	if req.UserId > 0 {
		condition["rpi.user_id"] = req.UserId
	}

	// 处理日期范围
	if req.DateRange != "" {
		dateRange := strings.Split(req.DateRange, ",")
		if len(dateRange) == 2 {
			condition["rpi.created_at >="] = dateRange[0] + " 00:00:00"
			condition["rpi.created_at <="] = dateRange[1] + " 23:59:59"
		}
	}

	// 代理查询条件（默认用户）
	if req.FirstAgentName != "" {
		condition["u_first_agent.username LIKE"] = "%" + req.FirstAgentName + "%"
	}
	if req.SecondAgentName != "" {
		condition["u_second_agent.username LIKE"] = "%" + req.SecondAgentName + "%"
	}
	if req.ThirdAgentName != "" {
		condition["u_third_agent.username LIKE"] = "%" + req.ThirdAgentName + "%"
	}

	// Telegram查询条件（默认用户）
	if req.TelegramId != "" {
		condition["u_uba.telegram_id LIKE"] = "%" + req.TelegramId + "%"
	}
	if req.TelegramUsername != "" {
		condition["u_uba.telegram_username LIKE"] = "%" + req.TelegramUsername + "%"
	}
	if req.FirstName != "" {
		condition["u_uba.first_name LIKE"] = "%" + req.FirstName + "%"
	}

	// 处理导出
	if req.Export {
		// 导出时不进行分页限制
		list, _, err := dao.RedPacketImages.ListAdminRedPacketImagesWithFullInfo(ctx, 1, 9999999, condition)
		if err != nil {
			return nil, gerror.Wrap(err, "导出查询失败")
		}

		// 转换为导出格式
		exportData := make([]interface{}, len(list))
		for i, item := range list {
			createdAt := ""
			if item.CreatedAt != nil {
				createdAt = item.CreatedAt.String()
			}
			updatedAt := ""
			if item.UpdatedAt != nil {
				updatedAt = item.UpdatedAt.String()
			}

			exportData[i] = struct {
				Id               int64  `json:"id" excel:"图片ID"`
				UserId           int64  `json:"userId" excel:"用户ID"`
				Username         string `json:"username" excel:"用户名"`
				ImageUrl         string `json:"imageUrl" excel:"图片URL"`
				Status           string `json:"status" excel:"状态"`
				RefuseReasonZh   string `json:"refuseReasonZh" excel:"拒绝原因(中文)"`
				RefuseReasonEn   string `json:"refuseReasonEn" excel:"拒绝原因(英文)"`
				CreatedAt        string `json:"createdAt" excel:"创建时间"`
				UpdatedAt        string `json:"updatedAt" excel:"更新时间"`
				FirstAgentName   string `json:"firstAgentName" excel:"一级代理"`
				SecondAgentName  string `json:"secondAgentName" excel:"二级代理"`
				ThirdAgentName   string `json:"thirdAgentName" excel:"三级代理"`
				TelegramId       string `json:"telegramId" excel:"Telegram ID"`
				TelegramUsername string `json:"telegramUsername" excel:"Telegram用户名"`
				FirstName        string `json:"firstName" excel:"真实姓名"`
			}{
				Id:               item.Id,
				UserId:           item.UserId,
				Username:         item.Username,
				ImageUrl:         item.ImageUrl,
				Status:           item.Status,
				RefuseReasonZh:   item.RefuseReasonZh,
				RefuseReasonEn:   item.RefuseReasonEn,
				CreatedAt:        createdAt,
				UpdatedAt:        updatedAt,
				FirstAgentName:   item.FirstAgentName,
				SecondAgentName:  item.SecondAgentName,
				ThirdAgentName:   item.ThirdAgentName,
				TelegramId:       item.TelegramId,
				TelegramUsername: item.TelegramUsername,
				FirstName:        item.FirstName,
			}
		}

		// 定义Excel表头
		excelTags := []string{} // excel.ExportByStructs 会自动从 struct tag 读取

		// 调用Excel导出工具
		return res, excel.ExportByStructs(ctx, excelTags, exportData, "红包封面图片", "红包封面图片列表")
	}

	// 查询分页数据
	list, total, err := dao.RedPacketImages.ListAdminRedPacketImagesWithFullInfo(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, gerror.Wrap(err, "查询红包封面图片列表失败")
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = common.CalculateTotalPage(total, req.PageSize)

	// 转换DAO结果到API响应格式
	res.List = make([]*v1.RedPacketImageInfoType, len(list))
	for i, item := range list {
		res.List[i] = &v1.RedPacketImageInfoType{
			RedPacketImages: entity.RedPacketImages{
				RedPacketImagesId: item.Id,
				UserId:            uint64(item.UserId),
				ImagesUrl:         item.ImageUrl,
				Status:            item.Status,
				RefuseReasonZh:    item.RefuseReasonZh,
				RefuseReasonEn:    item.RefuseReasonEn,
				CreatedAt:         item.CreatedAt,
				UpdatedAt:         item.UpdatedAt,
			},
			Username:         item.Username,
			FirstAgentName:   item.FirstAgentName,
			SecondAgentName:  item.SecondAgentName,
			ThirdAgentName:   item.ThirdAgentName,
			TelegramId:       item.TelegramId,
			TelegramUsername: item.TelegramUsername,
			FirstName:        item.FirstName,
		}
	}

	return res, nil
}
