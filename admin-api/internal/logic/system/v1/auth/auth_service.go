package auth

import (
	"context"
	"time"

	// "admin-api/internal/consts" // Removed as unused after replacing Jw<PERSON><PERSON><PERSON>uer
	// "admin-api/internal/service" // Removed as unused

	"admin-api/internal/consts"
	"admin-api/internal/dao"
	// "admin-api/internal/library/captcha"
	"admin-api/internal/service/system/auth" // Import the interface package
	"admin-api/utility/encrypt"              // Assuming bcrypt utility exists here

	"github.com/gogf/gf/v2/errors/gerror"
	// "github.com/gogf/gf/v2/os/gctx" // Removed as unused after changing captcha call
	// Using a standard JWT library
)

// authService implements the IAuthService interface.
type authService struct {
	// Add dependencies here if needed, e.g., config for JWT secret/expiry
}

// NewAuthService creates and returns a new instance of authService.
func NewAuthService() auth.IAuthService {
	return &authService{}
}

// VerifyPassword compares a plain password with a stored hash.
func (s *authService) VerifyPassword(ctx context.Context, plainPassword, hashedPassword string) bool {
	// Assuming encrypt.BcryptCheck exists and handles comparison
	return encrypt.BcryptVerify(plainPassword, hashedPassword)
}

// HashPassword generates a secure hash for a given password.
func (s *authService) HashPassword(ctx context.Context, password string) (string, error) {
	return encrypt.BcryptHash(password)
}

// GenerateToken creates a new authentication token for the given user ID and username.
func (s *authService) GenerateToken(ctx context.Context, userId uint, username string) (token string, expireAt int64, err error) {

	token, expireAt, err = dao.PersonalAccessTokens.GenerateToken(ctx, uint64(userId), username, time.Hour*72, consts.MemberTypeAdmin)
	if err != nil {
		return "", 0, gerror.Wrap(err, "Failed to generate personal access token")
	}

	return token, expireAt, nil

}

// // GenerateCaptcha creates a new captcha challenge.
// func (s *authService) GenerateCaptcha(ctx context.Context) (*captcha.SlideData, error) {
// 	// Assuming captcha.GetCaptcha() handles generation
// 	// data, err := captcha.GenerateCaptcha(ctx) // Use GenerateCaptcha and pass context
// 	// if err != nil {
// 	// 	return nil, gerror.Wrap(err, "Failed to generate captcha")
// 	// }
// 	return nil, nil
// }

// // VerifyCaptcha validates the user's response to a captcha challenge.
// func (s *authService) VerifyCaptcha(ctx context.Context, token, code string) bool {
// 	// Assuming captcha.VerifyCaptcha() handles verification
// 	// return captcha.VerifyCaptcha(ctx, token, code) // Pass context to VerifyCaptcha
// 	return true
// }
