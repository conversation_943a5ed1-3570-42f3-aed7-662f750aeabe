package v1

import (
	"admin-api/api/common"
	"context"
	"math"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/dao"
	"admin-api/utility/excel"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	// "github.com/gogf/gf/v2/util/gconv" // Removed unused import
)

// GetLoginLogList 获取登录日志列表
func (l *sSystemLogic) GetLoginLogList(ctx context.Context, req *v1.GetLoginLogListReq) (res *v1.GetLoginLogListRes, err error) {
	// 检查请求参数
	if req == nil {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "请求参数不能为空")
	}

	// 初始化返回结果，避免空指针
	res = &v1.GetLoginLogListRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
			TotalSize:   0,
			TotalPage:   0,
		},
		Data: make([]*v1.LoginLogListItem, 0),
	}

	// 构建查询条件
	condition := g.Map{}

	// 处理ID查询
	if req.Id > 0 {
		condition[dao.LoginLog.Columns().Id] = req.Id
	}

	// 处理用户名模糊查询
	if req.Username != "" {
		condition[dao.LoginLog.Columns().Username+" LIKE"] = "%" + req.Username + "%"
	}

	// 处理IP模糊查询
	if req.Ip != "" {
		condition[dao.LoginLog.Columns().LoginIp+" LIKE"] = "%" + req.Ip + "%"
	}

	// 处理状态查询（-1表示查询全部）
	if req.Status >= 0 {
		condition[dao.LoginLog.Columns().Status] = req.Status
	}

	// 处理时间范围查询
	startTime, endTime := dao.LoginLog.ParseTimeRange(req.DateRange, req.StartTime, req.EndTime)
	if startTime != "" {
		condition[dao.LoginLog.Columns().LoginAt+" >="] = startTime
	}
	if endTime != "" {
		condition[dao.LoginLog.Columns().LoginAt+" <="] = endTime
	}

	// 处理代理层级搜索
	if req.FirstAgentName != "" {
		condition["first_agent.username LIKE"] = "%" + req.FirstAgentName + "%"
	}
	if req.SecondAgentName != "" {
		condition["second_agent.username LIKE"] = "%" + req.SecondAgentName + "%"
	}
	if req.ThirdAgentName != "" {
		condition["third_agent.username LIKE"] = "%" + req.ThirdAgentName + "%"
	}

	// 处理Telegram信息搜索
	if req.TelegramId != "" {
		condition["uba.telegram_id LIKE"] = "%" + req.TelegramId + "%"
	}
	if req.TelegramUsername != "" {
		condition["uba.telegram_username LIKE"] = "%" + req.TelegramUsername + "%"
	}
	if req.FirstName != "" {
		condition["uba.first_name LIKE"] = "%" + req.FirstName + "%"
	}

	// 如果是导出，查询所有数据
	if req.Export == 1 {
		// 导出时不进行分页限制 (使用 Repository)
		// 注意：Repository List 方法返回 []*entity.LoginLog，无需再次转换
		loginLogs, _, err := l.loginLogRepo.List(ctx, 1, 9999999, condition)
		if err != nil {
			return res, err
		}
		// 移除 gconv.Structs 转换，因为 repo.List 直接返回 []*entity.LoginLog

		// 导出Excel
		// 定义Excel表头
		excelTags := []string{
			"日志ID", "用户名", "登录IP", "状态", "登录时间", "错误信息", "UA信息",
		}

		// 准备导出数据
		exportData := make([]interface{}, len(loginLogs))
		for i, v := range loginLogs {
			// 状态显示转换
			status := "失败"
			if v.Status == 1 {
				status = "成功"
			}

			exportData[i] = struct {
				Id        int64  `json:"id"`
				Username  string `json:"username"`
				LoginIp   string `json:"login_ip"`
				Status    string `json:"status"`
				LoginAt   string `json:"login_at"`
				ErrMsg    string `json:"err_msg"`
				UserAgent string `json:"user_agent"`
			}{
				Id:        v.Id,
				Username:  v.Username,
				LoginIp:   v.LoginIp,
				Status:    status,
				LoginAt:   v.LoginAt.Format("2006-01-02 15:04:05"),
				ErrMsg:    v.ErrMsg,
				UserAgent: v.UserAgent,
			}
		}

		// 调用Excel导出工具
		return res, excel.ExportByStructs(ctx, excelTags, exportData, "登录日志", "登录日志列表")
	}

	// 确保页码和每页大小合法
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 查询数据 (使用 Repository)
	// 注意：Repository List 方法返回 []*entity.LoginLog，无需再次转换
	loginLogs, total, err := l.loginLogRepo.List(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return res, err
	}
	// 移除 gconv.Structs 转换，因为 repo.List 直接返回 []*entity.LoginLog

	// 对每个日志对象中的 token 进行脱敏处理
	if len(loginLogs) > 0 {
		for _, log := range loginLogs {
			if log.Response != nil && log.Response.Get("token").String() != "" {
				log.Response.Set("token", "********************")
			}
		}
	}

	// 更新返回结果
	res.Page.TotalSize = total
	res.Page.TotalPage = int(math.Ceil(float64(total) / float64(req.PageSize)))

	// Convert LoginLogWithUserInfo to v1.LoginLogListItem
	items := make([]*v1.LoginLogListItem, len(loginLogs))
	for i, log := range loginLogs {
		// Convert TelegramId from string to int64
		telegramId := int64(0)
		if log.TelegramId != "" {
			telegramId = g.NewVar(log.TelegramId).Int64()
		}

		items[i] = &v1.LoginLogListItem{
			LoginLog:         log.LoginLog,
			Username:         log.LoginLog.Username, // From LoginLog entity
			Account:          log.UserAccount,       // From users table
			TelegramUsername: log.TelegramUsername,  // From user_backup_accounts table
			TelegramId:       telegramId,            // From user_backup_accounts table
			FirstAgentName:   log.FirstAgentName,    // From agent tables
			SecondAgentName:  log.SecondAgentName,   // From agent tables
			ThirdAgentName:   log.ThirdAgentName,    // From agent tables
		}
	}
	res.Data = items

	return res, nil
}

// GetLoginLogDetail 获取登录日志详情
// No-op change removed
func (l *sSystemLogic) GetLoginLogDetail(ctx context.Context, req *v1.GetLoginLogDetailReq) (res *v1.GetLoginLogDetailRes, err error) {
	// 根据ID查询日志详情 (使用 Repository)
	// 注意：Repository GetByID 方法返回 *entity.LoginLog，无需再次转换
	entityLogDetail, err := l.loginLogRepo.GetByID(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if entityLogDetail == nil {
		return &v1.GetLoginLogDetailRes{
			Info: nil,
		}, nil
	}
	// 移除 gconv.Struct 转换，因为 repo.GetByID 直接返回 *entity.LoginLog

	// 对响应中的token进行脱敏
	if entityLogDetail.Response != nil && entityLogDetail.Response.Get("token").String() != "" {
		entityLogDetail.Response.Set("token", "********************")
	}

	return &v1.GetLoginLogDetailRes{
		Info: entityLogDetail,
	}, nil
}
