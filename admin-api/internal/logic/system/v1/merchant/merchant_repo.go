package merchant

import (
	"admin-api/internal/codes"
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/merchant" // Import the interface package
	"context"
	"database/sql"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

type merchantRepository struct{}

// NewMerchantRepository creates and returns a new instance of IMerchantRepository.
func NewMerchantRepository() merchant.IMerchantRepository {
	return &merchantRepository{}
}

// Create inserts a new merchant record.
func (r *merchantRepository) Create(ctx context.Context, merchantDo *do.Merchants) (id uint, err error) {
	// Ensure timestamps
	if merchantDo.CreatedAt == nil {
		merchantDo.CreatedAt = gtime.Now()
	}
	if merchantDo.UpdatedAt == nil {
		merchantDo.UpdatedAt = gtime.Now()
	}

	result, err := dao.Merchants.Ctx(ctx).Data(merchantDo).OmitEmpty().Insert()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "创建商户失败")
	}
	lastId, err := result.LastInsertId()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "获取新商户ID失败")
	}
	return uint(lastId), nil
}

// Update updates merchant fields.
func (r *merchantRepository) Update(ctx context.Context, id uint, data g.Map) error {
	if len(data) == 0 {
		return nil
	}
	data[dao.Merchants.Columns().UpdatedAt] = gtime.Now() // Ensure UpdatedAt is set

	_, err := dao.Merchants.Ctx(ctx).
		Data(data).
		Where(dao.Merchants.Columns().MerchantId, id).
		Update()
	if err != nil {
		return gerror.WrapCodef(codes.CodeInternalError, err, "更新商户失败 (ID: %d)", id)
	}
	return nil
}

// Delete performs a hard delete on merchants and their associated API keys.
func (r *merchantRepository) Delete(ctx context.Context, ids []uint) error {
	if len(ids) == 0 {
		return nil
	}

	// Use transaction to ensure consistency
	return dao.Merchants.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// First, delete associated API keys
		_, err := dao.MerchantApiKeys.Ctx(ctx).
			WhereIn(dao.MerchantApiKeys.Columns().MerchantId, ids).
			Delete()
		if err != nil {
			return gerror.WrapCodef(codes.CodeInternalError, err, "删除商户API密钥失败 (IDs: %v)", ids)
		}

		// Then, hard delete the merchants
		_, err = dao.Merchants.Ctx(ctx).
			WhereIn(dao.Merchants.Columns().MerchantId, ids).
			Delete()
		if err != nil {
			return gerror.WrapCodef(codes.CodeInternalError, err, "删除商户失败 (IDs: %v)", ids)
		}

		return nil
	})
}

// GetByID retrieves a single merchant by ID.
func (r *merchantRepository) GetByID(ctx context.Context, id uint) (*entity.Merchants, error) {
	var merchant *entity.Merchants
	err := dao.Merchants.Ctx(ctx).
		Where(dao.Merchants.Columns().MerchantId, id).
		Scan(&merchant)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCodef(codes.CodeMerchantNotFound, "商户不存在 (ID: %d)", id)
		}
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询商户失败 (ID: %d)", id)
	}
	return merchant, nil
}

// GetByEmail retrieves a merchant by email address.
func (r *merchantRepository) GetByEmail(ctx context.Context, email string) (*entity.Merchants, error) {
	var merchant *entity.Merchants
	err := dao.Merchants.Ctx(ctx).
		Where(dao.Merchants.Columns().Email, email).
		Scan(&merchant)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCodef(codes.CodeMerchantNotFound, "商户不存在 (Email: %s)", email)
		}
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询商户失败 (Email: %s)", email)
	}
	return merchant, nil
}

// GetByMerchantName retrieves a merchant by merchant name.
func (r *merchantRepository) GetByMerchantName(ctx context.Context, merchantName string) (*entity.Merchants, error) {
	var merchant *entity.Merchants
	err := dao.Merchants.Ctx(ctx).
		Where(dao.Merchants.Columns().MerchantName, merchantName).
		Scan(&merchant)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCodef(codes.CodeMerchantNotFound, "商户不存在 (MerchantName: %s)", merchantName)
		}
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询商户失败 (MerchantName: %s)", merchantName)
	}
	return merchant, nil
}

// List retrieves a paginated list of merchants based on conditions.
func (r *merchantRepository) List(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.Merchants, total int, err error) {
	m := dao.Merchants.Ctx(ctx)
	if len(condition) > 0 {
		m = m.Where(condition) // Apply conditions passed from logic layer
	}

	total, err = m.Count()
	if err != nil || total == 0 {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "获取商户总数失败")
	}

	list = make([]*entity.Merchants, 0)
	err = m.Page(page, pageSize).OrderDesc(dao.Merchants.Columns().CreatedAt).Scan(&list)
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询商户列表失败")
	}

	return list, total, nil
}

// UpdateStatus updates the status of a merchant.
func (r *merchantRepository) UpdateStatus(ctx context.Context, id uint, status int) error {
	_, err := dao.Merchants.Ctx(ctx).
		Data(g.Map{
			dao.Merchants.Columns().Status:    status,
			dao.Merchants.Columns().UpdatedAt: gtime.Now(),
		}).
		Where(dao.Merchants.Columns().MerchantId, id).
		Update()
	if err != nil {
		return gerror.WrapCodef(codes.CodeInternalError, err, "更新商户状态失败 (ID: %d)", id)
	}
	return nil
}

// UpdatePermissions updates merchant permissions.
func (r *merchantRepository) UpdatePermissions(ctx context.Context, id uint, permissions g.Map) error {
	if len(permissions) == 0 {
		return nil
	}
	permissions[dao.Merchants.Columns().UpdatedAt] = gtime.Now()

	_, err := dao.Merchants.Ctx(ctx).
		Data(permissions).
		Where(dao.Merchants.Columns().MerchantId, id).
		Update()
	if err != nil {
		return gerror.WrapCodef(codes.CodeInternalError, err, "更新商户权限失败 (ID: %d)", id)
	}
	return nil
}

// ExistsByName checks if a merchant name exists, optionally excluding an ID.
func (r *merchantRepository) ExistsByName(ctx context.Context, name string, excludeId ...uint) (bool, error) {
	m := dao.Merchants.Ctx(ctx).
		Where(dao.Merchants.Columns().MerchantName, name)
	if len(excludeId) > 0 {
		m = m.WhereNot(dao.Merchants.Columns().MerchantId, excludeId[0])
	}
	count, err := m.Count()
	if err != nil {
		return false, gerror.WrapCodef(codes.CodeInternalError, err, "检查商户名称是否存在失败 (Name: %s)", name)
	}
	return count > 0, nil
}

// ExistsByEmail checks if a merchant email exists, optionally excluding an ID.
func (r *merchantRepository) ExistsByEmail(ctx context.Context, email string, excludeId ...uint) (bool, error) {
	m := dao.Merchants.Ctx(ctx).
		Where(dao.Merchants.Columns().Email, email)
	if len(excludeId) > 0 {
		m = m.WhereNot(dao.Merchants.Columns().MerchantId, excludeId[0])
	}
	count, err := m.Count()
	if err != nil {
		return false, gerror.WrapCodef(codes.CodeInternalError, err, "检查商户邮箱是否存在失败 (Email: %s)", email)
	}
	return count > 0, nil
}

// UpdatePaymentPassword updates merchant payment password.
func (r *merchantRepository) UpdatePaymentPassword(ctx context.Context, id uint, hashedPassword string) error {
	_, err := dao.Merchants.Ctx(ctx).
		Data(g.Map{
			dao.Merchants.Columns().PaymentPassword:    hashedPassword,
			dao.Merchants.Columns().PaymentPasswordSet: hashedPassword != "",
			dao.Merchants.Columns().UpdatedAt:          gtime.Now(),
		}).
		Where(dao.Merchants.Columns().MerchantId, id).
		Update()
	if err != nil {
		return gerror.WrapCodef(codes.CodeInternalError, err, "更新商户支付密码失败 (ID: %d)", id)
	}
	return nil
}

// UpdateGoogle2FA updates merchant Google 2FA settings.
func (r *merchantRepository) UpdateGoogle2FA(ctx context.Context, id uint, secret string, enabled bool) error {
	_, err := dao.Merchants.Ctx(ctx).
		Data(g.Map{
			dao.Merchants.Columns().Google2FaSecret:  secret,
			dao.Merchants.Columns().Google2FaEnabled: enabled,
			dao.Merchants.Columns().UpdatedAt:        gtime.Now(),
		}).
		Where(dao.Merchants.Columns().MerchantId, id).
		Update()
	if err != nil {
		return gerror.WrapCodef(codes.CodeInternalError, err, "更新商户Google 2FA失败 (ID: %d)", id)
	}
	return nil
}

// UpdateTOTP updates merchant TOTP settings and resets FirstLogin.
func (r *merchantRepository) UpdateTOTP(ctx context.Context, id uint, secret string, recoveryCodes string) error {
	_, err := dao.Merchants.Ctx(ctx).
		Data(g.Map{
			dao.Merchants.Columns().Google2FaSecret:  secret,
			dao.Merchants.Columns().Google2FaEnabled: 1, // Enable Google 2FA
			dao.Merchants.Columns().RecoveryCodes:    recoveryCodes,
			dao.Merchants.Columns().UpdatedAt:        gtime.Now(),
		}).
		Where(dao.Merchants.Columns().MerchantId, id).
		Update()
	if err != nil {
		return gerror.WrapCodef(codes.CodeInternalError, err, "更新商户TOTP失败 (ID: %d)", id)
	}
	return nil
}
