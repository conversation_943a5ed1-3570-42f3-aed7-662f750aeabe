package v1

import (
	"context"
	"strings"

	v1 "admin-api/api/system/v1"
	// "admin-api/internal/cache"

	"admin-api/internal/cache"
	"admin-api/internal/codes"
	"admin-api/internal/consts"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/utility"

	// Keep for RevokeAllUserTokens and AdminMember update (temporary)
	// "admin-api/internal/library/captcha" // Replaced by authService
	// "admin-api/utility/encrypt" // Replaced by authService

	"github.com/casdoor/casdoor-go-sdk/casdoorsdk" // Import gjson for Response field
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	// "github.com/gogf/gf/v2/os/gctx" // Import gctx for background context
)

// CasdoorSignin Casdoor 单点登录
func (l *sSystemLogic) CasdoorSignin(ctx context.Context, req *v1.CasdoorSigninReq) (res *v1.CasdoorSigninRes, err error) {

	code := req.Code
	state := req.State

	token, err := casdoorsdk.GetOAuthToken(code, state)
	if err != nil {
		// 提取错误信息
		errStr := err.Error()

		// 根据错误信息返回不同的错误码
		switch {
		case strings.Contains(errStr, "invalid_grant"):
			// 授权码已被使用或无效
			if strings.Contains(errStr, "has been used for token") {
				return nil, codes.NewError(codes.CodeAuthCodeAlreadyUsed)
			} else if strings.Contains(errStr, "expired") {
				return nil, codes.NewError(codes.CodeAuthCodeExpired)
			} else {
				return nil, codes.NewError(codes.CodeInvalidToken)
			}
		case strings.Contains(errStr, "server_error"):
			// 认证服务器内部错误
			return nil, codes.NewError(codes.CodeAuthServerError)
		case strings.Contains(errStr, "invalid_request"):
			// 无效的请求
			return nil, codes.NewError(codes.CodeAuthInvalidRequest)
		default:
			// 其他错误仍然作为内部错误处理，但记录详细信息
			g.Log().Errorf(ctx, "Casdoor OAuth2 错误: %v", err)
			return nil, codes.WrapError(err, codes.CodeInternalError)
		}
	}

	var authToken = token.AccessToken

	claims, err := casdoorsdk.ParseJwtToken(authToken)
	if err != nil {
		return nil, codes.WrapError(err, codes.CodeInternalError)
	}

	cacheManager := cache.GetInstance()

	// cache.GetInstance().SetUserPermissions(claims.Name)
	user, err := casdoorsdk.GetUser(claims.Name)
	if err != nil {
		return nil, codes.WrapError(err, codes.CodeInternalError)
	}
	if user == nil {
		return nil, codes.NewError(codes.CodeUserNotFound)
	}
	cacheManager.SetUserInfo(ctx, claims.Name, user)

	var MenusPermissions []cache.Permission
	var APIPermissions []cache.Permission

	var permission *casdoorsdk.Permission

	for _, permission = range user.Permissions {

		if strings.Contains(permission.DisplayName, "Menu") {
			MenusPermissions = append(MenusPermissions, cache.Permission{
				Name:         permission.Name,
				DisplayName:  permission.DisplayName,
				Description:  permission.Description,
				Resources:    permission.Resources,
				ResourceType: permission.ResourceType,
				Effect:       permission.Effect,
			})
		}

		if strings.Contains(permission.DisplayName, "API") {
			APIPermissions = append(APIPermissions, cache.Permission{
				Name:         permission.Name,
				DisplayName:  permission.DisplayName,
				Description:  permission.Description,
				Resources:    permission.Resources,
				ResourceType: permission.ResourceType,
				Effect:       permission.Effect,
			})
		}

	}
	if len(APIPermissions) > 0 {
		cacheManager.SetUserPermissions(ctx, claims.Name, APIPermissions)
	}
	if len(MenusPermissions) > 0 {
		cacheManager.SetUserPermissions(ctx, claims.Name, MenusPermissions)
	}

	// 创建结果对象并直接赋值
	res = &v1.CasdoorSigninRes{Token: *token}
	return res, nil
}

// 获取casdoor用户信息
func (l *sSystemLogic) GetCasdoorUserInfo(ctx context.Context, req *v1.GetCasdoorUserInfoReq) (res *v1.GetCasdoorUserInfoRes, err error) {
	// 从上下文中获取请求对象
	r := g.RequestFromCtx(ctx)
	if r == nil {
		return nil, codes.NewError(codes.CodeUnauthorized)
	}

	// 使用请求对象的GetCtxVar方法获取claims
	claimsVar := r.GetCtxVar("claims")
	if claimsVar.IsNil() {
		return nil, codes.NewError(codes.CodeUnauthorized)
	}

	claims := claimsVar.Val().(*casdoorsdk.Claims)

	// 创建结果对象并直接赋值
	res = &v1.GetCasdoorUserInfoRes{Claims: *claims}
	return res, nil
}

// GetAdminInfo 获取管理员详细信息
func (l *sSystemLogic) GetAdminInfo(ctx context.Context, req *v1.GetAdminInfoReq) (res *v1.GetAdminInfoRes, err error) {

	var username string

	if r := g.RequestFromCtx(ctx); r != nil {
		username = r.GetCtxVar("username").String()
	} else {
		return nil, codes.NewError(codes.CodeUnauthorized)
	}

	g.Log().Debugf(ctx, "username: %s", username)

	cacheManager := cache.GetInstance()

	var user *casdoorsdk.User

	if user, err = cacheManager.GetUserInfo(ctx, username); err != nil {
		return nil, codes.WrapError(err, codes.CodeInternalError)
	}

	// 如果缓存中没有用户信息，尝试从 Casdoor 获取
	if user == nil {
		g.Log().Debugf(ctx, "缓存中无用户信息，从 Casdoor 重新获取: username=%s", username)

		user, err = casdoorsdk.GetUser(username)
		if err != nil {
			g.Log().Errorf(ctx, "从 Casdoor 获取用户信息失败: username=%s, error=%v", username, err)
			return nil, codes.NewError(codes.CodeUserNotFound)
		}

		if user == nil {
			return nil, codes.NewError(codes.CodeUserNotFound)
		}

		// 将用户信息重新缓存到 Redis
		go func() {
			if cacheErr := cacheManager.SetUserInfo(ctx, username, user); cacheErr != nil {
				g.Log().Errorf(ctx, "重新缓存用户信息失败: username=%s, error=%v", username, cacheErr)
			}
		}()
	}
	g.Log().Debugf(ctx, "user: %v", user)

	var permissions []cache.Permission
	//获取用户权限缓存
	permissions, err = cacheManager.GetUserPermissions(ctx, username)
	if err != nil {
		return nil, codes.WrapError(err, codes.CodeInternalError)
	}

	// 如果缓存中没有权限信息，使用用户的权限信息
	if permissions == nil && user.Permissions != nil {
		g.Log().Debugf(ctx, "缓存中无权限信息，使用 Casdoor 用户权限: username=%s", username)

		var MenusPermissions []cache.Permission
		var APIPermissions []cache.Permission

		for _, permission := range user.Permissions {
			if strings.Contains(permission.DisplayName, "Menu") {
				MenusPermissions = append(MenusPermissions, cache.Permission{
					Name:         permission.Name,
					DisplayName:  permission.DisplayName,
					Description:  permission.Description,
					Resources:    permission.Resources,
					ResourceType: permission.ResourceType,
					Effect:       permission.Effect,
				})
			}

			if strings.Contains(permission.DisplayName, "API") {
				APIPermissions = append(APIPermissions, cache.Permission{
					Name:         permission.Name,
					DisplayName:  permission.DisplayName,
					Description:  permission.Description,
					Resources:    permission.Resources,
					ResourceType: permission.ResourceType,
					Effect:       permission.Effect,
				})
			}
		}

		// 合并所有权限用于菜单显示
		permissions = append(MenusPermissions, APIPermissions...)

		// 异步缓存权限
		go func() {
			if len(permissions) > 0 {
				if cacheErr := cacheManager.SetUserPermissions(ctx, username, permissions); cacheErr != nil {
					g.Log().Errorf(ctx, "重新缓存用户权限失败: username=%s, error=%v", username, cacheErr)
				}
			}
		}()
	}

	//获取所有的菜单权限
	var menuNames []string
	for _, permission := range permissions {
		if strings.Contains(permission.DisplayName, "Menu") {
			menuNames = append(menuNames, permission.Resources...)
		}
	}
	g.Log().Debugf(ctx, "menus: %v", menuNames)

	//添加whereIn
	condition := g.Map{
		dao.AdminMenu.Columns().Status: consts.StatusEnabled,
	}

	// 如果有权限菜单，则添加whereIn条件
	if len(menuNames) > 0 {
		condition[dao.AdminMenu.Columns().Path] = menuNames
	}

	menus, err := dao.AdminMenu.GetAllMenusTree(ctx, condition)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取菜单失败")
	}

	return &v1.GetAdminInfoRes{
		Id:       user.Id,
		RealName: user.DisplayName,
		Username: user.Name,
		Avatar:   user.Avatar,
		Email:    user.Email,
		Menus:    menus,
	}, nil

	// // 首先尝试从Redis缓存获取用户信息
	// res, err = l.getUserInfoFromCache(ctx, currentUserId)
	// if err != nil {
	// 	g.Log().Warningf(ctx, "从Redis获取用户信息失败，将从数据库获取: userId=%d, error=%v", currentUserId, err)
	// }

	// // 如果缓存中没有数据，从数据库获取
	// if res == nil {
	// 	g.Log().Debugf(ctx, "缓存中无用户信息，从数据库获取: userId=%d", currentUserId)

	// 	// 调用仓库层获取管理员详细信息
	// 	res, err = l.authRepo.GetAdminInfo(ctx, currentUserId)
	// 	if err != nil {
	// 		return nil, codes.WrapError(err, codes.CodeInternalError)
	// 	}

	// 	// 异步将用户信息存储到Redis缓存
	// 	go func(userId int64, userInfo *v1.GetAdminInfoRes) {
	// 		ctxBg := gctx.New()
	// 		if cacheErr := l.setUserInfoToCache(ctxBg, userId, userInfo); cacheErr != nil {
	// 			g.Log().Errorf(ctxBg, "存储用户信息到Redis失败: userId=%d, error=%v", userId, cacheErr)
	// 		}
	// 	}(currentUserId, res)

	// 	// 异步获取并缓存用户权限
	// 	go func(userId int64) {
	// 		ctxBg := gctx.New()
	// 		if permErr := l.cacheUserPermissions(ctxBg, userId); permErr != nil {
	// 			g.Log().Errorf(ctxBg, "缓存用户权限失败: userId=%d, error=%v", userId, permErr)
	// 		}
	// 	}(currentUserId)
	// } else {
	// 	g.Log().Debugf(ctx, "从Redis缓存成功获取用户信息: userId=%d", currentUserId)
	// }

	// return nil, nil
}

// Login 处理登录请求
func (l *sSystemLogic) Login(ctx context.Context, req *v1.LoginReq) (res *v1.LoginRes, err error) {
	// // 获取请求对象，用于记录登录日志
	// r := g.RequestFromCtx(ctx)

	// // 预设登录日志相关参数
	// userId := int64(0)
	// username := req.Username
	// status := consts.LoginStatusFailed // 默认登录失败
	// errMsg := ""
	// var loginResData interface{} = nil // Use a different name to avoid conflict with 'res'

	// // 对登录过程进行defer处理，确保登录日志记录
	// defer func() {
	// 	// 如果发生错误，记录错误信息
	// 	if err != nil {
	// 		errMsg = err.Error()
	// 		status = consts.LoginStatusFailed // 登录失败
	// 	} else {
	// 		status = consts.LoginStatusSuccess // 登录成功
	// 		loginResData = res                 // Assign successful response data for logging
	// 	}

	// 	// 记录登录日志 using repository
	// 	loginLog := &entity.LoginLog{
	// 		Username:   username,
	// 		LoginIp:    r.GetClientIp(),            // Correct field name: LoginIp
	// 		Status:     status,                     // Correct type: int
	// 		ErrMsg:     errMsg,                     // Correct field name: ErrMsg
	// 		MemberType: consts.MemberTypeAdmin,     // Correct field name: MemberType
	// 		MemberId:   userId,                     // Correct field name: MemberId, type: int64
	// 		Response:   gjson.New(loginResData),    // Correct type: *gjson.Json
	// 		UserAgent:  r.Header.Get("User-Agent"), // Correct field name: UserAgent
	// 		// ReqId, ProvinceId, CityId can be added if needed/available
	// 		LoginAt: gtime.Now(),
	// 	}
	// 	// Use background context for non-critical logging
	// 	go func(logEntity *entity.LoginLog) {
	// 		logCtx := gctx.New()
	// 		logErr := l.loginLogRepo.Create(logCtx, logEntity) // Use 'l' instead of 's', changed method name
	// 		if logErr != nil {
	// 			g.Log().Errorf(logCtx, "后台记录登录日志失败: %v", logErr)
	// 		}
	// 	}(loginLog) // Pass loginLog to goroutine to avoid data race issues with defer variables
	// }()

	// // 验证验证码
	// if req.CaptchaToken != "" && req.CaptchaCode != "" {
	// 	if !l.authService.VerifyCaptcha(ctx, req.CaptchaToken, req.CaptchaCode) { // Use 'l' instead of 's'
	// 		// 验证码验证失败
	// 		err = codes.NewError(codes.CodeInvalidCaptcha)
	// 		return nil, err
	// 	}
	// }

	// // 从数据库中查询用户 using repository
	// adminMember, err := l.authRepo.FindAdminMemberByUsername(ctx, req.Username) // Use 'l' instead of 's'
	// if err != nil {
	// 	// Assuming FindAdminMemberByUsername wraps errors appropriately
	// 	err = gerror.WrapCode(codes.CodeInternalError, err, "查询用户信息失败")
	// 	return nil, err
	// }

	// // 检查用户是否存在或状态是否正常
	// if adminMember == nil || adminMember.Status != 1 { // Check status as well
	// 	err = codes.NewError(codes.CodeLoginFailed)
	// 	return nil, err
	// }
	// //判断用户是否软删除
	// if adminMember.DeletedAt != nil {
	// 	err = codes.NewError(codes.CodeLoginFailed)
	// 	return nil, err
	// }

	// // 设置用户ID用于记录登录日志
	// userId = adminMember.Id

	// // 验证密码 using service
	// if !l.authService.VerifyPassword(ctx, req.Password, adminMember.PasswordHash) { // Use 'l' instead of 's'
	// 	err = codes.NewError(codes.CodeLoginFailed)
	// 	return nil, err
	// }

	// //首先撤销其他的令牌 (Keep direct DAO call for now)
	// // err = dao.PersonalAccessTokens.RevokeAllUserTokens(ctx, gconv.Uint64(adminMember.Id))
	// // if err != nil {
	// // 	err = codes.WrapError(err, codes.CodeInternalError)
	// // 	return nil, err
	// // }

	// // 生成认证令牌 using service
	// token, expireAt, err := l.authService.GenerateToken(ctx, uint(adminMember.Id), adminMember.Username) // Use 'l' instead of 's'
	// if err != nil {
	// 	err = gerror.WrapCode(codes.CodeInternalError, err, "生成认证令牌失败")
	// 	return nil, err
	// }

	// // 更新用户最后活跃时间 (Consider moving this to a dedicated repository method if needed)
	// // Run in background goroutine
	// go func(memberId int64) {
	// 	ctxBg := gctx.New() // Use a background context for non-critical background task
	// 	_, updateErr := dao.AdminMember.Ctx(ctxBg).
	// 		Data(g.Map{dao.AdminMember.Columns().LastActiveAt: gtime.Now()}).
	// 		Where(dao.AdminMember.Columns().Id, memberId).
	// 		Update()
	// 	if updateErr != nil {
	// 		g.Log().Errorf(ctxBg, "后台更新用户 %d 最后活跃时间失败: %v", memberId, updateErr)
	// 	}
	// }(adminMember.Id) // Pass memberId to goroutine

	// // 登录成功后，异步缓存用户信息和权限
	// go func(memberId int64, username string) {
	// 	ctxBg := gctx.New()

	// 	// 缓存用户基本信息
	// 	cacheManager := cache.GetInstance()
	// 	userInfo := &cache.UserInfo{
	// 		ID:       memberId,
	// 		Username: username,
	// 		Nickname: adminMember.RealName, // 使用RealName作为昵称
	// 		Avatar:   adminMember.Avatar,
	// 		Email:    adminMember.Email,
	// 		Phone:    adminMember.Mobile, // 使用Mobile字段
	// 		Status:   adminMember.Status,
	// 	}

	// 	if cacheErr := cacheManager.SetUserInfo(ctxBg, username, userInfo); cacheErr != nil {
	// 		g.Log().Errorf(ctxBg, "缓存用户信息失败: username=%s, error=%v", username, cacheErr)
	// 	}

	// 	// 缓存用户权限（从Casdoor获取）
	// 	permissionService := cache.NewPermissionService()
	// 	if permErr := permissionService.CacheUserPermissionsFromCasdoor(ctxBg, username); permErr != nil {
	// 		g.Log().Errorf(ctxBg, "缓存用户权限失败: username=%s, error=%v", username, permErr)
	// 	}
	// }(adminMember.Id, adminMember.Username)

	// // 登录成功，返回token
	// res = &v1.LoginRes{
	// 	Token:    token,    // Use token string from authService
	// 	ExpireAt: expireAt, // Use expireAt timestamp from authService
	// }
	return nil, nil // err is nil here for successful login
}

// // GetCaptcha 获取验证码
// func (l *sSystemLogic) GetCaptcha(ctx context.Context, req *v1.GetCaptchaReq) (res *v1.GetCaptchaRes, err error) {
// 	// // 生成滑动验证码 using service
// 	// slideData, err := l.authService.GenerateCaptcha(ctx) // Use 'l' instead of 's'
// 	// if err != nil {
// 	// 	return nil, codes.WrapError(err, codes.CodeInternalError)
// 	// }

// 	// // 返回滑动验证码数据
// 	// res = &v1.GetCaptchaRes{
// 	// 	SlideData: slideData,
// 	// }
// 	return nil, nil
// }

// UpdateAdminInfo 更新管理员个人信息
func (l *sSystemLogic) UpdateAdminInfo(ctx context.Context, req *v1.UpdateAdminInfoReq) (res *v1.UpdateAdminInfoRes, err error) {

	return &v1.UpdateAdminInfoRes{}, nil
}

// // Redis缓存相关常量
// const (
// 	// 用户信息缓存键前缀
// 	userInfoCachePrefix = "admin:user:%d:info"
// 	// 用户权限缓存键前缀
// 	userPermissionsCachePrefix = "admin:user:%d:permissions"
// 	// 用户角色缓存键前缀
// 	userRolesCachePrefix = "admin:user:%d:roles"
// 	// 缓存过期时间
// 	userInfoCacheTTL   = 30 * time.Minute // 用户信息缓存30分钟
// 	userPermissionsTTL = 60 * time.Minute // 权限缓存1小时
// )

// // getUserInfoFromCache 从Redis获取用户信息
// func (l *sSystemLogic) getUserInfoFromCache(ctx context.Context, userId int64) (*v1.GetAdminInfoRes, error) {
// 	redis := g.Redis()
// 	cacheKey := fmt.Sprintf(userInfoCachePrefix, userId)

// 	// 从Redis获取用户信息
// 	cachedData, err := redis.Get(ctx, cacheKey)
// 	if err != nil {
// 		g.Log().Debugf(ctx, "从Redis获取用户信息失败: userId=%d, error=%v", userId, err)
// 		return nil, err
// 	}

// 	if cachedData == nil {
// 		g.Log().Debugf(ctx, "Redis中不存在用户信息缓存: userId=%d", userId)
// 		return nil, nil
// 	}

// 	// 反序列化用户信息
// 	var userInfo v1.GetAdminInfoRes
// 	if err := json.Unmarshal([]byte(cachedData.String()), &userInfo); err != nil {
// 		g.Log().Errorf(ctx, "反序列化用户信息失败: userId=%d, error=%v", userId, err)
// 		return nil, err
// 	}

// 	g.Log().Debugf(ctx, "从Redis成功获取用户信息: userId=%d", userId)
// 	return &userInfo, nil
// }

// // setUserInfoToCache 将用户信息存储到Redis
// func (l *sSystemLogic) setUserInfoToCache(ctx context.Context, userId int64, userInfo *v1.GetAdminInfoRes) error {
// 	redis := g.Redis()
// 	cacheKey := fmt.Sprintf(userInfoCachePrefix, userId)

// 	// 序列化用户信息
// 	data, err := json.Marshal(userInfo)
// 	if err != nil {
// 		g.Log().Errorf(ctx, "序列化用户信息失败: userId=%d, error=%v", userId, err)
// 		return err
// 	}

// 	// 存储到Redis
// 	_, err = redis.Set(ctx, cacheKey, string(data))
// 	if err != nil {
// 		g.Log().Errorf(ctx, "存储用户信息到Redis失败: userId=%d, error=%v", userId, err)
// 		return err
// 	}

// 	// 设置过期时间
// 	_, err = redis.Expire(ctx, cacheKey, int64(userInfoCacheTTL.Seconds()))
// 	if err != nil {
// 		g.Log().Errorf(ctx, "设置用户信息缓存过期时间失败: userId=%d, error=%v", userId, err)
// 		return err
// 	}

// 	g.Log().Debugf(ctx, "成功存储用户信息到Redis: userId=%d", userId)
// 	return nil
// }

// // getUserPermissionsFromCache 从Redis获取用户权限
// func (l *sSystemLogic) getUserPermissionsFromCache(ctx context.Context, userId int64) ([]string, error) {
// 	redis := g.Redis()
// 	cacheKey := fmt.Sprintf(userPermissionsCachePrefix, userId)

// 	// 从Redis获取权限列表
// 	permissions, err := redis.SMembers(ctx, cacheKey)
// 	if err != nil {
// 		g.Log().Debugf(ctx, "从Redis获取用户权限失败: userId=%d, error=%v", userId, err)
// 		return nil, err
// 	}

// 	if len(permissions) == 0 {
// 		g.Log().Debugf(ctx, "Redis中不存在用户权限缓存: userId=%d", userId)
// 		return nil, nil
// 	}

// 	// 转换为字符串切片
// 	result := make([]string, len(permissions))
// 	for i, perm := range permissions {
// 		result[i] = perm.String()
// 	}

// 	g.Log().Debugf(ctx, "从Redis成功获取用户权限: userId=%d, count=%d", userId, len(result))
// 	return result, nil
// }

// // setUserPermissionsToCache 将用户权限存储到Redis
// func (l *sSystemLogic) setUserPermissionsToCache(ctx context.Context, userId int64, permissions []string) error {
// 	redis := g.Redis()
// 	cacheKey := fmt.Sprintf(userPermissionsCachePrefix, userId)

// 	// 先删除旧的权限集合
// 	_, _ = redis.Del(ctx, cacheKey)

// 	if len(permissions) > 0 {
// 		// 将权限添加到Redis集合
// 		for _, perm := range permissions {
// 			_, err := redis.SAdd(ctx, cacheKey, perm)
// 			if err != nil {
// 				g.Log().Errorf(ctx, "存储用户权限到Redis失败: userId=%d, perm=%s, error=%v", userId, perm, err)
// 				return err
// 			}
// 		}

// 		// 设置过期时间
// 		_, _ = redis.Expire(ctx, cacheKey, int64(userPermissionsTTL.Seconds()))
// 	}

// 	g.Log().Debugf(ctx, "成功存储用户权限到Redis: userId=%d, count=%d", userId, len(permissions))
// 	return nil
// }

// // clearUserCache 清除用户相关的所有缓存
// func (l *sSystemLogic) clearUserCache(ctx context.Context, userId int64) error {
// 	redis := g.Redis()

// 	// 构建缓存键
// 	userInfoKey := fmt.Sprintf(userInfoCachePrefix, userId)
// 	userPermissionsKey := fmt.Sprintf(userPermissionsCachePrefix, userId)
// 	userRolesKey := fmt.Sprintf(userRolesCachePrefix, userId)

// 	// 删除所有相关缓存
// 	keys := []string{userInfoKey, userPermissionsKey, userRolesKey}
// 	for _, key := range keys {
// 		if _, err := redis.Del(ctx, key); err != nil {
// 			g.Log().Errorf(ctx, "删除缓存失败: key=%s, error=%v", key, err)
// 			return err
// 		}
// 	}

// 	g.Log().Debugf(ctx, "成功清除用户缓存: userId=%d", userId)
// 	return nil
// }

// // cacheUserPermissions 获取并缓存用户权限
// func (l *sSystemLogic) cacheUserPermissions(ctx context.Context, userId int64) error {
// 	// 这里应该调用权限相关的仓库或服务来获取用户权限
// 	// 由于当前代码中权限管理可能还在开发中，这里先实现一个基础版本

// 	// TODO: 实现获取用户权限的逻辑
// 	// 1. 获取用户的角色
// 	// 2. 根据角色获取权限
// 	// 3. 将权限存储到Redis

// 	// 临时实现：获取用户基本权限
// 	permissions := []string{
// 		"admin:user:view",
// 		"admin:user:edit",
// 		"admin:dashboard:view",
// 	}

// 	// 存储权限到Redis
// 	return l.setUserPermissionsToCache(ctx, userId, permissions)
// }

// // getUserPermissions 获取用户权限（优先从缓存获取）
// func (l *sSystemLogic) getUserPermissions(ctx context.Context, userId int64) ([]string, error) {
// 	// 首先尝试从Redis缓存获取
// 	permissions, err := l.getUserPermissionsFromCache(ctx, userId)
// 	if err != nil {
// 		g.Log().Warningf(ctx, "从Redis获取用户权限失败: userId=%d, error=%v", userId, err)
// 	}

// 	// 如果缓存中没有权限数据，重新获取并缓存
// 	if permissions == nil || len(permissions) == 0 {
// 		g.Log().Debugf(ctx, "缓存中无用户权限，重新获取: userId=%d", userId)

// 		// 获取并缓存权限
// 		if cacheErr := l.cacheUserPermissions(ctx, userId); cacheErr != nil {
// 			g.Log().Errorf(ctx, "缓存用户权限失败: userId=%d, error=%v", userId, cacheErr)
// 			return nil, cacheErr
// 		}

// 		// 再次从缓存获取
// 		permissions, err = l.getUserPermissionsFromCache(ctx, userId)
// 		if err != nil {
// 			return nil, err
// 		}
// 	}

// 	return permissions, nil
// }

// hasPermission 检查用户是否拥有指定权限
// func (l *sSystemLogic) hasPermission(ctx context.Context, userId int64, permission string) (bool, error) {
// 	permissions, err := l.getUserPermissions(ctx, userId)
// 	if err != nil {
// 		return false, err
// 	}

// 	for _, perm := range permissions {
// 		if perm == permission {
// 			return true, nil
// 		}
// 	}

// 	return false, nil
// }

// VerifyTOTPCode 验证TOTP代码
func (l *sSystemLogic) VerifyTOTPCode(ctx context.Context, merchantId uint, code string) (bool, error) {
	// 获取商户信息
	var merchant *entity.Merchants
	err := dao.Merchants.Ctx(ctx).
		Where(dao.Merchants.Columns().MerchantId, merchantId).
		WhereNull(dao.Merchants.Columns().DeletedAt).
		Scan(&merchant)

	if err != nil || merchant == nil {
		return false, codes.NewError(codes.CodeMerchantNotFound)
	}

	// 验证TOTP码，使用Google2FaSecret字段
	valid := utility.VerifyGoogle2FACode(merchant.Google2FaSecret, code)
	return valid, nil
}
