package v1

import (
	"context"
	"fmt"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	adminConstants "admin-api/internal/constants"
	"admin-api/internal/utils"

	"github.com/yalks/wallet"
	"github.com/yalks/wallet/constants"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ListUserWithdraws 获取提现记录列表（带代理和telegram信息）
func (s *sSystemLogic) ListUserWithdraws(ctx context.Context, req *v1.ListUserWithdrawsReq) (res *v1.ListUserWithdrawsRes, err error) {
	// 初始化返回结果
	res = &v1.ListUserWithdrawsRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*v1.UserWithdrawsListItem, 0),
	}

	// 参数校验
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取用户提现记录列表（带代理和telegram信息）
	list, total, err := s.userWithdrawRepo.ListWithAgentInfo(ctx, req)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取用户提现记录列表失败")
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = common.CalculateTotalPage(total, req.PageSize)

	// 设置返回数据
	res.Data = list

	return res, nil
}

// GetUserWithdrawDetail 获取提现记录详情
func (s *sSystemLogic) GetUserWithdrawDetail(ctx context.Context, req *v1.GetUserWithdrawDetailReq) (res *v1.GetUserWithdrawDetailRes, err error) {
	// 初始化返回结果
	res = &v1.GetUserWithdrawDetailRes{
		Data: nil,
	}

	// 查询数据（带代理和telegram信息）
	detail, err := s.userWithdrawRepo.GetDetailWithAgentInfo(ctx, req.UserWithdrawsId)
	if err != nil {
		return nil, err
	}

	// 检查是否存在
	if detail == nil {
		return nil, gerror.NewCode(codes.CodeNotFound, "提现记录不存在")
	}

	// 直接使用返回的数据（已包含代理和telegram信息）
	res.Data = detail

	return res, nil
}

// ReviewUserWithdraw 审核提现记录
func (s *sSystemLogic) ReviewUserWithdraw(ctx context.Context, req *v1.ReviewUserWithdrawReq) (res *v1.ReviewUserWithdrawRes, err error) {
	// 初始化返回结果
	res = &v1.ReviewUserWithdrawRes{
		Success: false,
	}

	// 查询提现记录
	withdraw, err := s.userWithdrawRepo.GetByID(ctx, req.UserWithdrawsId)
	if err != nil {
		return nil, err
	}

	// 检查是否存在
	if withdraw == nil {
		return nil, gerror.NewCode(codes.CodeNotFound, "提现记录不存在")
	}

	// 检查状态是否为待审核
	if withdraw.State != 1 && withdraw.State != 2 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "只能审核待审核状态的提现记录")
	}

	// 使用事务处理
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var updateErr error

		// 根据操作类型更新状态
		if req.Action == "approve" {
			// 通过: 状态更新为已完成(4)
			updateErr = s.userWithdrawRepo.UpdateStateWithReason(ctx, tx, req.UserWithdrawsId, 4, "", "", req.AdminRemark)
		} else if req.Action == "reject" {
			// 拒绝: 状态更新为已拒绝(3)
			updateErr = s.userWithdrawRepo.UpdateStateWithReason(ctx, tx, req.UserWithdrawsId, 3, req.RefuseReasonZh, req.RefuseReasonEn, req.AdminRemark)

			// 获取代币信息以获取符号 (Symbol) 和 精度 (Decimals)
			token, err := s.tokenRepo.GetByID(ctx, uint(withdraw.TokenId))
			if err != nil {
				return gerror.Wrapf(err, "获取代币信息失败 (tokenId: %d)", withdraw.TokenId)
			}
			if token == nil {
				return gerror.NewCodef(codes.CodeTokenNotFound, "代币不存在 (tokenId: %d)", withdraw.TokenId)
			}
			symbol := token.Symbol

			// --- 事务开始 ---
			// 获取管理员名称
			adminName := ""
			if r := g.RequestFromCtx(ctx); r == nil {
				return codes.NewError(codes.CodeUnauthorized)
			} else {
				adminName = r.GetCtxVar("username").String()
			}

			// 使用新的资金操作描述器
			descriptor := utils.NewFundOperationDescriptor("zh")
			businessID := descriptor.GenerateBusinessID(adminConstants.FundOpWithdrawRefund, withdraw.UserId, gtime.Now().Unix())

			// 构建描述信息
			memo := fmt.Sprintf("%s-%s", adminName, req.AdminRemark)
			description := descriptor.FormatDescriptionWithMemo(adminConstants.FundOpWithdrawRefund, withdraw.Amount.String(), symbol, memo)

			// 使用新的请求构建方法
			walletReq := &constants.FundOperationRequest{
				UserID:      uint64(withdraw.UserId),
				TokenSymbol: symbol,
				Amount:      withdraw.Amount,
				BusinessID:  businessID,
				FundType:    constants.FundTypeWithdrawRefund,
				Description: description,
				Metadata: map[string]string{
					"type":          "withdraw_refund",
					"request_id":    fmt.Sprintf("%d", withdraw.UserId),
					"operation":     "withdraw_refund",
					"changeReason":  req.AdminRemark,
					"admin_name":    adminName,
					"withdraw_id":   fmt.Sprintf("%d", withdraw.UserWithdrawsId),
					"refuse_reason": req.RefuseReasonZh,
				},
				RequestSource: "admin",
			}

			_, err = wallet.Manager().ProcessFundOperationInTx(ctx, tx, walletReq)
			if err != nil {
				g.Log().Errorf(ctx, "退还用户资金失败: %v", err)
				return gerror.Wrap(err, "退还用户资金失败")
			}

		}

		if updateErr != nil {
			return updateErr
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	res.Success = true
	return res, nil
}

// UpdateUserWithdrawStatus 更新提现记录状态
func (s *sSystemLogic) UpdateUserWithdrawStatus(ctx context.Context, req *v1.UpdateUserWithdrawStatusReq) (res *v1.UpdateUserWithdrawStatusRes, err error) {
	// 初始化返回结果
	res = &v1.UpdateUserWithdrawStatusRes{
		Success: false,
	}

	// 查询提现记录
	withdraw, err := s.userWithdrawRepo.GetByID(ctx, req.UserWithdrawsId)
	if err != nil {
		return nil, err
	}

	// 检查是否存在
	if withdraw == nil {
		return nil, gerror.NewCode(codes.CodeNotFound, "提现记录不存在")
	}

	// 检查状态转换是否合法
	if !isValidStatusTransition(withdraw.State, req.State) {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的状态转换")
	}

	// 使用事务处理
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 更新状态
		return s.userWithdrawRepo.UpdateStateWithTxInfo(ctx, tx, req.UserWithdrawsId, req.State, req.TxHash, req.ErrorMessage, req.AdminRemark)
	})

	if err != nil {
		return nil, err
	}

	res.Success = true
	return res, nil
}

// isValidStatusTransition 检查状态转换是否合法
func isValidStatusTransition(currentState, targetState uint) bool {
	// 状态定义:
	// 1-待审核, 2-处理中, 3-已拒绝, 4-已完成, 5-失败

	// 只允许以下状态转换:
	// 2(处理中) -> 4(已完成)
	// 2(处理中) -> 5(失败)

	if currentState == 2 && (targetState == 4 || targetState == 5) {
		return true
	}

	// 特殊情况: 允许从任何状态直接设置为处理中(2)
	// 这通常用于管理员手动干预
	if targetState == 2 {
		return true
	}

	return false
}
