package v1

import (
	"admin-api/api/common"
	"context"
	"math"

	v1 "admin-api/api/system/v1"
	// "admin-api/internal/dao" // Will be removed later if fully replaced
	"admin-api/internal/codes"
	"admin-api/internal/model/entity"
	"admin-api/utility/excel" // Keep for export

	// operationLogLogic "admin-api/internal/logic/system/v1/operation_log" // Removed unused import
	"admin-api/utility/timeutil" // Import the new time utility package

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// GetOperationLogList 获取操作日志列表
func (l *sSystemLogic) GetOperationLogList(ctx context.Context, req *v1.GetOperationLogListReq) (res *v1.GetOperationLogListRes, err error) {
	// 检查请求参数
	if req == nil {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "请求参数不能为空")
	}
	// 初始化返回结果，避免空指针
	res = &v1.GetOperationLogListRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
			TotalSize:   0,
			TotalPage:   0,
		},
		Data: make([]*entity.OperationLog, 0),
	}

	// 构建查询条件
	condition := g.Map{}

	// 处理ID查询
	if req.Id > 0 {
		condition["id"] = req.Id
	}

	// 处理用户名模糊查询
	if req.Username != "" {
		condition["username LIKE ?"] = "%" + req.Username + "%"
	}

	// 处理操作IP模糊查询
	if req.OperationIp != "" {
		condition["operation_ip LIKE ?"] = "%" + req.OperationIp + "%"
	}

	// 处理模块模糊查询
	if req.Module != "" {
		condition["module LIKE ?"] = "%" + req.Module + "%"
	}

	// 处理操作名称模糊查询
	if req.Action != "" {
		condition["action LIKE ?"] = "%" + req.Action + "%"
	}

	// 处理请求方法查询
	if req.RequestMethod != "" {
		condition["request_method"] = req.RequestMethod
	}

	// 处理状态查询（-1表示查询全部）
	if req.Status >= 0 {
		condition["status"] = req.Status
	}

	// 处理时间范围查询
	// Use ParseTimeRange from the utility package
	startTime, endTime := timeutil.ParseTimeRange(req.DateRange, req.StartTime, req.EndTime)
	if startTime != "" {
		condition["created_at >= ?"] = startTime
	}
	if endTime != "" {
		condition["created_at <= ?"] = endTime
	}

	// 如果是导出，查询所有数据
	if req.Export == 1 {
		// 导出时不进行分页限制
		list, _, err := l.operationLogRepo.List(ctx, 1, 9999999, condition)
		if err != nil {
			return res, err
		}

		// 转换为响应结构
		operationLogs := make([]*entity.OperationLog, 0)
		if len(list) > 0 {
			operationLogs = make([]*entity.OperationLog, len(list))
			if err = gconv.Structs(list, &operationLogs); err != nil {
				return res, err
			}
		}

		// 导出Excel
		// 定义Excel表头
		excelTags := []string{
			"日志ID", "用户名", "操作IP", "操作模块", "操作名称", "请求方法", "请求URL", "状态", "操作时间",
		}

		// 准备导出数据
		exportData := make([]interface{}, len(operationLogs))
		for i, v := range operationLogs {
			// 状态显示转换
			status := "失败"
			if v.Status == 1 {
				status = "成功"
			}

			exportData[i] = struct {
				Id            int64  `json:"id"`
				Username      string `json:"username"`
				OperationIp   string `json:"operation_ip"`
				Module        string `json:"module"`
				Action        string `json:"action"`
				RequestMethod string `json:"request_method"`
				RequestUrl    string `json:"request_url"`
				Status        string `json:"status"`
				CreatedAt     string `json:"created_at"`
			}{
				Id:            v.Id,
				Username:      v.Username,
				OperationIp:   v.OperationIp,
				Module:        v.Module,
				Action:        v.Action,
				RequestMethod: v.RequestMethod,
				RequestUrl:    v.RequestUrl,
				Status:        status,
				CreatedAt:     v.CreatedAt.Format("2006-01-02 15:04:05"),
			}
		}

		// 调用Excel导出工具
		return res, excel.ExportByStructs(ctx, excelTags, exportData, "操作日志", "操作日志列表")
	}

	// 确保页码和每页大小合法
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 查询数据
	list, total, err := l.operationLogRepo.List(ctx, req.Page, req.PageSize, condition)
	if err != nil {
		return res, err
	}

	// 转换为响应结构
	operationLogs := make([]*entity.OperationLog, 0)
	if len(list) > 0 {
		operationLogs = make([]*entity.OperationLog, len(list))
		if err = gconv.Structs(list, &operationLogs); err != nil {
			return res, err
		}

		// 敏感数据处理，如果有需要的话
		// 例如：对请求参数或响应中的敏感数据进行脱敏
		// 这里可以根据具体需求添加相关逻辑
	}

	// 更新返回结果
	res.Page.TotalSize = total
	res.Page.TotalPage = int(math.Ceil(float64(total) / float64(req.PageSize)))
	res.Data = operationLogs

	return res, nil
}

// GetOperationLogDetail 获取操作日志详情
func (l *sSystemLogic) GetOperationLogDetail(ctx context.Context, req *v1.GetOperationLogDetailReq) (res *v1.GetOperationLogDetailRes, err error) {
	// 根据ID查询日志详情
	logDetail, err := l.operationLogRepo.GetByID(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if logDetail == nil {
		return &v1.GetOperationLogDetailRes{
			Info: nil,
		}, nil
	}

	// 转换为响应实体
	var entityLogDetail *entity.OperationLog
	if err = gconv.Struct(logDetail, &entityLogDetail); err != nil {
		return nil, err
	}

	// 敏感数据处理，如果有需要的话
	// 例如：对请求参数或响应中的敏感数据进行脱敏
	// 这里可以根据具体需求添加相关逻辑

	return &v1.GetOperationLogDetailRes{
		Info: entityLogDetail,
	}, nil
}
