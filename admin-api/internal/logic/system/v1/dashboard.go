package v1

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/logic/system/v1/dashboard"
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
)

// GetDashboardStats retrieves statistics for the admin dashboard homepage.
func (s *sSystemLogic) GetDashboardStats(ctx context.Context, req *v1.GetDashboardStatsReq) (res *v1.GetDashboardStatsRes, err error) {
	// Initialize response
	res = &v1.GetDashboardStatsRes{}

	// Create repository instance if not already available
	if s.dashboardRepo == nil {
		s.dashboardRepo = dashboard.NewDashboardRepository()
	}

	// Get user statistics
	userStats, err := s.dashboardRepo.GetUserStats(ctx)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取用户统计信息失败")
	}
	res.UserStats = *userStats

	// Get deposit statistics
	depositStats, err := s.dashboardRepo.GetDepositStats(ctx)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取充值统计信息失败")
	}
	res.DepositStats = *depositStats

	// Get withdrawal statistics
	withdrawalStats, err := s.dashboardRepo.GetWithdrawalStats(ctx)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取提现统计信息失败")
	}
	res.WithdrawalStats = *withdrawalStats

	// Get transfer statistics
	transferStats, err := s.dashboardRepo.GetTransferStats(ctx)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取转账统计信息失败")
	}
	res.TransferStats = *transferStats

	// Get payment statistics
	paymentStats, err := s.dashboardRepo.GetPaymentStats(ctx)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取收款统计信息失败")
	}
	res.PaymentStats = *paymentStats

	// Get red packet statistics
	redPacketStats, err := s.dashboardRepo.GetRedPacketStats(ctx)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取红包统计信息失败")
	}
	res.RedPacketStats = *redPacketStats

	return res, nil
}
