package menu

import (
	"context"
	"database/sql"

	// "fmt" // 移除未使用的 fmt 包
	"admin-api/internal/codes"
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/menu" // Import the interface package
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

type menuRepository struct{}

// NewMenuRepository creates and returns a new instance of IMenuRepository.
func NewMenuRepository() menu.IMenuRepository {
	return &menuRepository{}
}

// List retrieves a flat list of admin menus based on conditions.
func (r *menuRepository) List(ctx context.Context, condition g.Map) ([]*entity.AdminMenu, error) {
	m := dao.AdminMenu.Ctx(ctx).WhereNull(dao.AdminMenu.Columns().DeletedAt)
	if len(condition) > 0 {
		m = m.Where(condition)
	}
	m = m.OrderAsc(dao.AdminMenu.Columns().Sort).OrderAsc(dao.AdminMenu.Columns().Id)

	var menus []*entity.AdminMenu
	err := m.Scan(&menus)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询菜单列表失败")
	}
	return menus, nil
}

// ListPaginated retrieves a paginated list of admin menus based on conditions.
func (r *menuRepository) ListPaginated(ctx context.Context, page, pageSize int, condition g.Map) ([]*entity.AdminMenu, int, error) {
	m := dao.AdminMenu.Ctx(ctx).WhereNull(dao.AdminMenu.Columns().DeletedAt)
	if len(condition) > 0 {
		m = m.Where(condition)
	}

	// 获取总数
	total, err := m.Count()
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "获取菜单总数失败")
	}

	if total == 0 {
		return make([]*entity.AdminMenu, 0), 0, nil // 返回空切片
	}

	// 应用排序和分页
	m = m.OrderAsc(dao.AdminMenu.Columns().Sort).OrderAsc(dao.AdminMenu.Columns().Id)

	var menus []*entity.AdminMenu
	err = m.Page(page, pageSize).Scan(&menus)
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询菜单列表失败")
	}

	return menus, total, nil
}

// Create inserts a new menu record (without handling Tree).
func (r *menuRepository) Create(ctx context.Context, menuDo *do.AdminMenu) (id int64, err error) {
	// Ensure timestamps are set
	if menuDo.CreatedAt == nil {
		menuDo.CreatedAt = gtime.Now()
	}
	if menuDo.UpdatedAt == nil {
		menuDo.UpdatedAt = gtime.Now()
	}
	// 移除设置临时 Tree 值的逻辑，Tree 和 Level 将在 Logic 层事务中更新
	result, err := dao.AdminMenu.Ctx(ctx).Data(menuDo).Insert() // 移除 OmitEmpty()
	if err != nil {
		g.Log().Errorf(ctx, "创建菜单失败: %s 具体请求数据: %+v", err.Error(), menuDo)
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "创建菜单失败")
	}
	id, err = result.LastInsertId()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "获取新菜单ID失败")
	}
	return id, nil
}

// Update updates menu fields within a transaction.
func (r *menuRepository) Update(ctx context.Context, tx gdb.TX, id int64, data g.Map) error {
	if len(data) == 0 {
		return nil
	}
	data[dao.AdminMenu.Columns().UpdatedAt] = gtime.Now() // Ensure UpdatedAt is set

	_, err := tx.Model(dao.AdminMenu.Table()).
		Data(data).
		Where(dao.AdminMenu.Columns().Id, id).
		Update()
	if err != nil {
		return gerror.WrapCodef(codes.CodeInternalError, err, "更新菜单失败 (ID: %d)", id)
	}
	return nil
}

// Delete performs a soft delete on a menu within a transaction.
func (r *menuRepository) Delete(ctx context.Context, tx gdb.TX, id int64) error {
	_, err := tx.Model(dao.AdminMenu.Table()).
		Data(g.Map{dao.AdminMenu.Columns().DeletedAt: gtime.Now()}).
		Where(dao.AdminMenu.Columns().Id, id).
		WhereNull(dao.AdminMenu.Columns().DeletedAt).
		Update()
	// If the record was already deleted, it's not an error for the delete operation itself.
	if err != nil && err != sql.ErrNoRows {
		return gerror.WrapCodef(codes.CodeInternalError, err, "软删除菜单失败 (ID: %d)", id)
	}
	return nil
}

// GetByID retrieves a single admin menu by ID.
func (r *menuRepository) GetByID(ctx context.Context, id int64) (*entity.AdminMenu, error) {
	// 参数校验
	if id <= 0 {
		return nil, gerror.NewCodef(codes.CodeInvalidParameter, "无效的菜单ID: %d", id)
	}

	var menu *entity.AdminMenu
	err := dao.AdminMenu.Ctx(ctx).
		Where(dao.AdminMenu.Columns().Id, id).
		WhereNull(dao.AdminMenu.Columns().DeletedAt).
		Scan(&menu)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCodef(codes.CodeMenuNotFound, "菜单不存在 (ID: %d)", id)
		}
		g.Log().Errorf(ctx, "查询菜单失败 (ID: %d): %+v", id, err)
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询菜单失败 (ID: %d)", id)
	}

	// 额外安全检查
	if menu == nil {
		g.Log().Warningf(ctx, "查询到空的菜单 (ID: %d)", id)
		return nil, gerror.NewCodef(codes.CodeMenuNotFound, "菜单不存在 (ID: %d)", id)
	}

	return menu, nil
}

// ExistsByNameAndPid checks if a menu with the same name exists under the same parent, excluding a specific ID.
func (r *menuRepository) ExistsByNameAndPid(ctx context.Context, name string, pid int64, excludeId ...int64) (bool, error) {
	m := dao.AdminMenu.Ctx(ctx).
		Where(dao.AdminMenu.Columns().Name, name).
		Where(dao.AdminMenu.Columns().Pid, pid).
		WhereNull(dao.AdminMenu.Columns().DeletedAt)
	if len(excludeId) > 0 {
		m = m.WhereNot(dao.AdminMenu.Columns().Id, excludeId[0])
	}
	count, err := m.Count()
	if err != nil {
		return false, gerror.WrapCodef(codes.CodeInternalError, err, "检查同级菜单名称是否存在失败 (Name: %s, Pid: %d)", name, pid)
	}
	return count > 0, nil
}

// HasDirectChildren checks if a menu has any direct children.
func (r *menuRepository) HasDirectChildren(ctx context.Context, id int64) (bool, error) {
	count, err := dao.AdminMenu.Ctx(ctx).
		Where(dao.AdminMenu.Columns().Pid, id).
		WhereNull(dao.AdminMenu.Columns().DeletedAt).
		Count()
	if err != nil {
		return false, gerror.WrapCodef(codes.CodeInternalError, err, "检查子菜单是否存在失败 (ID: %d)", id)
	}
	return count > 0, nil
}

// IsMenuAssignedToRole checks if a menu is assigned to any role.
func (r *menuRepository) IsMenuAssignedToRole(ctx context.Context, id int64) (bool, error) {
	// count, err := dao.AdminRoleMenu.Ctx(ctx).
	// 	Where(dao.AdminRoleMenu.Columns().MenuId, id).
	// 	Count()
	// if err != nil {
	// 	return false, gerror.WrapCodef(codes.CodeInternalError, err, "检查菜单是否分配给角色失败 (MenuID: %d)", id)
	// }
	return true, nil
}

// FindDescendantsByTree finds all descendants of a menu based on the tree path prefix.
func (r *menuRepository) FindDescendantsByTree(ctx context.Context, treePrefix string) ([]*entity.AdminMenu, error) {
	if !strings.HasSuffix(treePrefix, "/") {
		treePrefix += "/" // Ensure the prefix ends with a slash
	}
	var descendants []*entity.AdminMenu
	err := dao.AdminMenu.Ctx(ctx).
		WhereLike(dao.AdminMenu.Columns().Tree, treePrefix+"%").
		WhereNot(dao.AdminMenu.Columns().Tree, treePrefix). // Exclude self
		WhereNull(dao.AdminMenu.Columns().DeletedAt).
		Scan(&descendants)
	if err != nil {
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "查询子孙菜单失败 (TreePrefix: %s)", treePrefix)
	}
	return descendants, nil
}

// BatchUpdateLevelTree updates the level and tree path for multiple menus within a transaction.
func (r *menuRepository) BatchUpdateLevelTree(ctx context.Context, tx gdb.TX, updates map[int64]g.Map) error {
	if len(updates) == 0 {
		return nil
	}
	for id, data := range updates {
		data[dao.AdminMenu.Columns().UpdatedAt] = gtime.Now() // Ensure UpdatedAt is set
		_, err := tx.Model(dao.AdminMenu.Table()).
			Data(data).
			Where(dao.AdminMenu.Columns().Id, id).
			Update()
		if err != nil {
			return gerror.WrapCodef(codes.CodeInternalError, err, "批量更新菜单 Level/Tree 失败 (ID: %d)", id)
		}
	}
	return nil
}

// GetMenusByPaths retrieves a list of admin menus based on their paths and status.
func (r *menuRepository) GetMenusByPaths(ctx context.Context, paths []string, status int) ([]*entity.AdminMenu, error) {
	if len(paths) == 0 {
		return make([]*entity.AdminMenu, 0), nil
	}

	var menus []*entity.AdminMenu
	err := dao.AdminMenu.Ctx(ctx).
		Where(dao.AdminMenu.Columns().Path, paths).    // 查询 Path 在给定列表中的菜单
		Where(dao.AdminMenu.Columns().Status, status). // 过滤指定状态的菜单
		WhereNull(dao.AdminMenu.Columns().DeletedAt).  // 过滤未软删除的菜单
		OrderAsc(dao.AdminMenu.Columns().Sort).        // 按 Sort 字段升序排序
		OrderAsc(dao.AdminMenu.Columns().Id).          // 按 ID 字段升序排序
		Scan(&menus)
	if err != nil {
		return nil, gerror.WrapCodef(codes.CodeInternalError, err, "根据路径查询菜单失败: %+v", err)
	}

	return menus, nil
}
