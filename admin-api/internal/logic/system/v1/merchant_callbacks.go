package v1

import (
	"context"
	"fmt"
	"strings"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"admin-api/internal/service"
	"admin-api/utility/csv"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// GetMyCallbacks 获取我的回调记录列表
func (s *sSystemLogic) GetMyCallbacks(ctx context.Context, req *v1.GetMyCallbacksReq) (res *v1.GetMyCallbacksRes, err error) {
	res = &v1.GetMyCallbacksRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*v1.MerchantCallbackInfoType, 0),
	}

	// 使用灵活的日期范围处理，支持dateRange和createdAt数组两种格式
	var startTime, endTime string
	if req.DateRange != "" {
		dates := strings.Split(req.DateRange, ",")
		if len(dates) == 2 {
			startTime = strings.TrimSpace(dates[0]) + " 00:00:00"
			endTime = strings.TrimSpace(dates[1]) + " 23:59:59"
		}
	} else if len(req.CreatedAt) == 2 {
		startTime = req.CreatedAt[0]
		endTime = req.CreatedAt[1]
	}

	// 如果是导出
	if req.Export {
		// 导出时不分页，查询所有符合条件的数据
		var callbacks []entity.MerchantCallbacks

		// 构建查询
		db := dao.MerchantCallbacks.Ctx(ctx)

		// 如果提供了商户ID，则筛选特定商户
		if req.MerchantId != nil && *req.MerchantId > 0 {
			db = db.Where("merchant_id", *req.MerchantId)
		}

		if req.Status != "" {
			db = db.Where("status", req.Status)
		}

		if req.CallbackType != "" {
			db = db.Where("callback_type", req.CallbackType)
		}

		if startTime != "" && endTime != "" {
			db = db.WhereBetween("created_at", startTime, endTime)
		}

		if req.RelatedId != nil {
			db = db.Where("related_id", *req.RelatedId)
		}
		// 查询所有数据
		err := db.OrderDesc("id").Scan(&callbacks)
		if err != nil {
			return nil, gerror.Wrap(err, "导出查询回调记录失败")
		}

		if len(callbacks) == 0 {
			return res, nil // 没有数据可导出
		}

		// 获取商户ID列表以批量查询商户名称
		merchantIds := make([]uint64, 0)
		merchantIdMap := make(map[uint64]bool)
		for _, callback := range callbacks {
			if !merchantIdMap[callback.MerchantId] {
				merchantIds = append(merchantIds, callback.MerchantId)
				merchantIdMap[callback.MerchantId] = true
			}
		}

		// 批量查询商户信息
		merchantNameMap := make(map[uint64]string)
		if len(merchantIds) > 0 {
			var merchants []entity.Merchants
			err = dao.Merchants.Ctx(ctx).WhereIn("merchant_id", merchantIds).Scan(&merchants)
			if err != nil {
				g.Log().Errorf(ctx, "查询商户信息失败: %v", err)
			} else {
				for _, merchant := range merchants {
					merchantNameMap[merchant.MerchantId] = merchant.MerchantName
				}
			}
		}

		// 准备导出数据
		exportData := make([]interface{}, 0, len(callbacks))
		for _, callback := range callbacks {
			// 准备导出结构体
			exportItem := struct {
				Id            int64  `json:"id" excel:"回调记录ID"`
				MerchantId    uint64 `json:"merchantId" excel:"商户ID"`
				MerchantName  string `json:"merchantName" excel:"商户名称"`
				CallbackType  string `json:"callbackType" excel:"回调类型"`
				RelatedId     int64  `json:"relatedId" excel:"关联记录ID"`
				CallbackUrl   string `json:"callbackUrl" excel:"回调URL"`
				Status        string `json:"status" excel:"状态"`
				RetryCount    int    `json:"retryCount" excel:"重试次数"`
				ResponseCode  int    `json:"responseCode" excel:"响应状态码"`
				LastAttemptAt string `json:"lastAttemptAt" excel:"最后尝试时间"`
				CreatedAt     string `json:"createdAt" excel:"创建时间"`
			}{
				Id:           gconv.Int64(callback.Id),
				MerchantId:   callback.MerchantId,
				MerchantName: merchantNameMap[callback.MerchantId],
				CallbackType: callback.CallbackType,
				RelatedId:    gconv.Int64(callback.RelatedId),
				CallbackUrl:  callback.CallbackUrl,
				Status:       s.getCallbackStatusText(callback.Status),
				RetryCount:   callback.RetryCount,
				ResponseCode: callback.ResponseCode,
			}

			if callback.LastAttemptAt != nil {
				exportItem.LastAttemptAt = callback.LastAttemptAt.Format("Y-m-d H:i:s")
			}
			if callback.CreatedAt != nil {
				exportItem.CreatedAt = callback.CreatedAt.Format("Y-m-d H:i:s")
			}

			exportData = append(exportData, exportItem)
		}

		// 定义Excel表头
		excelTags := []string{
			"回调记录ID", "商户ID", "商户名称", "回调类型", "关联记录ID", "回调URL", "状态",
			"重试次数", "响应状态码", "最后尝试时间", "创建时间",
		}

		// 调用CSV导出工具
		fileName := "merchant_callbacks"
		if req.MerchantId != nil && *req.MerchantId > 0 {
			fileName = fmt.Sprintf("merchant_callbacks_%d", *req.MerchantId)
		}
		return res, csv.ExportByStructs(ctx, excelTags, exportData, fileName, "回调记录")
	}

	// 正常查询 - 直接使用DAO查询
	var callbacks []entity.MerchantCallbacks

	// 构建查询条件函数
	buildQuery := func() *gdb.Model {
		db := dao.MerchantCallbacks.Ctx(ctx)

		// 如果提供了商户ID，则筛选特定商户
		if req.MerchantId != nil && *req.MerchantId > 0 {
			db = db.Where("merchant_id", *req.MerchantId)
		}

		if req.Status != "" {
			db = db.Where("status", req.Status)
		}

		if req.CallbackType != "" {
			db = db.Where("callback_type", req.CallbackType)
		}
		if req.RelatedId != nil {
			db = db.Where("related_id", *req.RelatedId)
		}
		if startTime != "" && endTime != "" {
			db = db.WhereBetween("created_at", startTime, endTime)
		}

		return db
	}

	// 查询总数
	total, err := buildQuery().Count()
	if err != nil {
		return nil, gerror.Wrap(err, "查询回调记录总数失败")
	}

	// 查询数据
	err = buildQuery().OrderDesc("id").Page(req.Page, req.PageSize).Scan(&callbacks)
	if err != nil {
		return nil, gerror.Wrap(err, "查询回调记录失败")
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = (total + req.PageSize - 1) / req.PageSize

	// 获取商户ID列表以批量查询商户名称
	merchantIds := make([]uint64, 0)
	merchantIdMap := make(map[uint64]bool)
	for _, callback := range callbacks {
		if !merchantIdMap[callback.MerchantId] {
			merchantIds = append(merchantIds, callback.MerchantId)
			merchantIdMap[callback.MerchantId] = true
		}
	}

	// 批量查询商户信息
	merchantNameMap := make(map[uint64]string)
	if len(merchantIds) > 0 {
		var merchants []entity.Merchants
		err = dao.Merchants.Ctx(ctx).WhereIn("merchant_id", merchantIds).Scan(&merchants)
		if err != nil {
			g.Log().Errorf(ctx, "查询商户信息失败: %v", err)
		} else {
			for _, merchant := range merchants {
				merchantNameMap[merchant.MerchantId] = merchant.MerchantName
			}
		}
	}

	// 转换数据格式
	for _, callback := range callbacks {
		callbackInfo := &v1.MerchantCallbackInfoType{
			Id:            callback.Id,
			MerchantId:    callback.MerchantId,
			MerchantName:  merchantNameMap[callback.MerchantId],
			CallbackType:  callback.CallbackType,
			RelatedId:     callback.RelatedId,
			CallbackUrl:   callback.CallbackUrl,
			Status:        callback.Status,
			StatusText:    s.getCallbackStatusText(callback.Status),
			RetryCount:    callback.RetryCount,
			ResponseCode:  callback.ResponseCode,
			LastAttemptAt: callback.LastAttemptAt,
			CreatedAt:     callback.CreatedAt,
			UpdatedAt:     callback.UpdatedAt,
		}

		res.Data = append(res.Data, callbackInfo)
	}

	return res, nil
}

// GetMyCallbackDetail 获取我的回调记录详情
func (s *sSystemLogic) GetMyCallbackDetail(ctx context.Context, req *v1.GetMyCallbackDetailReq) (res *v1.GetMyCallbackDetailRes, err error) {
	// 直接使用DAO查询，避免复杂的类型转换
	var callback entity.MerchantCallbacks
	err = dao.MerchantCallbacks.Ctx(ctx).
		Where("id", req.Id).
		Scan(&callback)

	if err != nil {
		return nil, gerror.Wrap(err, "查询回调记录详情失败")
	}

	// 检查是否找到记录
	if callback.Id == 0 {
		return nil, gerror.NewCode(codes.CodeNotFound, "回调记录不存在")
	}

	// 构建响应数据
	res = &v1.GetMyCallbackDetailRes{
		Data: &v1.MerchantCallbackDetailType{
			MerchantCallbacks: callback,
			StatusText:        s.getCallbackStatusText(callback.Status),
		},
	}

	return res, nil
}

// RetryCallback 重试失败的回调
func (s *sSystemLogic) RetryCallback(ctx context.Context, req *v1.RetryCallbackReq) (res *v1.RetryCallbackRes, err error) {
	// 直接使用DAO查询回调记录
	var callback entity.MerchantCallbacks
	err = dao.MerchantCallbacks.Ctx(ctx).
		Where("id", req.Id).
		Scan(&callback)

	if err != nil {
		return nil, gerror.Wrap(err, "查询回调记录失败")
	}

	// 检查是否找到记录
	if callback.Id == 0 {
		return nil, gerror.NewCode(codes.CodeNotFound, "回调记录不存在")
	}

	// 检查状态，只有失败的回调才能重试
	if callback.Status != "failed" {
		return &v1.RetryCallbackRes{
			Success: false,
			Message: "只有失败的回调才能重试",
		}, nil
	}

	// 使用CallbackSender重试
	sender := service.NewCallbackSender()
	success, err := sender.RetryCallback(ctx, &callback)
	if err != nil {
		return &v1.RetryCallbackRes{
			Success: false,
			Message: "重试失败: " + err.Error(),
		}, nil
	}

	if success {
		return &v1.RetryCallbackRes{
			Success: true,
			Message: "回调重试成功",
		}, nil
	}

	return &v1.RetryCallbackRes{
		Success: false,
		Message: "回调重试失败，请稍后再试",
	}, nil
}

// getCallbackStatusText 获取回调状态文本描述
func (s *sSystemLogic) getCallbackStatusText(status string) string {
	switch status {
	case "pending":
		return "待发送"
	case "success":
		return "成功"
	case "failed":
		return "失败"
	default:
		return "未知状态"
	}
}
