package v1

import (
	"context"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

// --- Withdrawal Amount Settings ---

// ListWithdrawalAmountSettings 获取提现金额设置列表
func (s *sSystemLogic) ListWithdrawalAmountSettings(ctx context.Context, req *v1.ListWithdrawalAmountSettingsReq) (res *v1.ListWithdrawalAmountSettingsRes, err error) {
	// 构建查询条件
	query := dao.WithdrawalAmountSettings.Ctx(ctx).Where("deleted_at IS NULL")

	// 添加筛选条件
	if req.Currency != nil && *req.Currency != "" {
		query = query.Where("currency = ?", *req.Currency)
	}
	if req.Network != nil && *req.Network != "" {
		query = query.Where("network = ?", *req.Network)
	}
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, gerror.Wrap(err, "获取提现金额设置总数失败")
	}

	// 分页查询
	var records []*entity.WithdrawalAmountSettings
	err = query.Page(req.Page, req.PageSize).OrderDesc("created_at").Scan(&records)
	if err != nil {
		return nil, gerror.Wrap(err, "获取提现金额设置列表失败")
	}

	// 转换为响应格式
	var data []*v1.WithdrawalAmountSettingInfo
	for _, record := range records {
		data = append(data, &v1.WithdrawalAmountSettingInfo{
			Id:        record.Id,
			Currency:  record.Currency,
			Network:   record.Network,
			MinAmount: record.MinAmount.String(),
			MaxAmount: record.MaxAmount.String(),
			Status:    record.Status,
			CreatedAt: record.CreatedAt.Time,
			UpdatedAt: record.UpdatedAt.Time,
		})
	}

	return &v1.ListWithdrawalAmountSettingsRes{
		PageResponse: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
			TotalSize:   total,
			TotalPage:   common.CalculateTotalPage(total, req.PageSize),
		},
		Data: data,
	}, nil
}

// CreateWithdrawalAmountSetting 创建提现金额设置
func (s *sSystemLogic) CreateWithdrawalAmountSetting(ctx context.Context, req *v1.CreateWithdrawalAmountSettingReq) (res *v1.CreateWithdrawalAmountSettingRes, err error) {
	// 检查是否已存在相同币种和网络的配置
	count, err := dao.WithdrawalAmountSettings.Ctx(ctx).
		Where("currency = ? AND network = ? AND deleted_at IS NULL", req.Currency, req.Network).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查重复配置失败")
	}
	if count > 0 {
		return nil, gerror.New("该币种和网络的配置已存在")
	}

	// 创建记录
	id, err := dao.WithdrawalAmountSettings.Ctx(ctx).Data(do.WithdrawalAmountSettings{
		Currency:  req.Currency,
		Network:   req.Network,
		MinAmount: req.MinAmount,
		MaxAmount: req.MaxAmount,
		Status:    req.Status,
		CreatedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	}).InsertAndGetId()
	if err != nil {
		return nil, gerror.Wrap(err, "创建提现金额设置失败")
	}

	return &v1.CreateWithdrawalAmountSettingRes{
		Id: gconv.Uint64(id),
	}, nil
}

// UpdateWithdrawalAmountSetting 更新提现金额设置
func (s *sSystemLogic) UpdateWithdrawalAmountSetting(ctx context.Context, req *v1.UpdateWithdrawalAmountSettingReq) (res *v1.UpdateWithdrawalAmountSettingRes, err error) {
	// 检查记录是否存在
	count, err := dao.WithdrawalAmountSettings.Ctx(ctx).
		Where("id = ? AND deleted_at IS NULL", req.Id).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查记录是否存在失败")
	}
	if count == 0 {
		return nil, gerror.New("记录不存在")
	}

	// 检查是否与其他记录冲突
	count, err = dao.WithdrawalAmountSettings.Ctx(ctx).
		Where("currency = ? AND network = ? AND id != ? AND deleted_at IS NULL", req.Currency, req.Network, req.Id).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查重复配置失败")
	}
	if count > 0 {
		return nil, gerror.New("该币种和网络的配置已存在")
	}

	// 更新记录
	_, err = dao.WithdrawalAmountSettings.Ctx(ctx).
		Where("id = ?", req.Id).
		Data(do.WithdrawalAmountSettings{
			Currency:  req.Currency,
			Network:   req.Network,
			MinAmount: req.MinAmount,
			MaxAmount: req.MaxAmount,
			Status:    req.Status,
			UpdatedAt: gtime.Now(),
		}).Update()
	if err != nil {
		return nil, gerror.Wrap(err, "更新提现金额设置失败")
	}

	return &v1.UpdateWithdrawalAmountSettingRes{
		Id: req.Id,
	}, nil
}

// DeleteWithdrawalAmountSetting 删除提现金额设置
func (s *sSystemLogic) DeleteWithdrawalAmountSetting(ctx context.Context, req *v1.DeleteWithdrawalAmountSettingReq) (res *v1.DeleteWithdrawalAmountSettingRes, err error) {
	if len(req.Ids) == 0 {
		return nil, gerror.New("请选择要删除的记录")
	}

	// 软删除记录
	_, err = dao.WithdrawalAmountSettings.Ctx(ctx).
		Where("id IN (?)", req.Ids).
		Data(do.WithdrawalAmountSettings{
			DeletedAt: gtime.Now(),
		}).Update()
	if err != nil {
		return nil, gerror.Wrap(err, "删除提现金额设置失败")
	}

	return &v1.DeleteWithdrawalAmountSettingRes{}, nil
}

// --- Withdrawal Approval Settings ---

// ListWithdrawalApprovalSettings 获取提现审核设置列表
func (s *sSystemLogic) ListWithdrawalApprovalSettings(ctx context.Context, req *v1.ListWithdrawalApprovalSettingsReq) (res *v1.ListWithdrawalApprovalSettingsRes, err error) {
	// 构建查询条件
	query := dao.WithdrawalApprovalSettings.Ctx(ctx).Where("deleted_at IS NULL")

	// 添加筛选条件
	if req.Currency != nil && *req.Currency != "" {
		query = query.Where("currency = ?", *req.Currency)
	}
	if req.Network != nil && *req.Network != "" {
		query = query.Where("network = ?", *req.Network)
	}
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, gerror.Wrap(err, "获取提现审核设置总数失败")
	}

	// 分页查询
	var records []*entity.WithdrawalApprovalSettings
	err = query.Page(req.Page, req.PageSize).OrderDesc("created_at").Scan(&records)
	if err != nil {
		return nil, gerror.Wrap(err, "获取提现审核设置列表失败")
	}

	// 转换为响应格式
	var data []*v1.WithdrawalApprovalSettingInfo
	for _, record := range records {
		data = append(data, &v1.WithdrawalApprovalSettingInfo{
			Id:                record.Id,
			Currency:          record.Currency,
			Network:           record.Network,
			AutoReleaseMin:    record.AutoReleaseMin.String(),
			AutoReleaseMax:    record.AutoReleaseMax.String(),
			ApprovalAutoMin:   record.ApprovalAutoMin.String(),
			ApprovalAutoMax:   record.ApprovalAutoMax.String(),
			ApprovalManualMin: record.ApprovalManualMin.String(),
			ApprovalManualMax: record.ApprovalManualMax.String(),
			Status:            record.Status,
			CreatedAt:         record.CreatedAt.Time,
			UpdatedAt:         record.UpdatedAt.Time,
		})
	}

	return &v1.ListWithdrawalApprovalSettingsRes{
		PageResponse: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
			TotalSize:   total,
			TotalPage:   common.CalculateTotalPage(total, req.PageSize),
		},
		Data: data,
	}, nil
}

// CreateWithdrawalApprovalSetting 创建提现审核设置
func (s *sSystemLogic) CreateWithdrawalApprovalSetting(ctx context.Context, req *v1.CreateWithdrawalApprovalSettingReq) (res *v1.CreateWithdrawalApprovalSettingRes, err error) {
	// 检查是否已存在相同币种和网络的配置
	count, err := dao.WithdrawalApprovalSettings.Ctx(ctx).
		Where("currency = ? AND network = ? AND deleted_at IS NULL", req.Currency, req.Network).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查重复配置失败")
	}
	if count > 0 {
		return nil, gerror.New("该币种和网络的配置已存在")
	}

	// 创建记录
	id, err := dao.WithdrawalApprovalSettings.Ctx(ctx).Data(do.WithdrawalApprovalSettings{
		Currency:          req.Currency,
		Network:           req.Network,
		AutoReleaseMin:    req.AutoReleaseMin,
		AutoReleaseMax:    req.AutoReleaseMax,
		ApprovalAutoMin:   req.ApprovalAutoMin,
		ApprovalAutoMax:   req.ApprovalAutoMax,
		ApprovalManualMin: req.ApprovalManualMin,
		ApprovalManualMax: req.ApprovalManualMax,
		Status:            req.Status,
		CreatedAt:         gtime.Now(),
		UpdatedAt:         gtime.Now(),
	}).InsertAndGetId()
	if err != nil {
		return nil, gerror.Wrap(err, "创建提现审核设置失败")
	}

	return &v1.CreateWithdrawalApprovalSettingRes{
		Id: gconv.Uint64(id),
	}, nil
}

// UpdateWithdrawalApprovalSetting 更新提现审核设置
func (s *sSystemLogic) UpdateWithdrawalApprovalSetting(ctx context.Context, req *v1.UpdateWithdrawalApprovalSettingReq) (res *v1.UpdateWithdrawalApprovalSettingRes, err error) {
	// 检查记录是否存在
	count, err := dao.WithdrawalApprovalSettings.Ctx(ctx).
		Where("id = ? AND deleted_at IS NULL", req.Id).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查记录是否存在失败")
	}
	if count == 0 {
		return nil, gerror.New("记录不存在")
	}

	// 检查是否与其他记录冲突
	count, err = dao.WithdrawalApprovalSettings.Ctx(ctx).
		Where("currency = ? AND network = ? AND id != ? AND deleted_at IS NULL", req.Currency, req.Network, req.Id).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查重复配置失败")
	}
	if count > 0 {
		return nil, gerror.New("该币种和网络的配置已存在")
	}

	// 更新记录
	_, err = dao.WithdrawalApprovalSettings.Ctx(ctx).
		Where("id = ?", req.Id).
		Data(do.WithdrawalApprovalSettings{
			Currency:          req.Currency,
			Network:           req.Network,
			AutoReleaseMin:    req.AutoReleaseMin,
			AutoReleaseMax:    req.AutoReleaseMax,
			ApprovalAutoMin:   req.ApprovalAutoMin,
			ApprovalAutoMax:   req.ApprovalAutoMax,
			ApprovalManualMin: req.ApprovalManualMin,
			ApprovalManualMax: req.ApprovalManualMax,
			Status:            req.Status,
			UpdatedAt:         gtime.Now(),
		}).Update()
	if err != nil {
		return nil, gerror.Wrap(err, "更新提现审核设置失败")
	}

	return &v1.UpdateWithdrawalApprovalSettingRes{
		Id: req.Id,
	}, nil
}

// DeleteWithdrawalApprovalSetting 删除提现审核设置
func (s *sSystemLogic) DeleteWithdrawalApprovalSetting(ctx context.Context, req *v1.DeleteWithdrawalApprovalSettingReq) (res *v1.DeleteWithdrawalApprovalSettingRes, err error) {
	if len(req.Ids) == 0 {
		return nil, gerror.New("请选择要删除的记录")
	}

	// 软删除记录
	_, err = dao.WithdrawalApprovalSettings.Ctx(ctx).
		Where("id IN (?)", req.Ids).
		Data(do.WithdrawalApprovalSettings{
			DeletedAt: gtime.Now(),
		}).Update()
	if err != nil {
		return nil, gerror.Wrap(err, "删除提现审核设置失败")
	}

	return &v1.DeleteWithdrawalApprovalSettingRes{}, nil
}

// --- Withdrawal Fee Settings ---

// ListWithdrawalFeeSettings 获取提现手续费设置列表
func (s *sSystemLogic) ListWithdrawalFeeSettings(ctx context.Context, req *v1.ListWithdrawalFeeSettingsReq) (res *v1.ListWithdrawalFeeSettingsRes, err error) {
	// 构建查询条件
	query := dao.WithdrawalFeeSettings.Ctx(ctx).Where("deleted_at IS NULL")

	// 添加筛选条件
	if req.Currency != nil && *req.Currency != "" {
		query = query.Where("currency = ?", *req.Currency)
	}
	if req.Network != nil && *req.Network != "" {
		query = query.Where("network = ?", *req.Network)
	}
	if req.FeeType != nil && *req.FeeType != "" {
		query = query.Where("fee_type = ?", *req.FeeType)
	}
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, gerror.Wrap(err, "获取提现手续费设置总数失败")
	}

	// 分页查询
	var records []*entity.WithdrawalFeeSettings
	err = query.Page(req.Page, req.PageSize).OrderDesc("created_at").Scan(&records)
	if err != nil {
		return nil, gerror.Wrap(err, "获取提现手续费设置列表失败")
	}

	// 转换为响应格式
	var data []*v1.WithdrawalFeeSettingInfo
	for _, record := range records {
		data = append(data, &v1.WithdrawalFeeSettingInfo{
			Id:        record.Id,
			Currency:  record.Currency,
			Network:   record.Network,
			AmountMin: record.AmountMin.String(),
			AmountMax: record.AmountMax.String(),
			FeeType:   record.FeeType,
			FeeValue:  record.FeeValue.String(),
			Status:    record.Status,
			CreatedAt: record.CreatedAt.Time,
			UpdatedAt: record.UpdatedAt.Time,
		})
	}

	return &v1.ListWithdrawalFeeSettingsRes{
		PageResponse: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
			TotalSize:   total,
			TotalPage:   common.CalculateTotalPage(total, req.PageSize),
		},
		Data: data,
	}, nil
}

// CreateWithdrawalFeeSetting 创建提现手续费设置
func (s *sSystemLogic) CreateWithdrawalFeeSetting(ctx context.Context, req *v1.CreateWithdrawalFeeSettingReq) (res *v1.CreateWithdrawalFeeSettingRes, err error) {
	// 检查是否已存在相同币种、网络和金额范围的配置
	count, err := dao.WithdrawalFeeSettings.Ctx(ctx).
		Where("currency = ? AND network = ? AND amount_min = ? AND amount_max = ? AND deleted_at IS NULL",
			req.Currency, req.Network, req.AmountMin, req.AmountMax).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查重复配置失败")
	}
	if count > 0 {
		return nil, gerror.New("该币种、网络和金额范围的配置已存在")
	}

	// 创建记录
	id, err := dao.WithdrawalFeeSettings.Ctx(ctx).Data(do.WithdrawalFeeSettings{
		Currency:  req.Currency,
		Network:   req.Network,
		AmountMin: req.AmountMin,
		AmountMax: req.AmountMax,
		FeeType:   req.FeeType,
		FeeValue:  req.FeeValue,
		Status:    req.Status,
		CreatedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	}).InsertAndGetId()
	if err != nil {
		return nil, gerror.Wrap(err, "创建提现手续费设置失败")
	}

	return &v1.CreateWithdrawalFeeSettingRes{
		Id: gconv.Uint64(id),
	}, nil
}

// UpdateWithdrawalFeeSetting 更新提现手续费设置
func (s *sSystemLogic) UpdateWithdrawalFeeSetting(ctx context.Context, req *v1.UpdateWithdrawalFeeSettingReq) (res *v1.UpdateWithdrawalFeeSettingRes, err error) {
	// 检查记录是否存在
	count, err := dao.WithdrawalFeeSettings.Ctx(ctx).
		Where("id = ? AND deleted_at IS NULL", req.Id).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查记录是否存在失败")
	}
	if count == 0 {
		return nil, gerror.New("记录不存在")
	}

	// 检查是否与其他记录冲突
	count, err = dao.WithdrawalFeeSettings.Ctx(ctx).
		Where("currency = ? AND network = ? AND amount_min = ? AND amount_max = ? AND id != ? AND deleted_at IS NULL",
			req.Currency, req.Network, req.AmountMin, req.AmountMax, req.Id).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查重复配置失败")
	}
	if count > 0 {
		return nil, gerror.New("该币种、网络和金额范围的配置已存在")
	}

	// 更新记录
	_, err = dao.WithdrawalFeeSettings.Ctx(ctx).
		Where("id = ?", req.Id).
		Data(do.WithdrawalFeeSettings{
			Currency:  req.Currency,
			Network:   req.Network,
			AmountMin: req.AmountMin,
			AmountMax: req.AmountMax,
			FeeType:   req.FeeType,
			FeeValue:  req.FeeValue,
			Status:    req.Status,
			UpdatedAt: gtime.Now(),
		}).Update()
	if err != nil {
		return nil, gerror.Wrap(err, "更新提现手续费设置失败")
	}

	return &v1.UpdateWithdrawalFeeSettingRes{
		Id: req.Id,
	}, nil
}

// DeleteWithdrawalFeeSetting 删除提现手续费设置
func (s *sSystemLogic) DeleteWithdrawalFeeSetting(ctx context.Context, req *v1.DeleteWithdrawalFeeSettingReq) (res *v1.DeleteWithdrawalFeeSettingRes, err error) {
	if len(req.Ids) == 0 {
		return nil, gerror.New("请选择要删除的记录")
	}

	// 软删除记录
	_, err = dao.WithdrawalFeeSettings.Ctx(ctx).
		Where("id IN (?)", req.Ids).
		Data(do.WithdrawalFeeSettings{
			DeletedAt: gtime.Now(),
		}).Update()
	if err != nil {
		return nil, gerror.Wrap(err, "删除提现手续费设置失败")
	}

	return &v1.DeleteWithdrawalFeeSettingRes{}, nil
}
