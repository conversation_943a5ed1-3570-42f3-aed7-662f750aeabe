package v1

import (
	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/config"
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model" // NEW: Add model import for table structures
	"admin-api/internal/utility"
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	// "admin-api/internal/model/entity" // Removed unused import
	// Import the interface definition

	// "github.com/gogf/gf/v2/container/gvar" // Removed unused gvar import
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv" // Ensure gconv is imported and uncommented
)

// ListCategories retrieves a list of config categories based on criteria (without pagination).
func (l *sSystemLogic) ListConfigCategory(ctx context.Context, req *v1.ListConfigCategoryReq) (res *v1.ListConfigCategoryRes, err error) {
	res = &v1.ListConfigCategoryRes{
		Data: make([]*v1.ConfigCategoryInfo, 0),
	}

	condition := g.Map{}
	if req.Name != nil && *req.Name != "" {
		condition[dao.AdminConfigCategories.Columns().Name+" LIKE ?"] = "%" + *req.Name + "%"
	}
	if req.CategoryKey != nil && *req.CategoryKey != "" {
		condition[dao.AdminConfigCategories.Columns().CategoryKey] = *req.CategoryKey
	}

	// Get all categories without pagination (page=1, pageSize=9999)
	list, total, err := l.configRepo.ListCategoriesPaginated(ctx, 1, 9999, condition)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取配置分类列表失败")
	}

	if total > 0 {
		res.Data = make([]*v1.ConfigCategoryInfo, len(list))
		for i, category := range list {
			res.Data[i] = &v1.ConfigCategoryInfo{
				Id:          gconv.Int64(category.Id), // Fixed: Convert string ID to int64
				Name:        category.Name,
				CategoryKey: category.CategoryKey,
				SortOrder:   category.SortOrder,
				CreatedAt:   category.CreatedAt.Time,
				UpdatedAt:   category.UpdatedAt.Time,
			}
		}
	}

	return res, nil
}

// isValidCategoryKey checks if the key contains only lowercase letters, numbers, and underscores.
func isValidCategoryKey(key string) bool {
	match, _ := regexp.MatchString(`^[a-z0-9_]+$`, key)
	return match
}

// CreateCategory adds a new config category. (Renamed from Create)
func (l *sSystemLogic) CreateConfigCategory(ctx context.Context, req *v1.CreateConfigCategoryReq) (res *v1.CreateConfigCategoryRes, err error) {
	if !isValidCategoryKey(req.CategoryKey) {
		return nil, gerror.NewCode(codes.CodeCategoryKeyInvalid)
	}

	existing, err := l.configRepo.FindCategoryByKey(ctx, req.CategoryKey)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "检查分类 Key 唯一性失败")
	}
	if existing != nil {
		return nil, gerror.NewCode(codes.CodeCategoryKeyExists)
	}

	categoryDo := &do.AdminConfigCategories{
		Name:        req.Name,
		CategoryKey: req.CategoryKey,
		SortOrder:   req.SortOrder,
	}

	newId, err := l.configRepo.CreateCategory(ctx, categoryDo)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "创建配置分类失败")
	}

	res = &v1.CreateConfigCategoryRes{
		Id: newId,
	}
	return res, nil
}

// UpdateCategory modifies an existing config category. (Renamed from Update)
func (l *sSystemLogic) UpdateConfigCategory(ctx context.Context, req *v1.UpdateConfigCategoryReq) (res *v1.UpdateConfigCategoryRes, err error) {
	category, err := l.configRepo.FindCategoryByID(ctx, req.Id)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询配置分类失败")
	}
	if category == nil {
		return nil, gerror.NewCode(codes.CodeCategoryNotFound)
	}

	updateData := g.Map{
		dao.AdminConfigCategories.Columns().Name:      req.Name,
		dao.AdminConfigCategories.Columns().SortOrder: req.SortOrder,
	}

	err = l.configRepo.UpdateCategoryFields(ctx, req.Id, updateData)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "更新配置分类失败")
	}

	res = &v1.UpdateConfigCategoryRes{
		Id: req.Id,
	}
	return res, nil
}

// DeleteCategories removes one or more config categories by their IDs. (Renamed from Delete)
func (l *sSystemLogic) DeleteConfigCategory(ctx context.Context, req *v1.DeleteConfigCategoryReq) (res *v1.DeleteConfigCategoryRes, err error) {
	if len(req.Ids) == 0 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "请选择要删除的分类ID")
	}

	// 获取要删除的分类的键，用于后续从缓存中移除
	var categoryKeys []string
	for _, categoryId := range req.Ids {
		cat, _ := l.configRepo.FindCategoryByID(ctx, categoryId)
		if cat != nil {
			categoryKeys = append(categoryKeys, cat.CategoryKey)
		}
	}

	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		for _, categoryId := range req.Ids {
			count, err := l.configRepo.CountItemsInCategory(ctx, categoryId)
			if err != nil {
				cat, _ := l.configRepo.FindCategoryByID(ctx, categoryId)
				catName := fmt.Sprintf("ID %d", categoryId)
				if cat != nil {
					catName = cat.Name
				}
				return gerror.WrapCodef(codes.CodeInternalError, err, "检查分类 [%s] 下配置项失败", catName)
			}
			if count > 0 {
				cat, _ := l.configRepo.FindCategoryByID(ctx, categoryId)
				catName := fmt.Sprintf("ID %d", categoryId)
				if cat != nil {
					catName = cat.Name
				}
				return gerror.NewCodef(codes.CodeCategoryHasItems, "分类 [%s] 下存在配置项，无法删除", catName)
			}
		}

		err = l.configRepo.DeleteItemsByCategoryIDList(ctx, req.Ids)
		if err != nil {
			return gerror.WrapCode(codes.CodeInternalError, err, "删除分类下的配置项失败")
		}

		err = l.configRepo.DeleteCategories(ctx, req.Ids)
		if err != nil {
			return gerror.WrapCode(codes.CodeInternalError, err, "删除配置分类失败")
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// 从缓存中移除已删除的分类及其配置项
	for _, categoryKey := range categoryKeys {
		config.OnConfigCategoryDeleted(ctx, categoryKey)
	}

	res = &v1.DeleteConfigCategoryRes{}
	return res, nil
}

// --- IConfigItem Implementation ---

// ListItems retrieves a paginated list of config items based on criteria.
func (l *sSystemLogic) ListConfigItem(ctx context.Context, req *v1.ListConfigItemReq) (res *v1.ListConfigItemRes, err error) {
	res = &v1.ListConfigItemRes{
		PageResponse: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*v1.ConfigItemInfo, 0),
	}

	category, err := l.configRepo.FindCategoryByID(ctx, req.CategoryId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询所属分类信息失败")
	}
	if category == nil {
		return nil, gerror.NewCode(codes.CodeItemCategoryNotFound)
	}

	// Build condition map for additional filters
	condition := g.Map{}
	if req.Key != nil && *req.Key != "" {
		condition[dao.AdminConfigItems.Columns().Key+" LIKE ?"] = "%" + *req.Key + "%"
	}

	// 使用统一的DateRange处理工具
	utility.AddDateRangeCondition(condition, req.DateRange)

	// Use the paginated repository method
	list, total, err := l.configRepo.ListItemsByCategoryIDPaginated(ctx, req.CategoryId, req.Page, req.PageSize, condition)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取配置项列表失败")
	}

	// Set pagination info
	res.TotalSize = total
	res.TotalPage = common.CalculateTotalPage(total, req.PageSize)

	if total > 0 {
		res.Data = make([]*v1.ConfigItemInfo, len(list))
		for i, item := range list {
			res.Data[i] = &v1.ConfigItemInfo{
				Id:          gconv.Int64(item.Id),         // Fixed: Convert string ID to int64
				CategoryId:  gconv.Int64(item.CategoryId), // Fixed: Convert string ID to int64
				Key:         item.Key,
				Value:       item.Value,
				ValueType:   v1.ValueType(item.ValueType),
				Description: item.Description,
				CreatedAt:   item.CreatedAt.Time,
				UpdatedAt:   item.UpdatedAt.Time,
			}
		}
	}

	return res, nil
}

// CreateItem adds a new config item. (Renamed from CreateItem)
func (l *sSystemLogic) CreateConfigItem(ctx context.Context, req *v1.CreateConfigItemReq) (res *v1.CreateConfigItemRes, err error) {
	category, err := l.configRepo.FindCategoryByID(ctx, req.CategoryId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询所属分类信息失败")
	}
	if category == nil {
		return nil, gerror.NewCode(codes.CodeItemCategoryNotFound)
	}

	if !checkItemKeyFormat(category.CategoryKey, req.Key) {
		return nil, gerror.NewCodef(codes.CodeItemKeyFormatError, "配置项 Key '%s' 必须以 '%s.' 开头且后面不包含'.'", req.Key, category.CategoryKey)
	}

	existingItem, err := l.configRepo.FindItemByKey(ctx, req.Key)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "检查配置项 Key 唯一性失败")
	}
	if existingItem != nil {
		return nil, gerror.NewCode(codes.CodeItemKeyExists)
	}

	stringValue, err := convertAndValidateValue(req.ValueType, req.Value)
	if err != nil {
		return nil, err
	}

	itemDo := &do.AdminConfigItems{
		CategoryId:  req.CategoryId,
		Key:         req.Key,
		Value:       stringValue,
		ValueType:   string(req.ValueType),
		Description: req.Description,
	}

	newId, err := l.configRepo.CreateItem(ctx, itemDo)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "创建配置项失败")
	}

	// 创建成功后，更新配置缓存
	item, _ := l.configRepo.FindItemByID(ctx, newId)
	if item != nil {
		// 更新配置缓存
		config.OnConfigItemCreated(ctx, item)
	}

	res = &v1.CreateConfigItemRes{
		Id: newId,
	}
	return res, nil
}

// UpdateItem modifies an existing config item. (Renamed from UpdateItem)
func (l *sSystemLogic) UpdateConfigItem(ctx context.Context, req *v1.UpdateConfigItemReq) (res *v1.UpdateConfigItemRes, err error) {
	item, err := l.configRepo.FindItemByID(ctx, req.Id)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询配置项失败")
	}
	if item == nil {
		return nil, gerror.NewCode(codes.CodeItemNotFound)
	}

	updateData := g.Map{}
	needsDBUpdate := false

	// ValueType is always required in the request, so set it.
	// If it changes, it will trigger an update.
	updateData[dao.AdminConfigItems.Columns().ValueType] = string(req.ValueType)
	if string(req.ValueType) != item.ValueType {
		needsDBUpdate = true
	}

	// Handle Value update:
	// req.Value is *string. If nil, client didn't send 'value'.
	// If not nil, client sent 'value'.
	if req.Value != nil {
		actualValue := *req.Value
		// If type is Password and the provided value is an empty string, do NOT update the value.
		if !(req.ValueType == v1.Password && actualValue == "") {
			validatedValue, convErr := convertAndValidateValue(req.ValueType, actualValue)
			if convErr != nil {
				return nil, convErr
			}
			// Only set for update if the new validated value is different from the current one,
			// or if it's not a password field being "emptied" (which is handled above).
			if validatedValue != item.Value {
				updateData[dao.AdminConfigItems.Columns().Value] = validatedValue
				needsDBUpdate = true
			}
		}
		// Else (Password type and empty string), Value is intentionally not added to updateData.
		// Or if req.Value was provided but resulted in the same validatedValue as current,
		// needsDBUpdate for Value is not set to true unless ValueType also changed.
	}

	// Handle Description update:
	// req.Description is *string. If nil, client didn't send 'description'.
	if req.Description != nil {
		actualDescription := *req.Description
		if actualDescription != item.Description {
			updateData[dao.AdminConfigItems.Columns().Description] = actualDescription
			needsDBUpdate = true
		}
	}

	if needsDBUpdate {
		err = l.configRepo.UpdateItemFields(ctx, req.Id, updateData)
		if err != nil {
			// If the error is about "no data to update", it might not be a "real" error
			// if all provided values were identical to existing ones.
			// However, gorm typically handles this gracefully.
			// For now, wrap and return any error.
			return nil, gerror.WrapCode(codes.CodeInternalError, err, "更新配置项失败")
		}

		// 更新成功后，更新配置缓存
		updatedItem, _ := l.configRepo.FindItemByID(ctx, req.Id)
		if updatedItem != nil {
			config.OnConfigItemUpdated(ctx, updatedItem)
		}
	}

	res = &v1.UpdateConfigItemRes{
		Id: req.Id,
	}
	return res, nil
}

// DeleteItems removes one or more config items by their IDs. (Renamed from DeleteItem)
func (l *sSystemLogic) DeleteConfigItem(ctx context.Context, req *v1.DeleteConfigItemReq) (res *v1.DeleteConfigItemRes, err error) {
	if len(req.Ids) == 0 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "请选择要删除的配置项ID")
	}

	// 获取要删除的配置项的键，用于后续从缓存中移除
	var keysToDelete []string
	for _, id := range req.Ids {
		item, _ := l.configRepo.FindItemByID(ctx, id)
		if item != nil {
			keysToDelete = append(keysToDelete, item.Key)
		}
	}

	err = l.configRepo.DeleteItems(ctx, req.Ids)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "删除配置项失败")
	}

	// 从缓存中移除已删除的配置项
	if len(keysToDelete) > 0 {
		config.OnConfigItemsDeleted(ctx, keysToDelete)
	}

	res = &v1.DeleteConfigItemRes{}
	return res, nil
}

// --- Helper to convert value based on type ---
func convertAndValidateValue(valueType v1.ValueType, value interface{}) (string, error) {
	strValue := gconv.String(value)
	switch valueType {
	case v1.Text, v1.Textarea, v1.Password: // Added v1.Password here
		return strValue, nil
	case v1.Number:
		// 使用 IsNumeric 替代 Float64E 进行数字验证
		if !isNumeric(gconv.String(value)) {
			return "", gerror.NewCodef(codes.CodeItemValueTypeError, "值 '%v' 不是有效的数字类型", value)
		}
		return gconv.String(gconv.Float64(value)), nil
	case v1.Boolean:
		// 用字符串前缀判断布尔类型
		s := strings.ToLower(gconv.String(value))
		if s != "true" && s != "false" && s != "1" && s != "0" {
			return "", gerror.NewCodef(codes.CodeItemValueTypeError, "值 '%v' 不是有效的布尔类型 (true/false/1/0)", value)
		}
		b := (s == "true" || s == "1")
		return gconv.String(b), nil
	case v1.JSON:
		// ...existing JSON 处理...
		jsonStr := gconv.String(value)
		if !gjson.Valid([]byte(jsonStr)) {
			if _, ok := value.(string); !ok {
				jsonBytes, marshalErr := gjson.Marshal(value)
				if marshalErr != nil || !gjson.Valid(jsonBytes) {
					return "", gerror.NewCode(codes.CodeItemInvalidJSON)
				}
				return string(jsonBytes), nil
			}
			return "", gerror.NewCode(codes.CodeItemInvalidJSON)
		}
		return jsonStr, nil
	case v1.Map:
		// Map 类型处理 - 验证输入是否为有效的 JSON 对象 (key-value pairs)
		jsonStr := gconv.String(value)
		if !gjson.Valid([]byte(jsonStr)) {
			// 如果不是字符串，尝试将其序列化为 JSON
			if _, ok := value.(string); !ok {
				jsonBytes, marshalErr := gjson.Marshal(value)
				if marshalErr != nil || !gjson.Valid(jsonBytes) {
					return "", gerror.NewCodef(codes.CodeItemValueTypeError, "值 '%v' 不是有效的 Map 类型 (必须是有效的 JSON 对象)", value)
				}
				jsonStr = string(jsonBytes)
			} else {
				return "", gerror.NewCodef(codes.CodeItemValueTypeError, "值 '%v' 不是有效的 Map 类型 (必须是有效的 JSON 对象)", value)
			}
		}

		// 验证 JSON 是否为对象类型 (而不是数组或基本类型)
		var mapData map[string]interface{}
		if err := gjson.DecodeTo(jsonStr, &mapData); err != nil {
			return "", gerror.NewCodef(codes.CodeItemValueTypeError, "值 '%v' 不是有效的 Map 类型 (必须是有效的 JSON 对象)", value)
		}

		return jsonStr, nil
	case v1.Table:
		// NEW: Table 类型处理 - 验证输入是否为有效的表格结构
		jsonStr := gconv.String(value)
		if !gjson.Valid([]byte(jsonStr)) {
			// 如果不是字符串，尝试将其序列化为 JSON
			if _, ok := value.(string); !ok {
				jsonBytes, marshalErr := gjson.Marshal(value)
				if marshalErr != nil || !gjson.Valid(jsonBytes) {
					return "", gerror.NewCodef(codes.CodeItemValueTypeError, "值 '%v' 不是有效的 Table 类型 (必须是有效的 JSON 对象)", value)
				}
				jsonStr = string(jsonBytes)
			} else {
				return "", gerror.NewCodef(codes.CodeItemValueTypeError, "值 '%v' 不是有效的 Table 类型 (必须是有效的 JSON 对象)", value)
			}
		}

		// 验证表格结构的完整性
		tableStructure, err := model.FromJSON(jsonStr)
		if err != nil {
			return "", gerror.NewCodef(codes.CodeItemValueTypeError, "值 '%v' 不是有效的 Table 类型: %v", value, err)
		}

		// 验证表格结构的逻辑正确性
		if err := tableStructure.ValidateTableStructure(); err != nil {
			return "", gerror.NewCodef(codes.CodeItemValueTypeError, "表格结构验证失败: %v", err)
		}

		// 将验证后的表格结构重新序列化为 JSON 字符串
		validatedJson, err := tableStructure.ToJSON()
		if err != nil {
			return "", gerror.NewCodef(codes.CodeInternalError, "表格结构序列化失败: %v", err)
		}

		return validatedJson, nil
	case v1.NumberRange:
		// NEW: Number range type - validates as a JSON object with min and max numbers
		jsonStr := gconv.String(value)
		if !gjson.Valid([]byte(jsonStr)) {
			// If not a string, try to serialize as JSON
			if _, ok := value.(string); !ok {
				jsonBytes, marshalErr := gjson.Marshal(value)
				if marshalErr != nil || !gjson.Valid(jsonBytes) {
					return "", gerror.NewCodef(codes.CodeItemValueTypeError, "值 '%v' 不是有效的数字区间类型 (必须是包含 min 和 max 的 JSON 对象)", value)
				}
				jsonStr = string(jsonBytes)
			} else {
				return "", gerror.NewCodef(codes.CodeItemValueTypeError, "值 '%v' 不是有效的数字区间类型 (必须是包含 min 和 max 的 JSON 对象)", value)
			}
		}

		// Validate it's a JSON object with min and max
		var rangeData map[string]interface{}
		if err := gjson.DecodeTo(jsonStr, &rangeData); err != nil {
			return "", gerror.NewCodef(codes.CodeItemValueTypeError, "值 '%v' 不是有效的数字区间类型 (必须是包含 min 和 max 的 JSON 对象)", value)
		}

		// Check min and max exist and are numbers
		minVal, hasMin := rangeData["min"]
		maxVal, hasMax := rangeData["max"]
		
		if !hasMin || !hasMax {
			return "", gerror.NewCodef(codes.CodeItemValueTypeError, "数字区间必须包含 'min' 和 'max' 字段")
		}

		// Validate min and max are numbers
		if !isNumeric(gconv.String(minVal)) {
			return "", gerror.NewCodef(codes.CodeItemValueTypeError, "数字区间的 'min' 值必须是数字: %v", minVal)
		}
		if !isNumeric(gconv.String(maxVal)) {
			return "", gerror.NewCodef(codes.CodeItemValueTypeError, "数字区间的 'max' 值必须是数字: %v", maxVal)
		}

		// Ensure min <= max
		minFloat := gconv.Float64(minVal)
		maxFloat := gconv.Float64(maxVal)
		if minFloat > maxFloat {
			return "", gerror.NewCodef(codes.CodeItemValueTypeError, "数字区间的 'min' 值 (%v) 不能大于 'max' 值 (%v)", minFloat, maxFloat)
		}

		// Store as normalized JSON with number values
		normalizedRange := map[string]float64{
			"min": minFloat,
			"max": maxFloat,
		}
		normalizedJson, _ := gjson.Marshal(normalizedRange)
		return string(normalizedJson), nil
	default:
		return "", gerror.NewCodef(codes.CodeInvalidParameter, "未知的值类型: %s", valueType)
	}
}

// --- Helper to check item key format ---
func checkItemKeyFormat(categoryKey, itemKey string) bool {
	prefix := categoryKey + "."
	return strings.HasPrefix(itemKey, prefix) && len(itemKey) > len(prefix) && !strings.Contains(itemKey[len(prefix):], ".")
}
func isNumeric(s string) bool {
	_, err := strconv.ParseFloat(s, 64)
	return err == nil
}
