package v1

import (
	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"context"
	"fmt"
	"math"
	"time"

	// "admin-api/internal/dao" // Replaced by repository
	"admin-api/internal/model/entity"
	"admin-api/utility/excel"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp" // Import ghttp for export exit

	// "github.com/gogf/gf/v2/os/gtime" // Removed as unused
	"github.com/gogf/gf/v2/util/gconv"
)

// GetIpAccessList 获取IP访问列表
func (l *sSystemLogic) GetIpAccessList(ctx context.Context, req *v1.GetIpAccessListReq) (res *v1.GetIpAccessListRes, err error) {
	// 检查请求参数
	if req == nil {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "请求参数不能为空")
	}
	// 初始化返回结果
	res = &v1.GetIpAccessListRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*entity.IpAccessList, 0),
	}

	// 使用 repository 获取列表和总数
	list, total, err := l.ipAccessListRepo.List(ctx, req)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取IP访问列表失败")
	}

	// 处理导出逻辑
	if req.Export == 1 {
		if total == 0 {
			g.Log().Info(ctx, "No IP access list entries found to export.")
			ghttp.RequestFromCtx(ctx).Exit()
			return nil, nil
		}
		// list already contains all data for export

		excelTags := []string{
			"记录ID", "列表类型", "IP地址", "原因", "添加者", "过期时间", "状态", "创建时间",
		}
		exportData := make([]interface{}, len(list))
		for i, v := range list {
			status := "禁用"
			if v.IsEnabled == 1 {
				status = "启用"
			}
			listType := "黑名单"
			if v.ListType == "whitelist" {
				listType = "白名单"
			}
			expiresAt := "永久"
			if v.ExpiresAt != nil {
				expiresAt = v.ExpiresAt.Format("2006-01-02 15:04:05")
			}

			exportData[i] = struct {
				Id        int64  `json:"id"`
				ListType  string `json:"list_type"`
				IpAddress string `json:"ip_address"`
				Reason    string `json:"reason"`
				AddedBy   string `json:"added_by"`
				ExpiresAt string `json:"expires_at"`
				Status    string `json:"status"`
				CreatedAt string `json:"created_at"`
			}{
				Id:        v.Id,
				ListType:  listType,
				IpAddress: v.IpAddress,
				Reason:    v.Reason,
				AddedBy:   v.AddedBy,
				ExpiresAt: expiresAt,
				Status:    status,
				CreatedAt: v.CreatedAt.Format("2006-01-02 15:04:05"),
			}
		}

		fileName := fmt.Sprintf("IP访问控制列表_%s.xlsx", time.Now().Format("20060102150405"))
		err = excel.ExportByStructs(ctx, excelTags, exportData, "IP访问控制列表", fileName)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeInternalError, err, "导出Excel失败")
		}
		ghttp.RequestFromCtx(ctx).Exit()
		return nil, nil

	} else {
		// 处理分页查询逻辑
		res.Page.TotalSize = total
		res.Page.TotalPage = int(math.Ceil(float64(total) / float64(req.PageSize)))
		res.Data = list
		return res, nil
	}
}

// AddIpAccessList 添加IP到访问列表
func (l *sSystemLogic) AddIpAccessList(ctx context.Context, req *v1.AddIpAccessListReq) (res *v1.AddIpAccessListRes, err error) {
	// 1. 校验 ListType
	if req.ListType != "blacklist" && req.ListType != "whitelist" {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的列表类型，仅支持 'blacklist' 或 'whitelist'")
	}

	// 2. 检查 IP 是否已存在于该类型列表中 using repository
	existingEntry, err := l.ipAccessListRepo.FindByIPAndType(ctx, req.IpAddress, req.ListType)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "检查IP是否存在失败")
	}
	if existingEntry != nil {
		// Use a generic error code or define CodeIPExistsInList
		return nil, gerror.NewCodef(codes.CodeInvalidParameter, "IP地址 %s 已存在于 %s", req.IpAddress, req.ListType)
	}

	// 3. 获取当前用户名 (如果需要记录添加者)
	username := "" // Default to empty or system user
	// Example: Fetch username from context if authentication middleware is used
	// if identity := service.Auth().GetIdentity(ctx); identity != nil {
	//     username = identity.Username
	// }
	// For now, let's keep it simple or assume it's handled elsewhere if needed.

	// 4. 准备实体数据
	entry := &entity.IpAccessList{
		ListType:  req.ListType,
		IpAddress: req.IpAddress,
		Reason:    req.Reason,
		AddedBy:   username, // Assign fetched username
		ExpiresAt: gconv.GTime(req.ExpiresAt),
		IsEnabled: 1, // Default to enabled
		UserId:    0, // 设置默认值
		AgentId:   0, // 设置默认值
		AdminId:   0, // 设置默认值
	}

	// 5. 添加记录 using repository
	newId, err := l.ipAccessListRepo.Create(ctx, entry)
	if err != nil {
		// Repository should wrap errors
		return nil, err
	}

	res = &v1.AddIpAccessListRes{
		Id: newId,
	}
	return res, nil
}

// DeleteIpAccessList 从访问列表删除IP
func (l *sSystemLogic) DeleteIpAccessList(ctx context.Context, req *v1.DeleteIpAccessListReq) (res *v1.DeleteIpAccessListRes, err error) {
	// 1. 检查记录是否存在 using repository (optional, delete is idempotent)
	entry, err := l.ipAccessListRepo.FindByID(ctx, req.Id)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询IP访问记录失败")
	}
	if entry == nil {
		// Treat as success if not found
		return &v1.DeleteIpAccessListRes{Id: req.Id}, nil
		// Or return specific error: return nil, gerror.NewCode(codes.CodeNotFound, "记录不存在")
	}

	// 2. 删除记录 using repository
	err = l.ipAccessListRepo.Delete(ctx, req.Id)
	if err != nil {
		// Repository should wrap errors
		return nil, err
	}

	res = &v1.DeleteIpAccessListRes{
		Id: req.Id,
	}
	return res, nil
}

// PatchIpAccessList 更新IP访问控制状态
// This method replaces the old EnableIpAccessList and DisableIpAccessList
func (l *sSystemLogic) PatchIpAccessList(ctx context.Context, req *v1.PatchIpAccessListReq) (res *v1.PatchIpAccessListRes, err error) {
	// 1. 检查记录是否存在 using repository
	entry, err := l.ipAccessListRepo.FindByID(ctx, req.Id)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "查询IP访问记录失败")
	}
	if entry == nil {
		return nil, gerror.NewCode(codes.CodeNotFound, "记录不存在")
	}

	// 2. 检查是否提供了状态
	if req.IsEnabled == nil {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "必须提供 'isEnabled' 状态")
	}

	// 3. 更新状态 using repository
	err = l.ipAccessListRepo.UpdateStatus(ctx, req.Id, *req.IsEnabled)
	if err != nil {
		// Repository should wrap errors
		return nil, err
	}

	res = &v1.PatchIpAccessListRes{
		Id: req.Id,
	}
	return res, nil
}

// // EnableIpAccessList and DisableIpAccessList are deprecated, replaced by PatchIpAccessList
// func (l *sSystemLogic) EnableIpAccessList(ctx context.Context, req *v1.EnableIpAccessListReq) (res *v1.EnableIpAccessListRes, err error) {
//     // ... implementation using l.ipAccessListRepo.UpdateStatus(ctx, req.Id, 1) ...
// }
// func (l *sSystemLogic) DisableIpAccessList(ctx context.Context, req *v1.DisableIpAccessListReq) (res *v1.DisableIpAccessListRes, err error) {
//     // ... implementation using l.ipAccessListRepo.UpdateStatus(ctx, req.Id, 0) ...
// }
