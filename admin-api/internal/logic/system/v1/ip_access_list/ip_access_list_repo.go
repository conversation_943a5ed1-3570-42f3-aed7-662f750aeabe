package ip_access_list

import (
	v1 "admin-api/api/system/v1" // Import v1 for request structure
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/internal/service/system/ip_access_list" // Import the interface package
	"context"
	"database/sql"

	"github.com/gogf/gf/v2/errors/gerror"
	// "github.com/gogf/gf/v2/frame/g" // Removed as unused
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ipAccessListRepository implements the IIpAccessListRepository interface.
type ipAccessListRepository struct{}

// NewIpAccessListRepository creates and returns a new instance of ipAccessListRepository.
func NewIpAccessListRepository() ip_access_list.IIpAccessListRepository {
	return &ipAccessListRepository{}
}

// List retrieves a paginated list of IP access list entries based on the provided criteria.
func (r *ipAccessListRepository) List(ctx context.Context, req *v1.GetIpAccessListReq) ([]*entity.IpAccessList, int, error) {
	m := dao.IpAccessList.Ctx(ctx).WhereNull(dao.IpAccessList.Columns().DeletedAt)

	// Apply filters
	if req.IpAddress != "" {
		m = m.WhereLike(dao.IpAccessList.Columns().IpAddress, "%"+req.IpAddress+"%")
	}
	if req.ListType != "" {
		m = m.Where(dao.IpAccessList.Columns().ListType, req.ListType)
	}
	if req.IsEnabled != -1 { // -1 means all
		m = m.Where(dao.IpAccessList.Columns().IsEnabled, req.IsEnabled)
	}

	total, err := m.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "Failed to count IP access list entries")
	}

	if req.Page > 0 && req.PageSize > 0 {
		m = m.Page(req.Page, req.PageSize)
	}

	// Default sorting
	m = m.OrderDesc(dao.IpAccessList.Columns().CreatedAt)

	var results []*entity.IpAccessList
	if err := m.Scan(&results); err != nil {
		return nil, 0, gerror.Wrap(err, "Failed to retrieve IP access list entries")
	}
	return results, total, nil
}

// Create adds a new IP access list entry.
func (r *ipAccessListRepository) Create(ctx context.Context, entry *entity.IpAccessList) (int64, error) {
	// Set timestamps and default IsEnabled if not provided
	now := gtime.Now()
	entry.CreatedAt = now
	entry.UpdatedAt = now
	if entry.IsEnabled == 0 { // Assuming 0 is the zero value for int and we default to enabled (1)
		entry.IsEnabled = 1
	}

	// 确保必填字段有值
	if entry.UserId == 0 {
		entry.UserId = 0
	}
	if entry.AgentId == 0 {
		entry.AgentId = 0
	}
	if entry.AdminId == 0 {
		entry.AdminId = 0
	}

	// 使用DAO的字段名明确添加所有必要字段
	data := g.Map{
		dao.IpAccessList.Columns().ListType:  entry.ListType,
		dao.IpAccessList.Columns().IpAddress: entry.IpAddress,
		dao.IpAccessList.Columns().Reason:    entry.Reason,
		dao.IpAccessList.Columns().AddedBy:   entry.AddedBy,
		dao.IpAccessList.Columns().ExpiresAt: entry.ExpiresAt,
		dao.IpAccessList.Columns().IsEnabled: entry.IsEnabled,
		dao.IpAccessList.Columns().CreatedAt: entry.CreatedAt,
		dao.IpAccessList.Columns().UpdatedAt: entry.UpdatedAt,
		dao.IpAccessList.Columns().UserId:    entry.UserId,
		dao.IpAccessList.Columns().AgentId:   entry.AgentId,
		dao.IpAccessList.Columns().AdminId:   entry.AdminId,
	}

	result, err := dao.IpAccessList.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return 0, gerror.Wrap(err, "Failed to create IP access list entry")
	}
	newId, err := result.LastInsertId()
	if err != nil {
		return 0, gerror.Wrap(err, "Failed to get last insert ID for IP access list entry")
	}
	return newId, nil
}

// Delete removes an IP access list entry by its ID. (Hard delete)
func (r *ipAccessListRepository) Delete(ctx context.Context, id int64) error {
	_, err := dao.IpAccessList.Ctx(ctx).Where(dao.IpAccessList.Columns().Id, id).Delete()
	if err != nil {
		return gerror.Wrapf(err, "Failed to delete IP access list entry ID: %d", id)
	}
	return nil
}

// UpdateStatus updates the enabled status of an IP access list entry.
func (r *ipAccessListRepository) UpdateStatus(ctx context.Context, id int64, isEnabled int) error {
	_, err := dao.IpAccessList.Ctx(ctx).
		Data(do.IpAccessList{IsEnabled: isEnabled, UpdatedAt: gtime.Now()}).
		Where(dao.IpAccessList.Columns().Id, id).
		Update()
	if err != nil {
		return gerror.Wrapf(err, "Failed to update status for IP access list entry ID: %d", id)
	}
	return nil
}

// FindByID retrieves a single IP access list entry by its ID.
func (r *ipAccessListRepository) FindByID(ctx context.Context, id int64) (*entity.IpAccessList, error) {
	var entry *entity.IpAccessList
	err := dao.IpAccessList.Ctx(ctx).Where(dao.IpAccessList.Columns().Id, id).Scan(&entry)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // Not found
		}
		return nil, gerror.Wrapf(err, "Failed to find IP access list entry by ID: %d", id)
	}
	return entry, nil
}

// FindByIPAndType checks if a specific IP address already exists in a specific list type.
func (r *ipAccessListRepository) FindByIPAndType(ctx context.Context, ipAddress string, listType string) (*entity.IpAccessList, error) {
	var entry *entity.IpAccessList
	err := dao.IpAccessList.Ctx(ctx).
		Where(dao.IpAccessList.Columns().IpAddress, ipAddress).
		Where(dao.IpAccessList.Columns().ListType, listType).
		Scan(&entry)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // Not found
		}
		return nil, gerror.Wrapf(err, "Failed to find IP access list entry by IP '%s' and type '%s'", ipAddress, listType)
	}
	return entry, nil
}
