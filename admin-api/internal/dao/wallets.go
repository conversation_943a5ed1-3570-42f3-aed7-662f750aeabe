// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"database/sql"
	"errors" // Needed for errors.Is
	"math/big"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"

	v1 "admin-api/api/system/v1" // Import for WalletListItem
	"admin-api/internal/codes"
	"admin-api/internal/dao/internal"
	"admin-api/internal/model"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	amountUtil "admin-api/utility/amount"
)

// WalletListInput defines the input parameters for listing wallets with details.
type WalletListInput struct {
	Page     int    // Current page number
	PageSize int    // Number of items per page
	Account  string // User account (optional, for filtering)
	Username string // Username (optional, for filtering, fuzzy match)
	Symbol   string // Token symbol (optional, for filtering, fuzzy match)
	TokenId  uint   // Token ID (optional, for filtering)
}

// walletsDao is the data access object for the table wallets.
// You can define custom methods on it to extend its functionality as needed.
type walletsDao struct {
	*internal.WalletsDao
}

var (
	// Wallets is a globally accessible object for table wallets operations.
	Wallets = walletsDao{internal.NewWalletsDao()}
)

// ListWalletsWithDetails retrieves a paginated list of wallets with associated user and token details.
func (d *walletsDao) ListWalletsWithDetails(ctx context.Context, input WalletListInput) (list []*v1.WalletListItem, total int, err error) {
	list = make([]*v1.WalletListItem, 0)
	db := g.DB(d.Group())

	// Base model for filtering and data retrieval
	queryModel := db.Model(d.Table()+" w").
		LeftJoin(Users.Table()+" u", "w.user_id = u.id")
		// LeftJoin(Tokens.Table()+" t", "w.token_id = t.token_id")

	if input.Account != "" {
		queryModel = queryModel.WhereLike("u.account", "%"+input.Account+"%") // Assuming 'account' is in the 'users' table
	}
	if input.Username != "" {
		queryModel = queryModel.WhereLike("u.name", "%"+input.Username+"%") // Using name field for username filtering
	}
	if input.Symbol != "" {
		queryModel = queryModel.WhereLike("w.symbol", "%"+input.Symbol+"%") // Assuming 'symbol' is in the 'tokens' table
	}

	// Perform count operation. GoFrame's Count() on a model without explicit Fields
	// will typically generate COUNT(*), which is what we need.
	total, err = queryModel.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "failed to count wallets")
	}

	if total == 0 {
		return list, 0, nil
	}

	// Now, apply field selection for retrieving the actual data.
	// Use aliases that match the fields in v1.WalletListItem for direct scanning.
	queryModel = queryModel.Fields("w.*, u.account AS Account, u.name AS Username")

	// Pagination
	if input.Page <= 0 {
		input.Page = 1
	}
	if input.PageSize <= 0 {
		input.PageSize = 10 // Default page size
	}

	err = queryModel.Page(input.Page, input.PageSize).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "failed to retrieve wallet list")
	}
	for i, wallet := range list {
		list[i].FormattedAvailableBalance = amountUtil.FormatBalance(big.NewInt(wallet.AvailableBalance), uint8(wallet.DecimalPlaces))
		list[i].FormattedFrozenBalance = amountUtil.FormatBalance(big.NewInt(wallet.FrozenBalance), uint8(wallet.DecimalPlaces))

	}
	return list, total, nil
}

// GetWalletByUserAndToken retrieves a wallet by user ID and token ID.
// It returns nil, nil if the wallet is not found.
func (d *walletsDao) GetWalletByUserAndToken(ctx context.Context, userId uint32, tokenId uint32) (*entity.Wallets, error) {
	var wallet *entity.Wallets
	err := d.Ctx(ctx).Where(g.Map{
		d.Columns().UserId:  userId,
		d.Columns().TokenId: tokenId,
	}).Scan(&wallet)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, gerror.Wrapf(err, "数据库查询钱包失败 (userId: %d, tokenId: %d)", userId, tokenId)
	}
	return wallet, nil
}

// GetWalletByUserAndTokenForUpdate 在事务中根据用户ID和代币ID获取钱包信息 (带行锁)
func (d *walletsDao) GetWalletByUserAndTokenForUpdate(ctx context.Context, tx gdb.TX, userId uint32, tokenId uint32) (*entity.Wallets, error) {
	var wallet *entity.Wallets
	err := tx.Model(d.Table()).Ctx(ctx).
		Where(g.Map{
			d.Columns().UserId:  userId,
			d.Columns().TokenId: tokenId,
		}).
		LockUpdate().
		Scan(&wallet)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, gerror.Wrapf(err, "查询钱包失败 (userId: %d, tokenId: %d) FOR UPDATE", userId, tokenId)
	}
	return wallet, nil
}

// CreateWalletWithDefaults creates a new wallet with default zero balances.
// It assumes the caller has already verified that the wallet does not exist and the token exists.
func (d *walletsDao) CreateWalletWithDefaults(ctx context.Context, userId uint32, tokenId uint32, decimalPlaces uint8, walletType string) (*entity.Wallets, error) {
	newWallet := &do.Wallets{
		UserId:           uint(userId),
		TokenId:          tokenId,
		Type:             walletType,
		DecimalPlaces:    uint(decimalPlaces),
		AvailableBalance: 0,
		FrozenBalance:    0,
		// CumulativeDeposit:    0, // Commented out due to missing field in do.Wallets
		// CumulativeWithdrawal: 0, // Commented out due to missing field in do.Wallets
	}

	result, err := d.Ctx(ctx).Data(newWallet).Insert()
	if err != nil {
		return nil, gerror.Wrapf(err, "创建钱包记录时发生数据库错误 (userId: %d, tokenId: %d): %v", userId, tokenId, err)
	}

	walletId, _ := result.LastInsertId()
	createdWalletEntity := &entity.Wallets{
		WalletId:         int(walletId), // Convert to int
		UserId:           uint(userId),
		TokenId:          uint(tokenId),
		Type:             walletType,
		DecimalPlaces:    uint(decimalPlaces),
		AvailableBalance: 0,
		FrozenBalance:    0,
		// CumulativeDeposit:    0, // Commented out due to missing field in entity.Wallets
		// CumulativeWithdrawal: 0, // Commented out due to missing field in entity.Wallets
	}
	return createdWalletEntity, nil
}

// IncreaseAvailableBalance atomically increases the available balance within a transaction.
func (d *walletsDao) IncreaseAvailableBalance(ctx context.Context, tx gdb.TX, userId uint32, tokenId uint32, amount int64) error {
	if amount <= 0 {
		return gerror.NewCodef(codes.CodeInvalidParameter, "增加的金额必须为正数 (amount: %d)", amount)
	}
	result, err := tx.Model(d.Table()).Ctx(ctx).
		Data(g.Map{d.Columns().AvailableBalance: gdb.Raw(d.Columns().AvailableBalance + " + ?")}).
		Where(g.Map{
			d.Columns().UserId:  userId,
			d.Columns().TokenId: tokenId,
		}).
		Args(amount).
		Update()

	if err != nil {
		return gerror.Wrapf(err, "数据库增加可用余额失败 (userId: %d, tokenId: %d, amount: %d)", userId, tokenId, amount)
	}
	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		return gerror.NewCodef(codes.CodeWalletNotFound, "钱包未找到，无法增加余额 (userId: %d, tokenId: %d)", userId, tokenId)
	}
	return nil
}

// DecreaseAvailableBalance atomically decreases the available balance within a transaction.
func (d *walletsDao) DecreaseAvailableBalance(ctx context.Context, tx gdb.TX, userId uint32, tokenId uint32, amount int64) error {
	if amount <= 0 {
		return gerror.NewCodef(codes.CodeInvalidParameter, "减少的金额必须为正数 (amount: %d)", amount)
	}
	result, err := tx.Model(d.Table()).Ctx(ctx).
		Data(g.Map{d.Columns().AvailableBalance: gdb.Raw(d.Columns().AvailableBalance + " - ?")}).
		Where(g.Map{
			d.Columns().UserId:                   userId,
			d.Columns().TokenId:                  tokenId,
			d.Columns().AvailableBalance + " >=": amount,
		}).
		Args(amount).
		Update()

	if err != nil {
		return gerror.Wrapf(err, "数据库减少可用余额失败 (userId: %d, tokenId: %d, amount: %d)", userId, tokenId, amount)
	}
	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		return gerror.NewCodef(codes.CodeInsufficientBalance, "可用余额不足 (userId: %d, tokenId: %d, requested: %d)", userId, tokenId, amount)
	}
	return nil
}

// FreezeBalance atomically moves funds from available to frozen balance within a transaction.
func (d *walletsDao) FreezeBalance(ctx context.Context, tx gdb.TX, userId uint32, tokenId uint32, amount int64) error {
	if amount <= 0 {
		return gerror.NewCodef(codes.CodeInvalidParameter, "冻结的金额必须为正数 (amount: %d)", amount)
	}
	result, err := tx.Model(d.Table()).Ctx(ctx).
		Data(g.Map{
			d.Columns().AvailableBalance: gdb.Raw(d.Columns().AvailableBalance + " - ?"),
			d.Columns().FrozenBalance:    gdb.Raw(d.Columns().FrozenBalance + " + ?"),
		}).
		Where(g.Map{
			d.Columns().UserId:                   userId,
			d.Columns().TokenId:                  tokenId,
			d.Columns().AvailableBalance + " >=": amount,
		}).
		Args(amount, amount).
		Update()

	if err != nil {
		return gerror.Wrapf(err, "数据库冻结余额失败 (userId: %d, tokenId: %d, amount: %d)", userId, tokenId, amount)
	}
	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		return gerror.NewCodef(codes.CodeInsufficientBalance, "可用余额不足，无法冻结 (userId: %d, tokenId: %d, requested: %d)", userId, tokenId, amount)
	}
	return nil
}

// UnfreezeBalance atomically moves funds from frozen to available balance within a transaction.
func (d *walletsDao) UnfreezeBalance(ctx context.Context, tx gdb.TX, userId uint32, tokenId uint32, amount int64) error {
	if amount <= 0 {
		return gerror.NewCodef(codes.CodeInvalidParameter, "解冻的金额必须为正数 (amount: %d)", amount)
	}
	result, err := tx.Model(d.Table()).Ctx(ctx).
		Data(g.Map{
			d.Columns().FrozenBalance:    gdb.Raw(d.Columns().FrozenBalance + " - ?"),
			d.Columns().AvailableBalance: gdb.Raw(d.Columns().AvailableBalance + " + ?"),
		}).
		Where(g.Map{
			d.Columns().UserId:                userId,
			d.Columns().TokenId:               tokenId,
			d.Columns().FrozenBalance + " >=": amount,
		}).
		Args(amount, amount).
		Update()

	if err != nil {
		return gerror.Wrapf(err, "数据库解冻余额失败 (userId: %d, tokenId: %d, amount: %d)", userId, tokenId, amount)
	}
	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		return gerror.NewCodef(codes.CodeInsufficientFrozenBalance, "冻结余额不足，无法解冻 (userId: %d, tokenId: %d, requested: %d)", userId, tokenId, amount)
	}
	return nil
}

// UpdateCumulativeDeposit atomically increases the cumulative deposit amount within a transaction.
func (d *walletsDao) UpdateCumulativeDeposit(ctx context.Context, tx gdb.TX, userId uint32, tokenId uint32, amount int64) error {
	if amount <= 0 {
		return gerror.NewCodef(codes.CodeInvalidParameter, "更新的累计存款金额必须为正数 (amount: %d)", amount)
	}
	// result, err := tx.Model(d.Table()).Ctx(ctx).
	// Data(g.Map{d.Columns().CumulativeDeposit: gdb.Raw(d.Columns().CumulativeDeposit + " + ?")}). // Commented out
	// Where(g.Map{
	// d.Columns().UserId: userId,
	// d.Columns().TokenId: tokenId,
	// }).
	// Args(amount).
	// Update()
	// if err != nil {
	// return gerror.Wrapf(err, "数据库更新累计存款失败 (userId: %d, tokenId: %d, amount: %d)", userId, tokenId, amount)
	// }
	// rowsAffected, _ := result.RowsAffected()
	// if rowsAffected == 0 {
	// return gerror.NewCodef(codes.CodeWalletNotFound, "钱包未找到，无法更新累计存款 (userId: %d, tokenId: %d)", userId, tokenId)
	// }
	// return nil
	g.Log().Warning(ctx, "UpdateCumulativeDeposit is currently a no-op due to missing CumulativeDeposit field")
	return nil // Temporarily return nil to allow compilation
}

// UpdateCumulativeWithdrawal atomically increases the cumulative withdrawal amount within a transaction.
func (d *walletsDao) UpdateCumulativeWithdrawal(ctx context.Context, tx gdb.TX, userId uint32, tokenId uint32, amount int64) error {
	if amount <= 0 {
		return gerror.NewCodef(codes.CodeInvalidParameter, "更新的累计取款金额必须为正数 (amount: %d)", amount)
	}
	// result, err := tx.Model(d.Table()).Ctx(ctx).
	// Data(g.Map{d.Columns().CumulativeWithdrawal: gdb.Raw(d.Columns().CumulativeWithdrawal + " + ?")}). // Commented out
	// Where(g.Map{
	// d.Columns().UserId: userId,
	// d.Columns().TokenId: tokenId,
	// }).
	// Args(amount).
	// Update()
	// if err != nil {
	// return gerror.Wrapf(err, "数据库更新累计取款失败 (userId: %d, tokenId: %d, amount: %d)", userId, tokenId, amount)
	// }
	// rowsAffected, _ := result.RowsAffected()
	// if rowsAffected == 0 {
	// return gerror.NewCodef(codes.CodeWalletNotFound, "钱包未找到，无法更新累计取款 (userId: %d, tokenId: %d)", userId, tokenId)
	// }
	// return nil
	g.Log().Warning(ctx, "UpdateCumulativeWithdrawal is currently a no-op due to missing CumulativeWithdrawal field")
	return nil // Temporarily return nil to allow compilation
}

// ListAdminWalletsWithFullInfo 查询后台钱包列表（含完整信息）
func (d *walletsDao) ListAdminWalletsWithFullInfo(ctx context.Context, page int, pageSize int, condition g.Map) (list []*model.WalletAdminInfo, total int, err error) {
	// 初始化返回列表
	list = make([]*model.WalletAdminInfo, 0)

	// 构建基础查询，使用别名避免字段冲突
	m := d.Ctx(ctx).As("w").
		LeftJoin("users u", "w.user_id = u.id")

	// 添加用户的代理关联
	m = m.LeftJoin("agents first_agent", "u.first_id = first_agent.agent_id")
	m = m.LeftJoin("agents second_agent", "u.second_id = second_agent.agent_id")
	m = m.LeftJoin("agents third_agent", "u.third_id = third_agent.agent_id")

	// 添加用户的 Telegram 信息关联
	m = m.LeftJoin("user_backup_accounts uba", "u.id = uba.user_id AND uba.is_master = 1")

	// 添加软删除条件
	m = m.WhereNull("w.deleted_at")

	// 处理查询条件（分类处理 LIKE、BETWEEN、精确匹配）
	query := m
	var betweenConditions = make(map[string]g.Slice)
	var likeConditions = make(map[string]string)
	var exactConditions = make(map[string]interface{})

	// 将条件按类型分类
	for key, value := range condition {
		if gstr.Contains(key, " BETWEEN ? AND ?") {
			fieldName := gstr.Replace(key, " BETWEEN ? AND ?", "")
			if timeSlice, ok := value.(g.Slice); ok && len(timeSlice) == 2 {
				betweenConditions[fieldName] = timeSlice
			}
		} else if gstr.Contains(key, " LIKE ?") || gstr.Contains(key, " LIKE") {
			fieldName := gstr.Replace(key, " LIKE ?", "")
			fieldName = gstr.Replace(fieldName, " LIKE", "")
			if valueStr, ok := value.(string); ok {
				likeConditions[fieldName] = valueStr
			}
		} else if gstr.Contains(key, " >=") {
			query = query.Where(key, value)
		} else if gstr.Contains(key, " <=") {
			query = query.Where(key, value)
		} else {
			exactConditions[key] = value
		}
	}

	// 按顺序应用条件
	// 1. 先应用精确匹配条件
	for key, value := range exactConditions {
		query = query.Where(key, value)
	}

	// 2. 再应用LIKE条件
	for fieldName, pattern := range likeConditions {
		query = query.WhereLike(fieldName, pattern)
	}

	// 3. 最后应用BETWEEN条件
	for fieldName, timeSlice := range betweenConditions {
		query = query.WhereBetween(fieldName, timeSlice[0], timeSlice[1])
	}

	// 克隆查询用于计算总数
	countQuery := query.Clone()
	total, err = countQuery.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询钱包总数失败")
	}

	if total == 0 {
		return list, 0, nil
	}

	// 定义查询字段
	baseFields := []string{
		"w.wallet_id",
		"w.user_id",
		"u.account as user_account",
		"w.token_id",
		"w.symbol",
		"w.type",
		"w.available_balance",
		"w.frozen_balance",
		"w.decimal_places",
		"w.created_at",
		"w.updated_at",
	}

	// 用户的代理和Telegram字段
	userAgentAndTelegramFields := []string{
		"first_agent.username as first_agent_name",
		"second_agent.username as second_agent_name",
		"third_agent.username as third_agent_name",
		"uba.telegram_id",
		"uba.telegram_username",
		"uba.first_name",
	}

	allFields := append(baseFields, userAgentAndTelegramFields...)

	// 转换为interface{}类型
	fieldInterfaces := make([]interface{}, len(allFields))
	for i, field := range allFields {
		fieldInterfaces[i] = field
	}

	// 执行查询
	err = query.Page(page, pageSize).Order("w.wallet_id DESC").Fields(fieldInterfaces...).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询钱包列表失败")
	}

	return list, total, nil
}
