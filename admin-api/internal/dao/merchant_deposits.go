// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// merchantDepositsDao is the data access object for the table merchant_deposits.
// You can define custom methods on it to extend its functionality as needed.
type merchantDepositsDao struct {
	*internal.MerchantDepositsDao
}

var (
	// MerchantDeposits is a globally accessible object for table merchant_deposits operations.
	MerchantDeposits = merchantDepositsDao{internal.NewMerchantDepositsDao()}
)

// Add your custom methods and functionality below.

// List retrieves merchant deposits with pagination and filtering.
func (dao *merchantDepositsDao) List(ctx context.Context, page, pageSize int, condition g.Map) (records []*entity.MerchantDeposits, total int, err error) {
	// Build the base query
	query := dao.Ctx(ctx)

	// Apply conditions
	if len(condition) > 0 {
		query = query.Where(condition)
	}

	// Get total count
	total, err = query.Count()
	if err != nil {
		return nil, 0, err
	}

	// Apply pagination and ordering
	if page > 0 && pageSize > 0 {
		query = query.Page(page, pageSize)
	}

	query = query.OrderDesc("created_at")

	// Execute query
	err = query.Scan(&records)
	return
}

// GetByRechargesIdAndMerchantId retrieves a specific deposit record by ID and merchant ID.
func (dao *merchantDepositsDao) GetByRechargesIdAndMerchantId(ctx context.Context, rechargesId uint, merchantId uint64) (record *entity.MerchantDeposits, err error) {
	err = dao.Ctx(ctx).
		Where("recharges_id", rechargesId).
		Where("merchant_id", merchantId).
		Scan(&record)
	return
}

// UpdateStatusToCompleted updates a deposit record status to completed.
func (dao *merchantDepositsDao) UpdateStatusToCompleted(ctx context.Context, depositId uint64, confirmations int, adminRemark string) error {
	updateData := g.Map{
		"state":         2, // 2-已完成
		"confirmations": confirmations,
		"completed_at":  "NOW()",
		"updated_at":    "NOW()",
	}

	_, err := dao.Ctx(ctx).Where("recharges_id", depositId).Update(updateData)
	return err
}

// UpdateStatus updates a deposit record status with optional fields.
func (dao *merchantDepositsDao) UpdateStatus(ctx context.Context, depositId uint64, state int, updateData g.Map) error {
	if updateData == nil {
		updateData = g.Map{}
	}

	updateData["state"] = state
	updateData["updated_at"] = "NOW()"

	// 根据状态设置时间戳
	switch state {
	case 2: // 已完成
		updateData["completed_at"] = "NOW()"
	}

	_, err := dao.Ctx(ctx).Where("recharges_id", depositId).Update(updateData)
	return err
}
