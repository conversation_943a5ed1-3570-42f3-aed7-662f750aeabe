// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// merchantWithdrawsDao is the data access object for the table merchant_withdraws.
// You can define custom methods on it to extend its functionality as needed.
type merchantWithdrawsDao struct {
	*internal.MerchantWithdrawsDao
}

var (
	// MerchantWithdraws is a globally accessible object for table merchant_withdraws operations.
	MerchantWithdraws = merchantWithdrawsDao{internal.NewMerchantWithdrawsDao()}
)

// Add your custom methods and functionality below.

// List retrieves merchant withdraws with pagination and filtering.
func (dao *merchantWithdrawsDao) List(ctx context.Context, page, pageSize int, condition g.Map) (records []*entity.MerchantWithdraws, total int, err error) {
	// Build the base query
	query := dao.Ctx(ctx)

	// Apply conditions
	if len(condition) > 0 {
		query = query.Where(condition)
	}

	// Get total count
	total, err = query.Count()
	if err != nil {
		return nil, 0, err
	}

	// Apply pagination and ordering
	if page > 0 && pageSize > 0 {
		query = query.Page(page, pageSize)
	}

	query = query.OrderDesc("created_at")

	// Execute query
	err = query.Scan(&records)
	return
}

// GetByWithdrawsIdAndMerchantId retrieves a specific withdraw record by ID and merchant ID.
func (dao *merchantWithdrawsDao) GetByWithdrawsIdAndMerchantId(ctx context.Context, withdrawsId uint, merchantId uint64) (record *entity.MerchantWithdraws, err error) {
	err = dao.Ctx(ctx).
		Where("withdraws_id", withdrawsId).
		Where("merchant_id", merchantId).
		Scan(&record)
	return
}

// GetPendingWithdrawByMerchantAndId retrieves a pending withdraw record by ID and merchant ID.
func (dao *merchantWithdrawsDao) GetPendingWithdrawByMerchantAndId(ctx context.Context, withdrawsId uint, merchantId uint64) (record *entity.MerchantWithdraws, err error) {
	err = dao.Ctx(ctx).
		Where("withdraws_id", withdrawsId).
		Where("merchant_id", merchantId).
		Where("state", 1). // Only pending withdraws can be cancelled
		Scan(&record)
	return
}

// UpdateStatusToCancel updates a withdraw record status to cancelled.
func (dao *merchantWithdrawsDao) UpdateStatusToCancel(ctx context.Context, withdrawsId uint, merchantId uint64, cancelReason string) (err error) {
	_, err = dao.Ctx(ctx).
		Where("withdraws_id", withdrawsId).
		Where("merchant_id", merchantId).
		Where("state", 1). // Only update if still pending
		Update(g.Map{
			"state":        6, // Set to cancelled status
			"admin_remark": cancelReason,
			"checked_at":   gtime.Now(),
			"updated_at":   gtime.Now(),
		})
	return
}

// CreateWithdraw creates a new withdraw record.
func (dao *merchantWithdrawsDao) CreateWithdraw(ctx context.Context, data g.Map) (withdrawsId int64, err error) {
	result, err := dao.Ctx(ctx).Insert(data)
	if err != nil {
		return 0, err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, err
	}

	return id, nil
}

// CheckOrderNoExists checks if an order number already exists for a merchant.
func (dao *merchantWithdrawsDao) CheckOrderNoExists(ctx context.Context, merchantId uint64, orderNo string) (exists bool, err error) {
	count, err := dao.Ctx(ctx).
		Where("merchant_id", merchantId).
		Where("order_no", orderNo).
		Count()

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// UpdateStatusToCompleted updates a withdraw record status to completed.
func (dao *merchantWithdrawsDao) UpdateStatusToCompleted(ctx context.Context, withdrawsId uint64, txHash string, adminRemark string) error {
	updateData := g.Map{
		"state":        4, // 4-已完成
		"tx_hash":      txHash,
		"admin_remark": adminRemark,
		"completed_at": gtime.Now(),
		"updated_at":   gtime.Now(),
	}

	_, err := dao.Ctx(ctx).Where("withdraws_id", withdrawsId).Update(updateData)
	return err
}

// UpdateStatus updates a withdraw record status with optional fields.
func (dao *merchantWithdrawsDao) UpdateStatus(ctx context.Context, withdrawsId uint64, state int, updateData g.Map) error {
	if updateData == nil {
		updateData = g.Map{}
	}

	updateData["state"] = state
	updateData["updated_at"] = gtime.Now()

	// 根据状态设置时间戳
	switch state {
	case 2: // 处理中
		updateData["processing_at"] = gtime.Now()
		updateData["checked_at"] = gtime.Now()
	case 3: // 已拒绝
		updateData["checked_at"] = gtime.Now()
	case 4: // 已完成
		updateData["completed_at"] = gtime.Now()
	case 5: // 失败
		updateData["completed_at"] = gtime.Now()
	}

	_, err := dao.Ctx(ctx).Where("withdraws_id", withdrawsId).Update(updateData)
	return err
}

// UpdateStatusToApproved updates a withdraw record status to approved (processing).
func (dao *merchantWithdrawsDao) UpdateStatusToApproved(ctx context.Context, withdrawsId uint64, approverId uint64, approverNotes string) error {
	updateData := g.Map{
		"state":         2, // 2-处理中
		"checked_by":    approverId,
		"admin_remark":  approverNotes,
		"checked_at":    gtime.Now(),
		"processing_at": gtime.Now(),
		"updated_at":    gtime.Now(),
	}

	_, err := dao.Ctx(ctx).
		Where("withdraws_id", withdrawsId).
		Where("state", 1). // Only update if still pending
		Update(updateData)
	return err
}

// UpdateStatusToRejected updates a withdraw record status to rejected.
func (dao *merchantWithdrawsDao) UpdateStatusToRejected(ctx context.Context, withdrawsId uint64, rejecterId uint64, rejectReason string) error {
	updateData := g.Map{
		"state":            3, // 3-已拒绝
		"checked_by":       rejecterId,
		"admin_remark":     rejectReason,
		"refuse_reason_zh": rejectReason,
		"refuse_reason_en": rejectReason, // Can be translated later
		"checked_at":       gtime.Now(),
		"updated_at":       gtime.Now(),
	}

	_, err := dao.Ctx(ctx).
		Where("withdraws_id", withdrawsId).
		Where("state", 1). // Only update if still pending
		Update(updateData)
	return err
}
