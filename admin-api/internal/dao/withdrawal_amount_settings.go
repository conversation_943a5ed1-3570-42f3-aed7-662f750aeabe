// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// withdrawalAmountSettingsDao is the data access object for the table withdrawal_amount_settings.
// You can define custom methods on it to extend its functionality as needed.
type withdrawalAmountSettingsDao struct {
	*internal.WithdrawalAmountSettingsDao
}

var (
	// WithdrawalAmountSettings is a globally accessible object for table withdrawal_amount_settings operations.
	WithdrawalAmountSettings = withdrawalAmountSettingsDao{internal.NewWithdrawalAmountSettingsDao()}
)

// Add your custom methods and functionality below.
