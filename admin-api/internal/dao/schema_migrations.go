// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// schemaMigrationsDao is the data access object for the table schema_migrations.
// You can define custom methods on it to extend its functionality as needed.
type schemaMigrationsDao struct {
	*internal.SchemaMigrationsDao
}

var (
	// SchemaMigrations is a globally accessible object for table schema_migrations operations.
	SchemaMigrations = schemaMigrationsDao{internal.NewSchemaMigrationsDao()}
)

// Add your custom methods and functionality below.
