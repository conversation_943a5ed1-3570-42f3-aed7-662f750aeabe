// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
	"admin-api/internal/model"
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
)

// userRechargesDao is the data access object for the table user_recharges.
// You can define custom methods on it to extend its functionality as needed.
type userRechargesDao struct {
	*internal.UserRechargesDao
}

var (
	// UserRecharges is a globally accessible object for table user_recharges operations.
	UserRecharges = userRechargesDao{internal.NewUserRechargesDao()}
)

// Add your custom methods and functionality below.

// ListAdminUserRechargesWithFullInfo 查询后台用户充值记录列表（含完整信息）
func (d *userRechargesDao) ListAdminUserRechargesWithFullInfo(ctx context.Context, page int, pageSize int, condition g.Map) (list []*model.UserRechargeAdminInfo, total int, err error) {
	// 初始化返回列表
	list = make([]*model.UserRechargeAdminInfo, 0)

	// 构建基础查询，使用别名避免字段冲突
	m := g.DB().Model("user_recharges ur").
		LeftJoin("users u", "ur.user_id = u.id")

	// 添加用户的代理关联
	m = m.LeftJoin("agents u_first_agent", "u.first_id = u_first_agent.agent_id")
	m = m.LeftJoin("agents u_second_agent", "u.second_id = u_second_agent.agent_id")
	m = m.LeftJoin("agents u_third_agent", "u.third_id = u_third_agent.agent_id")

	// 添加用户的 Telegram 信息关联
	m = m.LeftJoin("user_backup_accounts u_uba", "u.id = u_uba.user_id AND u_uba.is_master = 1")

	// 处理查询条件（分类处理 LIKE、BETWEEN、精确匹配）
	query := m
	var betweenConditions = make(map[string]g.Slice)
	var likeConditions = make(map[string]string)
	var exactConditions = make(map[string]interface{})

	// 将条件按类型分类
	for key, value := range condition {
		if gstr.Contains(key, " BETWEEN ? AND ?") {
			fieldName := gstr.Replace(key, " BETWEEN ? AND ?", "")
			if timeSlice, ok := value.(g.Slice); ok && len(timeSlice) == 2 {
				betweenConditions[fieldName] = timeSlice
			}
		} else if gstr.Contains(key, " LIKE ?") || gstr.Contains(key, " LIKE") {
			fieldName := gstr.Replace(key, " LIKE ?", "")
			fieldName = gstr.Replace(fieldName, " LIKE", "")
			if valueStr, ok := value.(string); ok {
				likeConditions[fieldName] = valueStr
			}
		} else if gstr.Contains(key, " >=") {
			query = query.Where(key, value)
		} else if gstr.Contains(key, " <=") {
			query = query.Where(key, value)
		} else {
			exactConditions[key] = value
		}
	}

	// 按顺序应用条件
	// 1. 先应用精确匹配条件
	for key, value := range exactConditions {
		query = query.Where(key, value)
	}

	// 2. 再应用LIKE条件
	for fieldName, pattern := range likeConditions {
		query = query.WhereLike(fieldName, pattern)
	}

	// 3. 最后应用BETWEEN条件
	for fieldName, timeSlice := range betweenConditions {
		query = query.WhereBetween(fieldName, timeSlice[0], timeSlice[1])
	}

	// 克隆查询用于计算总数
	countQuery := query.Clone()
	total, err = countQuery.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询用户充值记录总数失败")
	}

	if total == 0 {
		return list, 0, nil
	}

	// 定义查询字段
	baseFields := []string{
		"ur.user_recharges_id",
		"ur.user_id",
		"ur.token_id",
		"ur.name",
		"ur.chan",
		"ur.token_contract_address",
		"ur.from_address",
		"ur.to_address",
		"ur.tx_hash",
		"ur.amount",
		"ur.state",
		"ur.confirmations",
		"ur.error",
		"ur.failure_reason",
		"ur.notification_sent",
		"ur.notification_sent_at",
		"ur.created_at",
		"ur.completed_at",
		"ur.updated_at",
		"u.account as account",
	}

	// 用户的代理和Telegram字段
	userAgentAndTelegramFields := []string{
		"u_first_agent.username as first_agent_name",
		"u_second_agent.username as second_agent_name",
		"u_third_agent.username as third_agent_name",
		"u_uba.telegram_id",
		"u_uba.telegram_username",
		"u_uba.first_name",
	}

	allFields := append(baseFields, userAgentAndTelegramFields...)

	// 转换为interface{}类型
	fieldInterfaces := make([]interface{}, len(allFields))
	for i, field := range allFields {
		fieldInterfaces[i] = field
	}

	// 执行查询
	err = query.Page(page, pageSize).Order("ur.created_at DESC").Fields(fieldInterfaces...).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询用户充值记录列表失败")
	}

	return list, total, nil
}
