// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/entity"
	
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// merchantWalletsDao is the data access object for the table merchant_wallets.
// You can define custom methods on it to extend its functionality as needed.
type merchantWalletsDao struct {
	*internal.MerchantWalletsDao
}

var (
	// MerchantWallets is a globally accessible object for table merchant_wallets operations.
	MerchantWallets = merchantWalletsDao{internal.NewMerchantWalletsDao()}
)

// Add your custom methods and functionality below.

// GetMerchantWalletsList 获取商户钱包列表 (带分页、过滤、排序)
func (d *merchantWalletsDao) GetMerchantWalletsList(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.MerchantWallets, total int, err error) {
	m := d.Ctx(ctx).Where(condition).WhereNull(d.Columns().DeletedAt)

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询商户钱包总数失败")
	}

	// 获取列表数据
	err = m.Page(page, pageSize).OrderDesc(d.Columns().UpdatedAt).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询商户钱包列表失败")
	}

	return list, total, nil
}

// GetMerchantWalletsByMerchantId 根据商户ID获取所有钱包
func (d *merchantWalletsDao) GetMerchantWalletsByMerchantId(ctx context.Context, merchantId uint64) (list []*entity.MerchantWallets, err error) {
	err = d.Ctx(ctx).
		Where(d.Columns().MerchantId, merchantId).
		WhereNull(d.Columns().DeletedAt).
		OrderAsc(d.Columns().Symbol).
		Scan(&list)
	if err != nil {
		return nil, gerror.Wrap(err, "查询商户钱包失败")
	}
	return list, nil
}

// GetMerchantWallet 获取商户特定币种钱包
func (d *merchantWalletsDao) GetMerchantWallet(ctx context.Context, merchantId uint64, symbol string) (*entity.MerchantWallets, error) {
	var wallet *entity.MerchantWallets
	err := d.Ctx(ctx).
		Where(d.Columns().MerchantId, merchantId).
		Where(d.Columns().Symbol, symbol).
		WhereNull(d.Columns().DeletedAt).
		Scan(&wallet)
	if err != nil {
		return nil, gerror.Wrap(err, "查询商户钱包失败")
	}
	return wallet, nil
}

// UpdateMerchantWalletBalance 更新商户钱包余额 (需要在事务中调用)
func (d *merchantWalletsDao) UpdateMerchantWalletBalance(ctx context.Context, tx gdb.TX, walletId int, availableBalance, frozenBalance decimal.Decimal) error {
	var err error
	if tx != nil {
		_, err = tx.Model(d.Table()).Ctx(ctx).
			Where(d.Columns().WalletId, walletId).
			Data(g.Map{
				d.Columns().AvailableBalance: availableBalance,
				d.Columns().FrozenBalance:    frozenBalance,
			}).
			Update()
	} else {
		_, err = d.Ctx(ctx).
			Where(d.Columns().WalletId, walletId).
			Data(g.Map{
				d.Columns().AvailableBalance: availableBalance,
				d.Columns().FrozenBalance:    frozenBalance,
			}).
			Update()
	}
	
	if err != nil {
		return gerror.Wrap(err, "更新商户钱包余额失败")
	}
	return nil
}

// GetMerchantAssetsSummary 获取商户资产汇总
func (d *merchantWalletsDao) GetMerchantAssetsSummary(ctx context.Context, merchantId uint64) (list []*entity.MerchantWallets, err error) {
	err = d.Ctx(ctx).
		Where(d.Columns().MerchantId, merchantId).
		WhereNull(d.Columns().DeletedAt).
		Where("(available_balance > 0 OR frozen_balance > 0)"). // 只显示有余额的钱包
		OrderAsc(d.Columns().Symbol).
		Scan(&list)
	if err != nil {
		return nil, gerror.Wrap(err, "查询商户资产汇总失败")
	}
	return list, nil
}
