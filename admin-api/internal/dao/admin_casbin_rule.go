// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// adminCasbinRuleDao is the data access object for the table admin_casbin_rule.
// You can define custom methods on it to extend its functionality as needed.
type adminCasbinRuleDao struct {
	*internal.AdminCasbinRuleDao
}

var (
	// AdminCasbinRule is a globally accessible object for table admin_casbin_rule operations.
	AdminCasbinRule = adminCasbinRuleDao{internal.NewAdminCasbinRuleDao()}
)

// Add your custom methods and functionality below.
