// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================
package dao

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"

	"admin-api/internal/dao/internal"
	"admin-api/internal/model"
	"admin-api/internal/model/entity"
)

// transactionsDao is the data access object for the table transactions.
// You can define custom methods on it to extend its functionality as needed.
type transactionsDao struct {
	*internal.TransactionsDao
}

var (
	// Transactions is a globally accessible object for table transactions operations.
	Transactions = transactionsDao{internal.NewTransactionsDao()}
)

// InsertTransaction 在事务中插入单条交易记录
// 注意：传入的 data 中的 Amount, BalanceBefore, BalanceAfter 应为 DECIMAL 格式的字符串
func (d *transactionsDao) InsertTransaction(ctx context.Context, tx gdb.TX, data *entity.Transactions) error {
	// 确保使用传入的事务 tx
	_, err := tx.Model(d.Table()).Ctx(ctx).Data(data).Insert()
	if err != nil {
		// 包装数据库错误，方便上层判断和记录日志
		return gerror.Wrapf(err, "插入交易记录失败, data: %+v", data)
	}
	return nil
}

// ListAdminTransactionsWithFullInfo 查询后台交易记录列表（含完整信息）
func (d *transactionsDao) ListAdminTransactionsWithFullInfo(ctx context.Context, page int, pageSize int, condition g.Map) (list []*model.TransactionAdminInfo, total int, err error) {
	// 初始化返回列表
	list = make([]*model.TransactionAdminInfo, 0)

	// 构建基础查询，使用别名避免字段冲突
	m := g.DB().Model("transactions t").
		LeftJoin("users u", "t.user_id = u.id").
		LeftJoin("tokens tk", "t.token_id = tk.token_id")

	// 添加用户的代理关联
	m = m.LeftJoin("agents u_first_agent", "u.first_id = u_first_agent.agent_id")
	m = m.LeftJoin("agents u_second_agent", "u.second_id = u_second_agent.agent_id")
	m = m.LeftJoin("agents u_third_agent", "u.third_id = u_third_agent.agent_id")

	// 添加用户的 Telegram 信息关联
	m = m.LeftJoin("user_backup_accounts u_uba", "u.id = u_uba.user_id AND u_uba.is_master = 1")

	// 添加软删除条件
	m = m.WhereNull("t.deleted_at")

	// 处理查询条件（分类处理 LIKE、BETWEEN、精确匹配）
	query := m
	var betweenConditions = make(map[string]g.Slice)
	var likeConditions = make(map[string]string)
	var exactConditions = make(map[string]interface{})

	// 将条件按类型分类
	for key, value := range condition {
		if gstr.Contains(key, " BETWEEN ? AND ?") {
			fieldName := gstr.Replace(key, " BETWEEN ? AND ?", "")
			if timeSlice, ok := value.(g.Slice); ok && len(timeSlice) == 2 {
				betweenConditions[fieldName] = timeSlice
			}
		} else if gstr.Contains(key, " LIKE ?") || gstr.Contains(key, " LIKE") {
			fieldName := gstr.Replace(key, " LIKE ?", "")
			fieldName = gstr.Replace(fieldName, " LIKE", "")
			if valueStr, ok := value.(string); ok {
				likeConditions[fieldName] = valueStr
			}
		} else if gstr.Contains(key, " >=") {
			query = query.Where(key, value)
		} else if gstr.Contains(key, " <=") {
			query = query.Where(key, value)
		} else {
			exactConditions[key] = value
		}
	}

	// 按顺序应用条件
	// 1. 先应用精确匹配条件
	for key, value := range exactConditions {
		query = query.Where(key, value)
	}

	// 2. 再应用LIKE条件
	for fieldName, pattern := range likeConditions {
		query = query.WhereLike(fieldName, pattern)
	}

	// 3. 最后应用BETWEEN条件
	for fieldName, timeSlice := range betweenConditions {
		query = query.WhereBetween(fieldName, timeSlice[0], timeSlice[1])
	}

	// 克隆查询用于计算总数
	countQuery := query.Clone()
	total, err = countQuery.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询交易记录总数失败")
	}

	if total == 0 {
		return list, 0, nil
	}

	// 定义查询字段
	baseFields := []string{
		"t.transaction_id",
		"t.user_id",
		"u.account as username",
		"t.token_id",
		"COALESCE(tk.symbol, t.symbol) as token_symbol",
		"t.type",
		"t.status",
		"t.amount",
		"t.balance_before",
		"t.balance_after",
		"t.related_transaction_id",
		"t.related_entity_id",
		"t.related_entity_type",
		"t.memo",
		"t.created_at",
		"t.updated_at",
		"t.direction",
	}

	// 用户的代理和Telegram字段
	userAgentAndTelegramFields := []string{
		"u_first_agent.username as first_agent_name",
		"u_second_agent.username as second_agent_name",
		"u_third_agent.username as third_agent_name",
		"u_uba.telegram_id",
		"u_uba.telegram_username",
		"u_uba.first_name",
	}

	allFields := append(baseFields, userAgentAndTelegramFields...)

	// 转换为interface{}类型
	fieldInterfaces := make([]interface{}, len(allFields))
	for i, field := range allFields {
		fieldInterfaces[i] = field
	}

	// 执行查询
	err = query.Page(page, pageSize).Order("t.created_at DESC").Fields(fieldInterfaces...).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询交易记录列表失败")
	}

	return list, total, nil
}

// ListAdminTransactions 保留原方法名以兼容旧代码
func (d *transactionsDao) ListAdminTransactions(ctx context.Context, page int, pageSize int, condition g.Map) (list []*model.TransactionAdminInfo, total int, err error) {
	return d.ListAdminTransactionsWithFullInfo(ctx, page, pageSize, condition)
}
