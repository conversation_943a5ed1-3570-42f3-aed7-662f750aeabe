// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UsersDao is the data access object for the table users.
type UsersDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  UsersColumns       // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// UsersColumns defines and stores column names for the table users.
type UsersColumns struct {
	Id                             string //
	Email                          string //
	EmailVerifiedAt                string //
	Password                       string // 用户登录密码（经过哈希处理）
	RememberToken                  string //
	Account                        string // 唯一用户账户标识符（例如，用于登录）
	AccountType                    string // 账户类型 1 用户 2商户 3 代理
	InviteCode                     string // 用户唯一邀请码（用于分享给其他人）
	AreaCode                       string // 电话区号
	Phone                          string // 用户电话号码
	Avatar                         string // 用户头像URL或路径
	PaymentPassword                string // 支付密码（经过哈希处理，未设置时为NULL）
	RecommendId                    string // 推荐该用户的用户ID（关联users.id）
	Deep                           string // 推荐结构中的层级深度
	RecommendRelationship          string // 表示客户推荐层级关系的路径（例如，/1/5/10/）
	AgentRelationship              string // 表示代理推荐层级关系的路径
	FirstId                        string // 关联的代理一级ID
	SecondId                       string // 关联的代理二级ID
	ThirdId                        string // 关联的代理三级ID
	IsStop                         string // 用户账户暂停状态：0=活跃，1=已暂停
	CurrentToken                   string // 最后使用的认证令牌（例如，API令牌）
	LastLoginTime                  string // 最后一次成功登录的时间戳
	Reason                         string // 暂停或其他状态变更的原因
	CreatedAt                      string //
	UpdatedAt                      string //
	DeletedAt                      string // 软删除的时间戳
	Google2FaSecret                string // 谷歌2fa密钥
	Google2FaEnabled               string // 谷歌2fa是否启用
	IsPaymentPassword              string // 是否开启免密支付
	PaymentPasswordAmount          string // 免密支付额度
	BackupAccounts                 string // 备用账户
	Language                       string // 语言
	RedPacketPermission            string // 红包权限
	TransferPermission             string // 转账权限
	WithdrawPermission             string // 提现权限
	FlashTradePermission           string // 闪兑权限
	RechargePermission             string // 充值权限
	ReceivePermission              string // 收款权限
	ResetPaymentPasswordPermission string // 重置支付密码权限：0=无权限，1=有权限
	MainWalletId                   string // 钱包id
}

// usersColumns holds the columns for the table users.
var usersColumns = UsersColumns{
	Id:                             "id",
	Email:                          "email",
	EmailVerifiedAt:                "email_verified_at",
	Password:                       "password",
	RememberToken:                  "remember_token",
	Account:                        "account",
	AccountType:                    "account_type",
	InviteCode:                     "invite_code",
	AreaCode:                       "area_code",
	Phone:                          "phone",
	Avatar:                         "avatar",
	PaymentPassword:                "payment_password",
	RecommendId:                    "recommend_id",
	Deep:                           "deep",
	RecommendRelationship:          "recommend_relationship",
	AgentRelationship:              "agent_relationship",
	FirstId:                        "first_id",
	SecondId:                       "second_id",
	ThirdId:                        "third_id",
	IsStop:                         "is_stop",
	CurrentToken:                   "current_token",
	LastLoginTime:                  "last_login_time",
	Reason:                         "reason",
	CreatedAt:                      "created_at",
	UpdatedAt:                      "updated_at",
	DeletedAt:                      "deleted_at",
	Google2FaSecret:                "google2fa_secret",
	Google2FaEnabled:               "google2fa_enabled",
	IsPaymentPassword:              "is_payment_password",
	PaymentPasswordAmount:          "payment_password_amount",
	BackupAccounts:                 "backup_accounts",
	Language:                       "language",
	RedPacketPermission:            "red_packet_permission",
	TransferPermission:             "transfer_permission",
	WithdrawPermission:             "withdraw_permission",
	FlashTradePermission:           "flash_trade_permission",
	RechargePermission:             "recharge_permission",
	ReceivePermission:              "receive_permission",
	ResetPaymentPasswordPermission: "reset_payment_password_permission",
	MainWalletId:                   "main_wallet_id",
}

// NewUsersDao creates and returns a new DAO object for table data access.
func NewUsersDao(handlers ...gdb.ModelHandler) *UsersDao {
	return &UsersDao{
		group:    "default",
		table:    "users",
		columns:  usersColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UsersDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UsersDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UsersDao) Columns() UsersColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UsersDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UsersDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UsersDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
