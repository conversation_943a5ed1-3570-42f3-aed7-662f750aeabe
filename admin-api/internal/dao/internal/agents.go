// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AgentsDao is the data access object for the table agents.
type AgentsDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  AgentsColumns      // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// AgentsColumns defines and stores column names for the table agents.
type AgentsColumns struct {
	AgentId                   string // 代理唯一ID
	Username                  string // 代理登录用户名
	PasswordHash              string // 加密后的登录密码
	AgentName                 string // 代理真实姓名或昵称
	Email                     string // 电子邮箱
	Level                     string // 代理级别 (1: 一级代理, 2: 二级代理, 3: 三级代理)
	ParentAgentId             string // 上级代理ID (一级代理此字段为NULL)
	Status                    string // 账户状态 (0-禁用, 1-启用)
	InvitationCode            string // 专属邀请码 (用于下级注册)
	GoogleAuthenticatorSecret string // Google Authenticator 的秘钥 (用于2FA)
	Relationship              string // 代理商关系 (例如层级路径或其他关系标识)
	CreatedAt                 string // 创建时间
	UpdatedAt                 string // 最后更新时间
	DeletedAt                 string // 软删除时间
}

// agentsColumns holds the columns for the table agents.
var agentsColumns = AgentsColumns{
	AgentId:                   "agent_id",
	Username:                  "username",
	PasswordHash:              "password_hash",
	AgentName:                 "agent_name",
	Email:                     "email",
	Level:                     "level",
	ParentAgentId:             "parent_agent_id",
	Status:                    "status",
	InvitationCode:            "invitation_code",
	GoogleAuthenticatorSecret: "google_authenticator_secret",
	Relationship:              "relationship",
	CreatedAt:                 "created_at",
	UpdatedAt:                 "updated_at",
	DeletedAt:                 "deleted_at",
}

// NewAgentsDao creates and returns a new DAO object for table data access.
func NewAgentsDao(handlers ...gdb.ModelHandler) *AgentsDao {
	return &AgentsDao{
		group:    "default",
		table:    "agents",
		columns:  agentsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AgentsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AgentsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AgentsDao) Columns() AgentsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AgentsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AgentsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AgentsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
