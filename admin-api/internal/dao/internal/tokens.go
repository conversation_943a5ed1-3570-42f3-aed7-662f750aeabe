// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TokensDao is the data access object for the table tokens.
type TokensDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  TokensColumns      // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// TokensColumns defines and stores column names for the table tokens.
type TokensColumns struct {
	TokenId                 string // 代币内部 ID (主键)
	Symbol                  string // 代币符号 (例如: USDT, BTC, ETH)
	Name                    string // 代币名称 (例如: Tether, Bitcoin, Ethereum)
	Network                 string // 所属网络/链 (例如: Ethereum, Tron, Bitcoin, BSC, Solana)
	ChainId                 string // 链 ID (主要用于 EVM 兼容链)
	Confirmations           string // 确认次数
	ContractAddress         string // 代币合约地址 (对于非原生代币). 对于原生代币为 NULL.
	TokenStandard           string // 代币标准 (例如: native, ERC20, TRC20, BEP20, SPL)
	Decimals                string // 代币精度/小数位数
	IsStablecoin            string // 是否为稳定币
	IsFiat                  string // 是否为法币
	IsActive                string // 代币是否在系统内激活可用 (总开关)
	AllowDeposit            string // 是否允许充值 (设为 FALSE 即表示充值维护中)
	AllowWithdraw           string // 是否允许提现 (设为 FALSE 即表示提现维护中)
	AllowTransfer           string // 是否允许内部转账
	AllowReceive            string // 是否允许内部收款 (通常与 allow_transfer 联动或独立控制)
	AllowRedPacket          string // 是否允许发红包
	AllowTrading            string // 是否允许在交易对中使用
	MaintenanceMessage      string // 维护信息 (当充值/提现/其他功能禁用时显示)
	WithdrawalFeeType       string // 提币手续费类型: fixed-固定金额, percent-百分比
	WithdrawalFeeAmount     string // 提币手续费金额/比例 (根据 fee_type 决定)
	MinDepositAmount        string // 单笔最小充值金额
	MaxDepositAmount        string // 单笔最大充值金额 (NULL 表示无限制)
	MinWithdrawalAmount     string // 单笔最小提币金额
	MaxWithdrawalAmount     string // 单笔最大提币金额 (NULL 表示无限制)
	MinTransferAmount       string // 单笔最小转账金额
	MaxTransferAmount       string // 单笔最大转账金额 (NULL 表示无限制)
	MinReceiveAmount        string // 单笔最小收款金额
	MaxReceiveAmount        string // 单笔最大收款金额 (NULL 表示无限制)
	MinRedPacketAmount      string // 单个红包最小金额
	MaxRedPacketAmount      string // 单个红包最大金额 (NULL 表示无限制)
	MaxRedPacketCount       string // 单次发放红包最大个数 (NULL 表示无限制)
	MaxRedPacketTotalAmount string // 单次发放红包最大总金额 (NULL 表示无限制)
	LogoUrl                 string // 代币 Logo 图片 URL
	ProjectWebsite          string // 项目官方网站 URL
	Description             string // 代币或项目描述
	Order                   string // 排序字段 (用于前端展示时的排序)
	Status                  string // 状态 0-下架 1-上架
	CreatedAt               string // 创建时间
	UpdatedAt               string // 最后更新时间
	DeletedAt               string // 软删除时间
}

// tokensColumns holds the columns for the table tokens.
var tokensColumns = TokensColumns{
	TokenId:                 "token_id",
	Symbol:                  "symbol",
	Name:                    "name",
	Network:                 "network",
	ChainId:                 "chain_id",
	Confirmations:           "confirmations",
	ContractAddress:         "contract_address",
	TokenStandard:           "token_standard",
	Decimals:                "decimals",
	IsStablecoin:            "is_stablecoin",
	IsFiat:                  "is_fiat",
	IsActive:                "is_active",
	AllowDeposit:            "allow_deposit",
	AllowWithdraw:           "allow_withdraw",
	AllowTransfer:           "allow_transfer",
	AllowReceive:            "allow_receive",
	AllowRedPacket:          "allow_red_packet",
	AllowTrading:            "allow_trading",
	MaintenanceMessage:      "maintenance_message",
	WithdrawalFeeType:       "withdrawal_fee_type",
	WithdrawalFeeAmount:     "withdrawal_fee_amount",
	MinDepositAmount:        "min_deposit_amount",
	MaxDepositAmount:        "max_deposit_amount",
	MinWithdrawalAmount:     "min_withdrawal_amount",
	MaxWithdrawalAmount:     "max_withdrawal_amount",
	MinTransferAmount:       "min_transfer_amount",
	MaxTransferAmount:       "max_transfer_amount",
	MinReceiveAmount:        "min_receive_amount",
	MaxReceiveAmount:        "max_receive_amount",
	MinRedPacketAmount:      "min_red_packet_amount",
	MaxRedPacketAmount:      "max_red_packet_amount",
	MaxRedPacketCount:       "max_red_packet_count",
	MaxRedPacketTotalAmount: "max_red_packet_total_amount",
	LogoUrl:                 "logo_url",
	ProjectWebsite:          "project_website",
	Description:             "description",
	Order:                   "order",
	Status:                  "status",
	CreatedAt:               "created_at",
	UpdatedAt:               "updated_at",
	DeletedAt:               "deleted_at",
}

// NewTokensDao creates and returns a new DAO object for table data access.
func NewTokensDao(handlers ...gdb.ModelHandler) *TokensDao {
	return &TokensDao{
		group:    "default",
		table:    "tokens",
		columns:  tokensColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TokensDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TokensDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TokensDao) Columns() TokensColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TokensDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TokensDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TokensDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
