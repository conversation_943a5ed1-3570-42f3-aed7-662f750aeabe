// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AgentMenuDao is the data access object for the table agent_menu.
type AgentMenuDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  AgentMenuColumns   // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// AgentMenuColumns defines and stores column names for the table agent_menu.
type AgentMenuColumns struct {
	Id                 string // 菜单ID
	Pid                string // 父菜单ID
	Level              string // 关系树等级
	Tree               string // 关系树
	Name               string // 名称编码
	Path               string // 路由地址
	Icon               string // 菜单图标
	HideInMenu         string // 是否隐藏
	HideChildrenInMenu string // 是否隐藏子菜单
	Sort               string // 排序
	Remark             string // 备注
	Status             string // 菜单状态
	UpdatedAt          string // 更新时间
	CreatedAt          string // 创建时间
	DeletedAt          string // 软删除的时间戳
	Target             string //
	Access             string //
	Key                string //
}

// agentMenuColumns holds the columns for the table agent_menu.
var agentMenuColumns = AgentMenuColumns{
	Id:                 "id",
	Pid:                "pid",
	Level:              "level",
	Tree:               "tree",
	Name:               "name",
	Path:               "path",
	Icon:               "icon",
	HideInMenu:         "hide_in_menu",
	HideChildrenInMenu: "hide_childrenIn_menu",
	Sort:               "sort",
	Remark:             "remark",
	Status:             "status",
	UpdatedAt:          "updated_at",
	CreatedAt:          "created_at",
	DeletedAt:          "deleted_at",
	Target:             "target",
	Access:             "access",
	Key:                "key",
}

// NewAgentMenuDao creates and returns a new DAO object for table data access.
func NewAgentMenuDao(handlers ...gdb.ModelHandler) *AgentMenuDao {
	return &AgentMenuDao{
		group:    "default",
		table:    "agent_menu",
		columns:  agentMenuColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AgentMenuDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AgentMenuDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AgentMenuDao) Columns() AgentMenuColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AgentMenuDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AgentMenuDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AgentMenuDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
