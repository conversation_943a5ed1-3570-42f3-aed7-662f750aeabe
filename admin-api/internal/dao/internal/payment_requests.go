// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// PaymentRequestsDao is the data access object for the table payment_requests.
type PaymentRequestsDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  PaymentRequestsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// PaymentRequestsColumns defines and stores column names for the table payment_requests.
type PaymentRequestsColumns struct {
	RequestId              string // 收款请求 ID (主键)
	RequesterUserId        string // 收款发起者用户 ID (外键, 指向 users.user_id)
	RequesterUsername      string // 收款发起者telegram username
	PayerUserId            string // 指定付款人用户 ID (如果为空则任何人可付, 外键, 指向 users.user_id)
	PayerUsername          string // 指定付款人用户telegram username
	TokenId                string // 收款代币 ID (外键, 指向 tokens.token_id)
	Amount                 string // 收款金额
	Memo                   string // 收款说明/备注
	Status                 string // 请求状态: 1-待支付(pending), 2-已支付(paid), 3-已过期(expired), 4-已取消(cancelled)
	PaymentTransactionId   string // 关联的支付交易记录ID (外键, 指向 transactions.transaction_id)
	RequesterTransactionId string // 关联的支付交易记录ID (外键, 指向 transactions.transaction_id)
	TelegramChatId         string // 发起请求的 Telegram 聊天 ID (用于更新消息)
	TelegramMessageId      string // 原始收款请求消息的 Telegram 消息 ID (用于更新消息)
	CreatedAt              string // 创建时间
	ExpiresAt              string // 过期时间 (例如: 创建时间 + 24小时)
	PaidAt                 string // 支付时间
	CancelledAt            string // 取消时间
	InlineMessageId        string // 内联消息 ID，用于后续编辑
	UpdatedAt              string // 最后更新时间
	DeletedAt              string // 软删除的时间戳
	Symbol                 string //
}

// paymentRequestsColumns holds the columns for the table payment_requests.
var paymentRequestsColumns = PaymentRequestsColumns{
	RequestId:              "request_id",
	RequesterUserId:        "requester_user_id",
	RequesterUsername:      "requester_username",
	PayerUserId:            "payer_user_id",
	PayerUsername:          "payer_username",
	TokenId:                "token_id",
	Amount:                 "amount",
	Memo:                   "memo",
	Status:                 "status",
	PaymentTransactionId:   "payment_transaction_id",
	RequesterTransactionId: "requester_transaction_id",
	TelegramChatId:         "telegram_chat_id",
	TelegramMessageId:      "telegram_message_id",
	CreatedAt:              "created_at",
	ExpiresAt:              "expires_at",
	PaidAt:                 "paid_at",
	CancelledAt:            "cancelled_at",
	InlineMessageId:        "inline_message_id",
	UpdatedAt:              "updated_at",
	DeletedAt:              "deleted_at",
	Symbol:                 "symbol",
}

// NewPaymentRequestsDao creates and returns a new DAO object for table data access.
func NewPaymentRequestsDao(handlers ...gdb.ModelHandler) *PaymentRequestsDao {
	return &PaymentRequestsDao{
		group:    "default",
		table:    "payment_requests",
		columns:  paymentRequestsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *PaymentRequestsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *PaymentRequestsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *PaymentRequestsDao) Columns() PaymentRequestsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *PaymentRequestsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *PaymentRequestsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *PaymentRequestsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
