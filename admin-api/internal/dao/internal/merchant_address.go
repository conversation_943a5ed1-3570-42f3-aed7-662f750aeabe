// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// MerchantAddressDao is the data access object for the table merchant_address.
type MerchantAddressDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  MerchantAddressColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// MerchantAddressColumns defines and stores column names for the table merchant_address.
type MerchantAddressColumns struct {
	AddressId  string //
	TokenId    string // 币种ID
	MerchantId string // 用户id
	Lable      string // 备注
	Name       string // 币种
	Chan       string // 链
	Address    string // 地址
	Image      string // 二维码
	QrUrl      string // S3二维码URL
	CreatedAt  string //
	UpdatedAt  string //
	Type       string //
}

// merchantAddressColumns holds the columns for the table merchant_address.
var merchantAddressColumns = MerchantAddressColumns{
	AddressId:  "address_id",
	TokenId:    "token_id",
	MerchantId: "merchant_id",
	Lable:      "lable",
	Name:       "name",
	Chan:       "chan",
	Address:    "address",
	Image:      "image",
	QrUrl:      "qr_url",
	CreatedAt:  "created_at",
	UpdatedAt:  "updated_at",
	Type:       "type",
}

// NewMerchantAddressDao creates and returns a new DAO object for table data access.
func NewMerchantAddressDao(handlers ...gdb.ModelHandler) *MerchantAddressDao {
	return &MerchantAddressDao{
		group:    "default",
		table:    "merchant_address",
		columns:  merchantAddressColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *MerchantAddressDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *MerchantAddressDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *MerchantAddressDao) Columns() MerchantAddressColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *MerchantAddressDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *MerchantAddressDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *MerchantAddressDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
