// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// RedPacketImagesDao is the data access object for the table red_packet_images.
type RedPacketImagesDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  RedPacketImagesColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// RedPacketImagesColumns defines and stores column names for the table red_packet_images.
type RedPacketImagesColumns struct {
	RedPacketImagesId     string // 红包 ID (主键)
	Status                string // 图片状态 pending_review, fail, success
	ProcessingStatus      string // 处理状态: pending-待处理, processing-处理中, completed-已完成,    failed-处理失败
	ProcessingAttempts    string // 处理尝试次数
	ProcessingStartedAt   string // 开始处理时间
	ProcessingCompletedAt string // 处理完成时间
	ProcessingError       string // 处理错误信息
	OriginalFileSize      string // 原始文件大小(字节)
	ProcessedFileSize     string // 处理后文件大小(字节)
	ImageWidth            string // 图片宽度
	ImageHeight           string // 图片高度
	ImageFormat           string // 图片格式(jpeg, png等)
	CreatedAt             string // 创建时间
	DeletedAt             string // 软删除的时间戳
	UpdatedAt             string // 最后更新时间
	RefuseReasonZh        string // 拒绝原因 (中文)
	RefuseReasonEn        string // 拒绝原因 (英文)
	UserId                string // 用户ID (Foreign key to users table recommended)
	ImagesUrl             string //
	FileId                string //
	NotificationSent      string // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt    string // 通知发送时间
}

// redPacketImagesColumns holds the columns for the table red_packet_images.
var redPacketImagesColumns = RedPacketImagesColumns{
	RedPacketImagesId:     "red_packet_images_id",
	Status:                "status",
	ProcessingStatus:      "processing_status",
	ProcessingAttempts:    "processing_attempts",
	ProcessingStartedAt:   "processing_started_at",
	ProcessingCompletedAt: "processing_completed_at",
	ProcessingError:       "processing_error",
	OriginalFileSize:      "original_file_size",
	ProcessedFileSize:     "processed_file_size",
	ImageWidth:            "image_width",
	ImageHeight:           "image_height",
	ImageFormat:           "image_format",
	CreatedAt:             "created_at",
	DeletedAt:             "deleted_at",
	UpdatedAt:             "updated_at",
	RefuseReasonZh:        "refuse_reason_zh",
	RefuseReasonEn:        "refuse_reason_en",
	UserId:                "user_id",
	ImagesUrl:             "images_url",
	FileId:                "file_id",
	NotificationSent:      "notification_sent",
	NotificationSentAt:    "notification_sent_at",
}

// NewRedPacketImagesDao creates and returns a new DAO object for table data access.
func NewRedPacketImagesDao(handlers ...gdb.ModelHandler) *RedPacketImagesDao {
	return &RedPacketImagesDao{
		group:    "default",
		table:    "red_packet_images",
		columns:  redPacketImagesColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *RedPacketImagesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *RedPacketImagesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *RedPacketImagesDao) Columns() RedPacketImagesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *RedPacketImagesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *RedPacketImagesDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *RedPacketImagesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
