// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ExchangeProductsDao is the data access object for the table exchange_products.
type ExchangeProductsDao struct {
	table    string                  // table is the underlying table name of the DAO.
	group    string                  // group is the database configuration group name of the current DAO.
	columns  ExchangeProductsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler      // handlers for customized model modification.
}

// ExchangeProductsColumns defines and stores column names for the table exchange_products.
type ExchangeProductsColumns struct {
	ProductId              string // 兑换产品内部 ID (主键)
	BaseToken              string // 基础代币 ID (用户卖出/花费的代币, 外键关联 tokens.token_id)
	QuoteToken             string // 计价代币 ID (用户买入/获得的代币, 外键关联 tokens.token_id)
	Symbol                 string // 产品交易对符号 (例如: BTC/USDT, ETH/BTC)
	ProductType            string // 产品类型: swap-即时兑换, spot_limit-现货限价(若支持), 或其他自定义类型
	IsActive               string // 该兑换产品是否激活可用 (总开关)
	AllowBuy               string // 是否允许买入基础代币 (即 Quote->Base 的兑换)
	AllowSell              string // 是否允许卖出基础代币 (即 Base->Quote 的兑换)
	MaintenanceMessage     string // 维护信息 (当产品不可用或部分功能禁用时显示)
	MinBaseAmountPerTx     string // 单笔最小兑换的基础代币数量
	MaxBaseAmountPerTx     string // 单笔最大兑换的基础代币数量 (NULL 表示无特定限制)
	DailyBaseVolumeLimit   string // 产品每日总兑换基础代币量上限 (平台风控, NULL 不限制)
	TotalBaseVolumeLimit   string // 产品累计总兑换基础代币量上限 (平台风控, NULL 不限制)
	PriceSource            string // 价格/汇率来源标识 (例如: internal_oracle, binance_feed, weighted_average)
	AllowedSlippagePercent string // 允许的最大价格滑点百分比 (例如 0.0050 表示 0.5%). NULL 表示此产品无特定滑点设置
	SpreadRate             string // 平台在报价基础上额外添加的价差率 (例如 0.001 表示 0.1%)
	RateRefreshIntervalSec string // 建议的价格刷新频率 (秒), 应用层参考 (NULL 表示无建议)
	DisplayOrder           string // 显示排序 (数字越小越靠前)
	Description            string // 产品或交易对的描述信息
	CreatedAt              string // 创建时间
	UpdatedAt              string // 最后更新时间
	DeletedAt              string // 软删除时间
	Status                 string // 状态
	FeeStrategy            string // 手续费策略: output_token_percentage-从输出代币按百分比扣除
	OutputFeeRate          string // 输出代币手续费率 (例如: 0.002 表示 0.2%)
	MinOutputFeeAmount     string // 最小手续费金额（以输出代币计价）
}

// exchangeProductsColumns holds the columns for the table exchange_products.
var exchangeProductsColumns = ExchangeProductsColumns{
	ProductId:              "product_id",
	BaseToken:              "base_token",
	QuoteToken:             "quote_token",
	Symbol:                 "symbol",
	ProductType:            "product_type",
	IsActive:               "is_active",
	AllowBuy:               "allow_buy",
	AllowSell:              "allow_sell",
	MaintenanceMessage:     "maintenance_message",
	MinBaseAmountPerTx:     "min_base_amount_per_tx",
	MaxBaseAmountPerTx:     "max_base_amount_per_tx",
	DailyBaseVolumeLimit:   "daily_base_volume_limit",
	TotalBaseVolumeLimit:   "total_base_volume_limit",
	PriceSource:            "price_source",
	AllowedSlippagePercent: "allowed_slippage_percent",
	SpreadRate:             "spread_rate",
	RateRefreshIntervalSec: "rate_refresh_interval_sec",
	DisplayOrder:           "display_order",
	Description:            "description",
	CreatedAt:              "created_at",
	UpdatedAt:              "updated_at",
	DeletedAt:              "deleted_at",
	Status:                 "status",
	FeeStrategy:            "fee_strategy",
	OutputFeeRate:          "output_fee_rate",
	MinOutputFeeAmount:     "min_output_fee_amount",
}

// NewExchangeProductsDao creates and returns a new DAO object for table data access.
func NewExchangeProductsDao(handlers ...gdb.ModelHandler) *ExchangeProductsDao {
	return &ExchangeProductsDao{
		group:    "default",
		table:    "exchange_products",
		columns:  exchangeProductsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ExchangeProductsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ExchangeProductsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ExchangeProductsDao) Columns() ExchangeProductsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ExchangeProductsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ExchangeProductsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ExchangeProductsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
