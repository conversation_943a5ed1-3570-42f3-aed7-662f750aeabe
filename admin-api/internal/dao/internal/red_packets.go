// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// RedPacketsDao is the data access object for the table red_packets.
type RedPacketsDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  RedPacketsColumns  // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// RedPacketsColumns defines and stores column names for the table red_packets.
type RedPacketsColumns struct {
	RedPacketId       string // 红包 ID (主键)
	Uuid              string // 红包唯一id
	RedPacketImagesId string // 图片id 外键id 指向red_packet_images.red_packet_images_id
	CreatorUserId     string // 创建者用户 ID (外键, 指向 users.user_id)
	CreatorUsername   string // 创建者用户 ID (外键, 指向 users.user_id)
	CoverFileId       string // 创建者用户 ID (外键, 指向 users.user_id)
	ThumbUrl          string // 创建者用户 ID (外键, 指向 users.user_id)
	TokenId           string // 红包代币 ID (外键, 指向 tokens.token_id)
	TotalAmount       string // 红包总金额
	Quantity          string // 红包总个数
	RemainingAmount   string // 剩余金额
	RemainingQuantity string // 剩余个数
	Type              string // 红包类型: random-随机金额, fixed-固定金额
	Memo              string // 红包留言
	Status            string // 红包状态: active-可领取, expired-已过期, empty-已领完, cancelled-已取消
	CreatedAt         string // 创建时间
	ExpiresAt         string // 过期时间
	DeletedAt         string // 软删除的时间戳
	SenderUserId      string // 发送方用户 ID (外键, 指向 users.user_id)
	Symbol            string // 代币符号 (例如: USDT, BTC, ETH)
	TransactionId     string // 关联的扣款交易流水 ID (外键, 指向 transactions.id)
	IsPremium         string // 是否需要会员
	MessageId         string // 内联消息 ID，用于后续编辑
}

// redPacketsColumns holds the columns for the table red_packets.
var redPacketsColumns = RedPacketsColumns{
	RedPacketId:       "red_packet_id",
	Uuid:              "uuid",
	RedPacketImagesId: "red_packet_images_id",
	CreatorUserId:     "creator_user_id",
	CreatorUsername:   "creator_username",
	CoverFileId:       "cover_file_id",
	ThumbUrl:          "thumb_url",
	TokenId:           "token_id",
	TotalAmount:       "total_amount",
	Quantity:          "quantity",
	RemainingAmount:   "remaining_amount",
	RemainingQuantity: "remaining_quantity",
	Type:              "type",
	Memo:              "memo",
	Status:            "status",
	CreatedAt:         "created_at",
	ExpiresAt:         "expires_at",
	DeletedAt:         "deleted_at",
	SenderUserId:      "sender_user_id",
	Symbol:            "symbol",
	TransactionId:     "transaction_id",
	IsPremium:         "is_premium",
	MessageId:         "message_id",
}

// NewRedPacketsDao creates and returns a new DAO object for table data access.
func NewRedPacketsDao(handlers ...gdb.ModelHandler) *RedPacketsDao {
	return &RedPacketsDao{
		group:    "default",
		table:    "red_packets",
		columns:  redPacketsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *RedPacketsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *RedPacketsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *RedPacketsDao) Columns() RedPacketsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *RedPacketsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *RedPacketsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *RedPacketsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
