// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// MerchantCallbacksDao is the data access object for the table merchant_callbacks.
type MerchantCallbacksDao struct {
	table    string                   // table is the underlying table name of the DAO.
	group    string                   // group is the database configuration group name of the current DAO.
	columns  MerchantCallbacksColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler       // handlers for customized model modification.
}

// MerchantCallbacksColumns defines and stores column names for the table merchant_callbacks.
type MerchantCallbacksColumns struct {
	Id            string //
	MerchantId    string // 商户ID
	CallbackType  string // 回调事件类型
	RelatedId     string // 关联记录ID
	CallbackUrl   string // 回调URL
	Payload       string // 回调数据
	Status        string // 回调状态
	RetryCount    string // 重试次数
	ResponseCode  string // 响应状态码
	ResponseBody  string // 响应内容
	LastAttemptAt string // 最后尝试时间
	CreatedAt     string //
	UpdatedAt     string //
}

// merchantCallbacksColumns holds the columns for the table merchant_callbacks.
var merchantCallbacksColumns = MerchantCallbacksColumns{
	Id:            "id",
	MerchantId:    "merchant_id",
	CallbackType:  "callback_type",
	RelatedId:     "related_id",
	CallbackUrl:   "callback_url",
	Payload:       "payload",
	Status:        "status",
	RetryCount:    "retry_count",
	ResponseCode:  "response_code",
	ResponseBody:  "response_body",
	LastAttemptAt: "last_attempt_at",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
}

// NewMerchantCallbacksDao creates and returns a new DAO object for table data access.
func NewMerchantCallbacksDao(handlers ...gdb.ModelHandler) *MerchantCallbacksDao {
	return &MerchantCallbacksDao{
		group:    "default",
		table:    "merchant_callbacks",
		columns:  merchantCallbacksColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *MerchantCallbacksDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *MerchantCallbacksDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *MerchantCallbacksDao) Columns() MerchantCallbacksColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *MerchantCallbacksDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *MerchantCallbacksDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *MerchantCallbacksDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
