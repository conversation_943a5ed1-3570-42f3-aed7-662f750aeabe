// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ImagesDao is the data access object for the table images.
type ImagesDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  ImagesColumns      // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// ImagesColumns defines and stores column names for the table images.
type ImagesColumns struct {
	ImagesId       string // 红包 ID (主键)
	Status         string // 图片状态 pending_review, fail, success
	CreatedAt      string // 创建时间
	DeletedAt      string // 软删除的时间戳
	UpdatedAt      string // 最后更新时间
	RefuseReasonZh string // 拒绝原因 (中文)
	RefuseReasonEn string // 拒绝原因 (英文)
	UserId         string // 用户ID (Foreign key to users table recommended)
	ImagesUrl      string //
	FileId         string //
}

// imagesColumns holds the columns for the table images.
var imagesColumns = ImagesColumns{
	ImagesId:       "images_id",
	Status:         "status",
	CreatedAt:      "created_at",
	DeletedAt:      "deleted_at",
	UpdatedAt:      "updated_at",
	RefuseReasonZh: "refuse_reason_zh",
	RefuseReasonEn: "refuse_reason_en",
	UserId:         "user_id",
	ImagesUrl:      "images_url",
	FileId:         "file_id",
}

// NewImagesDao creates and returns a new DAO object for table data access.
func NewImagesDao(handlers ...gdb.ModelHandler) *ImagesDao {
	return &ImagesDao{
		group:    "default",
		table:    "images",
		columns:  imagesColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ImagesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ImagesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ImagesDao) Columns() ImagesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ImagesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ImagesDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ImagesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
