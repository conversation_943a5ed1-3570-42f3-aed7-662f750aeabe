// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ReferralRelationshipsDao is the data access object for the table referral_relationships.
type ReferralRelationshipsDao struct {
	table    string                       // table is the underlying table name of the DAO.
	group    string                       // group is the database configuration group name of the current DAO.
	columns  ReferralRelationshipsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler           // handlers for customized model modification.
}

// ReferralRelationshipsColumns defines and stores column names for the table referral_relationships.
type ReferralRelationshipsColumns struct {
	Type           string // 上级类型 代理，用户
	RelationshipId string // 关系 ID (主键)
	UserId         string // 被推荐人用户 ID (外键, 指向 users.user_id)
	ReferrerId     string // 推荐人用户 ID (外键, 指向 users.user_id)
	Level          string // 推荐层级 (1 表示直接推荐, 2 表示间接推荐, 以此类推)
	CreatedAt      string // 关系创建时间
	DeletedAt      string // 软删除的时间戳
}

// referralRelationshipsColumns holds the columns for the table referral_relationships.
var referralRelationshipsColumns = ReferralRelationshipsColumns{
	Type:           "type",
	RelationshipId: "relationship_id",
	UserId:         "user_id",
	ReferrerId:     "referrer_id",
	Level:          "level",
	CreatedAt:      "created_at",
	DeletedAt:      "deleted_at",
}

// NewReferralRelationshipsDao creates and returns a new DAO object for table data access.
func NewReferralRelationshipsDao(handlers ...gdb.ModelHandler) *ReferralRelationshipsDao {
	return &ReferralRelationshipsDao{
		group:    "default",
		table:    "referral_relationships",
		columns:  referralRelationshipsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ReferralRelationshipsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ReferralRelationshipsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ReferralRelationshipsDao) Columns() ReferralRelationshipsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ReferralRelationshipsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ReferralRelationshipsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ReferralRelationshipsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
