// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// MerchantDepositsDao is the data access object for the table merchant_deposits.
type MerchantDepositsDao struct {
	table    string                  // table is the underlying table name of the DAO.
	group    string                  // group is the database configuration group name of the current DAO.
	columns  MerchantDepositsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler      // handlers for customized model modification.
}

// MerchantDepositsColumns defines and stores column names for the table merchant_deposits.
type MerchantDepositsColumns struct {
	RechargesId          string // 主键ID
	MerchantId           string // 用户ID (Foreign key to users table recommended)
	TokenId              string // 币种ID
	Name                 string // 币种ID
	Chan                 string //
	TokenContractAddress string // 代币合约地址 (for non-native assets)
	FromAddress          string // 来源地址 (发送方地址)
	ToAddress            string // 目标地址 (平台分配的充值地址)
	TxHash               string // 链上交易哈希/ID (Should be unique)
	Error                string // 失败原因
	Amount               string // 充值数量
	State                string // 状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)
	FailureReason        string // 失败原因
	Confirmations        string // 状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)
	CreatedAt            string // 记录创建时间 (e.g., 链上发现时间)
	CompletedAt          string // 完成时间 (状态变为Completed的时间)
	NotificationSent     string // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt   string // 通知发送时间
	UpdatedAt            string // 最后更新时间
}

// merchantDepositsColumns holds the columns for the table merchant_deposits.
var merchantDepositsColumns = MerchantDepositsColumns{
	RechargesId:          "recharges_id",
	MerchantId:           "merchant_id",
	TokenId:              "token_id",
	Name:                 "name",
	Chan:                 "chan",
	TokenContractAddress: "token_contract_address",
	FromAddress:          "from_address",
	ToAddress:            "to_address",
	TxHash:               "tx_hash",
	Error:                "error",
	Amount:               "amount",
	State:                "state",
	FailureReason:        "failure_reason",
	Confirmations:        "confirmations",
	CreatedAt:            "created_at",
	CompletedAt:          "completed_at",
	NotificationSent:     "notification_sent",
	NotificationSentAt:   "notification_sent_at",
	UpdatedAt:            "updated_at",
}

// NewMerchantDepositsDao creates and returns a new DAO object for table data access.
func NewMerchantDepositsDao(handlers ...gdb.ModelHandler) *MerchantDepositsDao {
	return &MerchantDepositsDao{
		group:    "default",
		table:    "merchant_deposits",
		columns:  merchantDepositsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *MerchantDepositsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *MerchantDepositsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *MerchantDepositsDao) Columns() MerchantDepositsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *MerchantDepositsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *MerchantDepositsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *MerchantDepositsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
