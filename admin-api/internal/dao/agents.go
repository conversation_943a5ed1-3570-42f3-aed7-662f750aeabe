// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal" // Original import
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// agentsDao is the data access object for the table agents.
// You can define custom methods on it to extend its functionality as needed.
type agentsDao struct {
	*internal.AgentsDao
}

var (
	// Agents is a globally accessible object for table agents operations.
	Agents = agentsDao{internal.NewAgentsDao()}
)

// Add your custom methods and functionality below.

// GetAgentList 获取代理列表 (带分页、过滤、排序)
func (d *agentsDao) GetAgentList(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.Agents, err error) {
	m := d.Ctx(ctx).Where(condition).WhereNull(d.Columns().DeletedAt)
	err = m.Page(page, pageSize).OrderDesc(d.Columns().AgentId).Scan(&list)
	if err != nil {
		return nil, gerror.Wrap(err, "查询代理列表失败")
	}
	return list, nil
}

// GetAgentCount 获取代理数量 (带过滤)
func (d *agentsDao) GetAgentCount(ctx context.Context, condition g.Map) (total int, err error) {
	m := d.Ctx(ctx).Where(condition).WhereNull(d.Columns().DeletedAt)
	total, err = m.Count()
	if err != nil {
		return 0, gerror.Wrap(err, "查询代理数量失败")
	}
	return total, nil
}

// GetAgentById 根据 ID 获取未删除的代理信息
func (d *agentsDao) GetAgentById(ctx context.Context, agentId int64) (agent *entity.Agents, err error) {
	err = d.Ctx(ctx).Where(d.Columns().AgentId, agentId).WhereNull(d.Columns().DeletedAt).Scan(&agent)
	if err != nil {
		return nil, gerror.Wrapf(err, "查询代理失败, ID: %d", agentId)
	}
	return agent, nil
}

// GetAgentsByIds 根据 ID 列表获取未删除的代理信息
func (d *agentsDao) GetAgentsByIds(ctx context.Context, agentIds []int64) (agents []*entity.Agents, err error) {
	if len(agentIds) == 0 {
		return []*entity.Agents{}, nil // Return empty slice if no IDs provided
	}
	err = d.Ctx(ctx).WhereIn(d.Columns().AgentId, agentIds).WhereNull(d.Columns().DeletedAt).Scan(&agents)
	if err != nil {
		return nil, gerror.Wrapf(err, "根据ID列表查询代理失败, IDs: %v", agentIds)
	}
	return agents, nil
}

// GetAgentByUsername 根据用户名获取未删除的代理信息
func (d *agentsDao) GetAgentByUsername(ctx context.Context, username string) (agent *entity.Agents, err error) {
	err = d.Ctx(ctx).Where(d.Columns().Username, username).WhereNull(d.Columns().DeletedAt).Scan(&agent)
	if err != nil {
		return nil, gerror.Wrapf(err, "查询代理失败, Username: %s", username)
	}
	return agent, nil
}

// GetAgentByEmail 根据邮箱获取未删除的代理信息
func (d *agentsDao) GetAgentByEmail(ctx context.Context, email string) (agent *entity.Agents, err error) {
	err = d.Ctx(ctx).Where(d.Columns().Email, email).WhereNull(d.Columns().DeletedAt).Scan(&agent)
	if err != nil {
		return nil, gerror.Wrapf(err, "查询代理失败, Email: %s", email)
	}
	return agent, nil
}

// // GetAgentByPhoneNumber 根据手机号获取未删除的代理信息
// func (d *agentsDao) GetAgentByPhoneNumber(ctx context.Context, phoneNumber string) (agent *entity.Agents, err error) {
// 	err = d.Ctx(ctx).Where(d.Columns().PhoneNumber, phoneNumber).WhereNull(d.Columns().DeletedAt).Scan(&agent)
// 	if err != nil {
// 		return nil, gerror.Wrapf(err, "查询代理失败, PhoneNumber: %s", phoneNumber)
// 	}
// 	return agent, nil
// }

// UpdateAgent 更新代理信息 (使用 g.Map)
// 注意：此方法不处理密码、层级关系等特殊字段的更新逻辑，仅更新传入 map 中的字段
func (d *agentsDao) UpdateAgent(ctx context.Context, agentId int64, data g.Map) (err error) {
	// 确保更新时间和排除空值
	data[d.Columns().UpdatedAt] = gtime.Now()
	_, err = d.Ctx(ctx).Data(data).Where(d.Columns().AgentId, agentId).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.Wrapf(err, "更新代理信息失败, ID: %d", agentId)
	}
	return nil
}

// SoftDeleteAgentsByIds 批量软删除代理 (传入事务可选)
func (d *agentsDao) SoftDeleteAgentsByIds(ctx context.Context, tx gdb.TX, agentIds []int64) (err error) {
	if len(agentIds) == 0 {
		return nil // No IDs to delete
	}
	// 使用事务或直接执行
	m := d.Ctx(ctx)
	if tx != nil {
		m = m.TX(tx)
	}
	_, err = m.Data(g.Map{d.Columns().DeletedAt: gtime.Now()}).
		WhereIn(d.Columns().AgentId, agentIds).
		WhereNull(d.Columns().DeletedAt). // Ensure we only soft-delete records not already deleted
		Update()
	if err != nil {
		return gerror.Wrapf(err, "批量软删除代理失败, IDs: %v", agentIds)
	}
	return nil
}

// FindDescendantAgentIds 根据父级 relationship 路径查找所有后代代理 ID
// parentRelationshipPath 示例: "/1/2/"
func (d *agentsDao) FindDescendantAgentIds(ctx context.Context, parentRelationshipPath string) (ids []int64, err error) {
	// 使用 LIKE 查询，注意性能问题
	// 确保路径以 / 结尾，避免匹配到非直接后代，例如 /1/2 不会匹配 /1/20/
	likePattern := parentRelationshipPath + "%"

	var descendants []*entity.Agents
	err = d.Ctx(ctx).
		WhereLike(d.Columns().Relationship, likePattern).
		WhereNull(d.Columns().DeletedAt).
		Fields(d.Columns().AgentId). // 只查询 ID 字段
		Scan(&descendants)

	if err != nil {
		return nil, gerror.Wrapf(err, "查找后代代理ID失败, Path: %s", parentRelationshipPath)
	}

	ids = make([]int64, 0, len(descendants))
	for _, desc := range descendants {
		// 排除自身 (如果传入的路径是某个代理的完整路径)
		// 理论上 LIKE parentRelationshipPath + '%' 不会匹配自身，但以防万一
		if desc.Relationship != parentRelationshipPath {
			ids = append(ids, int64(desc.AgentId))
		}
	}

	return ids, nil
}

// AddAgent (插入数据) - Logic 层已通过事务实现，这里可以不提供或提供一个基础版本
// func (d *agentsDao) AddAgent(ctx context.Context, data *do.Agents) (lastInsertId int64, err error) {
// 	result, err := d.Ctx(ctx).Data(data).Insert()
// 	if err != nil {
// 		return 0, gerror.Wrap(err, "插入代理数据失败")
// 	}
// 	lastInsertId, err = result.LastInsertId()
// 	if err != nil {
// 		return 0, gerror.Wrap(err, "获取新代理ID失败")
// 	}
// 	return lastInsertId, nil
// }
