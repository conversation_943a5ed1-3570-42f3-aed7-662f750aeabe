// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"database/sql"
	"math/rand"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"admin-api/internal/utility"

	"github.com/gogf/gf/util/gconv"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
)

// usersDao is the data access object for the table users.
// You can define custom methods on it to extend its functionality as needed.
type usersDao struct {
	*internal.UsersDao
}

var (
	// Users is a globally accessible object for table users operations.
	Users = usersDao{internal.NewUsersDao()}
)

// Add your custom methods and functionality below.

// GetUserList 获取用户列表 (带分页、过滤、排序)
func (d *usersDao) GetUserList(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.Users, total int, err error) {
	m := d.Ctx(ctx).Where(d.Columns().AccountType, 0).Where(condition).WhereNull(d.Columns().DeletedAt)

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询用户总数失败")
	}

	// 获取列表数据
	err = m.Page(page, pageSize).OrderDesc(d.Columns().Id).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询用户列表失败")
	}

	return list, total, nil
}

// GetUserById 根据ID获取用户信息
func (d *usersDao) GetUserById(ctx context.Context, id uint64) (*entity.Users, error) {
	var user *entity.Users
	err := d.Ctx(ctx).Where(d.Columns().Id, id).WhereNull(d.Columns().DeletedAt).Scan(&user)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCode(codes.CodeUserNotFound)
		}
		return nil, gerror.Wrap(err, "查询用户信息失败")
	}
	if user == nil {
		return nil, gerror.NewCode(codes.CodeUserNotFound)
	}
	return user, nil
}

// CheckAccountExists 检查账号是否存在
func (d *usersDao) CheckAccountExists(ctx context.Context, account string, excludeId ...uint64) (bool, error) {
	m := d.Ctx(ctx).Where(d.Columns().Account, account).WhereNull(d.Columns().DeletedAt)
	if len(excludeId) > 0 {
		m = m.WhereNot(d.Columns().Id, excludeId[0])
	}
	count, err := m.Count()
	if err != nil {
		return false, gerror.Wrap(err, "检查账号是否存在失败")
	}
	return count > 0, nil
}

// CheckEmailExists 检查邮箱是否存在
func (d *usersDao) CheckEmailExists(ctx context.Context, email string, excludeId ...uint64) (bool, error) {
	if email == "" {
		return false, nil
	}
	m := d.Ctx(ctx).Where(d.Columns().Email, email).WhereNull(d.Columns().DeletedAt)
	if len(excludeId) > 0 {
		m = m.WhereNot(d.Columns().Id, excludeId[0])
	}
	count, err := m.Count()
	if err != nil {
		return false, gerror.Wrap(err, "检查邮箱是否存在失败")
	}
	return count > 0, nil
}

// CheckPhoneExists 检查手机号是否存在
func (d *usersDao) CheckPhoneExists(ctx context.Context, phone string, excludeId ...uint64) (bool, error) {
	if phone == "" {
		return false, nil
	}
	m := d.Ctx(ctx).Where(d.Columns().Phone, phone).WhereNull(d.Columns().DeletedAt)
	if len(excludeId) > 0 {
		m = m.WhereNot(d.Columns().Id, excludeId[0])
	}
	count, err := m.Count()
	if err != nil {
		return false, gerror.Wrap(err, "检查手机号是否存在失败")
	}
	return count > 0, nil
}

// CheckInviteCodeExists 检查邀请码是否存在
func (d *usersDao) CheckInviteCodeExists(ctx context.Context, inviteCode string, excludeId ...uint64) (bool, error) {
	if inviteCode == "" {
		return false, nil
	}
	m := d.Ctx(ctx).Where(d.Columns().InviteCode, inviteCode).WhereNull(d.Columns().DeletedAt)
	if len(excludeId) > 0 {
		m = m.WhereNot(d.Columns().Id, excludeId[0])
	}
	count, err := m.Count()
	if err != nil {
		return false, gerror.Wrap(err, "检查邀请码是否存在失败")
	}
	return count > 0, nil
}

// Add 添加用户
func (d *usersDao) Add(ctx context.Context, data *do.Users) (uint64, error) {
	res, err := d.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return 0, gerror.Wrap(err, "添加用户失败")
	}
	lastInsertId, err := res.LastInsertId()
	if err != nil {
		return 0, gerror.Wrap(err, "获取新增用户ID失败")
	}
	return uint64(lastInsertId), nil
}

// Update 更新用户信息
func (d *usersDao) Update(ctx context.Context, id uint64, data g.Map) error {
	_, err := d.Ctx(ctx).Data(data).Where(d.Columns().Id, id).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.Wrap(err, "更新用户信息失败")
	}
	return nil
}

// Delete 软删除用户
func (d *usersDao) Delete(ctx context.Context, ids []uint64) error {
	if len(ids) == 0 {
		return nil
	}
	_, err := d.Ctx(ctx).Data(g.Map{
		d.Columns().DeletedAt: gtime.Now(),
	}).WhereIn(d.Columns().Id, ids).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.Wrap(err, "删除用户失败")
	}
	return nil
}

// UpdateStatus 更新用户状态
func (d *usersDao) UpdateStatus(ctx context.Context, id uint64, isStop bool, reason string) error {
	data := g.Map{
		d.Columns().IsStop: isStop,
	}
	if reason != "" {
		data[d.Columns().Reason] = reason
	}
	_, err := d.Ctx(ctx).Data(data).Where(d.Columns().Id, id).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.Wrap(err, "更新用户状态失败")
	}
	return nil
}

// CreateMerchantUser 创建商户类型的用户 (AccountType=2)
func (d *usersDao) CreateMerchantUser(ctx context.Context, account string, hashedPassword string, email string) (userId uint64, err error) {
	// 1. 检查账号是否已存在
	exists, err := d.CheckAccountExists(ctx, account)
	if err != nil {
		return 0, err // 错误已在 CheckAccountExists 中包装
	}
	if exists {
		return 0, gerror.NewCodef(codes.CodeUsernameExists, "用户账号 [%s] 已存在", account)
	}

	// 2. 检查邮箱是否已存在 (如果业务要求邮箱唯一)
	// CheckEmailExists 会在 email 为空时直接返回 false, nil，这里我们要求 email 必须提供
	if email == "" {
		return 0, gerror.NewCode(codes.CodeEmailCannotBeEmpty) // 需要定义这个错误码
	}
	emailExists, err := d.CheckEmailExists(ctx, email)
	if err != nil {
		return 0, err // 错误已在 CheckEmailExists 中包装
	}
	if emailExists {
		return 0, gerror.NewCodef(codes.CodeEmailExists, "用户邮箱 [%s] 已存在", email)
	}

	// 3. 构造 do.Users 对象
	userData := &do.Users{
		Account:     account,
		Password:    hashedPassword,
		Email:       email,
		AccountType: 2, // 商户类型
		IsStop:      0, // 默认活跃状态
		CreatedAt:   gtime.Now(),
		UpdatedAt:   gtime.Now(),
		// 其他字段根据业务需求设置默认值或留空
	}

	// 4. 调用通用的 Add 方法插入数据
	return d.Add(ctx, userData)
}

// ResetPassword 重置用户密码
func (d *usersDao) ResetPassword(ctx context.Context, id uint64, passwordHash string) error {
	_, err := d.Ctx(ctx).Data(g.Map{
		d.Columns().Password: passwordHash,
	}).Where(d.Columns().Id, id).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.Wrap(err, "重置用户密码失败")
	}
	return nil
}

// 设置支付密码为nil
func (d *usersDao) ResetPaymentPassword(ctx context.Context, id uint64) error {
	_, err := d.Ctx(ctx).Data(g.Map{
		d.Columns().PaymentPassword: nil,
	}).Where(d.Columns().Id, id).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.Wrap(err, "重置用户支付密码失败")
	}
	return nil
}

// ResetGoogle2FA 重置用户Google 2FA
func (d *usersDao) ResetGoogle2FA(ctx context.Context, id uint64) error {
	_, err := d.Ctx(ctx).Data(g.Map{
		d.Columns().Google2FaSecret:  nil,
		d.Columns().Google2FaEnabled: 0,
	}).Where(d.Columns().Id, id).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.Wrap(err, "重置用户Google 2FA失败")
	}
	return nil
}

// GetAgentIdsByAgentNameLike 根据代理名称模糊查询代理ID列表
func (d *usersDao) GetAgentIdsByAgentNameLike(ctx context.Context, agentNameLike string) ([]uint64, error) {
	if agentNameLike == "" {
		return []uint64{}, nil
	}
	var agentIds []uint64
	likePattern := "%" + agentNameLike + "%"
	err := d.Ctx(ctx).
		Fields(d.Columns().Id).
		Where(d.Columns().AccountType, 3). // 假设3是代理类型
		WhereNull(d.Columns().DeletedAt).
		Where(d.Columns().Account+" LIKE ?", likePattern).
		Scan(&agentIds)

	if err != nil {
		if err == sql.ErrNoRows {
			return []uint64{}, nil
		}
		return nil, gerror.Wrapf(err, "根据代理名称查询代理ID列表失败: %s", agentNameLike)
	}
	return agentIds, nil
}

//生产唯一account

func (d *usersDao) GenerateUniqueAccount(ctx context.Context, accountType int, retry ...int) (account string, err error) {

	maxRetry := 10

	if len(retry) > 0 {
		if retry[0] > maxRetry {
			return "", gerror.NewCode(codes.CodeTooManyRetries, "生成唯一账户失败，重试次数过多")
		}
	}

	var prefix string

	switch accountType {
	case 1:
		prefix = "U_"
	case 2:
		prefix = "M_"
	case 3:
		prefix = "A_"
	default:
		return "", gerror.NewCode(codes.CodeInvalidParameter, "无效的账户类型")
	}
	account_int := uint64(rand.Intn(*********))
	// 检查是否已存在
	exists, err := d.CheckAccountExists(ctx, prefix+gconv.String(account_int))
	if err != nil {
		return "", err
	}
	if !exists {
		return account, nil
	}
	return d.GenerateUniqueAccount(ctx, accountType, retry[0]+1)

}

// GetUserListWithAgentInfo 获取用户列表（带代理和telegram信息）
func (d *usersDao) GetUserListWithAgentInfo(ctx context.Context, page int, pageSize int, condition g.Map) (list []*v1.UserInfoType, total int, err error) {
	// 使用 UserInfoType 模型来接收结果
	list = make([]*v1.UserInfoType, 0)

	// 构建基础查询，并指定别名以防字段冲突
	m := d.Ctx(ctx).As("u").
		WhereNull("u.deleted_at")

	// 添加代理和telegram表的关联查询
	m = utility.AddAgentAndTelegramJoins(m, "users", "user_backup_accounts")

	// 应用过滤条件
	query := m
	for key, value := range condition {
		// 特殊处理 LIKE 查询 - 添加防御性检查
		if gstr.Contains(key, " LIKE") {
			// 确保 LIKE 值不为空且有意义
			if valueStr, ok := value.(string); ok && valueStr != "" && valueStr != "%%" {
				// 提取字段名（去掉 " LIKE" 部分）
				fieldName := gstr.Replace(key, " LIKE", "")
				query = query.WhereLike(fieldName, valueStr)
			}
		} else if gstr.Contains(key, " BETWEEN ? AND ?") {
			// 处理日期范围查询 (BETWEEN 条件)
			if timeSlice, ok := value.(g.Slice); ok && len(timeSlice) == 2 {
				// 提取字段名 (去掉 " BETWEEN ? AND ?" 部分)
				fieldName := gstr.Replace(key, " BETWEEN ? AND ?", "")
				query = query.WhereBetween(fieldName, timeSlice[0], timeSlice[1])
			}
		} else {
			// 其他精确匹配条件
			query = query.Where(key, value)
		}
	}

	// 克隆查询用于计算总数 (避免影响后续分页查询)
	countQuery := query.Clone()
	total, err = countQuery.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询用户记录总数失败")
	}

	if total == 0 {
		return list, 0, nil // 没有数据，直接返回空列表和0总数
	}

	// 应用分页和排序，并指定查询字段
	// 明确指定所有需要的字段，并使用别名避免冲突
	baseFields := []string{
		"u.id",
		"u.account",
		"u.email",
		"u.area_code",
		"u.phone",
		"u.avatar",
		"u.account_type",
		"u.invite_code",
		"u.recommend_id",
		"u.is_stop",
		"u.google2fa_enabled",
		"u.red_packet_permission",
		"u.transfer_permission",
		"u.withdraw_permission",
		"u.flash_trade_permission",
		"u.recharge_permission",
		"u.receive_permission",
		"u.last_login_time",
		"u.created_at",
		"CASE WHEN u.payment_password IS NOT NULL THEN 1 ELSE 0 END as payment_password_set",
		"recommend_user.account as recommend_account",
	}

	// 添加代理和telegram字段
	agentAndTelegramFields := utility.GetAgentAndTelegramFields()
	allFields := append(baseFields, agentAndTelegramFields...)

	// 转换为interface{}类型
	fieldInterfaces := make([]interface{}, len(allFields))
	for i, field := range allFields {
		fieldInterfaces[i] = field
	}

	// 添加推荐人信息关联
	query = query.LeftJoin("users recommend_user", "u.recommend_id = recommend_user.id")

	err = query.Page(page, pageSize).Order("u.created_at DESC").Fields(fieldInterfaces...).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询用户记录列表失败")
	}

	return list, total, nil
}

// GetUsersByIDsWithAgentInfo 根据用户ID列表获取用户信息（包含代理和telegram信息）
func (d *usersDao) GetUsersByIDsWithAgentInfo(ctx context.Context, ids []uint64) (list []*v1.UserInfoType, err error) {
	// 使用 UserInfoType 模型来接收结果
	list = make([]*v1.UserInfoType, 0)

	if len(ids) == 0 {
		return list, nil
	}

	// 构建基础查询，并指定别名以防字段冲突
	m := d.Ctx(ctx).As("u").
		WhereNull("u.deleted_at").
		WhereIn("u.id", ids)

	// 添加代理和telegram表的关联查询
	m = utility.AddAgentAndTelegramJoins(m, "users", "user_backup_accounts")

	// 指定查询字段
	// 明确指定所有需要的字段，并使用别名避免冲突
	baseFields := []string{
		"u.id",
		"u.account",
		"u.email",
		"u.area_code",
		"u.phone",
		"u.account_type",
		"u.is_stop",
		"u.reason",
		"u.google2fa_enabled",
		"u.red_packet_permission",
		"u.transfer_permission",
		"u.withdraw_permission",
		"u.recharge_permission",
		"u.receive_permission",
		"u.last_login_time",
		"u.created_at",
		"CASE WHEN u.payment_password IS NOT NULL THEN 1 ELSE 0 END as payment_password_set",
		"recommend_user.account as recommend_account",
	}

	// 添加代理和telegram字段
	agentAndTelegramFields := utility.GetAgentAndTelegramFields()
	allFields := append(baseFields, agentAndTelegramFields...)

	// 转换为interface{}类型
	fieldInterfaces := make([]interface{}, len(allFields))
	for i, field := range allFields {
		fieldInterfaces[i] = field
	}

	// 添加推荐人信息关联
	m = m.LeftJoin("users recommend_user", "u.recommend_id = recommend_user.id")

	err = m.Fields(fieldInterfaces...).Scan(&list)
	if err != nil {
		return nil, gerror.Wrap(err, "查询用户记录列表失败")
	}

	return list, nil
}
