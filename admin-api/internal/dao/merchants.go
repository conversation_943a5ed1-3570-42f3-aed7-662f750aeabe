// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"database/sql"

	"admin-api/internal/codes"
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// merchantsDao is the data access object for the table merchants.
// You can define custom methods on it to extend its functionality as needed.
type merchantsDao struct {
	*internal.MerchantsDao
}

var (
	// Merchants is a globally accessible object for table merchants operations.
	Merchants = merchantsDao{internal.NewMerchantsDao()}
)

// Add your custom methods and functionality below.

// GetMerchantList 获取商户列表 (带分页、过滤、排序)
func (d *merchantsDao) GetMerchantList(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.Merchants, total int, err error) {
	m := d.Ctx(ctx).Where(condition).WhereNull(d.Columns().DeletedAt)

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询商户总数失败")
	}

	// 获取列表数据
	err = m.Page(page, pageSize).OrderDesc(d.Columns().MerchantId).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询商户列表失败")
	}

	return list, total, nil
}

// GetMerchantById 根据ID获取商户信息
func (d *merchantsDao) GetMerchantById(ctx context.Context, merchantId uint) (*entity.Merchants, error) {
	var merchant *entity.Merchants
	err := d.Ctx(ctx).Where(d.Columns().MerchantId, merchantId).WhereNull(d.Columns().DeletedAt).Scan(&merchant)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCode(codes.CodeMerchantNotFound)
		}
		return nil, gerror.Wrap(err, "查询商户信息失败")
	}
	if merchant == nil {
		return nil, gerror.NewCode(codes.CodeMerchantNotFound)
	}
	return merchant, nil
}

// GetMerchantByEmail 根据邮箱获取商户信息
func (d *merchantsDao) GetMerchantByEmail(ctx context.Context, email string) (*entity.Merchants, error) {
	var merchant *entity.Merchants
	err := d.Ctx(ctx).Where(d.Columns().Email, email).WhereNull(d.Columns().DeletedAt).Scan(&merchant)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, gerror.Wrap(err, "查询商户信息失败")
	}
	return merchant, nil
}

// GetMerchantByName 根据商户名称获取商户信息
func (d *merchantsDao) GetMerchantByName(ctx context.Context, merchantName string) (*entity.Merchants, error) {
	var merchant *entity.Merchants
	err := d.Ctx(ctx).Where(d.Columns().MerchantName, merchantName).WhereNull(d.Columns().DeletedAt).Scan(&merchant)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, gerror.Wrap(err, "查询商户信息失败")
	}
	return merchant, nil
}

// CheckMerchantNameExists 检查商户名称是否存在
func (d *merchantsDao) CheckMerchantNameExists(ctx context.Context, merchantName string, excludeMerchantId ...uint) (bool, error) {
	m := d.Ctx(ctx).Where(d.Columns().MerchantName, merchantName).WhereNull(d.Columns().DeletedAt)
	if len(excludeMerchantId) > 0 {
		m = m.WhereNot(d.Columns().MerchantId, excludeMerchantId[0])
	}
	count, err := m.Count()
	if err != nil {
		return false, gerror.Wrap(err, "检查商户名称是否存在失败")
	}
	return count > 0, nil
}

// Add 添加商户
func (d *merchantsDao) Add(ctx context.Context, data *do.Merchants) (uint, error) {
	res, err := d.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return 0, gerror.Wrap(err, "添加商户失败")
	}
	lastInsertId, err := res.LastInsertId()
	if err != nil {
		return 0, gerror.Wrap(err, "获取新增商户ID失败")
	}
	return uint(lastInsertId), nil
}

// Update 更新商户信息
func (d *merchantsDao) Update(ctx context.Context, merchantId uint, data g.Map) error {
	_, err := d.Ctx(ctx).Data(data).Where(d.Columns().MerchantId, merchantId).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.Wrap(err, "更新商户信息失败")
	}
	return nil
}

// Delete 软删除商户
func (d *merchantsDao) Delete(ctx context.Context, merchantIds []uint) error {
	if len(merchantIds) == 0 {
		return nil
	}
	_, err := d.Ctx(ctx).Data(g.Map{
		d.Columns().DeletedAt: gtime.Now(),
	}).WhereIn(d.Columns().MerchantId, merchantIds).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.Wrap(err, "删除商户失败")
	}
	return nil
}

// UpdateStatus 更新商户状态
func (d *merchantsDao) UpdateStatus(ctx context.Context, merchantId uint, status int) error {
	_, err := d.Ctx(ctx).Data(g.Map{
		d.Columns().Status:    status,
		d.Columns().UpdatedAt: gtime.Now(),
	}).Where(d.Columns().MerchantId, merchantId).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.Wrap(err, "更新商户状态失败")
	}
	return nil
}

// CheckEmailExists 检查邮箱是否存在
func (d *merchantsDao) CheckEmailExists(ctx context.Context, email string, excludeMerchantId ...uint) (bool, error) {
	m := d.Ctx(ctx).Where(d.Columns().Email, email).WhereNull(d.Columns().DeletedAt)
	if len(excludeMerchantId) > 0 {
		m = m.WhereNot(d.Columns().MerchantId, excludeMerchantId[0])
	}
	count, err := m.Count()
	if err != nil {
		return false, gerror.Wrap(err, "检查邮箱是否存在失败")
	}
	return count > 0, nil
}
