// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
	"admin-api/internal/model"
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
)

// redPacketImagesDao is the data access object for the table red_packet_images.
// You can define custom methods on it to extend its functionality as needed.
type redPacketImagesDao struct {
	*internal.RedPacketImagesDao
}

var (
	// RedPacketImages is a globally accessible object for table red_packet_images operations.
	RedPacketImages = redPacketImagesDao{internal.NewRedPacketImagesDao()}
)

// Add your custom methods and functionality below.

// ListAdminRedPacketImagesWithFullInfo 查询后台红包封面图片列表（含完整信息）
func (d *redPacketImagesDao) ListAdminRedPacketImagesWithFullInfo(ctx context.Context, page int, pageSize int, condition g.Map) (list []*model.RedPacketImageAdminInfo, total int, err error) {
	// 初始化返回列表
	list = make([]*model.RedPacketImageAdminInfo, 0)

	// 构建基础查询，使用别名避免字段冲突
	m := d.Ctx(ctx).As("rpi").
		LeftJoin("users u", "rpi.user_id = u.id")

	// 添加用户的代理和telegram表的关联查询
	m = m.LeftJoin("agents u_first_agent", "u.first_id = u_first_agent.agent_id")
	m = m.LeftJoin("agents u_second_agent", "u.second_id = u_second_agent.agent_id")
	m = m.LeftJoin("agents u_third_agent", "u.third_id = u_third_agent.agent_id")
	m = m.LeftJoin("user_backup_accounts u_uba", "u.id = u_uba.user_id AND u_uba.is_master = 1")

	// 添加软删除条件
	m = m.WhereNull("rpi.deleted_at")

	// 处理LEFT JOIN表的软删除 - 需要检查NULL或未删除
	// 用户必须存在且未删除
	m = m.WhereNull("u.deleted_at")
	// 代理表可能不存在记录，需要特殊处理
	m = m.Where("(u_first_agent.agent_id IS NULL OR u_first_agent.deleted_at IS NULL)")
	m = m.Where("(u_second_agent.agent_id IS NULL OR u_second_agent.deleted_at IS NULL)")
	m = m.Where("(u_third_agent.agent_id IS NULL OR u_third_agent.deleted_at IS NULL)")
	// 备份账户表可能不存在记录，需要特殊处理
	m = m.Where("(u_uba.user_id IS NULL OR u_uba.deleted_at IS NULL)")

	// 处理查询条件
	query := m
	var betweenConditions = make(map[string]g.Slice)
	var likeConditions = make(map[string]string)
	var exactConditions = make(map[string]interface{})

	// 将条件按类型分类
	for key, value := range condition {
		if gstr.Contains(key, " BETWEEN ? AND ?") {
			fieldName := gstr.Replace(key, " BETWEEN ? AND ?", "")
			if timeSlice, ok := value.(g.Slice); ok && len(timeSlice) == 2 {
				betweenConditions[fieldName] = timeSlice
			}
		} else if gstr.Contains(key, " LIKE ?") || gstr.Contains(key, " LIKE") {
			fieldName := gstr.Replace(key, " LIKE ?", "")
			fieldName = gstr.Replace(fieldName, " LIKE", "")
			if valueStr, ok := value.(string); ok {
				likeConditions[fieldName] = valueStr
			}
		} else if gstr.Contains(key, " >=") {
			query = query.Where(key, value)
		} else if gstr.Contains(key, " <=") {
			query = query.Where(key, value)
		} else {
			exactConditions[key] = value
		}
	}

	// 按顺序应用条件
	for key, value := range exactConditions {
		query = query.Where(key, value)
	}
	for fieldName, pattern := range likeConditions {
		query = query.WhereLike(fieldName, pattern)
	}
	for fieldName, timeSlice := range betweenConditions {
		query = query.WhereBetween(fieldName, timeSlice[0], timeSlice[1])
	}

	// 克隆查询用于计算总数
	countQuery := query.Clone()
	total, err = countQuery.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询红包封面图片总数失败")
	}

	if total == 0 {
		return list, 0, nil
	}

	// 定义查询字段
	baseFields := []string{
		"rpi.red_packet_images_id as id",
		"rpi.user_id",
		"u.account as username",
		"rpi.images_url as image_url",
		"rpi.status",
		"rpi.refuse_reason_zh",
		"rpi.refuse_reason_en",
		"rpi.created_at",
		"rpi.updated_at",
	}

	// 用户代理和telegram字段
	userAgentAndTelegramFields := []string{
		"u_first_agent.username as first_agent_name",
		"u_second_agent.username as second_agent_name",
		"u_third_agent.username as third_agent_name",
		"u_uba.telegram_id",
		"u_uba.telegram_username",
		"u_uba.first_name",
	}

	allFields := append(baseFields, userAgentAndTelegramFields...)

	// 转换为interface{}类型
	fieldInterfaces := make([]interface{}, len(allFields))
	for i, field := range allFields {
		fieldInterfaces[i] = field
	}

	err = query.Page(page, pageSize).Order("rpi.created_at DESC").Fields(fieldInterfaces...).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询红包封面图片列表失败")
	}

	return list, total, nil
}
