// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"crypto/rand"
	"database/sql"
	"encoding/hex"

	"admin-api/internal/codes"
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// userBackupAccountsDao is the data access object for the table user_backup_accounts.
// You can define custom methods on it to extend its functionality as needed.
type userBackupAccountsDao struct {
	*internal.UserBackupAccountsDao
}

var (
	// UserBackupAccounts is a globally accessible object for table user_backup_accounts operations.
	UserBackupAccounts = userBackupAccountsDao{internal.NewUserBackupAccountsDao()}
)

// Add your custom methods and functionality below.

// GetByUserId 根据用户ID获取备用账户列表
func (d *userBackupAccountsDao) GetByUserId(ctx context.Context, userId uint64, telegramUsername string) ([]*entity.UserBackupAccounts, error) {
	m := d.Ctx(ctx).Where(d.Columns().UserId, userId).WhereNull(d.Columns().DeletedAt)

	// 如果指定了电报用户名，则添加条件
	if telegramUsername != "" {
		m = m.Where(d.Columns().TelegramUsername, telegramUsername)
	}

	var list []*entity.UserBackupAccounts
	err := m.OrderDesc(d.Columns().BackupAccountId).Scan(&list)
	if err != nil {
		return nil, gerror.Wrap(err, "获取用户备用账户列表失败")
	}

	return list, nil
}

// GetById 根据ID获取备用账户
func (d *userBackupAccountsDao) GetById(ctx context.Context, backupAccountId uint64) (*entity.UserBackupAccounts, error) {
	var account *entity.UserBackupAccounts
	err := d.Ctx(ctx).Where(d.Columns().BackupAccountId, backupAccountId).WhereNull(d.Columns().DeletedAt).Scan(&account)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCode(codes.CodeBackupAccountNotFound)
		}
		return nil, gerror.Wrap(err, "获取备用账户失败")
	}
	if account == nil {
		return nil, gerror.NewCode(codes.CodeBackupAccountNotFound)
	}
	return account, nil
}

// CheckExists 检查备用账户是否存在
func (d *userBackupAccountsDao) CheckExists(ctx context.Context, userId uint64, telegramUsername string) (bool, error) {
	count, err := d.Ctx(ctx).Where(d.Columns().UserId, userId).
		Where(d.Columns().TelegramUsername, telegramUsername).
		WhereNull(d.Columns().DeletedAt).
		Count()
	if err != nil {
		return false, gerror.Wrap(err, "检查备用账户是否存在失败")
	}
	return count > 0, nil
}

// Add 添加备用账户
func (d *userBackupAccountsDao) Add(ctx context.Context, data *do.UserBackupAccounts) (uint64, error) {
	// 设置验证时间为nil，表示尚未验证
	data.VerifiedAt = nil

	res, err := d.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return 0, gerror.Wrap(err, "添加备用账户失败")
	}

	lastInsertId, err := res.LastInsertId()
	if err != nil {
		return 0, gerror.Wrap(err, "获取新增备用账户ID失败")
	}

	return uint64(lastInsertId), nil
}

// Verify 验证备用账户
func (d *userBackupAccountsDao) Verify(ctx context.Context, backupAccountId uint64) error {
	// 获取备用账户
	_, err := d.GetById(ctx, backupAccountId)
	if err != nil {
		return err
	}

	// 更新验证状态
	_, err = d.Ctx(ctx).Data(g.Map{
		d.Columns().VerifiedAt: gtime.Now(),
	}).Where(d.Columns().BackupAccountId, backupAccountId).Update()
	if err != nil {
		return gerror.Wrap(err, "更新验证状态失败")
	}

	return nil
}

// Delete 删除备用账户
func (d *userBackupAccountsDao) Delete(ctx context.Context, backupAccountId uint64) error {
	_, err := d.Ctx(ctx).Data(g.Map{
		d.Columns().DeletedAt: gtime.Now(),
	}).Where(d.Columns().BackupAccountId, backupAccountId).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.Wrap(err, "删除备用账户失败")
	}
	return nil
}

// RefreshVerificationToken 刷新验证令牌
func (d *userBackupAccountsDao) RefreshVerificationToken(ctx context.Context, backupAccountId uint64) (string, error) {
	// 生成新的验证令牌
	verificationToken, err := d.generateVerificationToken()
	if err != nil {
		return "", err
	}

	// 由于数据库结构不支持存储验证令牌，这里只返回生成的令牌
	// 在实际应用中，可能需要通过其他方式存储或者传递令牌
	return verificationToken, nil
}

// generateVerificationToken 生成验证令牌
func (d *userBackupAccountsDao) generateVerificationToken() (string, error) {
	// 生成随机的验证令牌 (32字节)
	tokenBytes := make([]byte, 32)
	_, err := rand.Read(tokenBytes)
	if err != nil {
		return "", gerror.Wrap(err, "生成验证令牌失败")
	}
	return hex.EncodeToString(tokenBytes), nil
}
