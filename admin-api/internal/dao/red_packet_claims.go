// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
	"admin-api/internal/model"
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
)

// redPacketClaimsDao is the data access object for the table red_packet_claims.
// You can define custom methods on it to extend its functionality as needed.
type redPacketClaimsDao struct {
	*internal.RedPacketClaimsDao
}

var (
	// RedPacketClaims is a globally accessible object for table red_packet_claims operations.
	RedPacketClaims = redPacketClaimsDao{internal.NewRedPacketClaimsDao()}
)

// Add your custom methods and functionality below.

// ListAdminRedPacketClaimsWithFullInfo 查询后台红包领取记录列表（含完整信息）
func (d *redPacketClaimsDao) ListAdminRedPacketClaimsWithFullInfo(ctx context.Context, page int, pageSize int, condition g.Map) (list []*model.RedPacketClaimAdminInfo, total int, err error) {
	// 初始化返回列表
	list = make([]*model.RedPacketClaimAdminInfo, 0)

	// 构建基础查询，使用别名避免字段冲突
	m := d.Ctx(ctx).As("rpc").
		LeftJoin("users claimer", "rpc.claimer_user_id = claimer.id").
		LeftJoin("red_packets rp", "rpc.red_packet_id = rp.red_packet_id").
		LeftJoin("users sender", "rp.sender_user_id = sender.id").
		LeftJoin("tokens token", "rp.token_id = token.token_id")

	// 添加领取者的代理和telegram表的关联查询
	m = m.LeftJoin("agents claimer_first_agent", "claimer.first_id = claimer_first_agent.agent_id")
	m = m.LeftJoin("agents claimer_second_agent", "claimer.second_id = claimer_second_agent.agent_id")
	m = m.LeftJoin("agents claimer_third_agent", "claimer.third_id = claimer_third_agent.agent_id")
	m = m.LeftJoin("user_backup_accounts claimer_uba", "claimer.id = claimer_uba.user_id AND claimer_uba.is_master = 1")

	// 添加发送方的代理和telegram表的关联查询
	m = m.LeftJoin("agents sender_first_agent", "sender.first_id = sender_first_agent.agent_id")
	m = m.LeftJoin("agents sender_second_agent", "sender.second_id = sender_second_agent.agent_id")
	m = m.LeftJoin("agents sender_third_agent", "sender.third_id = sender_third_agent.agent_id")
	m = m.LeftJoin("user_backup_accounts sender_uba", "sender.id = sender_uba.user_id AND sender_uba.is_master = 1")

	// 添加软删除条件
	m = m.WhereNull("rpc.deleted_at")

	// 处理LEFT JOIN表的软删除 - 需要检查NULL或未删除
	// 领取者用户、发送方用户和红包必须存在且未删除
	m = m.WhereNull("claimer.deleted_at")
	m = m.WhereNull("sender.deleted_at")
	m = m.WhereNull("rp.deleted_at")
	// 代币表通常都有记录，但为了安全也处理
	m = m.Where("(token.token_id IS NULL OR token.deleted_at IS NULL)")
	// 领取者代理表可能不存在记录，需要特殊处理
	m = m.Where("(claimer_first_agent.agent_id IS NULL OR claimer_first_agent.deleted_at IS NULL)")
	m = m.Where("(claimer_second_agent.agent_id IS NULL OR claimer_second_agent.deleted_at IS NULL)")
	m = m.Where("(claimer_third_agent.agent_id IS NULL OR claimer_third_agent.deleted_at IS NULL)")
	// 领取者备份账户表可能不存在记录，需要特殊处理
	m = m.Where("(claimer_uba.user_id IS NULL OR claimer_uba.deleted_at IS NULL)")
	// 发送方代理表可能不存在记录，需要特殊处理
	m = m.Where("(sender_first_agent.agent_id IS NULL OR sender_first_agent.deleted_at IS NULL)")
	m = m.Where("(sender_second_agent.agent_id IS NULL OR sender_second_agent.deleted_at IS NULL)")
	m = m.Where("(sender_third_agent.agent_id IS NULL OR sender_third_agent.deleted_at IS NULL)")
	// 发送方备份账户表可能不存在记录，需要特殊处理
	m = m.Where("(sender_uba.user_id IS NULL OR sender_uba.deleted_at IS NULL)")

	// 处理查询条件
	query := m
	var betweenConditions = make(map[string]g.Slice)
	var likeConditions = make(map[string]string)
	var exactConditions = make(map[string]interface{})

	// 将条件按类型分类
	for key, value := range condition {
		if gstr.Contains(key, " BETWEEN ? AND ?") {
			fieldName := gstr.Replace(key, " BETWEEN ? AND ?", "")
			if timeSlice, ok := value.(g.Slice); ok && len(timeSlice) == 2 {
				betweenConditions[fieldName] = timeSlice
			}
		} else if gstr.Contains(key, " LIKE ?") || gstr.Contains(key, " LIKE") {
			fieldName := gstr.Replace(key, " LIKE ?", "")
			fieldName = gstr.Replace(fieldName, " LIKE", "")
			if valueStr, ok := value.(string); ok {
				likeConditions[fieldName] = valueStr
			}
		} else if gstr.Contains(key, " >=") {
			query = query.Where(key, value)
		} else if gstr.Contains(key, " <=") {
			query = query.Where(key, value)
		} else {
			exactConditions[key] = value
		}
	}

	// 按顺序应用条件
	for key, value := range exactConditions {
		query = query.Where(key, value)
	}
	for fieldName, pattern := range likeConditions {
		query = query.WhereLike(fieldName, pattern)
	}
	for fieldName, timeSlice := range betweenConditions {
		query = query.WhereBetween(fieldName, timeSlice[0], timeSlice[1])
	}

	// 克隆查询用于计算总数
	countQuery := query.Clone()
	total, err = countQuery.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询红包领取记录总数失败")
	}

	if total == 0 {
		return list, 0, nil
	}

	// 定义查询字段
	baseFields := []string{
		"rpc.claim_id",
		"rpc.red_packet_id",
		"rpc.claimer_user_id",
		"claimer.account as claimer_username",
		"rpc.amount as claimed_amount",
		"rpc.transaction_id",
		"rp.message_id",
		"rpc.claimed_at as created_at",
		"rp.memo as red_packet_memo",
		"rp.type as red_packet_type",
		"rp.token_id",
		"COALESCE(token.symbol, rp.symbol) as token_symbol",
		"token.name as token_name",
		"rp.sender_user_id",
		"sender.account as sender_username",
		// 添加缺失的领取记录表字段
		"rpc.status",
		"rpc.symbol",
		"rpc.sender_user_id as claim_sender_user_id",
		"rpc.receiver_user_id as claim_receiver_user_id",
		"rpc.sender_username as claim_sender_username",
		"rpc.receiver_username as claim_receiver_username",
		// 添加红包UUID字段
		"rp.uuid as red_packet_uuid",
	}

	// 领取者代理和telegram字段
	claimerAgentAndTelegramFields := []string{
		"claimer_first_agent.username as first_agent_name",
		"claimer_second_agent.username as second_agent_name",
		"claimer_third_agent.username as third_agent_name",
		"claimer_uba.telegram_id",
		"claimer_uba.telegram_username",
		"claimer_uba.first_name",
	}

	// 发送方代理和telegram字段
	senderAgentAndTelegramFields := []string{
		"sender_first_agent.username as sender_first_agent_name",
		"sender_second_agent.username as sender_second_agent_name",
		"sender_third_agent.username as sender_third_agent_name",
		"sender_uba.telegram_id as sender_telegram_id",
		"sender_uba.telegram_username as sender_telegram_username",
		"sender_uba.first_name as sender_first_name",
	}

	allFields := append(baseFields, claimerAgentAndTelegramFields...)
	allFields = append(allFields, senderAgentAndTelegramFields...)

	// 转换为interface{}类型
	fieldInterfaces := make([]interface{}, len(allFields))
	for i, field := range allFields {
		fieldInterfaces[i] = field
	}

	err = query.Page(page, pageSize).Order("rpc.claimed_at DESC").Fields(fieldInterfaces...).Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "查询红包领取记录列表失败")
	}

	return list, total, nil
}
