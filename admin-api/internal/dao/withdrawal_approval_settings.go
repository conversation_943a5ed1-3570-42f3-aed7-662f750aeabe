// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// withdrawalApprovalSettingsDao is the data access object for the table withdrawal_approval_settings.
// You can define custom methods on it to extend its functionality as needed.
type withdrawalApprovalSettingsDao struct {
	*internal.WithdrawalApprovalSettingsDao
}

var (
	// WithdrawalApprovalSettings is a globally accessible object for table withdrawal_approval_settings operations.
	WithdrawalApprovalSettings = withdrawalApprovalSettingsDao{internal.NewWithdrawalApprovalSettingsDao()}
)

// Add your custom methods and functionality below.
