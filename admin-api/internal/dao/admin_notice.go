// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/codes"
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"context"
	"database/sql"
	"fmt" // 添加 fmt 包导入

	// "github.com/gogf/gf/v2/database/gdb" // 移除未使用的 gdb 导入
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// adminNoticeDao is the data access object for the table admin_notice.
// You can define custom methods on it to extend its functionality as needed.
type adminNoticeDao struct {
	*internal.AdminNoticeDao
}

var (
	// AdminNotice is a globally accessible object for table admin_notice operations.
	AdminNotice = adminNoticeDao{internal.NewAdminNoticeDao()}
)

// GetList 获取公告列表（分页）
func (d *adminNoticeDao) GetList(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.AdminNotice, total int, err error) {
	m := d.Ctx(ctx).Where(d.Columns().DeletedAt, nil) // 默认只查询未删除的

	// 构建查询条件
	if len(condition) > 0 {
		m = m.Where(condition)
	}

	// 查询总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询公告总数失败")
	}
	if total == 0 {
		return []*entity.AdminNotice{}, 0, nil
	}

	// 查询列表数据
	// 按 Sort 升序，然后按 ID 降序 (最新发布的靠前)
	err = m.Page(page, pageSize).OrderAsc(d.Columns().Sort).OrderDesc(d.Columns().Id).Scan(&list)
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询公告列表失败")
	}

	return list, total, nil
}

// GetById 根据ID获取公告信息 (包括已删除的，如果需要)
func (d *adminNoticeDao) GetById(ctx context.Context, id int64, includeDeleted ...bool) (*entity.AdminNotice, error) {
	var notice *entity.AdminNotice
	m := d.Ctx(ctx)
	if len(includeDeleted) == 0 || !includeDeleted[0] {
		m = m.Where(d.Columns().DeletedAt, nil)
	}
	err := m.Where(d.Columns().Id, id).Scan(&notice)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, gerror.NewCode(codes.CodeNoticeNotFound)
		}
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "数据库查询失败")
	}
	if notice == nil {
		return nil, gerror.NewCode(codes.CodeNoticeNotFound)
	}
	return notice, nil
}

// Add 新增公告
func (d *adminNoticeDao) Add(ctx context.Context, data *do.AdminNotice) (lastInsertId int64, err error) {
	res, err := d.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "新增公告失败")
	}
	lastInsertId, err = res.LastInsertId()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "获取新增公告ID失败")
	}
	return lastInsertId, nil
}

// Update 更新公告信息
func (d *adminNoticeDao) Update(ctx context.Context, data g.Map, id int64) (err error) {
	// 确保不更新创建者和创建时间等字段
	delete(data, d.Columns().CreatedBy)
	delete(data, d.Columns().CreatedAt)
	delete(data, d.Columns().DeletedAt)

	if len(data) == 0 {
		return nil // 没有需要更新的字段
	}

	_, err = d.Ctx(ctx).Data(data).Where(d.Columns().Id, id).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.WrapCode(codes.CodeInternalError, err, "更新公告信息失败")
	}
	return nil
}

// SoftDeleteByIds 软删除公告
func (d *adminNoticeDao) SoftDeleteByIds(ctx context.Context, ids []int64, userId int64) (err error) {
	if len(ids) == 0 {
		return nil
	}
	_, err = d.Ctx(ctx).Data(g.Map{
		d.Columns().DeletedAt: gtime.Now(),
		d.Columns().UpdatedBy: userId, // 记录删除操作者
	}).WhereIn(d.Columns().Id, ids).WhereNull(d.Columns().DeletedAt).Update()
	if err != nil {
		return gerror.WrapCode(codes.CodeInternalError, err, "删除公告失败")
	}
	return nil
}

// GetUserNotices 获取用户应接收的公告列表 (包含通知和私信)
func (d *adminNoticeDao) GetUserNotices(ctx context.Context, page, pageSize int, userId int64, condition g.Map) (list []*entity.AdminNotice, total int, err error) {
	m := d.Ctx(ctx).Where(d.Columns().DeletedAt, nil).Where(d.Columns().Status, 1) // 只查已发布且未删除的

	// 构建基础查询条件
	if len(condition) > 0 {
		m = m.Where(condition)
	}

	// 构建接收者条件：(类型=通知) OR (类型=私信 AND JSON_CONTAINS(receiver, '[userId]'))
	// 注意：JSON_CONTAINS 的语法可能因数据库而异，这里是 MySQL 的示例
	// 为了简化和提高性能，可以考虑将私信接收者单独存储或优化查询方式
	receiverCondition := fmt.Sprintf(`(%s = 1 OR (%s = 2 AND JSON_CONTAINS(%s, '%d')))`,
		d.Columns().Type, d.Columns().Type, d.Columns().Receiver, userId)
	m = m.Where(receiverCondition)

	// 查询总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询用户公告总数失败")
	}
	if total == 0 {
		return []*entity.AdminNotice{}, 0, nil
	}

	// 查询列表数据
	err = m.Page(page, pageSize).OrderAsc(d.Columns().Sort).OrderDesc(d.Columns().Id).Scan(&list)
	if err != nil {
		return nil, 0, gerror.WrapCode(codes.CodeInternalError, err, "查询用户公告列表失败")
	}

	return list, total, nil
}

// GetUserUnreadNoticeCount 获取用户未读公告数量
// 实现逻辑：查询用户应接收的公告总数 - 查询用户已读公告数
// 注意：此方法可能需要优化以提高性能，例如使用 JOIN 或子查询
func (d *adminNoticeDao) GetUserUnreadNoticeCount(ctx context.Context, userId int64) (count int, err error) {
	// 1. 查询用户应接收的已发布公告总数
	receiverCondition := fmt.Sprintf(`(%s = 1 OR (%s = 2 AND JSON_CONTAINS(%s, '%d')))`,
		d.Columns().Type, d.Columns().Type, d.Columns().Receiver, userId)
	totalNotice, err := d.Ctx(ctx).
		Where(d.Columns().DeletedAt, nil).
		Where(d.Columns().Status, 1). // 已发布
		Where(receiverCondition).
		Count()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "查询用户应接收公告总数失败")
	}
	if totalNotice == 0 {
		return 0, nil
	}

	// 2. 查询用户已读的公告数量 (需要连接 admin_notice_read 表)
	//    这里需要注意，只计算用户应接收的公告中的已读数量
	//    修正 LeftJoin 参数类型
	readCount, err := d.Ctx(ctx).As("n").
		LeftJoin(AdminNoticeRead.Table()+" nr", fmt.Sprintf("n.%s = nr.%s AND nr.%s = %d", d.Columns().Id, AdminNoticeRead.Columns().NoticeId, AdminNoticeRead.Columns().MemberId, userId)).
		Where(fmt.Sprintf("n.%s", d.Columns().DeletedAt), nil).
		Where(fmt.Sprintf("n.%s", d.Columns().Status), 1).
		Where(receiverCondition).
		WhereNotNull(fmt.Sprintf("nr.%s", AdminNoticeRead.Columns().Id)). // 只计算在 read 表中有记录的
		Count()

	if err != nil {
		return 0, gerror.WrapCode(codes.CodeInternalError, err, "查询用户已读公告数量失败")
	}

	count = totalNotice - readCount
	if count < 0 { // 理论上不应该发生，但做个保护
		count = 0
	}

	return count, nil
}
