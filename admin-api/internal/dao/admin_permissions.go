// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	v1 "admin-api/api/system/v1" // 导入 v1 包
	"admin-api/internal/codes"
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/entity" // 导入 entity 包
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// adminPermissionsDao is the data access object for the table admin_permissions.
// You can define custom methods on it to extend its functionality as needed.
type adminPermissionsDao struct {
	*internal.AdminPermissionsDao
}

var (
	// AdminPermissions is a globally accessible object for table admin_permissions operations.
	AdminPermissions = adminPermissionsDao{internal.NewAdminPermissionsDao()}
)

// GetAllPermissions retrieves all permissions based on the given condition.
func (d *adminPermissionsDao) GetAllPermissions(ctx context.Context, condition g.Map) ([]*entity.AdminPermissions, error) {
	var permissions []*entity.AdminPermissions
	query := d.Ctx(ctx)
	if len(condition) > 0 {
		query = query.Where(condition)
	}
	// Order by Sort ascending, then by Id ascending to ensure stable sorting
	err := query.OrderAsc(d.Columns().Sort).OrderAsc(d.Columns().Id).Scan(&permissions)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取所有权限列表失败")
	}
	return permissions, nil
}

// GetAllPermissionsTree retrieves all enabled permissions and builds a tree structure.
// It mirrors the logic of dao.AdminMenu.GetAllMenusTree.
func (d *adminPermissionsDao) GetAllPermissionsTree(ctx context.Context, condition g.Map) (tree []*v1.PermissionTreeNode, err error) {
	permissions, err := d.GetAllPermissions(ctx, condition)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取所有权限列表失败") // Keep error wrapping consistent
	}
	// Build the permission tree structure
	tree = buildPermissionTree(permissions)
	return tree, nil
}

// buildPermissionTree constructs a tree structure from a flat list of permissions.
func buildPermissionTree(permissions []*entity.AdminPermissions) []*v1.PermissionTreeNode {
	if len(permissions) == 0 {
		return []*v1.PermissionTreeNode{}
	}

	// Create a map for quick node lookup using the permission Key
	permissionMap := make(map[string]*v1.PermissionTreeNode)
	for _, p := range permissions {
		permissionMap[p.Key] = &v1.PermissionTreeNode{
			AdminPermissions: p,
			Children:         make([]*v1.PermissionTreeNode, 0),
		}
	}

	// Build the tree structure
	var rootNodes []*v1.PermissionTreeNode
	for _, p := range permissions {
		node := permissionMap[p.Key] // Get the node from the map using its Key
		if p.ParentKey != "" {       // Check if it's not a root node by ParentKey
			// Add the current node to its parent's children list using ParentKey
			if parent, exists := permissionMap[p.ParentKey]; exists {
				parent.Children = append(parent.Children, node)
			}
			// Consider logging or handling cases where the parent doesn't exist if necessary
		} else {
			// If it's a root node (ParentKey == ""), add it to the root nodes list
			rootNodes = append(rootNodes, node)
		}
	}

	return rootNodes
}

// Add your custom methods and functionality below.
