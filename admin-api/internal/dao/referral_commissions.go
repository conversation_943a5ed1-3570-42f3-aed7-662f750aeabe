// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// referralCommissionsDao is the data access object for the table referral_commissions.
// You can define custom methods on it to extend its functionality as needed.
type referralCommissionsDao struct {
	*internal.ReferralCommissionsDao
}

var (
	// ReferralCommissions is a globally accessible object for table referral_commissions operations.
	ReferralCommissions = referralCommissionsDao{internal.NewReferralCommissionsDao()}
)

// Add your custom methods and functionality below.

// GetReferralCommissionList 获取佣金记录列表
func (d *referralCommissionsDao) GetReferralCommissionList(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.ReferralCommissions, total int, err error) {
	// 构建查询对象
	m := d.Ctx(ctx).Where(condition)

	// 查询总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 无数据直接返回
	if total == 0 {
		return nil, 0, nil
	}

	// 查询数据
	list = make([]*entity.ReferralCommissions, 0)
	err = m.Page(page, pageSize).Order("commission_id DESC").Scan(&list)
	return list, total, err
}

// GetReferralCommissionListWithUserInfo 获取佣金记录列表，并关联用户和代币信息
func (d *referralCommissionsDao) GetReferralCommissionListWithUserInfo(ctx context.Context, page, pageSize int, condition g.Map) (list []map[string]interface{}, total int, err error) {
	// 构建基础查询，包含必要的 JOIN
	m := d.Ctx(ctx).
		LeftJoin("users referrer", "referral_commissions.referrer_id = referrer.id").
		LeftJoin("users invitee", "referral_commissions.invitee_id = invitee.id").
		LeftJoin("tokens t", "referral_commissions.token_id = t.token_id").
		// 推荐人代理层级信息
		LeftJoin("agents referrer_first_agent", "referrer.first_id = referrer_first_agent.agent_id").
		LeftJoin("agents referrer_second_agent", "referrer.second_id = referrer_second_agent.agent_id").
		LeftJoin("agents referrer_third_agent", "referrer.third_id = referrer_third_agent.agent_id").
		// 推荐人Telegram信息
		LeftJoin("user_backup_accounts referrer_uba", "referrer.id = referrer_uba.user_id AND referrer_uba.is_master = 1").
		// 被推荐人代理层级信息
		LeftJoin("agents invitee_first_agent", "invitee.first_id = invitee_first_agent.agent_id").
		LeftJoin("agents invitee_second_agent", "invitee.second_id = invitee_second_agent.agent_id").
		LeftJoin("agents invitee_third_agent", "invitee.third_id = invitee_third_agent.agent_id").
		// 被推荐人Telegram信息
		LeftJoin("user_backup_accounts invitee_uba", "invitee.id = invitee_uba.user_id AND invitee_uba.is_master = 1").
		Where(condition)

	// 查询总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 无数据直接返回
	if total == 0 {
		return make([]map[string]interface{}, 0), 0, nil
	}

	// 查询数据，包含所有需要的字段
	err = m.Fields(
		"referral_commissions.*",
		// 推荐人信息
		"referrer.account as referrer_account",
		"referrer.nickname as referrer_username",
		// 被推荐人信息
		"invitee.account as invitee_account",
		"invitee.nickname as invitee_username",
		// 代币信息
		"t.symbol as token_symbol",
		// 推荐人代理信息
		"referrer_first_agent.username as referrer_first_agent_name",
		"referrer_second_agent.username as referrer_second_agent_name",
		"referrer_third_agent.username as referrer_third_agent_name",
		// 推荐人Telegram信息
		"referrer_uba.telegram_id as referrer_telegram_id",
		"referrer_uba.telegram_username as referrer_telegram_username",
		"referrer_uba.first_name as referrer_first_name",
		// 被推荐人代理信息
		"invitee_first_agent.username as invitee_first_agent_name",
		"invitee_second_agent.username as invitee_second_agent_name",
		"invitee_third_agent.username as invitee_third_agent_name",
		// 被推荐人Telegram信息
		"invitee_uba.telegram_id as invitee_telegram_id",
		"invitee_uba.telegram_username as invitee_telegram_username",
		"invitee_uba.first_name as invitee_first_name",
	).Page(page, pageSize).Order("referral_commissions.commission_id DESC").Scan(&list)

	return list, total, err
}
