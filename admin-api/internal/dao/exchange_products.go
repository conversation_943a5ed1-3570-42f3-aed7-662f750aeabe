// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// exchangeProductsDao is the data access object for the table exchange_products.
// You can define custom methods on it to extend its functionality as needed.
type exchangeProductsDao struct {
	*internal.ExchangeProductsDao
}

var (
	// ExchangeProducts is a globally accessible object for table exchange_products operations.
	ExchangeProducts = exchangeProductsDao{internal.NewExchangeProductsDao()}
)

// Add your custom methods and functionality below.
