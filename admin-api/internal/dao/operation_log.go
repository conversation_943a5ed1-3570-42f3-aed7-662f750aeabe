// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
	"admin-api/internal/model/do"
	"context"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
)

// operationLogDao is the data access object for the table operation_log.
// You can define custom methods on it to extend its functionality as needed.
type operationLogDao struct {
	*internal.OperationLogDao
}

var (
	// OperationLog is a globally accessible object for table operation_log operations.
	OperationLog = operationLogDao{internal.NewOperationLogDao()}
)

// Add your custom methods and functionality below.

// GetOperationLogList 获取操作日志列表
func (d *operationLogDao) GetOperationLogList(ctx context.Context, page, pageSize int, condition g.Map) (list []*do.OperationLog, total int, err error) {
	// 初始化
	total = 0
	list = make([]*do.OperationLog, 0)

	// 计算分页
	offset := (page - 1) * pageSize

	// 查询总数
	count, err := d.Ctx(ctx).Where(condition).Count()
	if err != nil {
		return nil, 0, err
	}
	total = int(count)

	// 查询数据
	err = d.Ctx(ctx).
		Where(condition).
		Order(d.Columns().Id + " DESC").
		Limit(pageSize).
		Offset(offset).
		Scan(&list)

	return list, total, err
}

// GetOperationLogById 根据ID获取操作日志详情
func (d *operationLogDao) GetOperationLogById(ctx context.Context, id int64) (info *do.OperationLog, err error) {
	err = d.Ctx(ctx).
		Where(d.Columns().Id, id).
		Scan(&info)

	return
}

// ParseTimeRange 解析时间范围
func (d *operationLogDao) ParseTimeRange(dateRange, startTime, endTime string) (start, end string) {
	// 优先使用dateRange
	if dateRange != "" {
		timeArr := strings.Split(dateRange, ",")
		if len(timeArr) == 2 {
			return timeArr[0] + " 00:00:00", timeArr[1] + " 23:59:59"
		}
	}

	// 使用单独的开始和结束时间
	if startTime != "" {
		start = startTime + " 00:00:00"
	}
	if endTime != "" {
		end = endTime + " 23:59:59"
	}

	return
}
