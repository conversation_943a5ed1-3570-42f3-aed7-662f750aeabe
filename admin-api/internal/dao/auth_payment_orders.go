// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-api/internal/dao/internal"
)

// authPaymentOrdersDao is the data access object for the table auth_payment_orders.
// You can define custom methods on it to extend its functionality as needed.
type authPaymentOrdersDao struct {
	*internal.AuthPaymentOrdersDao
}

var (
	// AuthPaymentOrders is a globally accessible object for table auth_payment_orders operations.
	AuthPaymentOrders = authPaymentOrdersDao{internal.NewAuthPaymentOrdersDao()}
)

// Add your custom methods and functionality below.
