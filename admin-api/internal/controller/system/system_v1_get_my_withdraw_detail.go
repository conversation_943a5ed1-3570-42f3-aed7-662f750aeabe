package system

import (
	"context"

	"admin-api/internal/service"

	v1 "admin-api/api/system/v1"
)

// GetMyWithdrawDetail implements the logic for the GetMyWithdrawDetail controller.
func (c *ControllerV1) GetMyWithdrawDetail(ctx context.Context, req *v1.GetMyWithdrawDetailReq) (res *v1.GetMyWithdrawDetailRes, err error) {
	res, err = service.SystemServiceInstance.GetMyWithdrawDetail(ctx, req)
	return
}
