package system

import (
	"context"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"

	"github.com/gogf/gf/util/gconv"
	"github.com/gogf/gf/v2/net/ghttp"
)

func (c *ControllerV1) GetUserWithdrawDetail(ctx context.Context, req *v1.GetUserWithdrawDetailReq) (res *v1.GetUserWithdrawDetailRes, err error) {
	// 从路径参数中获取 withdrawId
	r := ghttp.RequestFromCtx(ctx)
	withdrawId := r.Get("withdrawId")
	req.UserWithdrawsId = gconv.Uint(withdrawId)
	return service.SystemServiceInstance.GetUserWithdrawDetail(ctx, req)
}
