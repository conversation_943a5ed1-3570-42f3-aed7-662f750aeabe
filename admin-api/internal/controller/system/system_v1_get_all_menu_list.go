package system

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	"admin-api/internal/consts"
	"admin-api/internal/dao"
)

func (c *ControllerV1) GetAllMenuList(ctx context.Context, req *v1.GetAllMenuListReq) (res *v1.GetAllMenuListRes, err error) {
	menus, err := dao.AdminMenu.GetAllMenusTree(ctx, g.Map{
		dao.AdminMenu.Columns().Status: consts.StatusEnabled,
	})
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取菜单失败")
	}
	return &v1.GetAllMenuListRes{
		Data: menus,
	}, nil
}
