package system

import (
	"context"

	"admin-api/internal/service"

	v1 "admin-api/api/system/v1"
)

// GetMerchantWalletDetail implements the logic for the GetMerchantWalletDetail controller.
func (c *ControllerV1) GetMerchantWalletDetail(ctx context.Context, req *v1.GetMerchantWalletDetailReq) (res *v1.GetMerchantWalletDetailRes, err error) {
	res, err = service.SystemServiceInstance.GetMerchantWalletDetail(ctx, req)
	return
}