package system

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"
	"context"

	"github.com/gogf/gf/v2/net/ghttp" // 导入 ghttp
)

func (c *ControllerV1) EditPermission(ctx context.Context, req *v1.EditPermissionReq) (res *v1.EditPermissionRes, err error) {
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Int64()                                      // 从路径获取 ID
	res, err = service.SystemServiceInstance.EditPermission(ctx, req) // 调用 Service 层
	return
}
