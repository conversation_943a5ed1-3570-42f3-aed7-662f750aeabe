package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// GetMember implements the logic for the GetMember controller.
func (c *ControllerV1) GetMember(ctx context.Context, req *v1.GetMemberReq) (res *v1.GetMemberRes, err error) {
	// 从路径参数中获取 ID
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Int64()
	res, err = service.SystemServiceInstance.GetMember(ctx, req)
	return
}
