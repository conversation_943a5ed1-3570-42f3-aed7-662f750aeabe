package system

import (
	"context"

	"admin-api/internal/service"

	v1 "admin-api/api/system/v1"
)

// GetMemberListForNotice implements the logic for the GetMemberListForNotice controller.
func (c *ControllerV1) GetMemberListForNotice(ctx context.Context, req *v1.GetMemberListForNoticeReq) (res *v1.GetMemberListForNoticeRes, err error) {
	res, err = service.SystemServiceInstance.GetMemberListForNotice(ctx, req)
	return
}
