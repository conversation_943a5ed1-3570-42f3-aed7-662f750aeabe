package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// UpdateUserStatus implements the logic for the UpdateUserStatus controller.
func (c *ControllerV1) UpdateUserStatus(ctx context.Context, req *v1.UpdateUserStatusReq) (res *v1.UpdateUserStatusRes, err error) {
	// 从路径参数中获取 id
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Uint64()
	res, err = service.SystemServiceInstance.UpdateUserStatus(ctx, req)
	return
}
