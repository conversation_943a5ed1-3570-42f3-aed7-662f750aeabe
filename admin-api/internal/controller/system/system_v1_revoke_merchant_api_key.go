package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// RevokeMerchantApiKey implements the logic for the RevokeMerchantApiKey controller.
func (c *ControllerV1) RevokeMerchantApiKey(ctx context.Context, req *v1.RevokeMerchantApiKeyReq) (res *v1.RevokeMerchantApiKeyRes, err error) {
	// 从路径参数中获取 apiKeyId
	r := ghttp.RequestFromCtx(ctx)
	req.ApiKeyId = r.Get("apiKeyId").Uint()
	res, err = service.SystemServiceInstance.RevokeMerchantApiKey(ctx, req)
	return
}
