package system

// import (
// 	"context"

// 	"admin-api/internal/service"

// 	"github.com/gogf/gf/v2/net/ghttp"

// 	v1 "admin-api/api/system/v1"
// )

// // GetPost implements the logic for the GetPost controller.
// func (c *ControllerV1) GetPost(ctx context.Context, req *v1.GetPostReq) (res *v1.GetPostRes, err error) {
// 	// 从路径参数中获取 ID 并设置到请求结构体中
// 	r := ghttp.RequestFromCtx(ctx)
// 	req.Id = r.Get("id").Int64()
// 	res, err = service.SystemServiceInstance.GetPost(ctx, req)
// 	return
// }
