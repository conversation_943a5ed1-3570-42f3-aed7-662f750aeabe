package system

import (
	"context"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"
)

func (c *ControllerV1) DeleteUserBackupAccount(ctx context.Context, req *v1.DeleteUserBackupAccountReq) (res *v1.DeleteUserBackupAccountRes, err error) {
	// 从路径参数中获取 associationId
	r := ghttp.RequestFromCtx(ctx)
	req.AssociationId = r.Get("associationId").Int64()
	res, err = service.SystemServiceInstance.DeleteUserBackupAccount(ctx, req)
	return
}
