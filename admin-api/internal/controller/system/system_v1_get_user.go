package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// GetUser implements the logic for the GetUser controller.
func (c *ControllerV1) GetUser(ctx context.Context, req *v1.GetUserReq) (res *v1.GetUserRes, err error) {
	// 从路径参数中获取 id
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Uint64()
	res, err = service.SystemServiceInstance.GetUser(ctx, req)
	return
}
