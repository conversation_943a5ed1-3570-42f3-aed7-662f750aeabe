package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// GetRole implements the logic for the GetRole controller.
func (c *ControllerV1) GetRole(ctx context.Context, req *v1.GetRoleReq) (res *v1.GetRoleRes, err error) {
	// 从路径参数中获取 ID
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Int64()
	res, err = service.SystemServiceInstance.GetRole(ctx, req)
	return
}
