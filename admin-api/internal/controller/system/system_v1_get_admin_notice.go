package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// GetAdminNotice implements the logic for the GetAdminNotice controller.
func (c *ControllerV1) GetAdminNotice(ctx context.Context, req *v1.GetAdminNoticeReq) (res *v1.GetAdminNoticeRes, err error) {
	// 从路径参数中获取 ID
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Int64()
	res, err = service.SystemServiceInstance.GetAdminNotice(ctx, req)
	return
}
