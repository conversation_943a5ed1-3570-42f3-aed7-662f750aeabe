package system

import (
	"admin-api/internal/service" // Added service import
	"context"

	// "github.com/gogf/gf/v2/errors/gcode" // Removed unused imports
	// "github.com/gogf/gf/v2/errors/gerror"

	v1 "admin-api/api/system/v1"
)

func (c *ControllerV1) ListPaymentRequest(ctx context.Context, req *v1.ListPaymentRequestReq) (res *v1.ListPaymentRequestRes, err error) {
	// 调用 Logic 层的方法
	return service.SystemServiceInstance.ListPaymentRequests(ctx, req)
}
