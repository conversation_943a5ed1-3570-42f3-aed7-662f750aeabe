package system

import (
	"context"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/service"

	"admin-api/internal/codes"

	"github.com/gogf/gf/v2/errors/gerror"
)

func (c *ControllerV1) CasdoorSignin(ctx context.Context, req *v1.CasdoorSigninReq) (res *v1.CasdoorSigninRes, err error) {
	state := req.State
	code := req.Code

	if state == "" || code == "" {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "state 和 code 不能为空")
	}

	res, err = service.SystemServiceInstance.CasdoorSignin(ctx, req)
	return
}
