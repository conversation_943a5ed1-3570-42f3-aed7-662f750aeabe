package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// ResetUserGoogle2FA implements the logic for the ResetUserGoogle2FA controller.
func (c *ControllerV1) ResetUserGoogle2FA(ctx context.Context, req *v1.ResetUserGoogle2FAReq) (res *v1.ResetUserGoogle2FARes, err error) {
	// 从路径参数中获取 id
	r := ghttp.RequestFromCtx(ctx)
	req.Id = r.Get("id").Uint64()
	res, err = service.SystemServiceInstance.ResetUserGoogle2FA(ctx, req)
	return
}
