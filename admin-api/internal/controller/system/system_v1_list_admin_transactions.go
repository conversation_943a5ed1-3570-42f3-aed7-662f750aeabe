package system

import (
	"context"

	// "github.com/gogf/gf/v2/errors/gcode" // No longer needed directly
	// "github.com/gogf/gf/v2/errors/gerror" // No longer needed directly
	"admin-api/internal/service" // Import service package

	v1 "admin-api/api/system/v1"
)

func (c *ControllerV1) ListAdminTransactions(ctx context.Context, req *v1.ListAdminTransactionsReq) (res *v1.ListAdminTransactionsRes, err error) {
	// Call the corresponding service method
	res, err = service.SystemServiceInstance.ListAdminTransactions(ctx, req)
	return res, err
}
