package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// UpdateMerchantStatus implements the logic for the UpdateMerchantStatus controller.
func (c *ControllerV1) UpdateMerchantStatus(ctx context.Context, req *v1.UpdateMerchantStatusReq) (res *v1.UpdateMerchantStatusRes, err error) {
	// 从路径参数中获取 merchantId
	r := ghttp.RequestFromCtx(ctx)
	req.MerchantId = r.Get("merchantId").Uint()
	res, err = service.SystemServiceInstance.UpdateMerchantStatus(ctx, req)
	return
}
