package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// UpdateApiKey implements the logic for the UpdateApiKey controller.
func (c *ControllerV1) UpdateApiKey(ctx context.Context, req *v1.UpdateApiKeyReq) (res *v1.UpdateApiKeyRes, err error) {
	// 从路径参数中获取 apiKeyId
	r := ghttp.RequestFromCtx(ctx)
	req.ApiKeyId = r.Get("apiKeyId").Uint()
	res, err = service.SystemServiceInstance.UpdateApiKey(ctx, req)
	return
}
