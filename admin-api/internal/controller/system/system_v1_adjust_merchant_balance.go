package system

import (
	"context"

	"admin-api/internal/service"

	v1 "admin-api/api/system/v1"
)

// AdjustMerchantBalance implements the logic for the AdjustMerchantBalance controller.
func (c *ControllerV1) AdjustMerchantBalance(ctx context.Context, req *v1.AdjustMerchantBalanceReq) (res *v1.AdjustMerchantBalanceRes, err error) {
	res, err = service.SystemServiceInstance.AdjustMerchantBalance(ctx, req)
	return
}