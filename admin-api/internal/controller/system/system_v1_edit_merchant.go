package system

import (
	"context"

	"admin-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "admin-api/api/system/v1"
)

// EditMerchant implements the logic for the EditMerchant controller.
func (c *ControllerV1) EditMerchant(ctx context.Context, req *v1.EditMerchantReq) (res *v1.EditMerchantRes, err error) {
	// 从路径参数中获取 merchantId
	r := ghttp.RequestFromCtx(ctx)
	req.MerchantId = r.Get("merchantId").Uint()
	res, err = service.SystemServiceInstance.EditMerchant(ctx, req)
	return
}
