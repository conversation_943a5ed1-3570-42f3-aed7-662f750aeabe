package middleware

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// See https://www.w3.org/TR/cors/ .
// 服务端允许跨域请求选项
type CORSOptions struct {
	AllowDomain      []string // Used for allowing requests from custom domains
	AllowOrigin      string   // Access-Control-Allow-Origin
	AllowCredentials string   // Access-Control-Allow-Credentials
	ExposeHeaders    string   // Access-Control-Expose-Headers
	MaxAge           int      // Access-Control-Max-Age
	AllowMethods     string   // Access-Control-Allow-Methods
	AllowHeaders     string   // Access-Control-Allow-Headers
}

// CORS 中间件处理跨域请求
func CORS(r *ghttp.Request) {
	// 从配置文件读取 CORS 配置
	corsOptions := r.Response.DefaultCORSOptions()
	corsOptions.AllowDomain = []string{"admin-dev.jjpay.co", "sso-dev.jjpay.co", "13.251.142.185"}
	g.Log().Debugf(r.Context(), "corsOptions: %s", gjson.MustEncode(corsOptions))
	// 应用 CORS 配置
	r.Response.CORS(corsOptions)
	r.Middleware.Next()
}
