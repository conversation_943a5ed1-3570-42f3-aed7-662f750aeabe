package middleware

import (
	"admin-api/internal/dao"
	"admin-api/internal/model/do"
	"context"
	"strings"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

// 忽略记录的路径前缀
var ignorePaths = []string{
	"/api/auth/login",                // 登录接口单独使用登录日志
	"/api/system/upload/avatar",      // 上传头像
	"/api/auth/captcha",              // 验证码不需要记录操作日志
	"/api/system/operation-log",      // 操作日志接口自身不记录
	"static",                         // 静态资源
	"favicon.ico",                    // 网站图标
	"/api/system/operation-log/list", // 操作日志接口自身不记录
}

// 忽略记录的HTTP方法
var ignoreMethods = []string{
	"OPTIONS",
	"HEAD",
}

// OperationLoggerMiddleware 操作日志中间件
func OperationLoggerMiddleware(r *ghttp.Request) {
	// 检查是否需要忽略该请求
	if shouldIgnoreRequest(r) {
		r.Middleware.Next()
		return
	}

	// 获取请求开始时间
	startTime := gtime.Now()

	// 获取请求ID
	reqId := r.GetCtxVar("requestId").String()
	if reqId == "" {
		reqId = gconv.String(startTime.UnixNano())
	}

	// 获取请求信息
	requestUrl := r.URL.Path
	requestMethod := r.Method

	// 获取请求参数
	var paramsData interface{} = nil
	if requestMethod == "GET" {
		paramsData = r.GetQueryMap()
	} else {
		// 对于POST、PUT等请求，获取请求体
		if r.GetBody() != nil && len(r.GetBody()) > 0 {
			paramsData = r.GetBody()
		} else {
			// 尝试获取表单数据
			paramsData = r.GetFormMap()
		}
	}

	// JSON序列化
	requestParams := gjson.New(paramsData)

	// 敏感字段脱敏处理
	sensitiveKeys := []string{"password", "pwd", "token", "secret", "key"}
	for _, key := range sensitiveKeys {

		//移除file
		if key == "file" || key == "avatar" {
			requestParams.Remove(key)
			continue
		}

		if requestParams.Contains(key) {
			requestParams.Set(key, "******")
		}
	}

	// 获取客户端信息
	clientIP := r.GetClientIp()
	userAgent := r.GetHeader("User-Agent")

	// 获取用户信息
	var memberId int64 = 0
	var username string = ""
	var memberType string = "0"

	// 从上下文中获取用户信息
	memberType = r.GetCtxVar("tokenType").String()
	username = r.GetCtxVar("username").String()

	// 获取模块和操作名称（从路由元数据中获取）
	module := ""
	action := ""

	// 从路由中获取模块和操作
	if r.Router != nil && r.Router.Uri != "" {
		// 将路径转换为模块/操作格式
		parts := strings.Split(strings.Trim(r.Router.Uri, "/"), "/")
		if len(parts) > 0 {
			module = parts[0]
		}
		if len(parts) > 1 {
			action = strings.Join(parts[1:], "/")
			// 限制action字段长度，避免数据库字段长度限制
			if len(action) > 100 {
				action = action[:100]
			}
		}
	}

	// 创建日志对象
	log := &do.OperationLog{
		ReqId:         reqId,
		MemberType:    memberType,
		MemberId:      memberId,
		Username:      username,
		Module:        module,
		Action:        action,
		RequestMethod: requestMethod,
		RequestUrl:    requestUrl,
		RequestParams: requestParams,
		OperationIp:   clientIP,
		UserAgent:     userAgent,
		Status:        1, // 默认成功
	}

	// 开启响应缓冲
	r.Response.Buffer()

	// 继续执行请求
	r.Middleware.Next()

	// 计算请求耗时
	endTime := gtime.Now()
	duration := endTime.UnixMilli() - startTime.UnixMilli()
	log.Duration = gconv.Int(duration)

	// 获取响应结果
	responseData := r.Response.BufferString()
	responseJson, err := gjson.DecodeToJson(responseData)
	if err == nil {
		log.Response = responseJson

		// 从响应中判断请求是否成功
		if responseJson.Contains("code") {
			code := responseJson.Get("code").Int()
			if code != 0 && code != 200 {
				// 非成功响应码
				log.Status = 0
				if responseJson.Contains("message") {
					log.ErrMsg = responseJson.Get("message").String()
				}
			}
		}
	}

	// 保存日志时使用独立的context，避免context canceled错误
	userIdValue := r.GetCtxVar("userId")
	tokenTypeValue := r.GetCtxVar("tokenType")

	// 使用独立的context保存日志
	go func() {
		// 创建一个新的context
		ctx := gctx.New()

		// 将用户信息添加到新的context中（如果需要）
		if !userIdValue.IsEmpty() {
			ctx = context.WithValue(ctx, "userId", userIdValue.Val())
		}
		if !tokenTypeValue.IsEmpty() {
			ctx = context.WithValue(ctx, "tokenType", tokenTypeValue.Val())
		}

		// 使用新的context保存日志
		saveOperationLog(ctx, log)
	}()
}

// shouldIgnoreRequest 判断是否需要忽略该请求
func shouldIgnoreRequest(r *ghttp.Request) bool {
	// 检查请求方法
	for _, method := range ignoreMethods {
		if r.Method == method {
			return true
		}
	}

	// 检查请求路径
	path := r.URL.Path
	for _, prefix := range ignorePaths {
		if strings.HasPrefix(path, prefix) {
			return true
		}
	}

	// 特定路由判断逻辑
	if strings.HasPrefix(path, "/api") && r.Method == "GET" {
		// 可以选择性忽略GET请求
		// return true
	}

	return false
}

// saveOperationLog 保存操作日志
func saveOperationLog(ctx context.Context, log *do.OperationLog) {
	// 设置默认值
	if log.ProvinceId == nil {
		log.ProvinceId = 0
	}
	if log.CityId == nil {
		log.CityId = 0
	}
	//移除 response
	// log.Response = nil

	// 尝试保存日志，忽略错误（避免影响主业务流程）
	_, err := dao.OperationLog.Ctx(ctx).Insert(log)
	if err != nil {
		// 记录错误日志，但不影响主业务
		g.Log().Error(ctx, "保存操作日志失败:", err)
	}
}
