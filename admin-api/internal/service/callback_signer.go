package service

import (
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"strconv"
	"time"
)

// CallbackSigner 回调签名服务
// 复用现有的签名机制: method + uri + timestamp + nonce + body
type CallbackSigner struct{}

// NewCallbackSigner 创建新的回调签名服务
func NewCallbackSigner() *CallbackSigner {
	return &CallbackSigner{}
}

// GenerateNonce 生成随机Nonce
func (s *CallbackSigner) GenerateNonce() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return base64.StdEncoding.EncodeToString(bytes)
}

// GenerateSignature 生成HMAC签名
// 使用与API请求相同的签名方式: method + uri + timestamp + nonce + body
func (s *CallbackSigner) GenerateSignature(method, uri, timestamp, nonce, body, secretSalt string) string {
	signString := method + uri + timestamp + nonce + body

	h := hmac.New(sha256.New, []byte(secretSalt))
	h.Write([]byte(signString))
	signature := hex.EncodeToString(h.Sum(nil))

	return signature
}

// GenerateCallbackHeaders 为回调请求生成完整的认证头信息
func (s *CallbackSigner) GenerateCallbackHeaders(method, uri, body, apiKey, secretSalt string) map[string]string {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := s.GenerateNonce()
	signature := s.GenerateSignature(method, uri, timestamp, nonce, body, secretSalt)

	// 新的 Webhook 头格式，不包含 API Key
	headers := map[string]string{
		"Content-Type":        "application/json",
		"X-Webhook-Timestamp": timestamp,
		"X-Webhook-Nonce":     nonce,
		"X-Webhook-Signature": signature,
	}

	// 如果提供了 API Key（向后兼容），则添加旧格式的头
	if apiKey != "" {
		headers["X-API-Key"] = apiKey
		headers["X-Timestamp"] = timestamp
		headers["X-Nonce"] = nonce
		headers["X-Signature"] = signature
	}

	return headers
}

// CallbackSignatureInfo 回调签名信息
type CallbackSignatureInfo struct {
	Timestamp string `json:"timestamp"`
	Nonce     string `json:"nonce"`
	Signature string `json:"signature"`
	APIKey    string `json:"api_key"`
}

// PrepareCallbackSignature 准备回调签名信息（用于记录到日志）
func (s *CallbackSigner) PrepareCallbackSignature(method, uri, body, apiKey, secretSalt string) *CallbackSignatureInfo {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := s.GenerateNonce()
	signature := s.GenerateSignature(method, uri, timestamp, nonce, body, secretSalt)

	return &CallbackSignatureInfo{
		Timestamp: timestamp,
		Nonce:     nonce,
		Signature: signature,
		APIKey:    apiKey,
	}
}
