package service

import (
	"admin-api/internal/constants"
	"admin-api/internal/dao"
	"admin-api/internal/model/entity"
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

// WithdrawService 提现服务
type WithdrawService struct {
	callbackManager *CallbackManager
}

// NewWithdrawService 创建新的提现服务
func NewWithdrawService() *WithdrawService {
	return &WithdrawService{
		callbackManager: NewCallbackManager(),
	}
}

// UpdateWithdrawStatusToCompleted 更新提现状态为已完成并创建回调记录
func (s *WithdrawService) UpdateWithdrawStatusToCompleted(ctx context.Context, withdrawsId uint64, txHash string, adminRemark string) error {
	// 1. 更新提现状态
	err := dao.MerchantWithdraws.UpdateStatusToCompleted(ctx, withdrawsId, txHash, adminRemark)
	if err != nil {
		g.Log().Errorf(ctx, "更新提现状态失败: withdrawsId=%d, error=%v", withdrawsId, err)
		return err
	}

	// 2. 获取提现信息
	withdraw, err := s.getWithdrawInfo(ctx, withdrawsId)
	if err != nil {
		g.Log().Errorf(ctx, "获取提现信息失败: withdrawsId=%d, error=%v", withdrawsId, err)
		return err
	}

	// 3. 创建回调记录（同步）
	callbackData := &WithdrawCompletedData{
		EventType:    constants.CallbackEventWithdrawCompleted,
		OrderNo:      withdraw.OrderNo,
		MerchantId:   withdraw.MerchantId,
		Amount:       withdraw.Amount.String(),
		Currency:     withdraw.Name,
		ActualAmount: withdraw.ActualAmount.String(),
		HandlingFee:  withdraw.HandlingFee.String(),
		TxHash:       txHash,
		CompletedAt:  time.Now().Format(time.RFC3339),
		Timestamp:    time.Now().Unix(),
	}

	if err := s.callbackManager.CreateCallbackRecord(ctx, constants.CallbackEventWithdrawCompleted,
		withdraw.MerchantId, withdrawsId, withdraw.OrderNo, callbackData); err != nil {
		g.Log().Errorf(ctx, "创建提现完成回调记录失败: withdrawsId=%d, error=%v", withdrawsId, err)
		// 不影响主流程，继续执行
	}

	g.Log().Infof(ctx, "提现状态更新成功: withdrawsId=%d", withdrawsId)
	return nil
}

// UpdateWithdrawStatus 更新提现状态
func (s *WithdrawService) UpdateWithdrawStatus(ctx context.Context, withdrawsId uint64, state int, updateData g.Map) error {
	// 更新提现状态
	err := dao.MerchantWithdraws.UpdateStatus(ctx, withdrawsId, state, updateData)
	if err != nil {
		g.Log().Errorf(ctx, "更新提现状态失败: withdrawsId=%d, state=%d, error=%v", withdrawsId, state, err)
		return err
	}

	// 如果状态是已完成，创建回调记录
	if state == constants.WithdrawStatusCompleted {
		// 获取提现信息
		withdraw, err := s.getWithdrawInfo(ctx, withdrawsId)
		if err != nil {
			g.Log().Errorf(ctx, "获取提现信息失败: withdrawsId=%d, error=%v", withdrawsId, err)
			return nil // 不影响主流程
		}

		// 创建回调记录
		callbackData := &WithdrawCompletedData{
			EventType:    constants.CallbackEventWithdrawCompleted,
			OrderNo:      withdraw.OrderNo,
			MerchantId:   withdraw.MerchantId,
			Amount:       withdraw.Amount.String(),
			Currency:     withdraw.Name,
			ActualAmount: withdraw.ActualAmount.String(),
			HandlingFee:  withdraw.HandlingFee.String(),
			TxHash:       withdraw.TxHash,
			CompletedAt:  time.Now().Format(time.RFC3339),
			Timestamp:    time.Now().Unix(),
		}

		if err := s.callbackManager.CreateCallbackRecord(ctx, constants.CallbackEventWithdrawCompleted,
			withdraw.MerchantId, withdrawsId, withdraw.OrderNo, callbackData); err != nil {
			g.Log().Errorf(ctx, "创建提现完成回调记录失败: withdrawsId=%d, error=%v", withdrawsId, err)
		}
	}

	g.Log().Infof(ctx, "提现状态更新成功: withdrawsId=%d, state=%d", withdrawsId, state)
	return nil
}

// ApproveWithdrawStatus 审批通过提现并创建回调记录
func (s *WithdrawService) ApproveWithdrawStatus(ctx context.Context, withdrawsId uint64, approverNotes string) error {
	// 更新提现状态为处理中
	updateData := g.Map{
		"admin_remark": approverNotes,
	}
	err := dao.MerchantWithdraws.UpdateStatus(ctx, withdrawsId, constants.WithdrawStatusProcessing, updateData)
	if err != nil {
		g.Log().Errorf(ctx, "更新提现状态为处理中失败: withdrawsId=%d, error=%v", withdrawsId, err)
		return err
	}

	// 获取提现信息
	withdraw, err := s.getWithdrawInfo(ctx, withdrawsId)
	if err != nil {
		g.Log().Errorf(ctx, "获取提现信息失败: withdrawsId=%d, error=%v", withdrawsId, err)
		return nil // 不影响主流程
	}

	// 创建审批通过回调记录
	callbackData := &WithdrawApprovedData{
		EventType:    constants.CallbackEventWithdrawApproved,
		OrderNo:      withdraw.OrderNo,
		MerchantId:   withdraw.MerchantId,
		ApprovedAt:   time.Now().Format(time.RFC3339),
		ApproverNote: approverNotes,
		Timestamp:    time.Now().Unix(),
	}

	if err := s.callbackManager.CreateCallbackRecord(ctx, constants.CallbackEventWithdrawApproved,
		withdraw.MerchantId, withdrawsId, withdraw.OrderNo, callbackData); err != nil {
		g.Log().Errorf(ctx, "创建提现审批通过回调记录失败: withdrawsId=%d, error=%v", withdrawsId, err)
	}

	g.Log().Infof(ctx, "提现审批通过，状态更新为处理中: withdrawsId=%d", withdrawsId)
	return nil
}

// RejectWithdrawStatus 拒绝提现并创建回调记录
func (s *WithdrawService) RejectWithdrawStatus(ctx context.Context, withdrawsId uint64, rejectReason string) error {
	// 更新提现状态为已拒绝
	updateData := g.Map{
		"admin_remark":     rejectReason,
		"refuse_reason_zh": rejectReason,
		"refuse_reason_en": rejectReason,
	}
	err := dao.MerchantWithdraws.UpdateStatus(ctx, withdrawsId, constants.WithdrawStatusRejected, updateData)
	if err != nil {
		g.Log().Errorf(ctx, "更新提现状态为已拒绝失败: withdrawsId=%d, error=%v", withdrawsId, err)
		return err
	}

	// 获取提现信息
	withdraw, err := s.getWithdrawInfo(ctx, withdrawsId)
	if err != nil {
		g.Log().Errorf(ctx, "获取提现信息失败: withdrawsId=%d, error=%v", withdrawsId, err)
		return nil // 不影响主流程
	}

	// 创建拒绝回调记录
	callbackData := &WithdrawRejectedData{
		EventType:    constants.CallbackEventWithdrawRejected,
		OrderNo:      withdraw.OrderNo,
		MerchantId:   withdraw.MerchantId,
		RejectReason: rejectReason,
		RejectedAt:   time.Now().Format(time.RFC3339),
		Timestamp:    time.Now().Unix(),
	}

	if err := s.callbackManager.CreateCallbackRecord(ctx, constants.CallbackEventWithdrawRejected,
		withdraw.MerchantId, withdrawsId, withdraw.OrderNo, callbackData); err != nil {
		g.Log().Errorf(ctx, "创建提现拒绝回调记录失败: withdrawsId=%d, error=%v", withdrawsId, err)
	}

	g.Log().Infof(ctx, "提现已拒绝: withdrawsId=%d, reason=%s", withdrawsId, rejectReason)
	return nil
}

// GetWithdrawService 获取全局提现服务实例
var withdrawServiceInstance *WithdrawService

func GetWithdrawService() *WithdrawService {
	if withdrawServiceInstance == nil {
		withdrawServiceInstance = NewWithdrawService()
	}
	return withdrawServiceInstance
}

// getWithdrawInfo 获取提现信息
func (s *WithdrawService) getWithdrawInfo(ctx context.Context, withdrawsId uint64) (*entity.MerchantWithdraws, error) {
	withdraw, err := dao.MerchantWithdraws.Ctx(ctx).Where("withdraws_id = ?", withdrawsId).One()
	if err != nil {
		return nil, fmt.Errorf("查询提现记录失败: %v", err)
	}
	if withdraw.IsEmpty() {
		return nil, fmt.Errorf("提现记录不存在: %d", withdrawsId)
	}

	var withdrawEntity *entity.MerchantWithdraws
	if err := withdraw.Struct(&withdrawEntity); err != nil {
		return nil, fmt.Errorf("转换提现实体失败: %v", err)
	}

	return withdrawEntity, nil
}

// CreateWithdraw 创建提现订单时记录回调
func (s *WithdrawService) CreateWithdraw(ctx context.Context, withdrawsId uint64) error {
	// 获取提现信息
	withdraw, err := s.getWithdrawInfo(ctx, withdrawsId)
	if err != nil {
		g.Log().Errorf(ctx, "获取提现信息失败: withdrawsId=%d, error=%v", withdrawsId, err)
		return err
	}

	// 创建回调记录
	callbackData := &WithdrawCreatedData{
		EventType:  constants.CallbackEventWithdrawCreated,
		OrderNo:    withdraw.OrderNo,
		MerchantId: withdraw.MerchantId,
		Amount:     withdraw.Amount.String(),
		Currency:   withdraw.Name,
		CreatedAt:  time.Now().Format(time.RFC3339),
		Timestamp:  time.Now().Unix(),
	}

	if err := s.callbackManager.CreateCallbackRecord(ctx, constants.CallbackEventWithdrawCreated,
		withdraw.MerchantId, withdrawsId, withdraw.OrderNo, callbackData); err != nil {
		g.Log().Errorf(ctx, "创建提现创建回调记录失败: withdrawsId=%d, error=%v", withdrawsId, err)
	}

	g.Log().Infof(ctx, "提现创建回调记录已生成: withdrawsId=%d", withdrawsId)
	return nil
}

// CancelWithdraw 撤销提现时记录回调
func (s *WithdrawService) CancelWithdraw(ctx context.Context, withdrawsId uint64, reason string) error {
	// 更新状态为已撤销
	updateData := g.Map{
		"admin_remark": reason,
	}
	err := dao.MerchantWithdraws.UpdateStatus(ctx, withdrawsId, constants.WithdrawStatusCancelled, updateData)
	if err != nil {
		g.Log().Errorf(ctx, "更新提现状态为已撤销失败: withdrawsId=%d, error=%v", withdrawsId, err)
		return err
	}

	// 获取提现信息
	withdraw, err := s.getWithdrawInfo(ctx, withdrawsId)
	if err != nil {
		g.Log().Errorf(ctx, "获取提现信息失败: withdrawsId=%d, error=%v", withdrawsId, err)
		return nil // 不影响主流程
	}

	// 创建回调记录
	callbackData := &WithdrawCancelledData{
		EventType:    constants.CallbackEventWithdrawCancelled,
		OrderNo:      withdraw.OrderNo,
		MerchantId:   withdraw.MerchantId,
		CancelReason: reason,
		CancelledAt:  time.Now().Format(time.RFC3339),
		Timestamp:    time.Now().Unix(),
	}

	if err := s.callbackManager.CreateCallbackRecord(ctx, constants.CallbackEventWithdrawCancelled,
		withdraw.MerchantId, withdrawsId, withdraw.OrderNo, callbackData); err != nil {
		g.Log().Errorf(ctx, "创建提现撤销回调记录失败: withdrawsId=%d, error=%v", withdrawsId, err)
	}

	g.Log().Infof(ctx, "提现已撤销: withdrawsId=%d, reason=%s", withdrawsId, reason)
	return nil
}
