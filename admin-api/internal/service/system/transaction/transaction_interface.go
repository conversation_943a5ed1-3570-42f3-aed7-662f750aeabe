package transaction

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/model" // Import model for TransactionAdminInfo
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// ITransactionRepository 定义了交易记录数据仓库的接口
type ITransactionRepository interface {
	// ListAdmin 获取后台交易记录列表
	// 返回包含关联信息的 DTO 列表
	ListAdmin(ctx context.Context, page, pageSize int, condition g.Map) (list []*model.TransactionAdminInfo, total int, err error)

	// ListAdminWithAgentInfo 获取后台交易记录列表（带代理和telegram信息）
	ListAdminWithAgentInfo(ctx context.Context, req *v1.ListAdminTransactionsReq) (list []*model.TransactionAdminInfo, total int, err error)

	// GetByID (如果需要)
	// GetByID(ctx context.Context, transactionId uint64) (*entity.Transactions, error)

	// Create (如果需要，注意事务处理)
	// Create(ctx context.Context, tx gdb.TX, data *do.Transactions) (id int64, err error)
}
