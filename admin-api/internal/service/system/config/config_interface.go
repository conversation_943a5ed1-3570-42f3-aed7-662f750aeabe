package config

import (
	"admin-api/internal/model/do"     // Import do package
	"admin-api/internal/model/entity" // Import entity package
	"context"                         // Import API definitions

	"github.com/gogf/gf/v2/frame/g" // Import g.Map
)

// IConfigRepository defines the interface for data access operations related to config.
type IConfigRepository interface {
	// --- Category Repository Methods ---
	CreateCategory(ctx context.Context, category *do.AdminConfigCategories) (id int64, err error)
	// UpdateCategory(ctx context.Context, category *entity.AdminConfigCategory) error // Full entity update
	UpdateCategoryFields(ctx context.Context, id int64, data g.Map) error // Partial update using g.Map
	DeleteCategories(ctx context.Context, ids []int64) error              // Soft or hard delete
	FindCategoryByID(ctx context.Context, id int64) (*entity.AdminConfigCategories, error)
	FindCategoryByKey(ctx context.Context, key string) (*entity.AdminConfigCategories, error)
	ListCategoriesPaginated(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.AdminConfigCategories, total int, err error)
	CountItemsInCategory(ctx context.Context, categoryId int64) (int, error) // For delete check

	// --- Item Repository Methods ---
	CreateItem(ctx context.Context, item *do.AdminConfigItems) (id int64, err error)
	// UpdateItem(ctx context.Context, item *entity.AdminConfigItem) error // Full entity update
	UpdateItemFields(ctx context.Context, id int64, data g.Map) error           // Partial update using g.Map
	DeleteItems(ctx context.Context, ids []int64) error                         // Soft or hard delete
	DeleteItemsByCategoryIDList(ctx context.Context, categoryIds []int64) error // For cascade delete by category IDs
	FindItemByID(ctx context.Context, id int64) (*entity.AdminConfigItems, error)
	FindItemByKey(ctx context.Context, key string) (*entity.AdminConfigItems, error)
	ListItemsByCategoryID(ctx context.Context, categoryId int64) ([]*entity.AdminConfigItems, error)                                                                   // Get all items for a category (no pagination)
	ListItemsByCategoryIDPaginated(ctx context.Context, categoryId int64, page, pageSize int, condition g.Map) (list []*entity.AdminConfigItems, total int, err error) // Get paginated items for a category
}

// Note: The IConfigRepository interface is defined here.
// Its implementation will reside in internal/logic/system/v1/config/config_repo.go.
