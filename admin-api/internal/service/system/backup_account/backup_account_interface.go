package backup_account

import (
	v1 "admin-api/api/system/v1" // Import v1 for request struct usage in ListByUserID
	"admin-api/internal/model/entity"
	"context"
	// "github.com/gogf/gf/v2/os/gtime" // Removed as unused
)

// IBackupAccountRepository defines the repository interface for backup account operations.
type IBackupAccountRepository interface {
	// List retrieves a paginated list of all backup accounts with optional filtering.
	// It takes filtering criteria similar to the API request.
	List(ctx context.Context, req *v1.GetBackupAccountsReq) ([]*entity.UserBackupAccounts, int, error)

	// ListByUserID retrieves a paginated list of backup accounts for a specific user.
	// It takes filtering criteria similar to the API request.
	ListByUserID(ctx context.Context, userId int64, req *v1.GetUserBackupAccountsReq) ([]*entity.UserBackupAccounts, int, error)

	// Create adds a new backup account record.
	Create(ctx context.Context, account *entity.UserBackupAccounts) (int64, error) // Returns the new ID

	// GetByID retrieves a single backup account by its ID.
	GetByID(ctx context.Context, id int64) (*entity.UserBackupAccounts, error)

	// UpdateVerifiedStatus updates the verification status of a backup account.
	UpdateVerifiedStatus(ctx context.Context, id int64, isVerified int) error

	// DeleteByID deletes a backup account by its ID.
	DeleteByID(ctx context.Context, id int64) error

	// FindByAccount checks if a specific account (by telegram username) already exists for any user.
	FindByAccount(ctx context.Context, telegramUsername string) (*entity.UserBackupAccounts, error)

	// FindByUserIDAndAccount checks if a specific account exists for a specific user.
	FindByUserIDAndAccount(ctx context.Context, userId int64, telegramUsername string) (*entity.UserBackupAccounts, error)

	// FindByTelegramId checks if a specific TelegramId already exists.
	FindByTelegramId(ctx context.Context, telegramId int64) (*entity.UserBackupAccounts, error)
}

// IUserRepository defines the repository interface for user data access needed by backup account logic.
// Note: This might already exist in another service package (e.g., user). If so, import it instead.
// For now, define it here for clarity.
type IUserRepository interface {
	// GetByID retrieves a user by their ID (changed name and type).
	GetByID(ctx context.Context, id uint) (*entity.Users, error)
	// GetByIDs retrieves multiple users by their IDs, returning a map.
	GetByIDs(ctx context.Context, ids []uint) (map[uint]*entity.Users, error)
	// UpdatePassword updates the user's password hash.
	UpdatePassword(ctx context.Context, id uint, passwordHash string) error
	// ResetGoogleSecret resets the user's Google Authenticator secret.
	ResetGoogleSecret(ctx context.Context, id uint) error
	// ExistsByID checks if a user exists by ID.
	ExistsByID(ctx context.Context, id uint) (bool, error)
}

// IVerificationCodeService defines the service interface for verification code operations.
type IVerificationCodeService interface {
	// SendVerificationCode generates and sends a verification code to the specified backup account.
	// It might need the backup account entity or just the account details.
	SendVerificationCode(ctx context.Context, backupAccount *entity.UserBackupAccounts) error

	// VerifyCode checks if the provided code is valid for the given backup account ID.
	VerifyCode(ctx context.Context, backupAccountId int64, code string) (bool, error)
}
