package permission

import (
	v1 "admin-api/api/system/v1" // 确保引入了 v1 API 定义
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// IPermissionRepository defines the interface for permission data operations.
type IPermissionRepository interface {
	// List retrieves a flat list of admin permissions based on conditions.
	List(ctx context.Context, condition g.Map) ([]*entity.AdminPermissions, error)

	// ListPaginated retrieves a paginated list of admin permissions based on conditions.
	ListPaginated(ctx context.Context, page, pageSize int, condition g.Map) ([]*entity.AdminPermissions, int, error)

	// Create inserts a new permission record (without handling Tree/Level) within a transaction.
	Create(ctx context.Context, tx gdb.TX, permissionDo *do.AdminPermissions) (id int64, err error)

	// Update updates permission fields within a transaction.
	Update(ctx context.Context, tx gdb.TX, id int64, data g.Map) error

	// Delete performs a soft delete on a permission within a transaction.
	Delete(ctx context.Context, tx gdb.TX, id int64) error

	// GetByID retrieves a single admin permission by ID, optionally within a transaction.
	GetByID(ctx context.Context, tx gdb.TX, id int64) (*entity.AdminPermissions, error)

	// ExistsByKey checks if a permission with the same key exists, excluding a specific ID, optionally within a transaction.
	ExistsByKey(ctx context.Context, tx gdb.TX, key string, excludeId ...int64) (bool, error)

	// ExistsByNameAndPid checks if a permission with the same name exists under the same parent, excluding a specific ID, optionally within a transaction.
	ExistsByNameAndPid(ctx context.Context, tx gdb.TX, name string, pid int64, excludeId ...int64) (bool, error)

	// HasDirectChildren checks if a permission has any direct children.
	HasDirectChildren(ctx context.Context, id int64) (bool, error)

	// FindDescendantsByTree finds all descendants of a permission based on the tree path prefix.
	FindDescendantsByTree(ctx context.Context, treePrefix string) ([]*entity.AdminPermissions, error)

	// BatchUpdateLevelTree updates the level and tree path for multiple permissions within a transaction.
	BatchUpdateLevelTree(ctx context.Context, tx gdb.TX, updates map[int64]g.Map) error

	// GetByKey retrieves a single admin permission by Key, optionally within a transaction.
	GetByKey(ctx context.Context, tx gdb.TX, key string) (*entity.AdminPermissions, error)

	// FindByType retrieves all permissions of a specific type.
	FindByType(ctx context.Context, tx gdb.TX, permType string) ([]*entity.AdminPermissions, error)

	// FindByKeyPrefix retrieves all permissions with keys starting with the given prefix.
	FindByKeyPrefix(ctx context.Context, tx gdb.TX, keyPrefix string) ([]*entity.AdminPermissions, error)
}

// IPermissionLogic defines the interface for permission business logic.
type IPermissionLogic interface {
	AddPermission(ctx context.Context, txIn gdb.TX, req *v1.AddPermissionReq) (res *v1.AddPermissionRes, err error)
	GetAllPermissionList(ctx context.Context, req *v1.GetAllPermissionListReq) (res *v1.GetAllPermissionListRes, err error)
	GetPermissionList(ctx context.Context, req *v1.GetPermissionListReq) (res *v1.GetPermissionListRes, err error)
	EditPermission(ctx context.Context, req *v1.EditPermissionReq) (res *v1.EditPermissionRes, err error)
	DeletePermission(ctx context.Context, req *v1.DeletePermissionReq) (res *v1.DeletePermissionRes, err error)
	PermissionExistsByKey(ctx context.Context, key string) (bool, error)
	GetPermissionByKey(ctx context.Context, tx gdb.TX, key string) (permission *entity.AdminPermissions, err error)
}
