package redpacket

import (
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// RedPacketAdminInfoDTO 定义了红包列表项的数据传输对象 (Admin)
type RedPacketAdminInfoDTO struct {
	entity.RedPackets
	CreatorUsername string `json:"creatorUsername"`
	TokenSymbol     string `json:"tokenSymbol"`
	TokenName       string `json:"tokenName"`
	TokenLogo       string `json:"tokenLogo"`

	// 新增：三级代理信息
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称"`

	// 新增：主备份账户telegram信息
	TelegramId       string `json:"telegramId" dc:"主Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"主Telegram用户名"`
	FirstName        string `json:"firstName" dc:"主备份账户名字"`
}

// RedPacketClaimAdminInfoDTO 定义了红包领取记录列表项的数据传输对象 (Admin)
type RedPacketClaimAdminInfoDTO struct {
	entity.RedPacketClaims
	ClaimerUsername string      `json:"claimerUsername"`
	RedPacketMemo   string      `json:"redPacketMemo"` // 从关联的 RedPacket 获取
	TokenSymbol     string      `json:"tokenSymbol"`   // 从关联的 RedPacket 获取
	TokenId         int         `json:"tokenId"`       // 从关联的 RedPacket 获取
	TokenName       string      `json:"tokenName"`     // 从关联的 RedPacket 获取
	RedPacketType   string      `json:"redPacketType"` // 从关联的 RedPacket 获取
	CreatedAt       *gtime.Time `json:"createdAt"`     // 覆盖嵌入字段以确保类型正确

	// 新增：三级代理信息
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称"`

	// 新增：主备份账户telegram信息
	TelegramId       string `json:"telegramId" dc:"主Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"主Telegram用户名"`
	FirstName        string `json:"firstName" dc:"主备份账户名字"`
}

// IRedPacketRepository 定义了红包数据仓库的接口
type IRedPacketRepository interface {
	// ListAdmin 获取红包列表 (Admin)
	ListAdmin(ctx context.Context, page, pageSize int, condition g.Map) (list []*RedPacketAdminInfoDTO, total int, err error)

	// GetAdminDetailByID 获取单个红包详情 (Admin DTO)
	GetAdminDetailByID(ctx context.Context, redPacketId int64) (*RedPacketAdminInfoDTO, error)

	// GetByID 获取原始红包实体
	GetByID(ctx context.Context, redPacketId int64) (*entity.RedPackets, error)

	// UpdateStatus 更新红包状态
	UpdateStatus(ctx context.Context, redPacketId int64, status string) error

	// UpdateRefundInfo 更新退款相关信息 (取消红包时使用)
	UpdateRefundInfo(ctx context.Context, redPacketId int64, status string, refundAmount int64, refundTransactionId int64) error
}

// IRedPacketClaimRepository 定义了红包领取记录数据仓库的接口
type IRedPacketClaimRepository interface {
	// ListAdmin 获取红包领取记录列表 (Admin)
	ListAdmin(ctx context.Context, page, pageSize int, condition g.Map) (list []*RedPacketClaimAdminInfoDTO, total int, err error)

	// GetClaimsByRedPacketID 获取指定红包的所有领取记录 (Admin DTO)
	GetClaimsByRedPacketID(ctx context.Context, redPacketId int64) ([]*RedPacketClaimAdminInfoDTO, error)
}
