package notice

import (
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// INoticeRepository defines the repository interface for admin notice operations.
type INoticeRepository interface {
	Create(ctx context.Context, notice *do.AdminNotice) (id int64, err error)
	Update(ctx context.Context, id int64, data g.Map) error
	Delete(ctx context.Context, ids []int64, deletedBy int64) error // Soft delete
	GetByID(ctx context.Context, id int64) (*entity.AdminNotice, error)
	ListAdmin(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.AdminNotice, total int, err error)
	ListUser(ctx context.Context, userId int64, condition g.Map) (list []*entity.AdminNotice, err error)
	GetUserUnreadNoticeCount(ctx context.Context, userId int64) (count int, err error)
}

// INoticeReadRepository defines the repository interface for notice read status operations.
type INoticeReadRepository interface {
	MarkReadOrIncrementClick(ctx context.Context, userId int64, noticeId int64) error
	GetReadStatusMap(ctx context.Context, userId int64, noticeIds []int64) (readMap map[int64]*entity.AdminNoticeRead, err error)
	GetReadMemberIDs(ctx context.Context, noticeId int64) ([]int64, error)
	DeleteByNoticeIDs(ctx context.Context, noticeIds []int64) error
	// GetUnreadUserIDs and GetReadUserIDs can be implemented in logic layer using GetReadMemberIDs and target user list
}
