package user

import (
	v1 "admin-api/api/system/v1" // Import API definitions for DTOs
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// IUserRepository 定义了用户数据仓库的接口
type IUserRepository interface {
	// List retrieves a paginated list of users based on criteria, returning DTOs.
	// Note: This DTO includes recommender account, requiring a join or separate query in implementation.
	List(ctx context.Context, page, pageSize int, condition g.Map) (list []*v1.UserInfoType, total int, err error)

	// GetDetailByID retrieves detailed user information by ID, returning a DTO.
	// Note: This DTO includes recommender account, requiring a join or separate query in implementation.
	GetDetailByID(ctx context.Context, id uint64) (*v1.UserDetailType, error)

	// GetByID retrieves the raw user entity by ID.
	GetByID(ctx context.Context, id uint64) (*entity.Users, error)

	// GetByAccount retrieves the raw user entity by account name.
	GetByAccount(ctx context.Context, account string) (*entity.Users, error)

	// GetByEmail retrieves the raw user entity by email.
	GetByEmail(ctx context.Context, email string) (*entity.Users, error)

	// GetByPhone retrieves the raw user entity by phone number.
	GetByPhone(ctx context.Context, phone string) (*entity.Users, error)

	// GetByInviteCode retrieves the raw user entity by invite code.
	GetByInviteCode(ctx context.Context, inviteCode string) (*entity.Users, error)

	// GetUserMapByIDs retrieves a map of user ID to user entity. Used for fetching recommender info.
	GetUserMapByIDs(ctx context.Context, ids []uint64) (map[uint64]*entity.Users, error)

	// Create adds a new user record.
	Create(ctx context.Context, data *do.Users) (id uint64, err error)

	// Update modifies specific fields of an existing user record.
	Update(ctx context.Context, id uint64, data g.Map) error

	// UpdateStatus updates the status (is_stop) and reason for a user.
	UpdateStatus(ctx context.Context, id uint64, isStop int, reason string) error

	// ResetPassword updates the user's password hash.
	ResetPassword(ctx context.Context, id uint64, passwordHash string) error

	// ResetGoogle2FA disables Google 2FA for the user.
	ResetGoogle2FA(ctx context.Context, id uint64) error

	// DeleteSoft soft deletes users by their IDs.
	DeleteSoft(ctx context.Context, ids []uint64) error

	// CheckAccountExists checks if an account name exists (excluding optional ID).
	CheckAccountExists(ctx context.Context, account string, excludeId ...uint64) (bool, error)

	// CheckEmailExists checks if an email exists (excluding optional ID).
	CheckEmailExists(ctx context.Context, email string, excludeId ...uint64) (bool, error)

	// CheckPhoneExists checks if a phone number exists (excluding optional ID).
	CheckPhoneExists(ctx context.Context, phone string, excludeId ...uint64) (bool, error)

	// CheckInviteCodeExists checks if an invite code exists (excluding optional ID).
	CheckInviteCodeExists(ctx context.Context, inviteCode string, excludeId ...uint64) (bool, error)

	// GetByIDs retrieves multiple user entities by their IDs.
	GetByIDs(ctx context.Context, ids []uint64) ([]*entity.Users, error)

	// ExistsByID checks if a user exists by ID.
	ExistsByID(ctx context.Context, id uint64) (bool, error)

	// ResetGoogleSecret clears the Google 2FA secret for a user (used by merchant logic).
	// Note: This might be better placed in an AuthService or specific 2FA service.
	ResetGoogleSecret(ctx context.Context, id uint64) error

	// UpdatePassword updates only the password hash for a user (used by merchant logic).
	// Note: This overlaps with ResetPassword, consider consolidating or clarifying purpose.
	UpdatePassword(ctx context.Context, id uint64, passwordHash string) error

	// CreateMerchantUser 创建商户类型的用户 (AccountType=2)
	CreateMerchantUser(ctx context.Context, account string, hashedPassword string, email string) (userId uint64, err error)

	// ResetPaymentPassword 重置用户支付密码
	ResetPaymentPassword(ctx context.Context, id uint64) error

	// ListWithAgentInfo 获取用户列表（带代理和telegram信息）
	ListWithAgentInfo(ctx context.Context, req *v1.GetUserListReq) (list []*v1.UserInfoType, total int, err error)

	// GetUserMapByIDsWithAgentInfo 根据用户ID列表获取用户信息（包含代理和telegram信息）
	GetUserMapByIDsWithAgentInfo(ctx context.Context, ids []uint64) (map[uint64]*v1.UserInfoType, error)
}
