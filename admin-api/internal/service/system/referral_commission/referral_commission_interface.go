package referral_commission

import (
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// ReferralCommissionWithUserInfoDTO 定义了包含用户信息的推荐佣金数据传输对象
type ReferralCommissionWithUserInfoDTO struct {
	*entity.ReferralCommissions

	// 推荐人信息
	ReferrerUsername string `json:"referrerUsername" dc:"推荐人用户名"`
	ReferrerAccount  string `json:"referrerAccount" dc:"推荐人账号"`

	// 被推荐人信息
	InviteeUsername string `json:"inviteeUsername" dc:"被推荐人用户名"`
	InviteeAccount  string `json:"inviteeAccount" dc:"被推荐人账号"`

	// 代币信息
	TokenSymbol string `json:"tokenSymbol" dc:"代币符号"`

	// 推荐人的三级代理信息
	ReferrerFirstAgentName  string `json:"referrerFirstAgentName" dc:"推荐人一级代理名称"`
	ReferrerSecondAgentName string `json:"referrerSecondAgentName" dc:"推荐人二级代理名称"`
	ReferrerThirdAgentName  string `json:"referrerThirdAgentName" dc:"推荐人三级代理名称"`

	// 推荐人的主备份账户telegram信息
	ReferrerTelegramId       string `json:"referrerTelegramId" dc:"推荐人主Telegram ID"`
	ReferrerTelegramUsername string `json:"referrerTelegramUsername" dc:"推荐人主Telegram用户名"`
	ReferrerFirstName        string `json:"referrerFirstName" dc:"推荐人主备份账户名字"`

	// 被推荐人的三级代理信息
	InviteeFirstAgentName  string `json:"inviteeFirstAgentName" dc:"被推荐人一级代理名称"`
	InviteeSecondAgentName string `json:"inviteeSecondAgentName" dc:"被推荐人二级代理名称"`
	InviteeThirdAgentName  string `json:"inviteeThirdAgentName" dc:"被推荐人三级代理名称"`

	// 被推荐人的主备份账户telegram信息
	InviteeTelegramId       string `json:"inviteeTelegramId" dc:"被推荐人主Telegram ID"`
	InviteeTelegramUsername string `json:"inviteeTelegramUsername" dc:"被推荐人主Telegram用户名"`
	InviteeFirstName        string `json:"inviteeFirstName" dc:"被推荐人主备份账户名字"`
}

// IReferralCommissionRepository 定义了推荐佣金数据仓库的接口
type IReferralCommissionRepository interface {
	// List 获取佣金记录列表（包含用户信息）
	List(ctx context.Context, page, pageSize int, condition g.Map) (list []*ReferralCommissionWithUserInfoDTO, total int, err error)

	// GetByID (如果需要，例如更新状态等操作)
	// GetByID(ctx context.Context, commissionId int64) (*entity.ReferralCommissions, error)

	// UpdateStatus (如果需要)
	// UpdateStatus(ctx context.Context, commissionId int64, status string) error
}
