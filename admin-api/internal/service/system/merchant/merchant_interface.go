package merchant

import (
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// IMerchantRepository defines the repository interface for merchant operations.
type IMerchantRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, merchant *do.Merchants) (id uint, err error)
	Update(ctx context.Context, id uint, data g.Map) error
	Delete(ctx context.Context, ids []uint) error
	GetByID(ctx context.Context, id uint) (*entity.Merchants, error)
	List(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.Merchants, total int, err error)

	// Authentication and lookup operations
	GetByEmail(ctx context.Context, email string) (*entity.Merchants, error)
	GetByMerchantName(ctx context.Context, merchantName string) (*entity.Merchants, error)

	// Status and permission operations
	UpdateStatus(ctx context.Context, id uint, status int) error
	UpdatePermissions(ctx context.Context, id uint, permissions g.Map) error

	// Validation operations
	ExistsByName(ctx context.Context, name string, excludeId ...uint) (bool, error)
	ExistsByEmail(ctx context.Context, email string, excludeId ...uint) (bool, error)

	// Authentication related operations
	UpdatePaymentPassword(ctx context.Context, id uint, hashedPassword string) error
	UpdateGoogle2FA(ctx context.Context, id uint, secret string, enabled bool) error
	UpdateTOTP(ctx context.Context, id uint, secret string, recoveryCodes string) error
}
