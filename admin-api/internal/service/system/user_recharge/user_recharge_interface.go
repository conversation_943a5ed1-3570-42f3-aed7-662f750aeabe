package user_recharge

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/model/entity"
	"context"
)

// IUserRechargeRepository defines the repository interface for user recharge operations.
type IUserRechargeRepository interface {
	// List retrieves a paginated list of user recharges based on the provided criteria.
	List(ctx context.Context, req *v1.ListUserRechargesReq) (list []*entity.UserRecharges, total int, err error)

	// ListWithAgentInfo retrieves a paginated list of user recharges with agent and telegram info.
	ListWithAgentInfo(ctx context.Context, req *v1.ListUserRechargesReq) (list []*v1.UserRechargeListItem, total int, err error)

	// GetByID retrieves a single user recharge by its ID.
	GetByID(ctx context.Context, id uint) (*entity.UserRecharges, error)

	// Export exports user recharges data based on the provided criteria.
	Export(ctx context.Context, req *v1.ListUserRechargesReq) ([]*entity.UserRecharges, error)
}
