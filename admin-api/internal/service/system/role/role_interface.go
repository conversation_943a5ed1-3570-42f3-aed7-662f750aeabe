package role

import (
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"context"

	// "github.com/gogf/gf/v2/encoding/gjson" // Removed unused import
	"github.com/gogf/gf/v2/frame/g"
)

// IRoleRepository 定义了角色数据仓库的接口
type IRoleRepository interface {
	// List 获取角色列表
	List(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.AdminRole, total int, err error)

	// GetByID 根据 ID 获取角色实体
	GetByID(ctx context.Context, id int64) (*entity.AdminRole, error)

	// GetByIDs 根据 ID 列表获取角色实体列表 (用于获取 Key)
	GetByIDs(ctx context.Context, ids []int64) ([]*entity.AdminRole, error)

	// Create 创建角色
	Create(ctx context.Context, data *do.AdminRole) (id int64, err error)

	// Update 更新角色基础信息 (不包括权限相关)
	Update(ctx context.Context, id int64, data g.Map) error

	// UpdateDataScope 更新角色数据范围
	UpdateDataScope(ctx context.Context, id int64, dataScope int, customDeptJson []byte) error

	// DeleteSoft 软删除角色
	DeleteSoft(ctx context.Context, ids []int64) error

	// IsKeyExist 检查 Key 是否存在 (排除指定 ID)
	IsKeyExist(ctx context.Context, key string, excludeId ...int64) (bool, error)
	// GetRoleMapByIds 根据 ID 列表获取 ID -> Role 实体的映射
	GetRoleMapByIds(ctx context.Context, ids []int64) (map[int64]*entity.AdminRole, error)
	// GetRolesByIDs 根据 ID 列表获取角色实体列表 (虽然与 GetByIDs 类似，但 member.go 中用到了，暂时保留)
	GetRolesByIDs(ctx context.Context, ids []int64) ([]*entity.AdminRole, error)
	// CountByIDs 根据 ID 列表计算角色数量
	CountByIDs(ctx context.Context, ids []int64) (int, error)
}

// IMenuRepository 定义了菜单数据仓库的部分接口 (角色模块需要用到)
// 注意：理想情况下，这应该由 Menu Service 提供，但为了简化，暂时定义在这里
type IMenuRepository interface {
	// GetPermissionsByIds 根据菜单 ID 列表获取权限标识列表
	GetPermissionsByIds(ctx context.Context, menuIds []int64) ([]string, error)
}
