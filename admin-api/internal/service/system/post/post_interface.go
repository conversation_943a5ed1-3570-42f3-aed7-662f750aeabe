package post

import (
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/database/gdb" // Import gdb package
	"github.com/gogf/gf/v2/frame/g"
)

// IPostRepository 定义了岗位数据仓库的接口
type IPostRepository interface {
	// List 获取岗位列表
	List(ctx context.Context, page, pageSize int, condition g.Map) (list []*entity.AdminPost, total int, err error)

	// GetByID 根据 ID 获取岗位实体
	GetByID(ctx context.Context, id int64) (*entity.AdminPost, error)

	// GetByCode 根据 Code 获取岗位实体
	GetByCode(ctx context.Context, code string) (*entity.AdminPost, error)

	// GetByName 根据 Name 获取岗位实体
	GetByName(ctx context.Context, name string) (*entity.AdminPost, error)

	// Create 创建岗位
	Create(ctx context.Context, data *do.AdminPost) (id int64, err error)

	// Update 更新岗位
	Update(ctx context.Context, data *do.AdminPost) error

	// DeleteSoft 软删除岗位
	DeleteSoft(ctx context.Context, ids []int64) error

	// IsCodeExist 检查 Code 是否存在 (排除指定 ID)
	IsCodeExist(ctx context.Context, code string, excludeId ...int64) (bool, error)

	// IsNameExist 检查 Name 是否存在 (排除指定 ID)
	IsNameExist(ctx context.Context, name string, excludeId ...int64) (bool, error)

	// IsPostAssigned 检查岗位是否已分配给成员
	IsPostAssigned(ctx context.Context, postIds []int64) (bool, error)
	// GetPostsByIDs 根据 ID 列表获取岗位实体列表
	GetPostsByIDs(ctx context.Context, ids []int64) ([]*entity.AdminPost, error)
	// CountByIDs 根据 ID 列表计算岗位数量
	CountByIDs(ctx context.Context, ids []int64) (int, error)
}

// IMemberPostRepository 定义了成员岗位关联数据仓库的接口
// (虽然 API 中没有直接操作，但删除岗位时需要检查关联，所以定义在这里)
type IMemberPostRepository interface {
	// GetPostIdsByMemberId 根据成员 ID 获取岗位 ID 列表
	GetPostIdsByMemberId(ctx context.Context, memberId int64) ([]int64, error)
	// GetMemberPostsMap 根据成员 ID 列表获取 成员ID -> 岗位列表 的映射
	GetMemberPostsMap(ctx context.Context, memberIds []int64) (map[int64][]*entity.AdminPost, error)

	// GetMemberIdsByPostIds 根据岗位 ID 列表获取成员 ID 列表
	GetMemberIdsByPostIds(ctx context.Context, postIds []int64) ([]int64, error)

	// AddMemberPosts 为成员添加岗位
	// AddMemberPosts 为成员添加岗位 (事务性)
	AddMemberPosts(ctx context.Context, tx gdb.TX, memberId int64, postIds []int64) error
	// UpdateMemberPosts 更新成员的岗位关联 (事务性, 先删后增)
	UpdateMemberPosts(ctx context.Context, tx gdb.TX, memberId int64, postIds []int64) error

	// DeleteMemberPostsByMemberId 删除成员的所有岗位关联
	// DeleteMemberPostsByMemberId 删除成员的所有岗位关联 (事务性)
	DeleteMemberPostsByMemberId(ctx context.Context, tx gdb.TX, memberId int64) error

	// DeleteMemberPostsByPostIds 删除指定岗位的所有成员关联
	// IsPostAssigned(ctx context.Context, postIds []int64) (bool, error) // Removed duplicate
	// DeleteMemberPostsByPostIds 删除指定岗位的所有成员关联 (非事务性，因为通常在删除岗位事务外调用)
	DeleteMemberPostsByPostIds(ctx context.Context, postIds []int64) error
	// IsPostAssigned 检查岗位是否已分配给成员 (保留一个)
	IsPostAssigned(ctx context.Context, postIds []int64) (bool, error)
}
