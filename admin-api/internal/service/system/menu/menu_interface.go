package menu

import (
	"admin-api/internal/model/do"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// IMenuRepository defines the repository interface for menu operations.
type IMenuRepository interface {
	// List retrieves a flat list of admin menus based on conditions.
	List(ctx context.Context, condition g.Map) ([]*entity.AdminMenu, error)

	// ListPaginated retrieves a paginated list of admin menus based on conditions.
	ListPaginated(ctx context.Context, page, pageSize int, condition g.Map) ([]*entity.AdminMenu, int, error)

	// GetByID retrieves a single admin menu by ID.
	GetByID(ctx context.Context, id int64) (*entity.AdminMenu, error)

	// Create inserts a new menu record (without handling Tree).
	Create(ctx context.Context, menuDo *do.AdminMenu) (id int64, err error)

	// Update updates menu fields within a transaction.
	Update(ctx context.Context, tx gdb.TX, id int64, data g.Map) error

	// Delete performs a soft delete on a menu within a transaction.
	Delete(ctx context.Context, tx gdb.TX, id int64) error

	// ExistsByNameAndPid checks if a menu with the same name exists under the same parent, excluding a specific ID.
	ExistsByNameAndPid(ctx context.Context, name string, pid int64, excludeId ...int64) (bool, error)

	// HasDirectChildren checks if a menu has any direct children.
	HasDirectChildren(ctx context.Context, id int64) (bool, error)

	// IsMenuAssignedToRole checks if a menu is assigned to any role.
	IsMenuAssignedToRole(ctx context.Context, id int64) (bool, error)

	// FindDescendantsByTree finds all descendants of a menu based on the tree path prefix.
	FindDescendantsByTree(ctx context.Context, treePrefix string) ([]*entity.AdminMenu, error)

	// BatchUpdateLevelTree updates the level and tree path for multiple menus within a transaction.
	BatchUpdateLevelTree(ctx context.Context, tx gdb.TX, updates map[int64]g.Map) error

	// GetMenusByPaths retrieves a list of admin menus based on their paths and status.
	GetMenusByPaths(ctx context.Context, paths []string, status int) ([]*entity.AdminMenu, error)
}
