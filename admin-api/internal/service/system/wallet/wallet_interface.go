package wallet

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/database/gdb"
)

// IWalletRepository 定义了钱包数据仓库的接口
type IWalletRepository interface {
	// GetOrCreate 获取或创建钱包记录 (需要处理并发)
	GetOrCreate(ctx context.Context, userId uint32, tokenId uint32) (*entity.Wallets, error)

	// GetForUpdate 获取钱包记录并锁定以进行更新 (事务性)
	GetForUpdate(ctx context.Context, tx gdb.TX, userId uint32, tokenId uint32) (*entity.Wallets, error)

	// UpdateBalance 更新钱包余额 (事务性)
	// availableDelta: 可用余额变化量 (正数增加, 负数减少)
	// frozenDelta: 冻结余额变化量 (正数增加, 负数减少)
	UpdateBalance(ctx context.Context, tx gdb.TX, walletId uint64, availableDelta int64, frozenDelta int64) error

	// GetAvailableBalanceInt64 获取可用余额 (int64 格式)
	// 返回可用余额和是否找到钱包
	GetAvailableBalanceInt64(ctx context.Context, userId uint32, tokenId uint32) (balance int64, found bool, err error)

	// GetWalletInfo 获取钱包实体信息 (包含可用和冻结余额)
	GetWalletInfo(ctx context.Context, userId uint32, tokenId uint32) (*entity.Wallets, error)

	// List 获取钱包列表 (包含用户信息)
	// Deprecated: Use ListWalletsWithDetails instead for more structured input and better clarity.
	List(ctx context.Context, page, pageSize int, condition map[string]interface{}) (list []*v1.WalletListItem, total int, err error)

	// ListWalletsWithDetails 获取钱包列表，包含用户和代币的详细信息
	ListWalletsWithDetails(ctx context.Context, input interface{}) (list []*v1.WalletListItem, total int, err error)

	// ListWalletsWithAgentInfo 获取钱包列表（带代理和telegram信息）
	ListWalletsWithAgentInfo(ctx context.Context, req *v1.ListWalletsReq) (list []*v1.WalletListItem, total int, err error)
}
