package user_ass

import (
	v1 "admin-api/api/system/v1"
	"admin-api/internal/model/entity"
	"context"
)

// IUserAssRepository defines the interface for user association repository operations.
type IUserAssRepository interface {
	// List retrieves a paginated list of user associations with optional filtering
	List(ctx context.Context, req *v1.GetBackupAccountsReq) ([]*UserAssWithUserInfo, int, error)

	// ListByAUserID retrieves backup accounts (B users) for a specific A user
	ListByAUserID(ctx context.Context, aUserId int64, req *v1.GetUserBackupAccountsReq) ([]*UserAssWithUserInfo, int, error)

	// <PERSON><PERSON> adds a new user association (A -> B relationship)
	Create(ctx context.Context, aUserId, bUserId int64) (int64, error)

	// GetByID retrieves a user association by ID
	GetByID(ctx context.Context, id int64) (*entity.UserAss, error)

	// DeleteByID deletes a user association by ID
	DeleteByID(ctx context.Context, id int64) error

	// CheckAssociationExists checks if association exists between two users
	CheckAssociationExists(ctx context.Context, aUserId, bUserId int64) (bool, error)

	// CheckReverseAssociationExists checks if reverse association exists (B -> A)
	CheckReverseAssociationExists(ctx context.Context, aUserId, bUserId int64) (bool, error)

	// UpdateVerifiedStatus updates the verification status of a user association
	UpdateVerifiedStatus(ctx context.Context, id int64, verified bool) error
}

// UserAssWithUserInfo represents user association with user information
type UserAssWithUserInfo struct {
	*entity.UserAss
	// B User information (backup user)
	TelegramUsername string `json:"telegramUsername" dc:"Telegram用户名"`
	TelegramId       int64  `json:"telegramId" dc:"Telegram用户ID"`
	// Agent information for B user (backup user)
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称"`
	// A User information (main user)
	MainTelegramId       string `json:"mainTelegramId" dc:"主用户Telegram ID"`
	MainTelegramUsername string `json:"mainTelegramUsername" dc:"主用户Telegram用户名"`
	MainFirstName        string `json:"mainFirstName" dc:"主用户真实姓名"`
}
