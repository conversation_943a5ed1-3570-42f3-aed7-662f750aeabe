package service

import (
	"context"
	"fmt"
	"strings"

	config "admin-api/internal/configv2"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// IWithdrawFeeService 提现手续费服务接口
type IWithdrawFeeService interface {
	// CalculateFee 计算提现手续费
	CalculateFee(ctx context.Context, chain, token, amount string) (fee, netAmount string, err error)
	// MapChainToTokenSymbol 映射链类型到标准代币符号
	MapChainToTokenSymbol(chain, token string) string
	// ValidateWithdrawLimits 验证提现限额
	ValidateWithdrawLimits(ctx context.Context, chain, token, amount string) error
	// GetWithdrawLimitsAndRate 获取提现限额和费率信息
	GetWithdrawLimitsAndRate(ctx context.Context, chain, token string) (minAmount, maxAmount, feeRate string)
}

// withdrawFeeService 提现手续费服务实现
type withdrawFeeService struct{}

// NewWithdrawFeeService 创建提现手续费服务
func NewWithdrawFeeService() IWithdrawFeeService {
	return &withdrawFeeService{}
}

// MapChainToTokenSymbol 映射链类型到标准代币符号（用于配置系统）
func (s *withdrawFeeService) MapChainToTokenSymbol(chain, token string) string {
	switch strings.ToUpper(chain) {
	case "TRC20":
		return "USDT" // TRC20 统一映射为 USDT
	case "ERC20":
		return "USDT" // ERC20 统一映射为 USDT
	case "TRX":
		return "TRX" // TRX 原生币
	case "ETH":
		return "ETH" // ETH 原生币
	default:
		// 对于其他情况，使用传入的token参数
		return strings.ToUpper(token)
	}
}

// calculateTokenFee 计算代币提现手续费（使用配置系统）
func (s *withdrawFeeService) calculateTokenFee(ctx context.Context, amountDec decimal.Decimal, chain, token string) (string, error) {
	// 映射到标准代币符号
	tokenSymbol := s.MapChainToTokenSymbol(chain, token)

	// 获取费用类型配置
	feeType, err := config.GetString(ctx, "withdrawals_setting.fee_type", "rate")
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get fee_type config: %v", err)
		return "", gerror.New("手续费配置错误 (无法获取费用类型配置)")
	}

	feeType = strings.ToLower(strings.TrimSpace(feeType))
	var feeDec decimal.Decimal

	switch feeType {
	case "rate":
		// 使用GetMapKey方法获取比例费率配置
		rateValue, err := config.GetMapKey(ctx, "withdrawals_setting.fee_rate", tokenSymbol, "0")
		if err != nil {
			g.Log().Errorf(ctx, "Failed to get fee_rate for token %s: %v", tokenSymbol, err)
			return "", gerror.Newf("手续费配置错误 (无法获取 %s 的费率配置)", tokenSymbol)
		}

		// 转换为字符串然后解析为decimal
		feeRateStr := fmt.Sprintf("%v", rateValue)
		rateDec, err := decimal.NewFromString(feeRateStr)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to parse fee_rate '%s' for token %s: %v", feeRateStr, tokenSymbol, err)
			return "", gerror.Wrapf(err, "手续费配置错误 (费率格式不正确): %s", tokenSymbol)
		}

		if rateDec.IsNegative() {
			g.Log().Warningf(ctx, "Fee rate is negative ('%s') for token %s, using 0", feeRateStr, tokenSymbol)
			rateDec = decimal.Zero
		}

		// 计算费用: amount * (rate / 100)
		feeDec = amountDec.Mul(rateDec).Div(decimal.NewFromInt(100))

	case "fix":
		// 使用GetMapKey方法获取固定费用配置
		amountValue, err := config.GetMapKey(ctx, "withdrawals_setting.fee_amount", tokenSymbol, "0")
		if err != nil {
			g.Log().Errorf(ctx, "Failed to get fee_amount for token %s: %v", tokenSymbol, err)
			return "", gerror.Newf("手续费配置错误 (无法获取 %s 的固定费用配置)", tokenSymbol)
		}

		// 转换为字符串然后解析为decimal
		feeAmountStr := fmt.Sprintf("%v", amountValue)
		fixedFee, err := decimal.NewFromString(feeAmountStr)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to parse fee_amount '%s' for token %s: %v", feeAmountStr, tokenSymbol, err)
			return "", gerror.Wrapf(err, "手续费配置错误 (固定费用格式不正确): %s", tokenSymbol)
		}

		if fixedFee.IsNegative() {
			g.Log().Warningf(ctx, "Fixed fee is negative ('%s') for token %s, using 0", feeAmountStr, tokenSymbol)
			fixedFee = decimal.Zero
		}

		feeDec = fixedFee

	case "":
		g.Log().Warningf(ctx, "Fee type not configured, using zero fee for token %s", tokenSymbol)
		feeDec = decimal.Zero

	default:
		g.Log().Errorf(ctx, "Unsupported fee_type '%s' for token %s", feeType, tokenSymbol)
		return "", gerror.Newf("不支持的手续费类型配置: %s", feeType)
	}

	// 确保最终计算的费用不为负数
	if feeDec.IsNegative() {
		feeDec = decimal.Zero
	}

	return feeDec.String(), nil
}

// CalculateFee 计算手续费（配置驱动）
func (s *withdrawFeeService) CalculateFee(ctx context.Context, chain, token, amount string) (fee, netAmount string, err error) {
	// 将字符串金额转换为 decimal
	amountDecimal, parseErr := decimal.NewFromString(amount)
	if parseErr != nil {
		return "", "", gerror.Wrap(parseErr, "金额格式错误")
	}

	// 使用配置驱动的代币手续费计算逻辑
	feeStr, err := s.calculateTokenFee(ctx, amountDecimal, chain, token)
	if err != nil {
		return "", "", gerror.Wrap(err, "计算手续费失败")
	}

	// 转换手续费为decimal以便计算净额
	feeDecimal, err := decimal.NewFromString(feeStr)
	if err != nil {
		return "", "", gerror.Wrap(err, "手续费格式错误")
	}

	// 计算实际到账金额
	netAmountDecimal := amountDecimal.Sub(feeDecimal)

	// 检查最小到账金额 - 使用配置的最小金额
	tokenSymbol := s.MapChainToTokenSymbol(chain, token)
	minAmountValue, err := config.GetMapKey(ctx, "withdrawals_setting.min_single_amount", tokenSymbol, "0.000001")
	if err != nil {
		g.Log().Warningf(ctx, "获取最小到账金额失败: %v，使用默认值", err)
		minAmountValue = "0.000001"
	}
	minAmountStr := fmt.Sprintf("%v", minAmountValue)
	minAmount, _ := decimal.NewFromString(minAmountStr)

	if netAmountDecimal.LessThan(minAmount) {
		return "", "", gerror.Newf("扣除手续费后金额过小，最小到账金额: %s %s", minAmount.String(), tokenSymbol)
	}

	// 确保到账金额不为负数
	if netAmountDecimal.IsNegative() {
		return "", "", gerror.New("手续费过高，到账金额为负数")
	}

	return feeDecimal.String(), netAmountDecimal.String(), nil
}

// ValidateWithdrawLimits 验证提现限额（配置驱动）
func (s *withdrawFeeService) ValidateWithdrawLimits(ctx context.Context, chain, token, amount string) error {
	// 检查提现功能是否启用
	withdrawalState, err := config.GetBool(ctx, "withdrawals_setting.state", true)
	if err != nil {
		g.Log().Warningf(ctx, "获取提现开关失败: %v，默认启用", err)
		withdrawalState = true
	}
	if !withdrawalState {
		return gerror.New("提现功能已暂停")
	}

	amountDecimal, err := decimal.NewFromString(amount)
	if err != nil {
		return gerror.Wrap(err, "金额格式错误")
	}

	// 获取最小限额配置 - 使用 GetMapKey 从 Map 配置中获取
	tokenSymbol := s.MapChainToTokenSymbol(chain, token)
	minAmountValue, err := config.GetMapKey(ctx, "withdrawals_setting.min_single_amount", tokenSymbol, "0.001")
	if err != nil {
		g.Log().Warningf(ctx, "获取最小限额失败: %v，使用默认值", err)
		minAmountValue = "0.001"
	}
	minAmountStr := fmt.Sprintf("%v", minAmountValue)

	minAmount, err := decimal.NewFromString(minAmountStr)
	if err != nil {
		g.Log().Warningf(ctx, "解析最小限额失败: %v，使用默认值", err)
		minAmount = decimal.NewFromFloat(0.001)
	}

	// 获取最大限额配置 - 使用 GetMapKey 从 Map 配置中获取
	maxAmountValue, err := config.GetMapKey(ctx, "withdrawals_setting.max_single_amount", tokenSymbol, "1000000")
	if err != nil {
		g.Log().Warningf(ctx, "获取最大限额失败: %v，使用默认值", err)
		maxAmountValue = "1000000"
	}
	maxAmountStr := fmt.Sprintf("%v", maxAmountValue)

	maxAmount, err := decimal.NewFromString(maxAmountStr)
	if err != nil {
		g.Log().Warningf(ctx, "解析最大限额失败: %v，使用默认值", err)
		maxAmount = decimal.NewFromFloat(1000000)
	}

	// 验证限额
	if amountDecimal.LessThan(minAmount) {
		return gerror.Newf("提现金额过小，最小限额: %s %s", minAmount.String(), token)
	}

	if amountDecimal.GreaterThan(maxAmount) {
		return gerror.Newf("提现金额过大，最大限额: %s %s", maxAmount.String(), token)
	}

	return nil
}

// GetWithdrawLimitsAndRate 获取提现限额和费率信息（配置驱动）
func (s *withdrawFeeService) GetWithdrawLimitsAndRate(ctx context.Context, chain, token string) (minAmount, maxAmount, feeRate string) {
	// 映射代币符号
	tokenSymbol := s.MapChainToTokenSymbol(chain, token)

	// 获取费用类型配置
	feeType, err := config.GetString(ctx, "withdrawals_setting.fee_type", "fix")
	if err != nil {
		g.Log().Warningf(ctx, "获取费用类型失败: %v，使用默认值", err)
		feeType = "fix"
	}

	// 构建费率显示字符串
	switch strings.ToLower(strings.TrimSpace(feeType)) {
	case "fix":
		// 获取固定费用
		amountValue, err := config.GetMapKey(ctx, "withdrawals_setting.fee_amount", tokenSymbol, "0")
		if err != nil {
			feeRate = "固定手续费"
		} else {
			feeAmountStr := fmt.Sprintf("%v", amountValue)
			feeRate = fmt.Sprintf("固定 %s %s", feeAmountStr, token)
		}
	case "rate":
		// 获取费率
		rateValue, err := config.GetMapKey(ctx, "withdrawals_setting.fee_rate", tokenSymbol, "0")
		if err != nil {
			feeRate = "比例手续费"
		} else {
			rateStr := fmt.Sprintf("%v", rateValue)
			feeRate = fmt.Sprintf("%s%%", rateStr)
		}
	default:
		feeRate = "动态费率"
	}

	// 获取最小和最大提现限额（使用实际配置）
	minAmountValue, err := config.GetMapKey(ctx, "withdrawals_setting.min_single_amount", tokenSymbol, "0.000001")
	if err != nil {
		g.Log().Warningf(ctx, "获取最小限额失败: %v，使用默认值", err)
		minAmountValue = "0.000001"
	}
	minAmount = fmt.Sprintf("%v", minAmountValue)

	maxAmountValue, err := config.GetMapKey(ctx, "withdrawals_setting.max_single_amount", tokenSymbol, "1000000")
	if err != nil {
		g.Log().Warningf(ctx, "获取最大限额失败: %v，使用默认值", err)
		maxAmountValue = "1000000"
	}
	maxAmount = fmt.Sprintf("%v", maxAmountValue)

	return minAmount, maxAmount, feeRate
}

// 全局服务实例
var withdrawFeeServiceInstance IWithdrawFeeService

// GetWithdrawFeeService 获取提现手续费服务实例
func GetWithdrawFeeService() IWithdrawFeeService {
	if withdrawFeeServiceInstance == nil {
		withdrawFeeServiceInstance = NewWithdrawFeeService()
	}
	return withdrawFeeServiceInstance
}
