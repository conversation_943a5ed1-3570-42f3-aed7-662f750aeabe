package service

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"admin-api/internal/dao"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CallbackSender 回调发送服务
type CallbackSender struct {
	client *http.Client
	signer *CallbackSigner
}

// NewCallbackSender 创建新的回调发送服务
func NewCallbackSender() *CallbackSender {
	config := GetCallbackConfig()

	// 根据配置创建HTTP客户端
	client := &http.Client{
		Timeout: config.Timeout,
	}

	// 如果禁用SSL验证
	if !config.VerifySSL {
		transport := &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		}
		client.Transport = transport
	}

	return &CallbackSender{
		client: client,
		signer: NewCallbackSigner(),
	}
}

// CallbackRequest 回调请求结构
type CallbackRequest struct {
	URL        string      `json:"url"`
	Method     string      `json:"method"`
	Body       interface{} `json:"body"`
	APIKey     string      `json:"api_key"`
	SecretSalt string      `json:"secret_salt"`
	EventType  string      `json:"event_type"` // 事件类型
}

// CallbackResponse 回调响应结构
type CallbackResponse struct {
	StatusCode   int           `json:"status_code"`
	Body         string        `json:"body"`
	ResponseTime time.Duration `json:"response_time"`
	Error        string        `json:"error,omitempty"`
	Success      bool          `json:"success"`
}

// SendCallback 发送回调请求
func (s *CallbackSender) SendCallback(ctx context.Context, req *CallbackRequest) *CallbackResponse {
	startTime := time.Now()

	// 序列化请求体
	var jsonData []byte
	var err error
	if req.Body != nil {
		jsonData, err = json.Marshal(req.Body)
		if err != nil {
			return &CallbackResponse{
				Success:      false,
				Error:        fmt.Sprintf("JSON序列化失败: %v", err),
				ResponseTime: time.Since(startTime),
			}
		}
	}

	body := string(jsonData)

	// 解析URL以获取路径
	parsedURL, err := url.Parse(req.URL)
	if err != nil {
		return &CallbackResponse{
			Success:      false,
			Error:        fmt.Sprintf("URL解析失败: %v", err),
			ResponseTime: time.Since(startTime),
		}
	}

	// 生成签名头信息
	headers := s.signer.GenerateCallbackHeaders(req.Method, parsedURL.Path, body, req.APIKey, req.SecretSalt)

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, req.Method, req.URL, bytes.NewReader(jsonData))
	if err != nil {
		return &CallbackResponse{
			Success:      false,
			Error:        fmt.Sprintf("创建请求失败: %v", err),
			ResponseTime: time.Since(startTime),
		}
	}

	// 设置请求头
	for key, value := range headers {
		httpReq.Header.Set(key, value)
	}

	// 添加事件类型头（如果提供）
	if req.EventType != "" {
		httpReq.Header.Set("X-Webhook-Event", req.EventType)
	}

	// 设置User-Agent
	httpReq.Header.Set("User-Agent", GetUserAgent())

	// 记录请求信息
	g.Log().Infof(ctx, "发送回调请求到: %s, 方法: %s", req.URL, req.Method)
	g.Log().Debugf(ctx, "回调请求体: %s", body)

	// 发送请求
	resp, err := s.client.Do(httpReq)
	if err != nil {
		errorMsg := fmt.Sprintf("请求发送失败: %v", err)
		g.Log().Errorf(ctx, errorMsg)
		return &CallbackResponse{
			Success:      false,
			Error:        errorMsg,
			ResponseTime: time.Since(startTime),
		}
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		errorMsg := fmt.Sprintf("读取响应失败: %v", err)
		g.Log().Errorf(ctx, errorMsg)
		return &CallbackResponse{
			StatusCode:   resp.StatusCode,
			Success:      false,
			Error:        errorMsg,
			ResponseTime: time.Since(startTime),
		}
	}

	responseTime := time.Since(startTime)
	responseBodyStr := string(responseBody)

	// 判断是否成功
	success := resp.StatusCode >= 200 && resp.StatusCode < 300

	// 记录响应信息
	if success {
		g.Log().Infof(ctx, "回调成功: URL=%s, 状态码=%d, 响应时间=%v", req.URL, resp.StatusCode, responseTime)
	} else {
		g.Log().Warningf(ctx, "回调失败: URL=%s, 状态码=%d, 响应=%s, 响应时间=%v",
			req.URL, resp.StatusCode, responseBodyStr, responseTime)
	}

	return &CallbackResponse{
		StatusCode:   resp.StatusCode,
		Body:         responseBodyStr,
		ResponseTime: responseTime,
		Success:      success,
	}
}

// ValidateCallbackURL 验证回调URL是否有效
func (s *CallbackSender) ValidateCallbackURL(callbackURL string) error {
	if callbackURL == "" {
		return fmt.Errorf("回调URL不能为空")
	}

	parsedURL, err := url.Parse(callbackURL)
	if err != nil {
		return fmt.Errorf("回调URL格式无效: %v", err)
	}

	if parsedURL.Scheme != "http" && parsedURL.Scheme != "https" {
		return fmt.Errorf("回调URL必须使用HTTP或HTTPS协议")
	}

	if parsedURL.Host == "" {
		return fmt.Errorf("回调URL必须包含有效的主机名")
	}

	// 检查是否强制使用HTTPS
	if ShouldForceHTTPS() && parsedURL.Scheme != "https" {
		return fmt.Errorf("配置要求强制使用HTTPS协议，但回调URL使用了: %s", parsedURL.Scheme)
	}

	// 在非强制HTTPS模式下警告使用HTTP
	if parsedURL.Scheme == "http" && !ShouldForceHTTPS() {
		g.Log().Warning(context.Background(), "回调URL使用HTTP协议，建议使用HTTPS: %s", callbackURL)
	}

	return nil
}

// RetryCallback 重试回调
func (s *CallbackSender) RetryCallback(ctx context.Context, callback *entity.MerchantCallbacks) (bool, error) {
	// 构建回调请求
	var payload map[string]interface{}
	if err := json.Unmarshal([]byte(callback.Payload), &payload); err != nil {
		return false, fmt.Errorf("解析回调数据失败: %v", err)
	}

	// 从merchant获取webhook secret
	merchant, err := dao.Merchants.GetMerchantById(ctx, uint(callback.MerchantId))
	if err != nil || merchant == nil {
		return false, fmt.Errorf("获取商户信息失败")
	}

	// 获取商户的active API key
	apiKeys, err := dao.MerchantApiKeys.GetApiKeysByMerchantId(ctx, uint(callback.MerchantId))
	if err != nil || len(apiKeys) == 0 {
		return false, fmt.Errorf("获取商户API密钥失败")
	}

	// 查找第一个active的API key
	var activeApiKey *entity.MerchantApiKeys
	for _, key := range apiKeys {
		if key.Status == "active" {
			activeApiKey = key
			break
		}
	}

	if activeApiKey == nil {
		return false, fmt.Errorf("商户没有可用的API密钥")
	}

	// 构建回调请求
	req := &CallbackRequest{
		URL:        callback.CallbackUrl,
		Method:     "POST",
		Body:       payload,
		APIKey:     activeApiKey.ApiKey,
		SecretSalt: merchant.WebhookSecret,
		EventType:  callback.CallbackType,
	}

	// 发送回调
	resp := s.SendCallback(ctx, req)

	// 更新回调记录
	updateData := g.Map{
		"retry_count":     callback.RetryCount + 1,
		"response_code":   resp.StatusCode,
		"response_body":   resp.Body,
		"last_attempt_at": gtime.Now(),
	}

	if resp.Success {
		updateData["status"] = "success"
	} else {
		updateData["status"] = "failed"
		if resp.Error != "" {
			updateData["response_body"] = resp.Error
		}
	}

	// 更新数据库记录
	_, err = dao.MerchantCallbacks.DB().Model(dao.MerchantCallbacks.Table()).
		Where("id", callback.Id).
		Data(updateData).
		Update()

	if err != nil {
		return false, fmt.Errorf("更新回调记录失败: %v", err)
	}

	return resp.Success, nil
}

// PrepareCallbackPayload 准备回调数据载荷
func (s *CallbackSender) PrepareCallbackPayload(eventType string, data interface{}) (map[string]interface{}, error) {
	payload := map[string]interface{}{
		"event_type": eventType,
		"timestamp":  time.Now().Unix(),
		"data":       data,
	}

	return payload, nil
}

// TestCallback 测试回调连接（用于商户配置验证）
func (s *CallbackSender) TestCallback(ctx context.Context, callbackURL, apiKey, secretSalt string) *CallbackResponse {
	// 验证URL
	if err := s.ValidateCallbackURL(callbackURL); err != nil {
		return &CallbackResponse{
			Success: false,
			Error:   err.Error(),
		}
	}

	// 准备测试数据
	testData := map[string]interface{}{
		"event_type": "test",
		"message":    "This is a test callback",
		"timestamp":  time.Now().Unix(),
	}

	req := &CallbackRequest{
		URL:        callbackURL,
		Method:     "POST",
		Body:       testData,
		APIKey:     apiKey,
		SecretSalt: secretSalt,
	}

	return s.SendCallback(ctx, req)
}
