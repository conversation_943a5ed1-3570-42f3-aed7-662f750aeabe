package service

import (
	"admin-api/internal/constants"
	"crypto/rand"
	"fmt"
	"time"
)

// CallbackMockDataGenerator 回调Mock数据生成器
type CallbackMockDataGenerator struct{}

// NewCallbackMockDataGenerator 创建新的Mock数据生成器
func NewCallbackMockDataGenerator() *CallbackMockDataGenerator {
	return &CallbackMockDataGenerator{}
}

// MockWithdrawSuccessData 提现成功Mock数据结构
type MockWithdrawSuccessData struct {
	EventType    string `json:"event_type"`
	OrderNo      string `json:"order_no"`
	MerchantId   uint64 `json:"merchant_id"`
	Amount       string `json:"amount"`
	Currency     string `json:"currency"`
	ActualAmount string `json:"actual_amount"`
	HandlingFee  string `json:"handling_fee"`
	TxHash       string `json:"tx_hash"`
	CompletedAt  string `json:"completed_at"`
	Timestamp    int64  `json:"timestamp"`
}

// MockDepositSuccessData 充值成功Mock数据结构
type MockDepositSuccessData struct {
	EventType     string `json:"event_type"`
	OrderNo       string `json:"order_no"`
	MerchantId    uint64 `json:"merchant_id"`
	Amount        string `json:"amount"`
	Currency      string `json:"currency"`
	FromAddress   string `json:"from_address"`
	ToAddress     string `json:"to_address"`
	TxHash        string `json:"tx_hash"`
	Confirmations int    `json:"confirmations"`
	CompletedAt   string `json:"completed_at"`
	Timestamp     int64  `json:"timestamp"`
}

// GenerateMockData 生成Mock回调数据
func (g *CallbackMockDataGenerator) GenerateMockData(callbackType string, merchantId uint64, customData interface{}) interface{} {
	// 如果提供了自定义数据，使用自定义数据
	if customData != nil {
		return customData
	}

	// 根据回调类型生成标准Mock数据
	switch callbackType {
	case constants.CallbackEventWithdrawCompleted:
		return g.generateWithdrawSuccessData(merchantId)
	case constants.CallbackEventDepositConfirmed:
		return g.generateDepositSuccessData(merchantId)
	default:
		return nil
	}
}

// generateWithdrawSuccessData 生成提现成功测试数据
func (g *CallbackMockDataGenerator) generateWithdrawSuccessData(merchantId uint64) *MockWithdrawSuccessData {
	now := time.Now()
	return &MockWithdrawSuccessData{
		EventType:    constants.CallbackEventWithdrawCompleted,
		OrderNo:      fmt.Sprintf("TEST_WD_%d_%d", merchantId, now.Unix()),
		MerchantId:   merchantId,
		Amount:       "100.00",
		Currency:     "USDT",
		ActualAmount: "99.00",
		HandlingFee:  "1.00",
		TxHash:       "0x" + g.generateRandomHash(64),
		CompletedAt:  now.Format(time.RFC3339),
		Timestamp:    now.Unix(),
	}
}

// generateDepositSuccessData 生成充值成功测试数据
func (g *CallbackMockDataGenerator) generateDepositSuccessData(merchantId uint64) *MockDepositSuccessData {
	now := time.Now()
	return &MockDepositSuccessData{
		EventType:     constants.CallbackEventDepositConfirmed,
		OrderNo:       fmt.Sprintf("TEST_DP_%d_%d", merchantId, now.Unix()),
		MerchantId:    merchantId,
		Amount:        "200.00",
		Currency:      "USDT",
		FromAddress:   "0x" + g.generateRandomHash(40),
		ToAddress:     "0x" + g.generateRandomHash(40),
		TxHash:        "0x" + g.generateRandomHash(64),
		Confirmations: 12,
		CompletedAt:   now.Format(time.RFC3339),
		Timestamp:     now.Unix(),
	}
}

// generateRandomHash 生成随机哈希字符串
func (g *CallbackMockDataGenerator) generateRandomHash(length int) string {
	const charset = "0123456789abcdef"
	result := make([]byte, length)
	randomBytes := make([]byte, length)

	rand.Read(randomBytes)
	for i := range result {
		result[i] = charset[randomBytes[i]%16]
	}

	return string(result)
}

// GenerateCustomWithdrawData 生成自定义提现测试数据
func (g *CallbackMockDataGenerator) GenerateCustomWithdrawData(merchantId uint64, options *WithdrawMockOptions) *MockWithdrawSuccessData {
	data := g.generateWithdrawSuccessData(merchantId)

	if options != nil {
		if options.Amount != "" {
			data.Amount = options.Amount
		}
		if options.Currency != "" {
			data.Currency = options.Currency
		}
		if options.ActualAmount != "" {
			data.ActualAmount = options.ActualAmount
		}
		if options.HandlingFee != "" {
			data.HandlingFee = options.HandlingFee
		}
		if options.OrderNo != "" {
			data.OrderNo = options.OrderNo
		}
		if options.TxHash != "" {
			data.TxHash = options.TxHash
		}
	}

	return data
}

// GenerateCustomDepositData 生成自定义充值测试数据
func (g *CallbackMockDataGenerator) GenerateCustomDepositData(merchantId uint64, options *DepositMockOptions) *MockDepositSuccessData {
	data := g.generateDepositSuccessData(merchantId)

	if options != nil {
		if options.Amount != "" {
			data.Amount = options.Amount
		}
		if options.Currency != "" {
			data.Currency = options.Currency
		}
		if options.FromAddress != "" {
			data.FromAddress = options.FromAddress
		}
		if options.ToAddress != "" {
			data.ToAddress = options.ToAddress
		}
		if options.OrderNo != "" {
			data.OrderNo = options.OrderNo
		}
		if options.TxHash != "" {
			data.TxHash = options.TxHash
		}
		if options.Confirmations > 0 {
			data.Confirmations = options.Confirmations
		}
	}

	return data
}

// WithdrawMockOptions 提现Mock数据选项
type WithdrawMockOptions struct {
	Amount       string `json:"amount,omitempty"`
	Currency     string `json:"currency,omitempty"`
	ActualAmount string `json:"actualAmount,omitempty"`
	HandlingFee  string `json:"handlingFee,omitempty"`
	OrderNo      string `json:"orderNo,omitempty"`
	TxHash       string `json:"txHash,omitempty"`
}

// DepositMockOptions 充值Mock数据选项
type DepositMockOptions struct {
	Amount        string `json:"amount,omitempty"`
	Currency      string `json:"currency,omitempty"`
	FromAddress   string `json:"fromAddress,omitempty"`
	ToAddress     string `json:"toAddress,omitempty"`
	OrderNo       string `json:"orderNo,omitempty"`
	TxHash        string `json:"txHash,omitempty"`
	Confirmations int    `json:"confirmations,omitempty"`
}

// GetMockDataTemplate 获取Mock数据模板（用于前端展示）
func (g *CallbackMockDataGenerator) GetMockDataTemplate(callbackType string) interface{} {
	switch callbackType {
	case constants.CallbackEventWithdrawCompleted:
		return &MockWithdrawSuccessData{
			EventType:    constants.CallbackEventWithdrawCompleted,
			OrderNo:      "TEST_WD_12345_1640995200",
			MerchantId:   12345,
			Amount:       "100.00",
			Currency:     "USDT",
			ActualAmount: "99.00",
			HandlingFee:  "1.00",
			TxHash:       "0x1234567890abcdef...",
			CompletedAt:  "2024-06-29T10:30:00Z",
			Timestamp:    1640995200,
		}
	case constants.CallbackEventDepositConfirmed:
		return &MockDepositSuccessData{
			EventType:     constants.CallbackEventDepositConfirmed,
			OrderNo:       "TEST_DP_12345_1640995200",
			MerchantId:    12345,
			Amount:        "200.00",
			Currency:      "USDT",
			FromAddress:   "0xabcdef1234567890...",
			ToAddress:     "0x0987654321fedcba...",
			TxHash:        "0x1234567890abcdef...",
			Confirmations: 12,
			CompletedAt:   "2024-06-29T10:30:00Z",
			Timestamp:     1640995200,
		}
	default:
		return nil
	}
}

// ValidateMockData 验证自定义Mock数据格式
func (g *CallbackMockDataGenerator) ValidateMockData(callbackType string, data interface{}) error {
	switch callbackType {
	case constants.CallbackEventWithdrawCompleted:
		return g.validateWithdrawData(data)
	case constants.CallbackEventDepositConfirmed:
		return g.validateDepositData(data)
	default:
		return fmt.Errorf("不支持的回调类型: %s", callbackType)
	}
}

// validateWithdrawData 验证提现数据格式
func (g *CallbackMockDataGenerator) validateWithdrawData(data interface{}) error {
	// 这里可以添加数据格式验证逻辑
	// 检查必要字段是否存在，格式是否正确等
	return nil
}

// validateDepositData 验证充值数据格式
func (g *CallbackMockDataGenerator) validateDepositData(data interface{}) error {
	// 这里可以添加数据格式验证逻辑
	// 检查必要字段是否存在，格式是否正确等
	return nil
}
