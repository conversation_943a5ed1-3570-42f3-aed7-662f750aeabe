// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	v1 "admin-api/api/system/v1"
	"context"
)

type (
	IExchangeOrder interface {
		// 兑换订单管理
		GetOrderList(ctx context.Context, req *v1.ExchangeOrderListReq) (*v1.ExchangeOrderListRes, error)
		GetOrderDetail(ctx context.Context, req *v1.ExchangeOrderDetailReq) (*v1.ExchangeOrderDetailRes, error)
	}
)

var (
	localExchangeOrder IExchangeOrder
)

func ExchangeOrder() IExchangeOrder {
	if localExchangeOrder == nil {
		panic("implement not found for interface IExchangeOrder, forgot register?")
	}
	return localExchangeOrder
}

func RegisterExchangeOrder(i IExchangeOrder) {
	localExchangeOrder = i
}
