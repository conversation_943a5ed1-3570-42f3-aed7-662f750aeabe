package service

import (
	"admin-api/internal/model/entity"
	"bytes"
	"context"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

// CallbackTestService 回调测试服务
type CallbackTestService struct {
	signer *CallbackSigner
	client *http.Client
}

// NewCallbackTestService 创建新的回调测试服务
func NewCallbackTestService() *CallbackTestService {
	return &CallbackTestService{
		signer: NewCallbackSigner(),
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// CallbackTestInfo 回调测试信息
type CallbackTestInfo struct {
	// 请求信息
	RequestURL     string            `json:"requestUrl"`
	RequestMethod  string            `json:"requestMethod"`
	RequestHeaders map[string]string `json:"requestHeaders"`
	RequestBody    string            `json:"requestBody"`

	// 签名调试信息
	SignatureInfo *SignatureDebugInfo `json:"signatureInfo"`

	// 响应信息
	ResponseStatus int    `json:"responseStatus"`
	ResponseBody   string `json:"responseBody"`
	ResponseTime   string `json:"responseTime"`

	// 测试结果
	Success      bool   `json:"success"`
	ErrorMessage string `json:"errorMessage,omitempty"`

	// 测试时间
	TestedAt time.Time `json:"testedAt"`
}

// SignatureDebugInfo 签名调试信息
type SignatureDebugInfo struct {
	Timestamp  string `json:"timestamp"`
	Nonce      string `json:"nonce"`
	SignString string `json:"signString"`
	Signature  string `json:"signature"`
	APIKey     string `json:"apiKey"`
	SecretSalt string `json:"secretSalt,omitempty"`
}

// TestCallback 执行回调测试
func (s *CallbackTestService) TestCallback(ctx context.Context, merchant *entity.Merchants, callbackType string, mockData interface{}) *CallbackTestInfo {
	startTime := time.Now()

	// 1. 序列化mock数据
	requestBody, err := json.Marshal(mockData)
	if err != nil {
		return &CallbackTestInfo{
			Success:      false,
			ErrorMessage: fmt.Sprintf("序列化测试数据失败: %v", err),
			TestedAt:     startTime,
		}
	}
	bodyStr := string(requestBody)

	// 2. 验证和解析回调URL
	if merchant.CallbackUrl == "" {
		return &CallbackTestInfo{
			Success:      false,
			ErrorMessage: "商户未配置回调URL",
			TestedAt:     startTime,
		}
	}

	parsedURL, err := url.Parse(merchant.CallbackUrl)
	if err != nil {
		return &CallbackTestInfo{
			Success:      false,
			ErrorMessage: fmt.Sprintf("回调URL格式无效: %v", err),
			TestedAt:     startTime,
		}
	}

	// 3. 生成签名信息
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := s.generateNonce()
	uriPath := parsedURL.Path
	if uriPath == "" {
		uriPath = "/"
	}

	// 构建签名字符串: method + uri + timestamp + nonce + body
	signString := "POST" + uriPath + timestamp + nonce + bodyStr
	signature := s.signer.GenerateSignature("POST", uriPath, timestamp, nonce, bodyStr, merchant.WebhookSecret)

	// 4. 构建请求头
	headers := map[string]string{
		"Content-Type": "application/json",
		"X-API-Key":    merchant.WebhookSecret, // 使用商户的WebhookSecret作为API Key
		"X-Timestamp":  timestamp,
		"X-Nonce":      nonce,
		"X-Signature":  signature,
		"User-Agent":   GetUserAgent(),
	}

	// 5. 准备签名调试信息
	signatureInfo := &SignatureDebugInfo{
		Timestamp:  timestamp,
		Nonce:      nonce,
		SignString: signString,
		Signature:  signature,
		APIKey:     merchant.WebhookSecret,
		SecretSalt: merchant.WebhookSecret, // 在调试信息中包含，帮助商户调试
	}

	// 6. 发送测试请求
	response := s.sendTestRequest(ctx, merchant.CallbackUrl, requestBody, headers)
	responseTime := time.Since(startTime)

	// 7. 构建完整的测试信息
	testInfo := &CallbackTestInfo{
		RequestURL:     merchant.CallbackUrl,
		RequestMethod:  "POST",
		RequestHeaders: headers,
		RequestBody:    bodyStr,
		SignatureInfo:  signatureInfo,
		ResponseStatus: response.StatusCode,
		ResponseBody:   response.Body,
		ResponseTime:   responseTime.String(),
		Success:        response.Success,
		ErrorMessage:   response.Error,
		TestedAt:       startTime,
	}

	// 8. 记录测试日志
	s.logTestResult(ctx, merchant.MerchantId, callbackType, testInfo)

	return testInfo
}

// TestCallbackResponse 测试回调响应
type TestCallbackResponse struct {
	StatusCode int
	Body       string
	Success    bool
	Error      string
}

// sendTestRequest 发送测试请求
func (s *CallbackTestService) sendTestRequest(ctx context.Context, url string, body []byte, headers map[string]string) *TestCallbackResponse {
	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewReader(body))
	if err != nil {
		return &TestCallbackResponse{
			Success: false,
			Error:   fmt.Sprintf("创建请求失败: %v", err),
		}
	}

	// 设置请求头
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// 发送请求
	resp, err := s.client.Do(req)
	if err != nil {
		return &TestCallbackResponse{
			Success: false,
			Error:   fmt.Sprintf("发送请求失败: %v", err),
		}
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return &TestCallbackResponse{
			StatusCode: resp.StatusCode,
			Success:    false,
			Error:      fmt.Sprintf("读取响应失败: %v", err),
		}
	}

	// 判断成功状态
	success := resp.StatusCode >= 200 && resp.StatusCode < 300

	return &TestCallbackResponse{
		StatusCode: resp.StatusCode,
		Body:       string(responseBody),
		Success:    success,
	}
}

// generateNonce 生成随机nonce
func (s *CallbackTestService) generateNonce() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return fmt.Sprintf("%x", bytes)
}

// logTestResult 记录测试结果
func (s *CallbackTestService) logTestResult(ctx context.Context, merchantId uint64, callbackType string, testInfo *CallbackTestInfo) {
	// 记录测试日志，但不包含敏感信息
	logData := map[string]interface{}{
		"merchant_id":     merchantId,
		"callback_type":   callbackType,
		"callback_url":    testInfo.RequestURL,
		"response_status": testInfo.ResponseStatus,
		"success":         testInfo.Success,
		"response_time":   testInfo.ResponseTime,
		"tested_at":       testInfo.TestedAt,
	}

	if testInfo.ErrorMessage != "" {
		logData["error"] = testInfo.ErrorMessage
	}

	if testInfo.Success {
		g.Log().Infof(ctx, "回调测试成功: %+v", logData)
	} else {
		g.Log().Warningf(ctx, "回调测试失败: %+v", logData)
	}
}

// ValidateCallbackURL 验证回调URL格式
func (s *CallbackTestService) ValidateCallbackURL(callbackURL string) error {
	if callbackURL == "" {
		return fmt.Errorf("回调URL不能为空")
	}

	parsedURL, err := url.Parse(callbackURL)
	if err != nil {
		return fmt.Errorf("回调URL格式无效: %v", err)
	}

	if parsedURL.Scheme != "http" && parsedURL.Scheme != "https" {
		return fmt.Errorf("回调URL必须使用HTTP或HTTPS协议")
	}

	if parsedURL.Host == "" {
		return fmt.Errorf("回调URL必须包含有效的主机名")
	}

	// 检查是否强制使用HTTPS
	if ShouldForceHTTPS() && parsedURL.Scheme != "https" {
		return fmt.Errorf("配置要求强制使用HTTPS协议")
	}

	return nil
}

// GetTestResultMessage 获取测试结果消息
func (s *CallbackTestService) GetTestResultMessage(testInfo *CallbackTestInfo) string {
	if testInfo.Success {
		return fmt.Sprintf("回调测试成功! 响应状态码: %d, 响应时间: %s",
			testInfo.ResponseStatus, testInfo.ResponseTime)
	}

	if testInfo.ErrorMessage != "" {
		return fmt.Sprintf("回调测试失败: %s", testInfo.ErrorMessage)
	}

	return fmt.Sprintf("回调测试失败: HTTP状态码 %d", testInfo.ResponseStatus)
}
