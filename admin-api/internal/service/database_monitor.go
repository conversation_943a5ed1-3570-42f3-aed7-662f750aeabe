package service

import (
	"context"
	"sync"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DatabaseMonitor 数据库连接监控器
type DatabaseMonitor struct {
	mu                sync.RWMutex
	lastHealthCheck   time.Time
	connectionStats   *ConnectionStats
	healthCheckTicker *time.Ticker
	ctx               context.Context
	cancel            context.CancelFunc
}

// ConnectionStats 连接统计信息
type ConnectionStats struct {
	OpenConnections    int         `json:"open_connections"`
	IdleConnections    int         `json:"idle_connections"`
	InUseConnections   int         `json:"in_use_connections"`
	MaxOpenConnections int         `json:"max_open_connections"`
	LastError          string      `json:"last_error,omitempty"`
	LastErrorTime      *gtime.Time `json:"last_error_time,omitempty"`
}

var (
	dbMonitor     *DatabaseMonitor
	dbMonitorOnce sync.Once
)

// GetDatabaseMonitor 获取数据库监控器单例
func GetDatabaseMonitor() *DatabaseMonitor {
	dbMonitorOnce.Do(func() {
		ctx, cancel := context.WithCancel(context.Background())
		dbMonitor = &DatabaseMonitor{
			ctx:             ctx,
			cancel:          cancel,
			connectionStats: &ConnectionStats{},
		}
		dbMonitor.startHealthCheck()
	})
	return dbMonitor
}

// startHealthCheck 启动健康检查
func (dm *DatabaseMonitor) startHealthCheck() {
	dm.healthCheckTicker = time.NewTicker(30 * time.Second)

	go func() {
		defer dm.healthCheckTicker.Stop()

		for {
			select {
			case <-dm.ctx.Done():
				return
			case <-dm.healthCheckTicker.C:
				dm.checkDatabaseHealth()
			}
		}
	}()
}

// checkDatabaseHealth 检查数据库健康状态
func (dm *DatabaseMonitor) checkDatabaseHealth() {
	dm.mu.Lock()
	defer dm.mu.Unlock()

	ctx := context.Background()
	db := g.DB()

	// 获取连接池统计信息 (简化版本，记录基本信息)
	// 注意：GoFrame v2 的连接池统计获取方式可能不同，这里使用基本监控
	dm.connectionStats.OpenConnections = 0 // 将在后续版本中实现详细统计
	dm.connectionStats.IdleConnections = 0
	dm.connectionStats.InUseConnections = 0
	dm.connectionStats.MaxOpenConnections = 200 // 从配置中获取

	// 记录基本监控信息
	g.Log().Debugf(ctx, "[DatabaseMonitor] Health check performed at %v", time.Now())

	// 执行简单的健康检查查询
	if err := dm.performHealthCheck(ctx, db); err != nil {
		dm.connectionStats.LastError = err.Error()
		dm.connectionStats.LastErrorTime = gtime.Now()
		g.Log().Errorf(ctx, "[DatabaseMonitor] Health check failed: %v", err)
	} else {
		dm.connectionStats.LastError = ""
		dm.connectionStats.LastErrorTime = nil
	}

	dm.lastHealthCheck = time.Now()
}

// performHealthCheck 执行数据库健康检查
func (dm *DatabaseMonitor) performHealthCheck(ctx context.Context, db gdb.DB) error {
	// 执行简单的查询来测试连接
	_, err := db.Ctx(ctx).GetValue(ctx, "SELECT 1")
	return err
}

// GetConnectionStats 获取连接统计信息
func (dm *DatabaseMonitor) GetConnectionStats() *ConnectionStats {
	dm.mu.RLock()
	defer dm.mu.RUnlock()

	// 返回副本以避免并发访问问题
	stats := *dm.connectionStats
	return &stats
}

// IsHealthy 检查数据库是否健康
func (dm *DatabaseMonitor) IsHealthy() bool {
	dm.mu.RLock()
	defer dm.mu.RUnlock()

	// 如果最近5分钟内没有错误，认为是健康的
	if dm.connectionStats.LastErrorTime != nil {
		return time.Since(dm.connectionStats.LastErrorTime.Time) > 5*time.Minute
	}

	return dm.connectionStats.LastError == ""
}

// WaitForHealthy 等待数据库恢复健康状态
func (dm *DatabaseMonitor) WaitForHealthy(ctx context.Context, timeout time.Duration) error {
	deadline := time.Now().Add(timeout)

	for time.Now().Before(deadline) {
		if dm.IsHealthy() {
			return nil
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(1 * time.Second):
			// 继续等待
		}
	}

	return context.DeadlineExceeded
}

// LogConnectionStats 记录连接统计信息
func (dm *DatabaseMonitor) LogConnectionStats(ctx context.Context) {
	stats := dm.GetConnectionStats()
	g.Log().Infof(ctx, "[DatabaseMonitor] Current stats: Open=%d, Idle=%d, InUse=%d, Max=%d",
		stats.OpenConnections, stats.IdleConnections, stats.InUseConnections, stats.MaxOpenConnections)
}

// Stop 停止监控器
func (dm *DatabaseMonitor) Stop() {
	if dm.cancel != nil {
		dm.cancel()
	}
	if dm.healthCheckTicker != nil {
		dm.healthCheckTicker.Stop()
	}
}

// WithDatabaseMonitoring 为数据库操作添加监控装饰器
func WithDatabaseMonitoring(ctx context.Context, operation string, fn func() error) error {
	monitor := GetDatabaseMonitor()

	// 记录操作开始
	start := time.Now()
	g.Log().Debugf(ctx, "[DatabaseMonitor] Starting operation: %s", operation)

	// 检查数据库健康状态
	if !monitor.IsHealthy() {
		g.Log().Warningf(ctx, "[DatabaseMonitor] Database unhealthy before operation: %s", operation)

		// 等待数据库恢复（最多等待10秒）
		if err := monitor.WaitForHealthy(ctx, 10*time.Second); err != nil {
			return err
		}
	}

	// 执行操作
	err := fn()

	// 记录操作结果
	duration := time.Since(start)
	if err != nil {
		g.Log().Errorf(ctx, "[DatabaseMonitor] Operation failed: %s, duration: %v, error: %v",
			operation, duration, err)
	} else {
		g.Log().Debugf(ctx, "[DatabaseMonitor] Operation completed: %s, duration: %v",
			operation, duration)
	}

	return err
}

// GetHealthStatus 获取数据库健康状态报告
func (dm *DatabaseMonitor) GetHealthStatus() map[string]any {
	dm.mu.RLock()
	defer dm.mu.RUnlock()

	stats := dm.GetConnectionStats()

	return map[string]any{
		"healthy":           dm.IsHealthy(),
		"last_health_check": dm.lastHealthCheck,
		"connection_stats":  stats,
		"last_error":        stats.LastError,
		"last_error_time":   stats.LastErrorTime,
	}
}
