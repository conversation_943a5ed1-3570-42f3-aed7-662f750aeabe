// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	v1 "admin-api/api/system/v1"
	"context"
)

type (
	IExchange interface {
		// 兑换产品管理
		GetProductList(ctx context.Context, req *v1.ExchangeProductListReq) (*v1.ExchangeProductListRes, error)
		GetProductDetail(ctx context.Context, req *v1.ExchangeProductDetailReq) (*v1.ExchangeProductDetailRes, error)
		CreateProduct(ctx context.Context, req *v1.ExchangeProductCreateReq) (*v1.ExchangeProductCreateRes, error)
		UpdateProduct(ctx context.Context, req *v1.ExchangeProductUpdateReq) (*v1.ExchangeProductUpdateRes, error)
		DeleteProduct(ctx context.Context, req *v1.ExchangeProductDeleteReq) (*v1.ExchangeProductDeleteRes, error)
		UpdateProductStatus(ctx context.Context, req *v1.ExchangeProductStatusReq) (*v1.ExchangeProductStatusRes, error)
		GetProductVolume(ctx context.Context, req *v1.ExchangeProductVolumeReq) (*v1.ExchangeProductVolumeRes, error)
	}
)

var (
	localExchange IExchange
)

func Exchange() IExchange {
	if localExchange == nil {
		panic("implement not found for interface IExchange, forgot register?")
	}
	return localExchange
}

func RegisterExchange(i IExchange) {
	localExchange = i
}
