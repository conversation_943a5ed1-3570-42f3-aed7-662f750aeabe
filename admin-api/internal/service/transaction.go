package service

import (
	"admin-api/internal/dao"          // 导入 dao 包
	"admin-api/internal/model/entity" // 导入 entity 包
	"context"
	"strconv" // 导入 strconv 包

	"github.com/a19ba14d/tg-bot-common/consts"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror" // 导入 gerror 包
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// ITransaction 定义交易流水服务接口
type ITransaction interface {
	// Record 记录一笔交易流水
	// tx: 数据库事务对象，如果需要在事务中执行，则传入；否则传入 nil
	// userID: 用户 ID (数据库 users.id)
	// username: 用户 ID (数据库 users.id, 对应 transaction.username)
	// amount: 交易金额 (绝对值)
	// symbol: 代币符号
	// transactionType: 交易类型
	// balanceBefore: 交易前余额
	// referenceID: 关联实体 ID (字符串形式)
	// description: 交易描述
	// 返回: 交易流水 ID 和错误
	Record(ctx context.Context, tx gdb.TX, userID uint, username uint, amount decimal.Decimal, symbol string, transactionType consts.TransactionType, balanceBefore decimal.Decimal, referenceID string, description string) (transactionID int64, err error)
}

type sTransaction struct{}

var insTransaction ITransaction = NewTransaction()

func NewTransaction() *sTransaction {
	return &sTransaction{}
}

// Transaction 返回交易流水服务的单例
func Transaction() ITransaction {
	return insTransaction
}

// Record 记录一笔交易流水
func (s *sTransaction) Record(ctx context.Context, tx gdb.TX, userID uint, username uint, amount decimal.Decimal, symbol string, transactionType consts.TransactionType, balanceBefore decimal.Decimal, referenceID string, description string) (transactionID int64, err error) {
	// 1. 获取 Token 信息
	token, err := Token().GetTokenBySymbol(ctx, symbol)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get token info for symbol %s in Transaction().Record: %v", symbol, err)
		return 0, gerror.Wrapf(err, "failed to get token info (symbol: %s)", symbol)
	}
	if token == nil {
		// GetTokenBySymbol 内部已处理 nil 情况并返回错误，理论上这里不会执行，但作为防御性编程保留
		return 0, gerror.Newf("token info not found (symbol: %s)", symbol)
	}
	tokenId := token.TokenId

	// 2. 确定资金方向
	var direction string
	switch transactionType {
	case consts.TransactionTypeWithdraw, consts.TransactionTypeTransferOut, consts.TransactionTypeRedPacketCreate:
		direction = "out" // Use string literal
	case consts.TransactionTypeDeposit, consts.TransactionTypeTransferIn, consts.TransactionTypeRedPacketClaim, consts.TransactionTypeRedPacketRefund:
		direction = "in" // Use string literal
	default:
		g.Log().Errorf(ctx, "Unknown transaction type '%s' encountered when determining direction.", transactionType)
		return 0, gerror.Newf("unknown transaction type, cannot determine direction: %s", transactionType)
	}

	// 3. 计算交易后余额
	var balanceAfter decimal.Decimal
	if direction == "out" { // Use string literal
		balanceAfter = balanceBefore.Sub(amount)
	} else {
		balanceAfter = balanceBefore.Add(amount)
	}

	// 4. 确定关联实体类型
	var relatedEntityType string
	switch transactionType {
	case consts.TransactionTypeWithdraw:
		relatedEntityType = consts.EntityTypeWithdrawal
	case consts.TransactionTypeRedPacketCreate, consts.TransactionTypeRedPacketClaim, consts.TransactionTypeRedPacketRefund:
		relatedEntityType = "red_packet" // 假设红包关联类型
	case consts.TransactionTypeTransferIn, consts.TransactionTypeTransferOut:
		relatedEntityType = "transfer" // 假设转账关联类型
	// TODO: 添加其他交易类型的关联实体类型判断
	default:
		relatedEntityType = "" // 或根据业务逻辑处理
	}

	// 5. 转换关联实体 ID
	var relatedEntityId uint64
	if referenceID != "" {
		relatedEntityId, err = strconv.ParseUint(referenceID, 10, 64)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to parse referenceID '%s' to uint64 in Transaction().Record: %v", referenceID, err)
			return 0, gerror.Wrapf(err, "invalid reference ID format: %s", referenceID)
		}
	}

	// 6. 设置状态 (默认为成功)
	status := uint(1)
	amountFloat64, _ := amount.Float64()
	balanceBeforeFloat64, _ := balanceBefore.Float64()
	balanceAfterFloat64, _ := balanceAfter.Float64()

	// 7. 构造实体对象
	transactionEntity := entity.Transactions{
		UserId:            userID,
		Username:          username, // 使用传入的 uint username
		TokenId:           tokenId,
		Type:              string(transactionType),
		Direction:         direction,
		Amount:            decimal.NewFromFloat(amountFloat64),
		BalanceBefore:     decimal.NewFromFloat(balanceBeforeFloat64),
		BalanceAfter:      decimal.NewFromFloat(balanceAfterFloat64),
		RelatedEntityId:   relatedEntityId,
		RelatedEntityType: relatedEntityType,
		Status:            status,
		Memo:              description,
		Symbol:            symbol, // 添加 symbol 字段
		// CreatedAt, UpdatedAt 由数据库自动填充或 GORM 自动处理
	}

	// 8. 插入数据库
	db := dao.Transactions.Ctx(ctx)
	if tx != nil {
		db = db.TX(tx)
	}

	result, err := db.Data(transactionEntity).Insert()
	if err != nil {
		g.Log().Errorf(ctx, "Failed to insert transaction record: UserID=%d, Type=%s, Amount=%s %s, Error: %v",
			userID, transactionType, amount.String(), symbol, err)
		return 0, gerror.Wrap(err, "failed to record transaction to database")
	}

	transactionID, err = result.LastInsertId()
	if err != nil {
		// 插入成功但获取 ID 失败，记录错误但可能不影响核心流程，取决于业务需求
		g.Log().Errorf(ctx, "Failed to get last insert ID for transaction after successful insert: %v", err)
		// 可以选择返回 0 和 nil，或者返回 0 和这个错误
		return 0, gerror.Wrap(err, "failed to get new transaction ID")
	}

	g.Log().Infof(ctx, "Successfully recorded transaction: ID=%d, UserID=%d, Type=%s, Amount=%s %s, Direction=%s, BalanceBefore=%s, BalanceAfter=%s",
		transactionID, userID, transactionType, amount.String(), symbol, direction, balanceBefore.String(), balanceAfter.String())

	return transactionID, nil
}
