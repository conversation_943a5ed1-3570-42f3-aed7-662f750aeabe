package minio

import (
	"context"
	"fmt"
	"io"
	"net/url"
	"path"
	"strings"

	"admin-api/internal/model" // Assuming your module path is admin-api

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

// Service manages Minio operations.
type Service struct {
	client *minio.Client
	config model.MinioConfig
}

// NewClient initializes and returns a new Minio service client.
// It reads configuration from the global config.
func NewClient(ctx context.Context) (*Service, error) {
	var minioCfg model.MinioConfig
	// Assuming configuration is loaded into the default group or a specific 'minio' group
	// Adjust the Get call based on how you load/structure your config
	err := g.Cfg().MustGet(ctx, "minio").Scan(&minioCfg)
	if err != nil {
		g.Log().Errorf(ctx, "读取Minio配置失败: %v", err)
		return nil, gerror.Wrap(err, "Failed to read minio configuration")
	}

	// 记录配置信息（不包含敏感信息）
	g.Log().Debugf(ctx, "Minio配置: Endpoint=%s, BucketName=%s, UseSSL=%v",
		minioCfg.Endpoint, minioCfg.BucketName, minioCfg.UseSSL)

	if minioCfg.Endpoint == "" || minioCfg.AccessKeyID == "" || minioCfg.SecretAccessKey == "" || minioCfg.BucketName == "" {
		g.Log().Errorf(ctx, "Minio配置不完整: Endpoint=%s, AccessKeyID=%s, SecretAccessKey=****, BucketName=%s",
			minioCfg.Endpoint, minioCfg.AccessKeyID, minioCfg.BucketName)
		return nil, gerror.New("Minio configuration is incomplete (endpoint, accessKeyID, secretAccessKey, bucketName are required)")
	}

	// 处理endpoint中可能存在的http/https前缀
	endpoint := minioCfg.Endpoint
	if strings.HasPrefix(endpoint, "http://") {
		endpoint = strings.TrimPrefix(endpoint, "http://")
		minioCfg.UseSSL = false
	} else if strings.HasPrefix(endpoint, "https://") {
		endpoint = strings.TrimPrefix(endpoint, "https://")
		minioCfg.UseSSL = true
	}

	// Initialize minio client object.
	g.Log().Infof(ctx, "正在初始化Minio客户端, Endpoint=%s, UseSSL=%v", endpoint, minioCfg.UseSSL)
	minioClient, err := minio.New(endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(minioCfg.AccessKeyID, minioCfg.SecretAccessKey, ""),
		Secure: minioCfg.UseSSL,
	})
	if err != nil {
		g.Log().Errorf(ctx, "初始化Minio客户端失败: %v", err)
		return nil, gerror.Wrapf(err, "Failed to initialize Minio client with endpoint: %s", minioCfg.Endpoint)
	}

	// 验证bucket是否存在
	g.Log().Infof(ctx, "正在检查Minio bucket '%s'是否存在", minioCfg.BucketName)
	exists, err := minioClient.BucketExists(ctx, minioCfg.BucketName)
	if err != nil {
		g.Log().Errorf(ctx, "检查Minio bucket '%s'是否存在失败: %v", minioCfg.BucketName, err)
		return nil, gerror.Wrapf(err, "Failed to check if bucket '%s' exists", minioCfg.BucketName)
	}
	if !exists {
		g.Log().Errorf(ctx, "Minio bucket '%s'不存在", minioCfg.BucketName)
		return nil, gerror.Newf("Minio bucket '%s' does not exist", minioCfg.BucketName)
	}

	g.Log().Infof(ctx, "Minio客户端初始化成功")
	return &Service{
		client: minioClient,
		config: minioCfg,
	}, nil
}

// UploadObject uploads a file stream to Minio.
// objectKey: The full path and filename for the object in the bucket (e.g., "avatars/2025-04-06/random-uuid.jpg").
// reader: The file content stream.
// size: The size of the file content.
// contentType: The MIME type of the file (e.g., "image/jpeg").
// Returns the public URL of the uploaded object and an error if any occurred.
func (s *Service) UploadObject(ctx context.Context, objectKey string, reader io.Reader, size int64, contentType string) (string, error) {
	if s.client == nil {
		return "", gerror.New("Minio client is not initialized")
	}

	uploadInfo, err := s.client.PutObject(ctx, s.config.BucketName, objectKey, reader, size, minio.PutObjectOptions{ContentType: contentType})
	if err != nil {
		g.Log().Errorf(ctx, "Failed to upload object '%s' to bucket '%s': %v", objectKey, s.config.BucketName, err)
		return "", gerror.Wrapf(err, "Failed to upload object '%s' to Minio bucket '%s'", objectKey, s.config.BucketName)
	}

	g.Log().Infof(ctx, "Successfully uploaded object '%s' to bucket '%s', ETag: %s, Size: %d", uploadInfo.Key, uploadInfo.Bucket, uploadInfo.ETag, uploadInfo.Size)

	// Construct the public URL
	if s.config.PublicURLPrefix == "" {
		// Attempt to construct URL based on endpoint if prefix is missing, but this might be inaccurate
		// It's better to configure PublicURLPrefix correctly.
		g.Log().Warningf(ctx, "Minio PublicURLPrefix is not configured. Attempting to construct URL, but it might be incorrect.")
		baseURL, parseErr := url.Parse(s.config.Endpoint)
		if parseErr != nil {
			return "", gerror.Wrapf(parseErr, "Failed to parse Minio endpoint '%s' to construct URL", s.config.Endpoint)
		}
		// Ensure scheme is present (http or https based on UseSSL)
		if baseURL.Scheme == "" {
			if s.config.UseSSL {
				baseURL.Scheme = "https"
			} else {
				baseURL.Scheme = "http"
			}
		}
		// Basic joining, might need more robust handling for paths/ports
		publicURL := fmt.Sprintf("%s://%s/%s/%s", baseURL.Scheme, baseURL.Host, s.config.BucketName, objectKey)
		return publicURL, nil

	}

	// Use the configured prefix
	// Ensure the prefix ends with a slash and the object key doesn't start with one.
	prefixURL, err := url.Parse(s.config.PublicURLPrefix)
	if err != nil {
		return "", gerror.Wrapf(err, "Failed to parse configured PublicURLPrefix: %s", s.config.PublicURLPrefix)
	}
	// Use path.Join for cleaner path segment joining (handles slashes)
	prefixURL.Path = path.Join(prefixURL.Path, objectKey)

	return prefixURL.String(), nil
}
