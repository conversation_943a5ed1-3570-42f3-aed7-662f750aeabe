package captcha

// import (
// 	"context"
// 	"encoding/json"
// 	"fmt"
// 	"math/rand"
// 	"time"

// 	"github.com/gogf/gf/v2/crypto/gmd5"
// 	"github.com/gogf/gf/v2/frame/g"
// 	"github.com/gogf/gf/v2/os/gcache"
// 	images "github.com/wenlng/go-captcha-assets/resources/images_v2"
// 	"github.com/wenlng/go-captcha-assets/resources/tiles"
// )

// // 默认缓存时间 (10分钟)
// const defaultCacheExpire = 10 * time.Minute

// // 全局滑动验证码实例
// var slideCaptcha slide.Captcha

// func init() {
// 	builder := slide.NewBuilder()

// 	// 加载背景图片
// 	imgs, err := images.GetImages()
// 	if err != nil {
// 		g.Log().Fatal(context.Background(), "加载验证码背景图失败", err)
// 	}

// 	// 加载滑块图片
// 	graphs, err := tiles.GetTiles()
// 	if err != nil {
// 		g.Log().Fatal(context.Background(), "加载验证码滑块图失败", err)
// 	}

// 	// 转换滑块图片格式
// 	var newGraphs = make([]*slide.GraphImage, 0, len(graphs))
// 	for i := 0; i < len(graphs); i++ {
// 		graph := graphs[i]
// 		newGraphs = append(newGraphs, &slide.GraphImage{
// 			OverlayImage: graph.OverlayImage,
// 			MaskImage:    graph.MaskImage,
// 			ShadowImage:  graph.ShadowImage,
// 		})
// 	}

// 	// 设置资源
// 	builder.SetResources(
// 		slide.WithGraphImages(newGraphs),
// 		slide.WithBackgrounds(imgs),
// 	)

// 	// 创建验证码实例
// 	slideCaptcha = builder.Make()
// }

// // SlideData 滑动验证码数据
// type SlideData struct {
// 	// 主图 Base64
// 	MasterBase64 string `json:"masterBase64" dc:"主图Base64"`
// 	// 滑块图 Base64
// 	TileBase64 string `json:"tileBase64" dc:"滑块图Base64"`
// 	// 验证码 Token
// 	Token string `json:"token" dc:"token"`
// 	// 滑块X坐标
// 	TileX int `json:"tileX" dc:"滑块X坐标"`
// 	// 滑块Y坐标
// 	TileY int `json:"tileY" dc:"滑块Y坐标"`
// 	// 滑块宽度
// 	TileWidth int `json:"tileWidth" dc:"滑块宽度"`
// 	// 滑块高度
// 	TileHeight int `json:"tileHeight" dc:"滑块高度"`
// }

// // GenerateCaptcha 生成滑动验证码
// func GenerateCaptcha(ctx context.Context) (*SlideData, error) {
// 	// 生成验证码
// 	captData, err := slideCaptcha.Generate()
// 	if err != nil {
// 		return nil, err
// 	}

// 	// 获取验证码数据
// 	dotData := captData.GetData()
// 	if dotData == nil {
// 		return nil, fmt.Errorf("生成验证码数据失败")
// 	}

// 	// 生成唯一token
// 	rand.Seed(time.Now().UnixNano())
// 	randomStr := fmt.Sprintf("%d%d", time.Now().UnixNano(), rand.Intn(999999))
// 	token := gmd5.MustEncryptString(randomStr)

// 	// 获取主图和滑块图的 Base64
// 	masterBase64, err := captData.GetMasterImage().ToBase64()
// 	if err != nil {
// 		return nil, err
// 	}
// 	tileBase64, err := captData.GetTileImage().ToBase64()
// 	if err != nil {
// 		return nil, err
// 	}

// 	// 序列化验证码数据，存入缓存
// 	dotsBytes, err := json.Marshal(dotData)
// 	if err != nil {
// 		return nil, err
// 	}

// 	// 从dotData中获取滑块位置信息
// 	var tileInfo map[string]interface{}
// 	if err := json.Unmarshal(dotsBytes, &tileInfo); err != nil {
// 		return nil, err
// 	}

// 	// 提取滑块位置坐标
// 	tileX, _ := tileInfo["x"].(float64)
// 	tileY, _ := tileInfo["y"].(float64)
// 	tileWidth := 60  // 默认滑块宽度
// 	tileHeight := 60 // 默认滑块高度

// 	// 将验证码答案存入缓存
// 	_ = gcache.Set(ctx, "captcha_"+token, string(dotsBytes), defaultCacheExpire)

// 	return &SlideData{
// 		MasterBase64: masterBase64,
// 		TileBase64:   tileBase64,
// 		Token:        token,
// 		TileX:        int(tileX),
// 		TileY:        int(tileY),
// 		TileWidth:    tileWidth,
// 		TileHeight:   tileHeight,
// 	}, nil
// }

// // VerifyCaptcha 验证滑动验证码
// // answer 格式为 {"x":123,"y":0} 的JSON字符串
// func VerifyCaptcha(ctx context.Context, token string, answer string) bool {
// 	if token == "" || answer == "" {
// 		return false
// 	}
// 	//todo 验证暂时关闭
// 	return true

// 	// 从缓存中获取正确答案
// 	cacheValue, err := gcache.Get(ctx, "captcha_"+token)
// 	if err != nil {
// 		g.Log().Error(ctx, "获取验证码答案失败", err)
// 		return false
// 	}

// 	// 校验答案
// 	correctAnswer := cacheValue.String()
// 	if correctAnswer == "" {
// 		return false
// 	}

// 	// 解析用户提交的答案
// 	var userDot map[string]interface{}
// 	if err := json.Unmarshal([]byte(answer), &userDot); err != nil {
// 		g.Log().Error(ctx, "解析用户答案失败", err)
// 		return false
// 	}

// 	// 解析正确答案
// 	var correctDot map[string]interface{}
// 	if err := json.Unmarshal([]byte(correctAnswer), &correctDot); err != nil {
// 		g.Log().Error(ctx, "解析正确答案失败", err)
// 		return false
// 	}

// 	// 获取正确的X坐标和用户提交的X坐标
// 	correctX, _ := correctDot["x"].(float64)
// 	userX, _ := userDot["x"].(float64)

// 	// 验证后清除缓存
// 	_, _ = gcache.Remove(ctx, "captcha_"+token)

// 	// 允许5像素的误差
// 	return abs(int(correctX), int(userX)) <= 5
// }

// // abs 计算绝对值
// func abs(a, b int) int {
// 	if a > b {
// 		return a - b
// 	}
// 	return b - a
// }
