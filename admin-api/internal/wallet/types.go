// Package wallet 提供通用的钱包资金操作功能
package wallet

import (
	"context"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/shopspring/decimal"
)

// Operation 定义钱包操作类型
type Operation string

const (
	OperationDeposit    Operation = "deposit"    // 充值
	OperationWithdraw   Operation = "withdraw"   // 提现
	OperationTransfer   Operation = "transfer"   // 转账
	OperationFreeze     Operation = "freeze"     // 冻结
	OperationUnfreeze   Operation = "unfreeze"   // 解冻
	OperationAdjust     Operation = "adjust"     // 调账
	OperationCommission Operation = "commission" // 佣金
)

// Direction 定义资金流向
type Direction string

const (
	DirectionIn  Direction = "in"  // 资金流入
	DirectionOut Direction = "out" // 资金流出
)

// WalletType 定义钱包类型
type WalletType string

const (
	WalletTypeAvailable WalletType = "available" // 可用余额
	WalletTypeFrozen    WalletType = "frozen"    // 冻结余额
)

// Status 定义交易状态
type Status int

const (
	StatusFailed  Status = 0 // 失败
	StatusSuccess Status = 1 // 成功
)

// TransactionRequest 交易请求结构
type TransactionRequest struct {
	// 基本信息
	MerchantID  uint64          `json:"merchant_id"`  // 商户ID
	TokenSymbol string          `json:"token_symbol"` // 代币符号
	Amount      decimal.Decimal `json:"amount"`       // 交易金额
	Operation   Operation       `json:"operation"`    // 操作类型
	Direction   Direction       `json:"direction"`    // 资金方向
	WalletType  WalletType      `json:"wallet_type"`  // 钱包类型

	// 业务信息
	BusinessID        string  `json:"business_id"`         // 业务唯一标识
	RelatedEntityID   *uint64 `json:"related_entity_id"`   // 关联实体ID
	RelatedEntityType *string `json:"related_entity_type"` // 关联实体类型
	Memo              *string `json:"memo"`                // 备注

	// 请求信息
	RequestAmount    *decimal.Decimal       `json:"request_amount"`     // 用户原始请求金额
	RequestReference *string                `json:"request_reference"`  // 请求参考信息
	RequestMetadata  map[string]interface{} `json:"request_metadata"`   // 请求元数据
	RequestSource    *string                `json:"request_source"`     // 请求来源
	RequestIP        *string                `json:"request_ip"`         // 请求IP
	RequestUserAgent *string                `json:"request_user_agent"` // 请求User-Agent

	// 手续费信息
	FeeAmount *decimal.Decimal `json:"fee_amount"` // 手续费金额
	FeeType   *string          `json:"fee_type"`   // 手续费类型

	// 目标用户信息（转账等）
	TargetMerchantID *uint64 `json:"target_merchant_id"` // 目标商户ID
	TargetUsername   *string `json:"target_username"`    // 目标用户名
}

// BalanceSnapshot 余额快照
type BalanceSnapshot struct {
	Available decimal.Decimal `json:"available"` // 可用余额
	Frozen    decimal.Decimal `json:"frozen"`    // 冻结余额
	Total     decimal.Decimal `json:"total"`     // 总余额
}

// TransactionResult 交易结果
type TransactionResult struct {
	TransactionID uint64           `json:"transaction_id"` // 交易ID
	BalanceBefore *BalanceSnapshot `json:"balance_before"` // 交易前余额
	BalanceAfter  *BalanceSnapshot `json:"balance_after"`  // 交易后余额
	ProcessedAt   time.Time        `json:"processed_at"`   // 处理时间
	Status        Status           `json:"status"`         // 交易状态
}

// WalletInfo 钱包信息
type WalletInfo struct {
	WalletID      int64           `json:"wallet_id"`      // 钱包ID
	MerchantID    uint64          `json:"merchant_id"`    // 商户ID
	TokenSymbol   string          `json:"token_symbol"`   // 代币符号
	Available     decimal.Decimal `json:"available"`      // 可用余额
	Frozen        decimal.Decimal `json:"frozen"`         // 冻结余额
	DecimalPlaces uint            `json:"decimal_places"` // 小数位数
}

// TokenConfig 代币配置
type TokenConfig struct {
	Symbol        string          `json:"symbol"`         // 代币符号
	DecimalPlaces uint            `json:"decimal_places"` // 小数位数
	MinAmount     decimal.Decimal `json:"min_amount"`     // 最小金额
	MaxAmount     decimal.Decimal `json:"max_amount"`     // 最大金额
}

// Manager 钱包管理器接口
// 重要：所有写操作（创建、更新、锁定等）的 tx 参数不能为 nil，必须在事务中执行
// 读操作（查询、验证等）的 tx 参数可以为 nil，此时不使用事务
type Manager interface {
	// ========== 读操作（tx 可以为 nil）==========

	// GetBalance 获取钱包余额信息
	// @param tx: 可选的事务对象，如果为 nil 则不使用事务
	GetBalance(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string) (*WalletInfo, error)

	// ValidateBalance 验证余额是否充足
	// @param tx: 可选的事务对象，如果为 nil 则不使用事务
	ValidateBalance(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string, amount decimal.Decimal, walletType WalletType) error

	// ========== 写操作（tx 不能为 nil）==========

	// CreateWallet 创建新钱包
	// @param tx: 必需的事务对象，不能为 nil
	CreateWallet(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string) (*WalletInfo, error)

	// EnsureWallet 确保钱包存在，不存在则创建
	// @param tx: 必需的事务对象，不能为 nil
	EnsureWallet(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string) (*WalletInfo, error)

	// LockBalance 锁定余额（从可用余额转到冻结余额）
	// @param tx: 必需的事务对象，不能为 nil
	LockBalance(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string, amount decimal.Decimal) error

	// UnlockBalance 解锁余额（从冻结余额转到可用余额）
	// @param tx: 必需的事务对象，不能为 nil
	UnlockBalance(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string, amount decimal.Decimal) error

	// UpdateBalance 更新余额
	// @param tx: 必需的事务对象，不能为 nil
	UpdateBalance(ctx context.Context, tx gdb.TX, req *TransactionRequest) (*TransactionResult, error)

	// BatchUpdateBalance 批量更新余额
	// @param tx: 必需的事务对象，不能为 nil
	BatchUpdateBalance(ctx context.Context, tx gdb.TX, reqs []*TransactionRequest) ([]*TransactionResult, error)

	// ========== 配置操作（不需要事务）==========

	// GetTokenConfig 获取代币配置
	GetTokenConfig(tokenSymbol string) (*TokenConfig, error)

	// RegisterTokenConfig 注册代币配置
	RegisterTokenConfig(config *TokenConfig) error

	// ========== 工具方法（不需要事务）==========

	// ConvertToInt 将decimal金额转换为int64存储格式
	ConvertToInt(amount decimal.Decimal, tokenSymbol string) (int64, error)

	// ConvertToDecimal 将int64金额转换为decimal格式
	ConvertToDecimal(amount int64, tokenSymbol string) (decimal.Decimal, error)

	// ValidateAmount 验证金额是否符合代币配置
	ValidateAmount(amount decimal.Decimal, tokenSymbol string) error
}

// TransactionRecorder 交易记录器接口
type TransactionRecorder interface {
	// 记录交易
	RecordTransaction(ctx context.Context, req *TransactionRequest, balanceBefore, balanceAfter *BalanceSnapshot) (*TransactionResult, error)
	RecordTransactionInTx(ctx context.Context, tx gdb.TX, req *TransactionRequest, balanceBefore, balanceAfter *BalanceSnapshot) (*TransactionResult, error)

	// 记录冻结/解冻交易（生成两条相关记录）
	RecordFreezeTransaction(ctx context.Context, tx gdb.TX, availableReq, frozenReq *TransactionRequest, balanceBefore, balanceAfter *BalanceSnapshot) ([]*TransactionResult, error)

	// 查询交易记录
	GetTransaction(ctx context.Context, transactionID uint64) (*TransactionResult, error)
	GetTransactionsByMerchant(ctx context.Context, merchantID uint64, limit, offset int) ([]*TransactionResult, error)
	GetTransactionsByBusiness(ctx context.Context, businessID string) (*TransactionResult, error)
}

// Option 配置选项
type Option func(*Config)

// Config 库配置
type Config struct {
	// 数据库配置
	DBGroup string

	// 默认代币配置
	DefaultTokenConfigs map[string]*TokenConfig

	// 并发控制
	MaxConcurrent int

	// 重试配置
	MaxRetries int
	RetryDelay time.Duration

	// 审计配置
	EnableAudit bool
	AuditLevel  string

	// 配置文件路径
	ConfigPath string

	// 自动确保钱包存在
	AutoEnsureWallet bool
}
