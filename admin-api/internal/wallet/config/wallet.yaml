{"audit": {"enabled": true, "level": "info"}, "concurrency": {"max_concurrent": 100}, "database": {"group": "default"}, "monitoring": {"enabled": false, "interval": 30000000000, "metrics_port": 9090}, "retry": {"max_retries": 3, "retry_delay": 100000000}, "tokens": {"BTC": {"decimal_places": 8, "description": "Bitcoin", "enabled": true, "max_amount": "100", "min_amount": "0.00000001", "symbol": "BTC"}, "ETH": {"decimal_places": 18, "description": "Ethereum", "enabled": true, "max_amount": "10000", "min_amount": "0.000000000000000001", "symbol": "ETH"}, "TRX": {"decimal_places": 6, "description": "TRON", "enabled": true, "max_amount": "1000000", "min_amount": "0.000001", "symbol": "TRX"}, "USDT": {"decimal_places": 6, "description": "Tether USD", "enabled": true, "max_amount": "1000000", "min_amount": "0.000001", "symbol": "USDT"}}}