package wallet

import (
	"testing"
)

func TestValidateIPAddress(t *testing.T) {
	v := &validator{}

	tests := []struct {
		name    string
		ip      string
		wantErr bool
	}{
		{
			name:    "Valid IPv4",
			ip:      "***********",
			wantErr: false,
		},
		{
			name:    "Valid IPv6",
			ip:      "2001:0db8:85a3:0000:0000:8a2e:0370:7334",
			wantErr: false,
		},
		{
			name:    "IPv6 localhost",
			ip:      "::1",
			wantErr: false,
		},
		{
			name:    "IPv4 localhost",
			ip:      "127.0.0.1",
			wantErr: false,
		},
		{
			name:    "IPv4 with port should be cleaned",
			ip:      "***********:8080",
			wantErr: false,
		},
		{
			name:    "IPv6 with port should be cleaned",
			ip:      "[::1]:8080",
			wantErr: false,
		},
		{
			name:    "Empty string should be allowed",
			ip:      "",
			wantErr: false,
		},
		{
			name:    "Whitespace should be trimmed",
			ip:      "  ***********  ",
			wantErr: false,
		},
		{
			name:    "Invalid IP format",
			ip:      "999.999.999.999",
			wantErr: true,
		},
		{
			name:    "Invalid string",
			ip:      "not-an-ip",
			wantErr: true,
		},
		{
			name:    "Incomplete IPv4",
			ip:      "192.168.1",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := v.ValidateIPAddress(tt.ip)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateIPAddress() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
