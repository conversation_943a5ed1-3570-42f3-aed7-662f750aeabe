package wallet

import (
	"context"
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/shopspring/decimal"
)

// BatchUpdateBalance 批量更新余额
// tx 参数不能为 nil，必须在事务中执行
func (m *manager) BatchUpdateBalance(ctx context.Context, tx gdb.TX, reqs []*TransactionRequest) ([]*TransactionResult, error) {
	// 检查事务参数
	if tx == nil {
		return nil, ErrTransactionRequired("BatchUpdateBalance")
	}
	if len(reqs) == 0 {
		return nil, gerror.New("批量请求不能为空")
	}

	results := make([]*TransactionResult, 0, len(reqs))

	// 按照merchantID和tokenSymbol分组处理，确保同一钱包的操作按顺序执行
	for _, req := range reqs {
		result, err := m.UpdateBalance(ctx, tx, req)
		if err != nil {
			return nil, gerror.Wrapf(err, "批量更新余额失败, businessID: %s", req.BusinessID)
		}
		results = append(results, result)
	}

	return results, nil
}

// ValidateBalance 验证余额
// 如果 tx 为 nil，则不使用事务；否则在指定事务中执行
func (m *manager) ValidateBalance(ctx context.Context, tx gdb.TX, merchantID uint64, tokenSymbol string, amount decimal.Decimal, walletType WalletType) error {
	if amount.IsNegative() || amount.IsZero() {
		return gerror.New("验证金额必须大于0")
	}

	wallet, err := m.GetBalance(ctx, tx, merchantID, tokenSymbol)
	if err != nil {
		return gerror.Wrapf(err, "获取钱包信息失败")
	}

	switch walletType {
	case WalletTypeAvailable:
		realAvailable := wallet.Available.Sub(wallet.Frozen)
		if realAvailable.LessThan(amount) {
			return gerror.Newf("可用余额不足, 需要: %s, 可用: %s", amount.String(), realAvailable.String())
		}
	case WalletTypeFrozen:
		if wallet.Frozen.LessThan(amount) {
			return gerror.Newf("冻结余额不足, 需要: %s, 冻结: %s", amount.String(), wallet.Frozen.String())
		}
	default:
		return gerror.Newf("不支持的钱包类型: %s", walletType)
	}

	return nil
}

// GetTokenConfig 获取代币配置
func (m *manager) GetTokenConfig(tokenSymbol string) (*TokenConfig, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	symbol := strings.ToUpper(tokenSymbol)
	config, exists := m.tokenConfig[symbol]
	if !exists {
		return nil, ErrTokenNotSupported(tokenSymbol)
	}

	// 返回副本以避免并发修改
	return &TokenConfig{
		Symbol:        config.Symbol,
		DecimalPlaces: config.DecimalPlaces,
		MinAmount:     config.MinAmount,
		MaxAmount:     config.MaxAmount,
	}, nil
}

// RegisterTokenConfig 注册代币配置
func (m *manager) RegisterTokenConfig(config *TokenConfig) error {
	if config == nil {
		return gerror.New("代币配置不能为空")
	}

	if config.Symbol == "" {
		return gerror.New("代币符号不能为空")
	}

	if config.DecimalPlaces > 18 {
		return gerror.New("小数位数不能超过18位")
	}

	if config.MinAmount.IsNegative() {
		return gerror.New("最小金额不能为负数")
	}

	if config.MaxAmount.IsNegative() || config.MaxAmount.LessThanOrEqual(config.MinAmount) {
		return gerror.New("最大金额必须大于最小金额")
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	symbol := strings.ToUpper(config.Symbol)
	m.tokenConfig[symbol] = &TokenConfig{
		Symbol:        symbol,
		DecimalPlaces: config.DecimalPlaces,
		MinAmount:     config.MinAmount,
		MaxAmount:     config.MaxAmount,
	}

	return nil
}

// ConvertToInt 将decimal金额转换为int64存储格式
func (m *manager) ConvertToInt(amount decimal.Decimal, tokenSymbol string) (int64, error) {
	config, err := m.GetTokenConfig(tokenSymbol)
	if err != nil {
		return 0, err
	}

	return m.convertDecimalToInt(amount, config.DecimalPlaces), nil
}

// ConvertToDecimal 将int64金额转换为decimal格式
func (m *manager) ConvertToDecimal(amount int64, tokenSymbol string) (decimal.Decimal, error) {
	config, err := m.GetTokenConfig(tokenSymbol)
	if err != nil {
		return decimal.Zero, err
	}

	return m.convertIntToDecimal(amount, config.DecimalPlaces), nil
}

// 配置选项函数

// WithDBGroup 设置数据库组
func WithDBGroup(group string) Option {
	return func(c *Config) {
		c.DBGroup = group
	}
}

// WithMaxConcurrent 设置最大并发数
func WithMaxConcurrent(max int) Option {
	return func(c *Config) {
		c.MaxConcurrent = max
	}
}

// WithDefaultTokenConfig 设置默认代币配置
func WithDefaultTokenConfig(configs map[string]*TokenConfig) Option {
	return func(c *Config) {
		if c.DefaultTokenConfigs == nil {
			c.DefaultTokenConfigs = make(map[string]*TokenConfig)
		}
		for symbol, config := range configs {
			c.DefaultTokenConfigs[strings.ToUpper(symbol)] = config
		}
	}
}

// WithAudit 设置审计配置
func WithAudit(enable bool, level string) Option {
	return func(c *Config) {
		c.EnableAudit = enable
		c.AuditLevel = level
	}
}

// WithConfigPath 设置配置文件路径
func WithConfigPath(path string) Option {
	return func(c *Config) {
		c.ConfigPath = path
	}
}

// WithAutoEnsureWallet 设置是否自动确保钱包存在
func WithAutoEnsureWallet(enable bool) Option {
	return func(c *Config) {
		c.AutoEnsureWallet = enable
	}
}

// 辅助函数

// ValidateAmount 验证金额是否符合代币配置
func (m *manager) ValidateAmount(amount decimal.Decimal, tokenSymbol string) error {
	config, err := m.GetTokenConfig(tokenSymbol)
	if err != nil {
		return err
	}

	if amount.LessThan(config.MinAmount) {
		return ErrAmountTooSmall(amount.String(), config.MinAmount.String(), tokenSymbol)
	}

	if amount.GreaterThan(config.MaxAmount) {
		return ErrAmountTooLarge(amount.String(), config.MaxAmount.String(), tokenSymbol)
	}

	return nil
}

// GetDecimalPlaces 获取代币小数位数
func (m *manager) GetDecimalPlaces(tokenSymbol string) (uint, error) {
	config, err := m.GetTokenConfig(tokenSymbol)
	if err != nil {
		return 0, err
	}
	return config.DecimalPlaces, nil
}
