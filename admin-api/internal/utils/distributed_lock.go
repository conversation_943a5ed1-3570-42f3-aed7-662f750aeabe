package utils

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/util/guid"
)

// DistributedLock provides distributed locking using Redis
type DistributedLock struct {
	redis  *gredis.Redis
	key    string
	value  string
	ttl    time.Duration
	locked bool
}

// NewDistributedLock creates a new distributed lock
func NewDistributedLock(redis *gredis.Redis, key string, ttl time.Duration) *DistributedLock {
	return &DistributedLock{
		redis: redis,
		key:   fmt.Sprintf("lock:%s", key),
		value: guid.S(),
		ttl:   ttl,
	}
}

// Lock attempts to acquire the lock
func (l *DistributedLock) Lock(ctx context.Context) error {
	if l.locked {
		return fmt.Errorf("lock already held")
	}

	// Use SET NX EX for atomic lock acquisition
	result, err := l.redis.Do(ctx, "SET", l.key, l.value, "NX", "EX", int64(l.ttl.Seconds()))
	if err != nil {
		return fmt.Errorf("failed to acquire lock: %w", err)
	}

	if result == nil {
		return fmt.Errorf("lock is already held by another process")
	}

	l.locked = true
	return nil
}

// LockWithRetry attempts to acquire the lock with retries
func (l *DistributedLock) LockWithRetry(ctx context.Context, maxRetries int, retryDelay time.Duration) error {
	for i := 0; i < maxRetries; i++ {
		err := l.Lock(ctx)
		if err == nil {
			return nil
		}

		if i < maxRetries-1 {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(retryDelay):
				continue
			}
		}
	}

	return fmt.Errorf("failed to acquire lock after %d retries", maxRetries)
}

// Unlock releases the lock
func (l *DistributedLock) Unlock(ctx context.Context) error {
	if !l.locked {
		return nil
	}

	// Use Lua script for atomic unlock
	luaScript := `
		if redis.call("get", KEYS[1]) == ARGV[1] then
			return redis.call("del", KEYS[1])
		else
			return 0
		end
	`

	result, err := l.redis.Do(ctx, "EVAL", luaScript, 1, l.key, l.value)
	if err != nil {
		return fmt.Errorf("failed to release lock: %w", err)
	}

	if result.Int() == 0 {
		return fmt.Errorf("lock was not held or has expired")
	}

	l.locked = false
	return nil
}

// Extend extends the lock TTL
func (l *DistributedLock) Extend(ctx context.Context, ttl time.Duration) error {
	if !l.locked {
		return fmt.Errorf("lock not held")
	}

	// Use Lua script for atomic TTL extension
	luaScript := `
		if redis.call("get", KEYS[1]) == ARGV[1] then
			return redis.call("expire", KEYS[1], ARGV[2])
		else
			return 0
		end
	`

	result, err := l.redis.Do(ctx, "EVAL", luaScript, 1, l.key, l.value, int64(ttl.Seconds()))
	if err != nil {
		return fmt.Errorf("failed to extend lock: %w", err)
	}

	if result.Int() == 0 {
		return fmt.Errorf("lock was not held or has expired")
	}

	return nil
}

// IsLocked checks if the lock is currently held by this instance
func (l *DistributedLock) IsLocked() bool {
	return l.locked
}

// WithLock executes a function with the lock held
func WithLock(ctx context.Context, redis *gredis.Redis, key string, ttl time.Duration, fn func() error) error {
	lock := NewDistributedLock(redis, key, ttl)

	if err := lock.LockWithRetry(ctx, 3, 100*time.Millisecond); err != nil {
		return fmt.Errorf("failed to acquire lock: %w", err)
	}

	defer func() {
		if err := lock.Unlock(ctx); err != nil {
			// Log error but don't fail the operation
			fmt.Printf("Failed to unlock: %v\n", err)
		}
	}()

	return fn()
}
