package utils

import (
	"admin-api/internal/constants"
	"fmt"
	"strings"
)

// 注意：此文件已迁移到新的资金操作系统 (fund_operations_v2.go)
// 这些函数保持向后兼容性，内部使用新的 FundOperationDescriptor 系统
// 新代码应该直接使用 NewFundOperationDescriptor 和枚举类型

// GenerateBusinessID generates a standardized BusinessID with the given prefix and identifiers
// 保持向后兼容性，内部使用新的系统
func GenerateBusinessID(prefix string, identifiers ...interface{}) string {
	parts := []string{prefix}
	for _, id := range identifiers {
		parts = append(parts, fmt.Sprintf("%v", id))
	}
	return strings.Join(parts, "_")
}

// FormatTransferDescription formats transfer descriptions based on direction
// 保持向后兼容性，内部使用新的系统
func FormatTransferDescription(direction string, username string, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("zh")
	if direction == "out" {
		return descriptor.FormatDescriptionWithTarget(constants.FundOpTransferOut, username, amount, symbol)
	}
	return descriptor.FormatBasicDescription(constants.FundOpTransferIn, amount, symbol)
}

// FormatTransferDescriptionEN formats transfer descriptions in English
// 保持向后兼容性，内部使用新的系统
func FormatTransferDescriptionEN(direction string, username string, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("en")
	if direction == "out" {
		return descriptor.FormatDescriptionWithTarget(constants.FundOpTransferOut, username, amount, symbol)
	}
	return descriptor.FormatBasicDescription(constants.FundOpTransferIn, amount, symbol)
}

// FormatRedPacketDescription formats red packet descriptions based on operation type
// 保持向后兼容性，内部使用新的系统
func FormatRedPacketDescription(operation string, uuid string, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("zh")
	switch operation {
	case "create":
		return descriptor.FormatBasicDescription(constants.FundOpRedPacketCreate, amount, symbol)
	case "claim":
		return descriptor.FormatBasicDescription(constants.FundOpRedPacketClaim, amount, symbol)
	case "cancel":
		return descriptor.FormatBasicDescription(constants.FundOpRedPacketCancel, amount, symbol)
	case "expire":
		return descriptor.FormatBasicDescription(constants.FundOpRedPacketExpire, amount, symbol)
	default:
		return ""
	}
}

// FormatRedPacketDescriptionEN formats red packet descriptions in English
// 保持向后兼容性，内部使用新的系统
func FormatRedPacketDescriptionEN(operation string, uuid string, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("en")
	switch operation {
	case "create":
		return descriptor.FormatBasicDescription(constants.FundOpRedPacketCreate, amount, symbol)
	case "claim":
		return descriptor.FormatBasicDescription(constants.FundOpRedPacketClaim, amount, symbol)
	case "cancel":
		return descriptor.FormatBasicDescription(constants.FundOpRedPacketCancel, amount, symbol)
	case "expire":
		return descriptor.FormatBasicDescription(constants.FundOpRedPacketExpire, amount, symbol)
	default:
		return ""
	}
}

// FormatWithdrawDescription formats withdrawal descriptions
// 保持向后兼容性，内部使用新的系统
func FormatWithdrawDescription(operation string, destination string, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("zh")
	switch operation {
	case "withdraw":
		return descriptor.FormatDescriptionWithTarget(constants.FundOpWithdrawReq, destination, amount, symbol)
	case "refund":
		return descriptor.FormatBasicDescription(constants.FundOpWithdrawRefund, amount, symbol)
	default:
		return ""
	}
}

// FormatWithdrawDescriptionEN formats withdrawal descriptions in English
// 保持向后兼容性，内部使用新的系统
func FormatWithdrawDescriptionEN(operation string, destination string, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("en")
	switch operation {
	case "withdraw":
		return descriptor.FormatDescriptionWithTarget(constants.FundOpWithdrawReq, destination, amount, symbol)
	case "refund":
		return descriptor.FormatBasicDescription(constants.FundOpWithdrawRefund, amount, symbol)
	default:
		return ""
	}
}

// FormatSwapDescription formats swap/exchange descriptions
// 保持向后兼容性，内部使用新的系统
func FormatSwapDescription(direction string, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("zh")
	if direction == "out" {
		return descriptor.FormatBasicDescription(constants.FundOpSwapOut, amount, symbol)
	}
	return descriptor.FormatBasicDescription(constants.FundOpSwapIn, amount, symbol)
}

// FormatSwapDescriptionEN formats swap/exchange descriptions in English
// 保持向后兼容性，内部使用新的系统
func FormatSwapDescriptionEN(direction string, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("en")
	if direction == "out" {
		return descriptor.FormatBasicDescription(constants.FundOpSwapOut, amount, symbol)
	}
	return descriptor.FormatBasicDescription(constants.FundOpSwapIn, amount, symbol)
}

// FormatSwapFullDescription formats complete swap descriptions
// 保持向后兼容性，内部使用新的系统
func FormatSwapFullDescription(fromAmount, fromSymbol, toAmount, toSymbol string) string {
	descriptor := NewFundOperationDescriptor("zh")
	return descriptor.FormatSwapDescription(fromAmount, fromSymbol, toAmount, toSymbol)
}

// FormatSwapFullDescriptionEN formats complete swap descriptions in English
// 保持向后兼容性，内部使用新的系统
func FormatSwapFullDescriptionEN(fromAmount, fromSymbol, toAmount, toSymbol string) string {
	descriptor := NewFundOperationDescriptor("en")
	return descriptor.FormatSwapDescription(fromAmount, fromSymbol, toAmount, toSymbol)
}

// FormatPaymentDescription formats payment descriptions
// 保持向后兼容性，内部使用新的系统
func FormatPaymentDescription(direction string, username string, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("zh")
	if direction == "out" {
		return descriptor.FormatDescriptionWithTarget(constants.FundOpPaymentOut, username, amount, symbol)
	}
	return descriptor.FormatBasicDescription(constants.FundOpPaymentIn, amount, symbol)
}

// FormatPaymentDescriptionEN formats payment descriptions in English
// 保持向后兼容性，内部使用新的系统
func FormatPaymentDescriptionEN(direction string, username string, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("en")
	if direction == "out" {
		return descriptor.FormatDescriptionWithTarget(constants.FundOpPaymentOut, username, amount, symbol)
	}
	return descriptor.FormatBasicDescription(constants.FundOpPaymentIn, amount, symbol)
}

// FormatAdminDescription formats admin operation descriptions
// 保持向后兼容性，内部使用新的系统
func FormatAdminDescription(operation string, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("zh")
	switch operation {
	case "add":
		return descriptor.FormatBasicDescription(constants.FundOpAdminAdd, amount, symbol)
	case "deduct":
		return descriptor.FormatBasicDescription(constants.FundOpAdminDeduct, amount, symbol)
	default:
		return ""
	}
}

// FormatAdminDescriptionEN formats admin operation descriptions in English
// 保持向后兼容性，内部使用新的系统
func FormatAdminDescriptionEN(operation string, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("en")
	switch operation {
	case "add":
		return descriptor.FormatBasicDescription(constants.FundOpAdminAdd, amount, symbol)
	case "deduct":
		return descriptor.FormatBasicDescription(constants.FundOpAdminDeduct, amount, symbol)
	default:
		return ""
	}
}

// FormatSystemDescription formats system operation descriptions
// 保持向后兼容性，内部使用新的系统
func FormatSystemDescription(operation string, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("zh")
	switch operation {
	case "commission":
		return descriptor.FormatBasicDescription(constants.FundOpCommission, amount, symbol)
	case "referral":
		return descriptor.FormatBasicDescription(constants.FundOpReferral, amount, symbol)
	case "adjust":
		return descriptor.FormatBasicDescription(constants.FundOpSystemAdjust, amount, symbol)
	default:
		return ""
	}
}

// FormatSystemDescriptionEN formats system operation descriptions in English
// 保持向后兼容性，内部使用新的系统
func FormatSystemDescriptionEN(operation string, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("en")
	switch operation {
	case "commission":
		return descriptor.FormatBasicDescription(constants.FundOpCommission, amount, symbol)
	case "referral":
		return descriptor.FormatBasicDescription(constants.FundOpReferral, amount, symbol)
	case "adjust":
		return descriptor.FormatBasicDescription(constants.FundOpSystemAdjust, amount, symbol)
	default:
		return ""
	}
}

// GetDescriptionByLanguage returns the appropriate description based on language preference
func GetDescriptionByLanguage(descCN, descEN, language string) string {
	if language == "en" || language == "EN" {
		return descEN
	}
	return descCN
}

// FormatTransferWithMemoDescription formats transfer descriptions with memo
// 保持向后兼容性，内部使用新的系统
func FormatTransferWithMemoDescription(direction string, recipient string, amount string, symbol string, memo string) string {
	descriptor := NewFundOperationDescriptor("zh")
	if direction == "out" {
		return descriptor.FormatDescriptionWithTargetAndMemo(constants.FundOpTransferOut, recipient, amount, symbol, memo)
	}
	return descriptor.FormatDescriptionWithMemo(constants.FundOpTransferIn, amount, symbol, memo)
}

// FormatTransferWithMemoDescriptionEN formats transfer descriptions with memo in English
// 保持向后兼容性，内部使用新的系统
func FormatTransferWithMemoDescriptionEN(direction string, recipient string, amount string, symbol string, memo string) string {
	descriptor := NewFundOperationDescriptor("en")
	if direction == "out" {
		return descriptor.FormatDescriptionWithTargetAndMemo(constants.FundOpTransferOut, recipient, amount, symbol, memo)
	}
	return descriptor.FormatDescriptionWithMemo(constants.FundOpTransferIn, amount, symbol, memo)
}

// FormatDemoFundsDescription formats demo funds descriptions
// 保持向后兼容性，内部使用新的系统
func FormatDemoFundsDescription(amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("zh")
	return descriptor.FormatBasicDescription(constants.FundOpAdminAdd, amount, symbol)
}

// FormatDemoFundsDescriptionEN formats demo funds descriptions in English
// 保持向后兼容性，内部使用新的系统
func FormatDemoFundsDescriptionEN(amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("en")
	return descriptor.FormatBasicDescription(constants.FundOpAdminAdd, amount, symbol)
}

// FormatWithdrawRefundDetailDescription formats detailed withdrawal refund descriptions
// 保持向后兼容性，内部使用新的系统
func FormatWithdrawRefundDetailDescription(symbolName string, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("zh")
	return descriptor.FormatBasicDescription(constants.FundOpWithdrawRefund, amount, symbol)
}

// FormatWithdrawRefundDetailDescriptionEN formats detailed withdrawal refund descriptions in English
// 保持向后兼容性，内部使用新的系统
func FormatWithdrawRefundDetailDescriptionEN(symbolName string, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("en")
	return descriptor.FormatBasicDescription(constants.FundOpWithdrawRefund, amount, symbol)
}

// FormatPaymentRequestDescription formats payment request descriptions
// 保持向后兼容性，内部使用新的系统
func FormatPaymentRequestDescription(operation string, requestID int64, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("zh")
	if operation == "pay" {
		return descriptor.FormatPaymentRequestDescription(constants.FundOpPaymentOut, requestID, amount, symbol)
	}
	return descriptor.FormatPaymentRequestDescription(constants.FundOpPaymentIn, requestID, amount, symbol)
}

// FormatPaymentRequestDescriptionEN formats payment request descriptions in English
// 保持向后兼容性，内部使用新的系统
func FormatPaymentRequestDescriptionEN(operation string, requestID int64, amount string, symbol string) string {
	descriptor := NewFundOperationDescriptor("en")
	if operation == "pay" {
		return descriptor.FormatPaymentRequestDescription(constants.FundOpPaymentOut, requestID, amount, symbol)
	}
	return descriptor.FormatPaymentRequestDescription(constants.FundOpPaymentIn, requestID, amount, symbol)
}

// FormatTransferCollectDescription formats transfer collection descriptions
// 保持向后兼容性，内部使用新的系统
func FormatTransferCollectDescription(direction string, amount string, symbol string, memo string) string {
	descriptor := NewFundOperationDescriptor("zh")
	if direction == "out" {
		return descriptor.FormatDescriptionWithMemo(constants.FundOpTransferOut, amount, symbol, memo)
	}
	return descriptor.FormatDescriptionWithMemo(constants.FundOpTransferIn, amount, symbol, memo)
}

// FormatTransferCollectDescriptionEN formats transfer collection descriptions in English
// 保持向后兼容性，内部使用新的系统
func FormatTransferCollectDescriptionEN(direction string, amount string, symbol string, memo string) string {
	descriptor := NewFundOperationDescriptor("en")
	if direction == "out" {
		return descriptor.FormatDescriptionWithMemo(constants.FundOpTransferOut, amount, symbol, memo)
	}
	return descriptor.FormatDescriptionWithMemo(constants.FundOpTransferIn, amount, symbol, memo)
}
