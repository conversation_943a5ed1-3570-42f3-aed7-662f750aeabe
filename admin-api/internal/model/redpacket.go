package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// RedPacketAdminInfo 红包管理后台信息模型
type RedPacketAdminInfo struct {
	// 基础字段
	RedPacketId       int64           `json:"redPacketId" orm:"red_packet_id" description:"红包ID"`
	Uuid              string          `json:"uuid" orm:"uuid" description:"红包UUID"`
	CreatorUserId     int64           `json:"creatorUserId" orm:"creator_user_id" description:"创建者用户ID"`
	CreatorUsername   string          `json:"creatorUsername" orm:"creator_username" description:"创建者用户名"`
	TokenId           int             `json:"tokenId" orm:"token_id" description:"代币ID"`
	TokenSymbol       string          `json:"tokenSymbol" orm:"token_symbol" description:"代币符号"`
	TokenName         string          `json:"tokenName" orm:"token_name" description:"代币名称"`
	TokenLogo         string          `json:"tokenLogo" orm:"token_logo" description:"代币Logo"`
	Type              string          `json:"type" orm:"type" description:"红包类型"`
	TotalAmount       decimal.Decimal `json:"totalAmount" orm:"total_amount" description:"总金额"`
	TotalQuantity     int             `json:"totalQuantity" orm:"total_quantity" description:"总数量"`
	RemainingAmount   decimal.Decimal `json:"remainingAmount" orm:"remaining_amount" description:"剩余金额"`
	RemainingQuantity int             `json:"remainingQuantity" orm:"remaining_quantity" description:"剩余数量"`
	Status            string          `json:"status" orm:"status" description:"状态"`
	Memo              string          `json:"memo" orm:"memo" description:"留言"`
	Symbol            string          `json:"symbol" orm:"symbol" description:"红包记录的币种符号"`
	ImageId           int64           `json:"imageId" orm:"image_id" description:"封面图片ID"`
	ImageUrl          string          `json:"imageUrl" orm:"image_url" description:"封面图片URL"`
	MessageId         string          `json:"messageId" orm:"message_id" description:"消息ID"`
	ExpiresAt         *gtime.Time     `json:"expiresAt" orm:"expires_at" description:"过期时间"`
	CreatedAt         *gtime.Time     `json:"createdAt" orm:"created_at" description:"创建时间"`

	// 创建者代理信息（默认用户不加前缀）
	FirstAgentName  string `json:"firstAgentName" orm:"first_agent_name" description:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" orm:"second_agent_name" description:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" orm:"third_agent_name" description:"三级代理名称"`

	// 创建者Telegram信息（默认用户不加前缀）
	TelegramId       string `json:"telegramId" orm:"telegram_id" description:"Telegram ID"`
	TelegramUsername string `json:"telegramUsername" orm:"telegram_username" description:"Telegram用户名"`
	FirstName        string `json:"firstName" orm:"first_name" description:"真实姓名"`
}

// RedPacketClaimAdminInfo 红包领取记录管理后台信息模型
type RedPacketClaimAdminInfo struct {
	// 基础字段
	ClaimId         int64           `json:"claimId" orm:"claim_id" description:"领取记录ID"`
	RedPacketId     int64           `json:"redPacketId" orm:"red_packet_id" description:"红包ID"`
	ClaimerUserId   int64           `json:"claimerUserId" orm:"claimer_user_id" description:"领取者用户ID"`
	ClaimerUsername string          `json:"claimerUsername" orm:"claimer_username" description:"领取者用户名"`
	ClaimedAmount   decimal.Decimal `json:"claimedAmount" orm:"claimed_amount" description:"领取金额"`
	TransactionId   int64           `json:"transactionId" orm:"transaction_id" description:"交易ID"`
	MessageId       string          `json:"messageId" orm:"message_id" description:"消息ID"`
	CreatedAt       *gtime.Time     `json:"createdAt" orm:"created_at" description:"领取时间"`

	// 红包相关信息
	RedPacketMemo string `json:"redPacketMemo" orm:"red_packet_memo" description:"红包留言"`
	RedPacketType string `json:"redPacketType" orm:"red_packet_type" description:"红包类型"`
	TokenId       int    `json:"tokenId" orm:"token_id" description:"代币ID"`
	TokenSymbol   string `json:"tokenSymbol" orm:"token_symbol" description:"代币符号"`
	TokenName     string `json:"tokenName" orm:"token_name" description:"代币名称"`

	// 领取者代理信息（默认用户不加前缀）
	FirstAgentName  string `json:"firstAgentName" orm:"first_agent_name" description:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" orm:"second_agent_name" description:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" orm:"third_agent_name" description:"三级代理名称"`

	// 领取者Telegram信息（默认用户不加前缀）
	TelegramId       string `json:"telegramId" orm:"telegram_id" description:"Telegram ID"`
	TelegramUsername string `json:"telegramUsername" orm:"telegram_username" description:"Telegram用户名"`
	FirstName        string `json:"firstName" orm:"first_name" description:"真实姓名"`

	// 发送方基础信息
	SenderUserId   int64  `json:"senderUserId" orm:"sender_user_id" description:"发送方用户ID"`
	SenderUsername string `json:"senderUsername" orm:"sender_username" description:"发送方用户名"`

	// 发送方代理信息
	SenderFirstAgentName  string `json:"senderFirstAgentName" orm:"sender_first_agent_name" description:"发送方一级代理名称"`
	SenderSecondAgentName string `json:"senderSecondAgentName" orm:"sender_second_agent_name" description:"发送方二级代理名称"`
	SenderThirdAgentName  string `json:"senderThirdAgentName" orm:"sender_third_agent_name" description:"发送方三级代理名称"`

	// 发送方Telegram信息
	SenderTelegramId       string `json:"senderTelegramId" orm:"sender_telegram_id" description:"发送方Telegram ID"`
	SenderTelegramUsername string `json:"senderTelegramUsername" orm:"sender_telegram_username" description:"发送方Telegram用户名"`
	SenderFirstName        string `json:"senderFirstName" orm:"sender_first_name" description:"发送方真实姓名"`

	// 领取记录表中的字段
	Status                string `json:"status" orm:"status" description:"红包状态"`
	Symbol                string `json:"symbol" orm:"symbol" description:"代币符号"`
	ClaimSenderUserId     uint64 `json:"claimSenderUserId" orm:"claim_sender_user_id" description:"领取记录中的发送方用户ID"`
	ClaimReceiverUserId   uint64 `json:"claimReceiverUserId" orm:"claim_receiver_user_id" description:"领取记录中的接收方用户ID"`
	ClaimSenderUsername   string `json:"claimSenderUsername" orm:"claim_sender_username" description:"领取记录中的发送方用户名"`
	ClaimReceiverUsername string `json:"claimReceiverUsername" orm:"claim_receiver_username" description:"领取记录中的接收方用户名"`
	RedPacketUuid         string `json:"redPacketUuid" orm:"red_packet_uuid" description:"红包UUID"`
}

// RedPacketImageAdminInfo 红包封面图片管理后台信息模型
type RedPacketImageAdminInfo struct {
	// 基础字段
	Id             int64       `json:"id" orm:"id" description:"图片ID"`
	UserId         int64       `json:"userId" orm:"user_id" description:"用户ID"`
	Username       string      `json:"username" orm:"username" description:"用户名"`
	ImageUrl       string      `json:"imageUrl" orm:"image_url" description:"图片URL"`
	Status         string      `json:"status" orm:"status" description:"状态"`
	RefuseReasonZh string      `json:"refuseReasonZh" orm:"refuse_reason_zh" description:"拒绝原因(中文)"`
	RefuseReasonEn string      `json:"refuseReasonEn" orm:"refuse_reason_en" description:"拒绝原因(英文)"`
	CreatedAt      *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`
	UpdatedAt      *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"`
	ReviewedAt     *gtime.Time `json:"reviewedAt" orm:"reviewed_at" description:"审核时间"`

	// 用户代理信息（默认用户不加前缀）
	FirstAgentName  string `json:"firstAgentName" orm:"first_agent_name" description:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" orm:"second_agent_name" description:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" orm:"third_agent_name" description:"三级代理名称"`

	// 用户Telegram信息（默认用户不加前缀）
	TelegramId       string `json:"telegramId" orm:"telegram_id" description:"Telegram ID"`
	TelegramUsername string `json:"telegramUsername" orm:"telegram_username" description:"Telegram用户名"`
	FirstName        string `json:"firstName" orm:"first_name" description:"真实姓名"`
}
