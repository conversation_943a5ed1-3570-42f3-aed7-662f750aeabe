package model

import (
	"admin-api/internal/model/entity"
)

// UserRechargeAdminInfo 用于后台用户充值记录列表展示，包含关联信息
type UserRechargeAdminInfo struct {
	entity.UserRecharges        // 嵌入基础充值记录实体
	Account              string `json:"account" orm:"account" description:"用户账号"`

	// 三级代理信息
	FirstAgentName  string `json:"firstAgentName" orm:"first_agent_name" description:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" orm:"second_agent_name" description:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" orm:"third_agent_name" description:"三级代理名称"`

	// 主备份账户telegram信息
	TelegramId       string `json:"telegramId" orm:"telegram_id" description:"主Telegram ID"`
	TelegramUsername string `json:"telegramUsername" orm:"telegram_username" description:"主Telegram用户名"`
	FirstName        string `json:"firstName" orm:"first_name" description:"主备份账户名字"`
}
