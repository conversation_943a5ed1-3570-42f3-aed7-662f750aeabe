package model

import (
	"encoding/json"
	"fmt"
)

// TableStructure represents the complete structure of a table configuration
type TableStructure struct {
	Columns []TableColumn `json:"columns"` // Column definitions
	Rows    []TableRow    `json:"rows"`    // Row data
	Meta    TableMeta     `json:"meta"`    // Metadata and configuration
}

// TableColumn defines a single column in the table
type TableColumn struct {
	ID           string                 `json:"id"`                     // Unique column identifier
	Name         string                 `json:"name"`                   // Display name for the column (shown to users)
	Key          string                 `json:"key"`                    // Actual key for data storage (used in key-value mapping)
	DataType     string                 `json:"dataType"`               // Column data type (text, number, boolean, date, select, etc.)
	Required     bool                   `json:"required"`               // Whether this column is required
	DefaultValue interface{}            `json:"defaultValue,omitempty"` // Default value for new rows
	Options      []string               `json:"options,omitempty"`      // For select types - predefined options
	RemoteConfig *RemoteDataConfig      `json:"remoteConfig,omitempty"` // For remote data sources
	Validation   map[string]interface{} `json:"validation,omitempty"`   // Validation rules (min, max, pattern, etc.)
	Width        string                 `json:"width,omitempty"`        // Column width (optional)
	Sortable     bool                   `json:"sortable"`               // Whether column is sortable
}

// TableRow represents a single row of data
type TableRow struct {
	ID   string                 `json:"id"`   // Unique row identifier
	Data map[string]interface{} `json:"data"` // Column_id -> value mapping
}

// TableMeta contains table configuration and metadata
type TableMeta struct {
	AllowAddRows      bool `json:"allowAddRows"`      // Allow users to add new rows
	AllowDeleteRows   bool `json:"allowDeleteRows"`   // Allow users to delete rows
	AllowEditRows     bool `json:"allowEditRows"`     // Allow users to edit row data
	AllowAddColumns   bool `json:"allowAddColumns"`   // Allow users to add new columns
	AllowDeleteColumns bool `json:"allowDeleteColumns"` // Allow users to delete columns
	AllowEditColumns  bool `json:"allowEditColumns"`  // Allow users to edit column definitions
	MaxRows           int  `json:"maxRows,omitempty"`     // Maximum number of rows (0 = unlimited)
	MaxColumns        int  `json:"maxColumns,omitempty"`  // Maximum number of columns (0 = unlimited)
	ShowRowNumbers    bool `json:"showRowNumbers"`        // Display row numbers
	Paginated         bool `json:"paginated"`             // Enable pagination for large tables
	PageSize          int  `json:"pageSize,omitempty"`    // Rows per page when paginated
}

// RemoteDataConfig defines configuration for fetching data from remote APIs
type RemoteDataConfig struct {
	Endpoint    string            `json:"endpoint"`              // Remote API endpoint URL
	Method      string            `json:"method,omitempty"`      // HTTP method (GET, POST) - default GET
	Headers     map[string]string `json:"headers,omitempty"`     // Custom headers for the request
	ValueField  string            `json:"valueField"`            // Field name to use as option value
	LabelField  string            `json:"labelField"`            // Field name to use as option label
	SearchParam string            `json:"searchParam,omitempty"` // Parameter name for search/filter functionality
	CacheTTL    int               `json:"cacheTtl,omitempty"`    // Cache time-to-live in seconds
	DependsOn   []string          `json:"dependsOn,omitempty"`   // Other column IDs this depends on
}

// Supported column data types
const (
	ColumnTypeText         = "text"
	ColumnTypeTextarea     = "textarea"
	ColumnTypeNumber       = "number"
	ColumnTypeNumberRange  = "number_range"
	ColumnTypeBoolean      = "boolean"
	ColumnTypeDate         = "date"
	ColumnTypeDatetime     = "datetime"
	ColumnTypeSingleSelect = "single_select"
	ColumnTypeMultiSelect  = "multi_select"
	ColumnTypeRadio        = "radio"
	ColumnTypeRemoteSelect = "remote_select"
	ColumnTypeRemoteMulti  = "remote_multi_select"
)

// ValidateTableStructure validates the table structure for consistency and correctness
func (ts *TableStructure) ValidateTableStructure() error {
	// Validate columns
	if len(ts.Columns) == 0 {
		return fmt.Errorf("table must have at least one column")
	}

	columnIDs := make(map[string]bool)
	columnKeys := make(map[string]bool)
	for i, col := range ts.Columns {
		// Check for duplicate column IDs
		if columnIDs[col.ID] {
			return fmt.Errorf("duplicate column ID: %s", col.ID)
		}
		columnIDs[col.ID] = true
		
		// Check for duplicate column keys
		if columnKeys[col.Key] {
			return fmt.Errorf("duplicate column key: %s", col.Key)
		}
		columnKeys[col.Key] = true

		// Validate column data type
		if !isValidColumnType(col.DataType) {
			return fmt.Errorf("invalid column data type: %s", col.DataType)
		}

		// Validate required fields
		if col.Name == "" {
			return fmt.Errorf("column %d: name is required", i)
		}
		if col.Key == "" {
			return fmt.Errorf("column %d: key is required", i)
		}
		if col.ID == "" {
			return fmt.Errorf("column %d: id is required", i)
		}

		// Validate remote config for remote types
		if isRemoteColumnType(col.DataType) && col.RemoteConfig == nil {
			return fmt.Errorf("column %s: remote configuration required for type %s", col.ID, col.DataType)
		}
		if col.RemoteConfig != nil {
			if err := col.RemoteConfig.Validate(); err != nil {
				return fmt.Errorf("column %s: %w", col.ID, err)
			}
		}

		// Validate select options for local select types
		if isLocalSelectType(col.DataType) && len(col.Options) == 0 {
			return fmt.Errorf("column %s: options required for type %s", col.ID, col.DataType)
		}
	}

	// Validate rows
	for i, row := range ts.Rows {
		if row.ID == "" {
			return fmt.Errorf("row %d: id is required", i)
		}

		// Validate row data against column definitions
		for colKey, value := range row.Data {
			// Find column by key
			var col *TableColumn
			for _, c := range ts.Columns {
				if c.Key == colKey {
					col = &c
					break
				}
			}
			
			if col == nil {
				return fmt.Errorf("row %s: unknown column key %s", row.ID, colKey)
			}

			// Validate value against column type
			if err := validateCellValue(value, col); err != nil {
				return fmt.Errorf("row %s, column %s: %w", row.ID, colKey, err)
			}
		}

		// Check required columns
		for _, col := range ts.Columns {
			if col.Required {
				if _, exists := row.Data[col.Key]; !exists {
					return fmt.Errorf("row %s: required column %s (key: %s) is missing", row.ID, col.Name, col.Key)
				}
			}
		}
	}

	return nil
}

// Validate validates remote data configuration
func (rdc *RemoteDataConfig) Validate() error {
	if rdc.Endpoint == "" {
		return fmt.Errorf("remote endpoint is required")
	}
	if rdc.ValueField == "" {
		return fmt.Errorf("value field is required")
	}
	if rdc.LabelField == "" {
		return fmt.Errorf("label field is required")
	}
	if rdc.Method != "" && rdc.Method != "GET" && rdc.Method != "POST" {
		return fmt.Errorf("invalid HTTP method: %s", rdc.Method)
	}
	return nil
}

// Helper functions

func isValidColumnType(dataType string) bool {
	validTypes := []string{
		ColumnTypeText, ColumnTypeTextarea, ColumnTypeNumber, ColumnTypeNumberRange, ColumnTypeBoolean,
		ColumnTypeDate, ColumnTypeDatetime, ColumnTypeSingleSelect, ColumnTypeMultiSelect,
		ColumnTypeRadio, ColumnTypeRemoteSelect, ColumnTypeRemoteMulti,
	}
	for _, valid := range validTypes {
		if dataType == valid {
			return true
		}
	}
	return false
}

func isRemoteColumnType(dataType string) bool {
	return dataType == ColumnTypeRemoteSelect || dataType == ColumnTypeRemoteMulti
}

func isLocalSelectType(dataType string) bool {
	return dataType == ColumnTypeSingleSelect || dataType == ColumnTypeMultiSelect || dataType == ColumnTypeRadio
}

func validateCellValue(value interface{}, col *TableColumn) error {
	// Basic type validation based on column type
	switch col.DataType {
	case ColumnTypeNumber:
		if value != nil {
			switch v := value.(type) {
			case float64, int, int64:
				// Valid number types
			case string:
				// Try to parse string as number
				if v != "" {
					var f float64
					if err := json.Unmarshal([]byte(v), &f); err != nil {
						return fmt.Errorf("invalid number value: %v", value)
					}
				}
			default:
				return fmt.Errorf("invalid number value: %v", value)
			}
		}
	case ColumnTypeNumberRange:
		if value != nil {
			// Number range should be a JSON object with min and max
			var rangeData map[string]interface{}
			switch v := value.(type) {
			case string:
				if v != "" {
					if err := json.Unmarshal([]byte(v), &rangeData); err != nil {
						return fmt.Errorf("invalid number range format: %v", err)
					}
				}
			case map[string]interface{}:
				rangeData = v
			default:
				return fmt.Errorf("number range must be a JSON object with min and max fields")
			}
			
			if rangeData != nil {
				// Check min and max exist
				minVal, hasMin := rangeData["min"]
				maxVal, hasMax := rangeData["max"]
				
				if !hasMin || !hasMax {
					return fmt.Errorf("number range must contain 'min' and 'max' fields")
				}
				
				// Validate min and max are numbers
				minFloat, minOk := minVal.(float64)
				maxFloat, maxOk := maxVal.(float64)
				
				if !minOk || !maxOk {
					return fmt.Errorf("'min' and 'max' must be numbers")
				}
				
				// Ensure min <= max
				if minFloat > maxFloat {
					return fmt.Errorf("'min' (%v) cannot be greater than 'max' (%v)", minFloat, maxFloat)
				}
			}
		}
	case ColumnTypeBoolean:
		if value != nil {
			switch v := value.(type) {
			case bool:
				// Valid boolean
			case string:
				if v != "true" && v != "false" && v != "" {
					return fmt.Errorf("invalid boolean value: %v", value)
				}
			default:
				return fmt.Errorf("invalid boolean value: %v", value)
			}
		}
	case ColumnTypeSingleSelect, ColumnTypeRadio:
		if value != nil {
			strValue := fmt.Sprintf("%v", value)
			if strValue != "" {
				// Check if value is in allowed options
				found := false
				for _, option := range col.Options {
					if option == strValue {
						found = true
						break
					}
				}
				if !found {
					return fmt.Errorf("value %v not in allowed options", value)
				}
			}
		}
	case ColumnTypeMultiSelect:
		if value != nil {
			// Multi-select values should be arrays
			switch v := value.(type) {
			case []interface{}:
				for _, item := range v {
					strItem := fmt.Sprintf("%v", item)
					found := false
					for _, option := range col.Options {
						if option == strItem {
							found = true
							break
						}
					}
					if !found {
						return fmt.Errorf("value %v not in allowed options", item)
					}
				}
			case []string:
				for _, item := range v {
					found := false
					for _, option := range col.Options {
						if option == item {
							found = true
							break
						}
					}
					if !found {
						return fmt.Errorf("value %v not in allowed options", item)
					}
				}
			default:
				return fmt.Errorf("multi-select value must be an array")
			}
		}
	}

	return nil
}

// ToJSON converts the table structure to JSON string for storage
func (ts *TableStructure) ToJSON() (string, error) {
	data, err := json.Marshal(ts)
	if err != nil {
		return "", fmt.Errorf("failed to marshal table structure: %w", err)
	}
	return string(data), nil
}

// FromJSON creates a TableStructure from JSON string
func FromJSON(jsonStr string) (*TableStructure, error) {
	var ts TableStructure
	if err := json.Unmarshal([]byte(jsonStr), &ts); err != nil {
		return nil, fmt.Errorf("failed to unmarshal table structure: %w", err)
	}
	return &ts, nil
}