// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdminConfigItemsCopy1 is the golang structure of table admin_config_items_copy1 for DAO operations like Where/Data.
type AdminConfigItemsCopy1 struct {
	g.Meta      `orm:"table:admin_config_items_copy1, do:true"`
	Id          interface{} // 配置项唯一标识符 (例如 UUID)
	CategoryId  interface{} // 所属分类 ID
	Key         interface{} // 配置键 (全局唯一, 包含分类前缀)
	Value       interface{} // 配置值 (以字符串存储)
	ValueType   interface{} // 配置值类型 (text, number, boolean, json etc.)
	Description interface{} // 描述 (可选)
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 更新时间
}
