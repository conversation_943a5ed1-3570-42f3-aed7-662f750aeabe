// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Wallets is the golang structure of table wallets for DAO operations like Where/Data.
type Wallets struct {
	g.Meta           `orm:"table:wallets, do:true"`
	WalletId         interface{} // 钱包记录唯一 ID
	LedgerId         interface{} // ledger 钱包id
	UserId           interface{} // 用户 ID (外键, 关联 users.user_id)
	TokenId          interface{} // 用户 ID (外键, 关联 users.user_id)
	AvailableBalance interface{} // 可用余额
	FrozenBalance    interface{} // 冻结余额 (例如: 挂单中, 提现处理中)
	DecimalPlaces    interface{} // 精度
	CreatedAt        *gtime.Time // 钱包记录创建时间
	UpdatedAt        *gtime.Time // 余额最后更新时间
	DeletedAt        *gtime.Time // 软删除的时间戳
	TelegramId       interface{} //
	Type             interface{} // 类型
	Symbol           interface{} // 代币符号 (例如: USDT, BTC, ETH)
}
