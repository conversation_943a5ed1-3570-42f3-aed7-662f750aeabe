// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// UserAddress is the golang structure of table user_address for DAO operations like Where/Data.
type UserAddress struct {
	g.Meta        `orm:"table:user_address, do:true"`
	UserAddressId interface{} //
	TokenId       interface{} // 币种ID
	UserId        interface{} // 用户id
	Lable         interface{} // 备注
	Name          interface{} // 币种
	Chan          interface{} // 链
	Address       interface{} // 地址
	Image         interface{} // 二维码
	CreatedAt     *gtime.Time //
	UpdatedAt     *gtime.Time //
	Type          interface{} //
}
