// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Images is the golang structure of table images for DAO operations like Where/Data.
type Images struct {
	g.Meta         `orm:"table:images, do:true"`
	ImagesId       interface{} // 红包 ID (主键)
	Status         interface{} // 图片状态 pending_review, fail, success
	CreatedAt      *gtime.Time // 创建时间
	DeletedAt      *gtime.Time // 软删除的时间戳
	UpdatedAt      *gtime.Time // 最后更新时间
	RefuseReasonZh interface{} // 拒绝原因 (中文)
	RefuseReasonEn interface{} // 拒绝原因 (英文)
	UserId         interface{} // 用户ID (Foreign key to users table recommended)
	ImagesUrl      interface{} //
	FileId         interface{} //
}
