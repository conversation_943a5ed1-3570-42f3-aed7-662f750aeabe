// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ExchangeProducts is the golang structure of table exchange_products for DAO operations like Where/Data.
type ExchangeProducts struct {
	g.Meta                 `orm:"table:exchange_products, do:true"`
	ProductId              interface{} // 兑换产品内部 ID (主键)
	BaseToken              interface{} // 基础代币 ID (用户卖出/花费的代币, 外键关联 tokens.token_id)
	QuoteToken             interface{} // 计价代币 ID (用户买入/获得的代币, 外键关联 tokens.token_id)
	Symbol                 interface{} // 产品交易对符号 (例如: BTC/USDT, ETH/BTC)
	ProductType            interface{} // 产品类型: swap-即时兑换, spot_limit-现货限价(若支持), 或其他自定义类型
	IsActive               interface{} // 该兑换产品是否激活可用 (总开关)
	AllowBuy               interface{} // 是否允许买入基础代币 (即 Quote->Base 的兑换)
	AllowSell              interface{} // 是否允许卖出基础代币 (即 Base->Quote 的兑换)
	MaintenanceMessage     interface{} // 维护信息 (当产品不可用或部分功能禁用时显示)
	MinBaseAmountPerTx     interface{} // 单笔最小兑换的基础代币数量
	MaxBaseAmountPerTx     interface{} // 单笔最大兑换的基础代币数量 (NULL 表示无特定限制)
	DailyBaseVolumeLimit   interface{} // 产品每日总兑换基础代币量上限 (平台风控, NULL 不限制)
	TotalBaseVolumeLimit   interface{} // 产品累计总兑换基础代币量上限 (平台风控, NULL 不限制)
	PriceSource            interface{} // 价格/汇率来源标识 (例如: internal_oracle, binance_feed, weighted_average)
	AllowedSlippagePercent interface{} // 允许的最大价格滑点百分比 (例如 0.0050 表示 0.5%). NULL 表示此产品无特定滑点设置
	SpreadRate             interface{} // 平台在报价基础上额外添加的价差率 (例如 0.001 表示 0.1%)
	RateRefreshIntervalSec interface{} // 建议的价格刷新频率 (秒), 应用层参考 (NULL 表示无建议)
	DisplayOrder           interface{} // 显示排序 (数字越小越靠前)
	Description            interface{} // 产品或交易对的描述信息
	CreatedAt              *gtime.Time // 创建时间
	UpdatedAt              *gtime.Time // 最后更新时间
	DeletedAt              *gtime.Time // 软删除时间
	Status                 interface{} // 状态
	FeeStrategy            interface{} // 手续费策略: output_token_percentage-从输出代币按百分比扣除
	OutputFeeRate          interface{} // 输出代币手续费率 (例如: 0.002 表示 0.2%)
	MinOutputFeeAmount     interface{} // 最小手续费金额（以输出代币计价）
}
