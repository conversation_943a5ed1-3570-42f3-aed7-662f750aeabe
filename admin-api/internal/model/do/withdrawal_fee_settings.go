// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// WithdrawalFeeSettings is the golang structure of table withdrawal_fee_settings for DAO operations like Where/Data.
type WithdrawalFeeSettings struct {
	g.Meta    `orm:"table:withdrawal_fee_settings, do:true"`
	Id        interface{} //
	Currency  interface{} // 币种符号 (如 USDT, BTC)
	Network   interface{} // 网络类型 (如 TRC20, ERC20)
	AmountMin interface{} // 单笔提现金额范围最小值
	AmountMax interface{} // 单笔提现金额范围最大值
	FeeType   interface{} // 手续费类型: fixed-固定金额, percent-百分比
	FeeValue  interface{} // 手续费值 (固定金额或百分比)
	Status    interface{} // 状态: 1-启用, 0-禁用
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
	DeletedAt *gtime.Time // 软删除时间
}
