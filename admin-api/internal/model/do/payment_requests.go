// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// PaymentRequests is the golang structure of table payment_requests for DAO operations like Where/Data.
type PaymentRequests struct {
	g.Meta                 `orm:"table:payment_requests, do:true"`
	RequestId              interface{} // 收款请求 ID (主键)
	RequesterUserId        interface{} // 收款发起者用户 ID (外键, 指向 users.user_id)
	RequesterUsername      interface{} // 收款发起者telegram username
	PayerUserId            interface{} // 指定付款人用户 ID (如果为空则任何人可付, 外键, 指向 users.user_id)
	PayerUsername          interface{} // 指定付款人用户telegram username
	TokenId                interface{} // 收款代币 ID (外键, 指向 tokens.token_id)
	Amount                 interface{} // 收款金额
	Memo                   interface{} // 收款说明/备注
	Status                 interface{} // 请求状态: 1-待支付(pending), 2-已支付(paid), 3-已过期(expired), 4-已取消(cancelled)
	PaymentTransactionId   interface{} // 关联的支付交易记录ID (外键, 指向 transactions.transaction_id)
	RequesterTransactionId interface{} // 关联的支付交易记录ID (外键, 指向 transactions.transaction_id)
	TelegramChatId         interface{} // 发起请求的 Telegram 聊天 ID (用于更新消息)
	TelegramMessageId      interface{} // 原始收款请求消息的 Telegram 消息 ID (用于更新消息)
	CreatedAt              *gtime.Time // 创建时间
	ExpiresAt              *gtime.Time // 过期时间 (例如: 创建时间 + 24小时)
	PaidAt                 *gtime.Time // 支付时间
	CancelledAt            *gtime.Time // 取消时间
	InlineMessageId        interface{} // 内联消息 ID，用于后续编辑
	UpdatedAt              *gtime.Time // 最后更新时间
	DeletedAt              *gtime.Time // 软删除的时间戳
	Symbol                 interface{} //
}
