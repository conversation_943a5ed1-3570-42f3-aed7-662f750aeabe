// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// MerchantCallbacks is the golang structure of table merchant_callbacks for DAO operations like Where/Data.
type MerchantCallbacks struct {
	g.Meta        `orm:"table:merchant_callbacks, do:true"`
	Id            interface{} //
	MerchantId    interface{} // 商户ID
	CallbackType  interface{} // 回调事件类型
	RelatedId     interface{} // 关联记录ID
	CallbackUrl   interface{} // 回调URL
	Payload       interface{} // 回调数据
	Status        interface{} // 回调状态
	RetryCount    interface{} // 重试次数
	ResponseCode  interface{} // 响应状态码
	ResponseBody  interface{} // 响应内容
	LastAttemptAt *gtime.Time // 最后尝试时间
	CreatedAt     *gtime.Time //
	UpdatedAt     *gtime.Time //
}
