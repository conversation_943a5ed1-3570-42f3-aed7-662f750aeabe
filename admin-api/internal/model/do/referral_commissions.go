// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ReferralCommissions is the golang structure of table referral_commissions for DAO operations like Where/Data.
type ReferralCommissions struct {
	g.Meta           `orm:"table:referral_commissions, do:true"`
	CommissionId     interface{} // 佣金记录 ID (主键)
	TransactionId    interface{} // 关联的触发佣金的交易 ID (例如: 下级的某笔操作, 外键指向 transactions.transaction_id)
	ReferrerId       interface{} // 获得佣金的用户 ID (外键, 指向 users.user_id)
	InviteeId        interface{} // 产生佣金的被推荐人用户 ID (外键, 指向 users.user_id)
	Level            interface{} // 佣金产生的推荐层级
	CommissionAmount interface{} // 佣金金额
	CommissionRate   interface{} // 佣金比率 (例如: 0.01 表示 1%)
	TokenId          interface{} // 佣金代币 ID (外键, 指向 tokens.token_id)
	Status           interface{} // 佣金状态: pending-待发放, paid-已发放, cancelled-已取消
	CreatedAt        *gtime.Time // 创建时间
	UpdatedAt        *gtime.Time // 最后更新时间
	DeletedAt        *gtime.Time // 软删除时间
}
