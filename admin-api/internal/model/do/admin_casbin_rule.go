// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// AdminCasbinRule is the golang structure of table admin_casbin_rule for DAO operations like Where/Data.
type AdminCasbinRule struct {
	g.Meta `orm:"table:admin_casbin_rule, do:true"`
	Id     interface{} //
	Ptype  interface{} //
	V0     interface{} //
	V1     interface{} //
	V2     interface{} //
	V3     interface{} //
	V4     interface{} //
	V5     interface{} //
}
