// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// MerchantDeposits is the golang structure of table merchant_deposits for DAO operations like Where/Data.
type MerchantDeposits struct {
	g.Meta               `orm:"table:merchant_deposits, do:true"`
	RechargesId          interface{} // 主键ID
	MerchantId           interface{} // 用户ID (Foreign key to users table recommended)
	TokenId              interface{} // 币种ID
	Name                 interface{} // 币种ID
	Chan                 interface{} //
	TokenContractAddress interface{} // 代币合约地址 (for non-native assets)
	FromAddress          interface{} // 来源地址 (发送方地址)
	ToAddress            interface{} // 目标地址 (平台分配的充值地址)
	TxHash               interface{} // 链上交易哈希/ID (Should be unique)
	Error                interface{} // 失败原因
	Amount               interface{} // 充值数量
	State                interface{} // 状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)
	FailureReason        interface{} // 失败原因
	Confirmations        interface{} // 状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)
	CreatedAt            *gtime.Time // 记录创建时间 (e.g., 链上发现时间)
	CompletedAt          *gtime.Time // 完成时间 (状态变为Completed的时间)
	NotificationSent     interface{} // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt   *gtime.Time // 通知发送时间
	UpdatedAt            *gtime.Time // 最后更新时间
}
