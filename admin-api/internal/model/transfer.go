package model

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// TransferAdminInfo 转账管理后台信息模型
type TransferAdminInfo struct {
	TransferId       int64       `json:"transferId"        orm:"transfer_id"         description:"转账记录ID"`
	SenderUserId     int64       `json:"senderUserId"      orm:"sender_user_id"      description:"发送方用户ID"`
	SenderUsername   string      `json:"senderUsername"    orm:"sender_username"     description:"发送方用户名"`
	SenderAccount    string      `json:"senderAccount"     orm:"sender_account"      description:"发送方账户"`
	ReceiverUserId   int64       `json:"receiverUserId"    orm:"receiver_user_id"    description:"接收方用户ID"`
	ReceiverUsername string      `json:"receiverUsername"  orm:"receiver_username"   description:"接收方用户名"`
	ReceiverAccount  string      `json:"receiverAccount"     orm:"receiver_account"      description:"接收方账户"`
	TokenId          int         `json:"tokenId"           orm:"token_id"            description:"代币ID"`
	TokenSymbol      string      `json:"tokenSymbol"       orm:"token_symbol"        description:"代币符号"` // This is from tokens table
	TokenDecimals    uint8       `json:"tokenDecimals"     orm:"token_decimals"      description:"代币小数位数"`
	Amount           int64       `json:"amount"            orm:"amount"              description:"转账金额(最小单位)"`
	AmountStr        string      `json:"amountStr"         orm:"-"                   description:"转账金额(格式化后)"`
	Memo             string      `json:"memo"              orm:"memo"                description:"转账备注"`
	SenderTxId       int64       `json:"senderTxId"        orm:"sender_transaction_id" description:"发送方交易ID"`
	ReceiverTxId     int64       `json:"receiverTxId"      orm:"receiver_transaction_id" description:"接收方交易ID"`
	CreatedAt        *gtime.Time `json:"createdAt"         orm:"created_at"          description:"创建时间"`
	MessageId        int64       `json:"messageId"         orm:"message_id"          description:"消息ID"`
	ChatId           int64       `json:"chatId"            orm:"chat_id"             description:"聊天ID"`
	Status           string      `json:"status"            orm:"status"              description:"转账状态"`
	HoldId           string      `json:"holdId"            orm:"hold_id"             description:"冻结ID"`
	ExpiresAt        *gtime.Time `json:"expiresAt"         orm:"expires_at"          description:"过期时间"`
	UpdatedAt        *gtime.Time `json:"updatedAt"         orm:"updated_at"          description:"更新时间"`
	NeedPass         int         `json:"needPass"          orm:"need_pass"           description:"是否需要密码 (0:否, 1:是)"`
	Key              string      `json:"key"               orm:"key"                 description:"转账密钥/唯一标识"`
	Symbol           string      `json:"symbol"            orm:"symbol"              description:"转账记录的币种符号 (来自transfers表)"`
	Message          string      `json:"message"           orm:"message"             description:"消息内容"`
	InlineMessageId  string      `json:"inlineMessageId"   orm:"inline_message_id"   description:"内联消息ID"`

	// 新增：发送方三级代理信息
	FirstAgentName  string `json:"firstAgentName" orm:"sender_first_agent_name" dc:"发送方一级代理名称"`
	SecondAgentName string `json:"secondAgentName" orm:"sender_second_agent_name" dc:"发送方二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" orm:"sender_third_agent_name" dc:"发送方三级代理名称"`

	// 新增：发送方主备份账户telegram信息
	TelegramId       string `json:"telegramId" orm:"sender_telegram_id" dc:"发送方Telegram ID"`
	TelegramUsername string `json:"telegramUsername" orm:"sender_telegram_username" dc:"发送方Telegram用户名"`
	FirstName        string `json:"firstName" orm:"sender_first_name" dc:"发送方备份账户名字"`

	// 新增：接收方三级代理信息
	ReceiverFirstAgentName  string `json:"receiverFirstAgentName" orm:"receiver_first_agent_name" dc:"接收方一级代理名称"`
	ReceiverSecondAgentName string `json:"receiverSecondAgentName" orm:"receiver_second_agent_name" dc:"接收方二级代理名称"`
	ReceiverThirdAgentName  string `json:"receiverThirdAgentName" orm:"receiver_third_agent_name" dc:"接收方三级代理名称"`

	// 新增：接收方主备份账户telegram信息
	ReceiverTelegramId       string `json:"receiverTelegramId" orm:"receiver_telegram_id" dc:"接收方Telegram ID"`
	ReceiverTelegramUsername string `json:"receiverTelegramUsername" orm:"receiver_telegram_username" dc:"接收方Telegram用户名"`
	ReceiverFirstName        string `json:"receiverFirstName" orm:"receiver_first_name" dc:"接收方备份账户名字"`
}
