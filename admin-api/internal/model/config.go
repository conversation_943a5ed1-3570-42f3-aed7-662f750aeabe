package model

// MinioConfig holds the configuration for Minio object storage.
type MinioConfig struct {
	Endpoint        string `mapstructure:"endpoint"        json:"endpoint"`        // Minio 服务器地址和端口
	AccessKeyID     string `mapstructure:"accessKeyID"     json:"accessKeyID"`     // Access Key ID
	SecretAccessKey string `mapstructure:"secretAccessKey" json:"secretAccessKey"` // Secret Access Key
	BucketName      string `mapstructure:"bucketName"      json:"bucketName"`      // Bucket 名称
	UseSSL          bool   `mapstructure:"useSSL"          json:"useSSL"`          // 是否使用 HTTPS (true/false)
	PublicURLPrefix string `mapstructure:"publicURLPrefix" json:"publicURLPrefix"` // 用于构建文件访问 URL 的前缀
}

// Add other shared configuration models here if needed in the future.
