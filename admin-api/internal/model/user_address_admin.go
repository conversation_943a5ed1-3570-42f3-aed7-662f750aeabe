package model

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// UserAddressAdminInfo 用户充值地址后台管理列表信息模型
type UserAddressAdminInfo struct {
	// 充值地址基础字段
	UserAddressId uint        `json:"userAddressId" orm:"user_address_id" description:"地址ID"`
	TokenId       uint        `json:"tokenId" orm:"token_id" description:"币种ID"`
	UserId        int64       `json:"userId" orm:"user_id" description:"用户ID"`
	UserAccount   string      `json:"userAccount" orm:"user_account" description:"用户账号"`
	Lable         string      `json:"lable" orm:"lable" description:"备注"`
	Name          string      `json:"name" orm:"name" description:"币种名称"`
	Chan          string      `json:"chan" orm:"chan" description:"链"`
	Address       string      `json:"address" orm:"address" description:"地址"`
	Image         string      `json:"image" orm:"image" description:"二维码"`
	Type          string      `json:"type" orm:"type" description:"地址类型"`
	CreatedAt     *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`
	UpdatedAt     *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"`

	// 用户代理信息
	FirstAgentName  string `json:"firstAgentName" orm:"first_agent_name" description:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" orm:"second_agent_name" description:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" orm:"third_agent_name" description:"三级代理名称"`

	// Telegram信息
	TelegramId       string `json:"telegramId" orm:"telegram_id" description:"Telegram ID"`
	TelegramUsername string `json:"telegramUsername" orm:"telegram_username" description:"Telegram用户名"`
	FirstName        string `json:"firstName" orm:"first_name" description:"真实姓名"`
}
