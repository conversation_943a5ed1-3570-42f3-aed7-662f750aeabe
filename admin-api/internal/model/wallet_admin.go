package model

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// WalletAdminInfo 钱包后台管理列表信息模型
type WalletAdminInfo struct {
	// 钱包基础字段
	WalletId         int64       `json:"walletId" orm:"wallet_id" description:"钱包ID"`
	UserId           int64       `json:"userId" orm:"user_id" description:"用户ID"`
	UserAccount      string      `json:"userAccount" orm:"user_account" description:"用户账号"`
	TokenId          int         `json:"tokenId" orm:"token_id" description:"代币ID"`
	Symbol           string      `json:"symbol" orm:"symbol" description:"代币符号"`
	Type             string      `json:"type" orm:"type" description:"钱包类型"`
	AvailableBalance int64       `json:"availableBalance" orm:"available_balance" description:"可用余额"`
	FrozenBalance    int64       `json:"frozenBalance" orm:"frozen_balance" description:"冻结余额"`
	DecimalPlaces    uint        `json:"decimalPlaces" orm:"decimal_places" description:"小数位数"`
	CreatedAt        *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`
	UpdatedAt        *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"`

	// 用户代理信息
	FirstAgentName  string `json:"firstAgentName" orm:"first_agent_name" description:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" orm:"second_agent_name" description:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" orm:"third_agent_name" description:"三级代理名称"`

	// Telegram信息
	TelegramId       string `json:"telegramId" orm:"telegram_id" description:"Telegram ID"`
	TelegramUsername string `json:"telegramUsername" orm:"telegram_username" description:"Telegram用户名"`
	FirstName        string `json:"firstName" orm:"first_name" description:"真实姓名"`
}
