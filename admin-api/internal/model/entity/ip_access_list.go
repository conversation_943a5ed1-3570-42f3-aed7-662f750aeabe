// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// IpAccessList is the golang structure for table ip_access_list.
type IpAccessList struct {
	Id          int64       `json:"id"          orm:"id"          description:"记录ID"`                                // 记录ID
	UserId      int64       `json:"userId"      orm:"user_id"     description:"用户ID (关联用户表)"`                        // 用户ID (关联用户表)
	AgentId     int64       `json:"agentId"     orm:"agent_id"    description:"代理ID (关联代理表)"`                        // 代理ID (关联代理表)
	AdminId     int64       `json:"adminId"     orm:"admin_id"    description:"管理员ID (关联管理员表)"`                      // 管理员ID (关联管理员表)
	MerchantId  int64       `json:"merchantId"  orm:"merchant_id" description:"管理员ID (关联管理员表)"`                      // 管理员ID (关联管理员表)
	ListType    string      `json:"listType"    orm:"list_type"   description:"列表类型 (blacklist-黑名单, whitelist-白名单)"` // 列表类型 (blacklist-黑名单, whitelist-白名单)
	UseType     string      `json:"useType"     orm:"use_type"    description:"适用类型"`                                // 适用类型
	Description string      `json:"description" orm:"description" description:"规则描述 (可选, 如添加原因或来源)"`                 // 规则描述 (可选, 如添加原因或来源)
	IpAddress   string      `json:"ipAddress"   orm:"ip_address"  description:"IP地址 (支持IPv4和IPv6)"`                  // IP地址 (支持IPv4和IPv6)
	Reason      string      `json:"reason"      orm:"reason"      description:"原因 (拉黑或加白)"`                          // 原因 (拉黑或加白)
	AddedBy     string      `json:"addedBy"     orm:"added_by"    description:"操作者 (用户名或系统标识)"`                      // 操作者 (用户名或系统标识)
	ExpiresAt   *gtime.Time `json:"expiresAt"   orm:"expires_at"  description:"过期时间 (NULL表示永久)"`                     // 过期时间 (NULL表示永久)
	IsEnabled   int         `json:"isEnabled"   orm:"is_enabled"  description:"是否启用 (1-启用, 0-禁用)"`                   // 是否启用 (1-启用, 0-禁用)
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"  description:"创建时间"`                                // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"  description:"修改时间"`                                // 修改时间
	DeletedAt   *gtime.Time `json:"deletedAt"   orm:"deleted_at"  description:"软删除的时间戳"`                             // 软删除的时间戳
}
