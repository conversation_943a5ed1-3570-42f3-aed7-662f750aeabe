// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// RedPackets is the golang structure for table red_packets.
type RedPackets struct {
	RedPacketId       int64           `json:"redPacketId"       orm:"red_packet_id"        description:"红包 ID (主键)"`                                              // 红包 ID (主键)
	Uuid              string          `json:"uuid"              orm:"uuid"                 description:"红包唯一id"`                                                  // 红包唯一id
	RedPacketImagesId int64           `json:"redPacketImagesId" orm:"red_packet_images_id" description:"图片id 外键id 指向red_packet_images.red_packet_images_id"`      // 图片id 外键id 指向red_packet_images.red_packet_images_id
	CreatorUserId     int64           `json:"creatorUserId"     orm:"creator_user_id"      description:"创建者用户 ID (外键, 指向 users.user_id)"`                         // 创建者用户 ID (外键, 指向 users.user_id)
	CreatorUsername   string          `json:"creatorUsername"   orm:"creator_username"     description:"创建者用户 ID (外键, 指向 users.user_id)"`                         // 创建者用户 ID (外键, 指向 users.user_id)
	CoverFileId       string          `json:"coverFileId"       orm:"cover_file_id"        description:"创建者用户 ID (外键, 指向 users.user_id)"`                         // 创建者用户 ID (外键, 指向 users.user_id)
	ThumbUrl          string          `json:"thumbUrl"          orm:"thumb_url"            description:"创建者用户 ID (外键, 指向 users.user_id)"`                         // 创建者用户 ID (外键, 指向 users.user_id)
	TokenId           int             `json:"tokenId"           orm:"token_id"             description:"红包代币 ID (外键, 指向 tokens.token_id)"`                        // 红包代币 ID (外键, 指向 tokens.token_id)
	TotalAmount       decimal.Decimal `json:"totalAmount"       orm:"total_amount"         description:"红包总金额"`                                                   // 红包总金额
	Quantity          int             `json:"quantity"          orm:"quantity"             description:"红包总个数"`                                                   // 红包总个数
	RemainingAmount   decimal.Decimal `json:"remainingAmount"   orm:"remaining_amount"     description:"剩余金额"`                                                    // 剩余金额
	RemainingQuantity int             `json:"remainingQuantity" orm:"remaining_quantity"   description:"剩余个数"`                                                    // 剩余个数
	Type              string          `json:"type"              orm:"type"                 description:"红包类型: random-随机金额, fixed-固定金额"`                           // 红包类型: random-随机金额, fixed-固定金额
	Memo              string          `json:"memo"              orm:"memo"                 description:"红包留言"`                                                    // 红包留言
	Status            string          `json:"status"            orm:"status"               description:"红包状态: active-可领取, expired-已过期, empty-已领完, cancelled-已取消"` // 红包状态: active-可领取, expired-已过期, empty-已领完, cancelled-已取消
	CreatedAt         *gtime.Time     `json:"createdAt"         orm:"created_at"           description:"创建时间"`                                                    // 创建时间
	ExpiresAt         *gtime.Time     `json:"expiresAt"         orm:"expires_at"           description:"过期时间"`                                                    // 过期时间
	DeletedAt         *gtime.Time     `json:"deletedAt"         orm:"deleted_at"           description:"软删除的时间戳"`                                                 // 软删除的时间戳
	SenderUserId      uint64          `json:"senderUserId"      orm:"sender_user_id"       description:"发送方用户 ID (外键, 指向 users.user_id)"`                         // 发送方用户 ID (外键, 指向 users.user_id)
	Symbol            string          `json:"symbol"            orm:"symbol"               description:"代币符号 (例如: USDT, BTC, ETH)"`                               // 代币符号 (例如: USDT, BTC, ETH)
	TransactionId     uint64          `json:"transactionId"     orm:"transaction_id"       description:"关联的扣款交易流水 ID (外键, 指向 transactions.id)"`                   // 关联的扣款交易流水 ID (外键, 指向 transactions.id)
	IsPremium         int             `json:"isPremium"         orm:"is_premium"           description:"是否需要会员"`                                                  // 是否需要会员
	MessageId         string          `json:"messageId"         orm:"message_id"           description:"内联消息 ID，用于后续编辑"`                                          // 内联消息 ID，用于后续编辑
}
