// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SystemSettings is the golang structure for table system_settings.
type SystemSettings struct {
	SettingKey   string      `json:"settingKey"   orm:"setting_key"   description:"设置键 (主键)"` // 设置键 (主键)
	SettingValue string      `json:"settingValue" orm:"setting_value" description:"设置值"`      // 设置值
	Description  string      `json:"description"  orm:"description"   description:"设置描述"`     // 设置描述
	CreatedAt    *gtime.Time `json:"createdAt"    orm:"created_at"    description:"创建时间"`     // 创建时间
	UpdatedAt    *gtime.Time `json:"updatedAt"    orm:"updated_at"    description:"最后更新时间"`   // 最后更新时间
	DeletedAt    *gtime.Time `json:"deletedAt"    orm:"deleted_at"    description:"软删除时间"`    // 软删除时间
}
