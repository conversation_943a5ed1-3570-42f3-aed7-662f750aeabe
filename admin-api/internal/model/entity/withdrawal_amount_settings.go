// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// WithdrawalAmountSettings is the golang structure for table withdrawal_amount_settings.
type WithdrawalAmountSettings struct {
	Id        uint64          `json:"id"        orm:"id"         description:""`                      //
	Currency  string          `json:"currency"  orm:"currency"   description:"币种符号 (如 USDT, BTC)"`    // 币种符号 (如 USDT, BTC)
	Network   string          `json:"network"   orm:"network"    description:"网络类型 (如 TRC20, ERC20)"` // 网络类型 (如 TRC20, ERC20)
	MinAmount decimal.Decimal `json:"minAmount" orm:"min_amount" description:"单笔最小提现金额"`              // 单笔最小提现金额
	MaxAmount decimal.Decimal `json:"maxAmount" orm:"max_amount" description:"单笔最大提现金额"`              // 单笔最大提现金额
	Status    int             `json:"status"    orm:"status"     description:"状态: 1-启用, 0-禁用"`        // 状态: 1-启用, 0-禁用
	CreatedAt *gtime.Time     `json:"createdAt" orm:"created_at" description:"创建时间"`                  // 创建时间
	UpdatedAt *gtime.Time     `json:"updatedAt" orm:"updated_at" description:"更新时间"`                  // 更新时间
	DeletedAt *gtime.Time     `json:"deletedAt" orm:"deleted_at" description:"软删除时间"`                 // 软删除时间
}
