// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// MerchantAddress is the golang structure for table merchant_address.
type MerchantAddress struct {
	AddressId  uint        `json:"addressId"  orm:"address_id"  description:""`         //
	TokenId    uint        `json:"tokenId"    orm:"token_id"    description:"币种ID"`     // 币种ID
	MerchantId int64       `json:"merchantId" orm:"merchant_id" description:"用户id"`     // 用户id
	Lable      string      `json:"lable"      orm:"lable"       description:"备注"`       // 备注
	Name       string      `json:"name"       orm:"name"        description:"币种"`       // 币种
	Chan       string      `json:"chan"       orm:"chan"        description:"链"`        // 链
	Address    string      `json:"address"    orm:"address"     description:"地址"`       // 地址
	Image      string      `json:"image"      orm:"image"       description:"二维码"`      // 二维码
	QrUrl      string      `json:"qrUrl"      orm:"qr_url"      description:"S3二维码URL"` // S3二维码URL
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"  description:""`         //
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at"  description:""`         //
	Type       string      `json:"type"       orm:"type"        description:""`         //
}
