// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/os/gtime"
)

// OperationLog is the golang structure for table operation_log.
type OperationLog struct {
	Id            int64       `json:"id"            orm:"id"             description:"日志ID"`           // 日志ID
	ReqId         string      `json:"reqId"         orm:"req_id"         description:"请求ID"`           // 请求ID
	MemberType    string      `json:"memberType"    orm:"member_type"    description:"用户类型"`           // 用户类型
	MemberId      int64       `json:"memberId"      orm:"member_id"      description:"用户ID"`           // 用户ID
	Username      string      `json:"username"      orm:"username"       description:"用户名"`            // 用户名
	Module        string      `json:"module"        orm:"module"         description:"操作模块"`           // 操作模块
	Action        string      `json:"action"        orm:"action"         description:"操作名称"`           // 操作名称
	RequestMethod string      `json:"requestMethod" orm:"request_method" description:"请求方法"`           // 请求方法
	RequestUrl    string      `json:"requestUrl"    orm:"request_url"    description:"请求URL"`          // 请求URL
	RequestParams *gjson.Json `json:"requestParams" orm:"request_params" description:"请求参数"`           // 请求参数
	Response      *gjson.Json `json:"response"      orm:"response"       description:"响应数据"`           // 响应数据
	Duration      int         `json:"duration"      orm:"duration"       description:"操作时长(ms)"`       // 操作时长(ms)
	OperationIp   string      `json:"operationIp"   orm:"operation_ip"   description:"操作IP"`           // 操作IP
	ProvinceId    int64       `json:"provinceId"    orm:"province_id"    description:"省编码"`            // 省编码
	CityId        int64       `json:"cityId"        orm:"city_id"        description:"市编码"`            // 市编码
	UserAgent     string      `json:"userAgent"     orm:"user_agent"     description:"UA信息"`           // UA信息
	ErrMsg        string      `json:"errMsg"        orm:"err_msg"        description:"错误提示"`           // 错误提示
	Status        int         `json:"status"        orm:"status"         description:"状态 (1-成功 0-失败)"` // 状态 (1-成功 0-失败)
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"     description:"创建时间 (操作时间)"`    // 创建时间 (操作时间)
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"     description:"修改时间"`           // 修改时间
	DeletedAt     *gtime.Time `json:"deletedAt"     orm:"deleted_at"     description:"软删除的时间戳"`        // 软删除的时间戳
}
