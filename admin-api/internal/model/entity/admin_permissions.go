// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdminPermissions is the golang structure for table admin_permissions.
type AdminPermissions struct {
	Id        int64       `json:"id"        orm:"id"         description:""`                                          //
	Name      string      `json:"name"      orm:"name"       description:"权限名称 (给人看，例如：保存订单按钮)"`                      // 权限名称 (给人看，例如：保存订单按钮)
	Pid       int64       `json:"pid"       orm:"pid"        description:"父权限ID"`                                     // 父权限ID
	Key       string      `json:"key"       orm:"key"        description:"权限标识符 (给 Casbin 用，例如：page:order:btn_save)"` // 权限标识符 (给 Casbin 用，例如：page:order:btn_save)
	Type      string      `json:"type"      orm:"type"       description:"权限类型 (例如: menu, api, button)"`              // 权限类型 (例如: menu, api, button)
	ParentKey string      `json:"parentKey" orm:"parent_key" description:"父权限标识符 (用于分组展示)"`                           // 父权限标识符 (用于分组展示)
	Remark    string      `json:"remark"    orm:"remark"     description:"备注"`                                        // 备注
	Status    int         `json:"status"    orm:"status"     description:"角色状态"`                                      // 角色状态
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`                                      // 创建时间
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"`                                      // 更新时间
	DeletedAt *gtime.Time `json:"deletedAt" orm:"deleted_at" description:"软删除的时间戳"`                                   // 软删除的时间戳
	Level     int         `json:"level"     orm:"level"      description:"关系树等级"`                                     // 关系树等级
	Tree      string      `json:"tree"      orm:"tree"       description:"关系树"`                                       // 关系树
	Sort      int         `json:"sort"      orm:"sort"       description:"排序"`                                        // 排序
}
