// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdminMenu is the golang structure for table admin_menu.
type AdminMenu struct {
	Id                 int64       `json:"id"                 orm:"id"                   description:"菜单ID"`    // 菜单ID
	Pid                int64       `json:"pid"                orm:"pid"                  description:"父菜单ID"`   // 父菜单ID
	Level              int         `json:"level"              orm:"level"                description:"关系树等级"`   // 关系树等级
	Tree               string      `json:"tree"               orm:"tree"                 description:"关系树"`     // 关系树
	Name               string      `json:"name"               orm:"name"                 description:"名称编码"`    // 名称编码
	Path               string      `json:"path"               orm:"path"                 description:"路由地址"`    // 路由地址
	Icon               string      `json:"icon"               orm:"icon"                 description:"菜单图标"`    // 菜单图标
	HideInMenu         int         `json:"hideInMenu"         orm:"hide_in_menu"         description:"是否隐藏"`    // 是否隐藏
	HideChildrenInMenu int         `json:"hideChildrenInMenu" orm:"hide_childrenIn_menu" description:"是否隐藏子菜单"` // 是否隐藏子菜单
	Sort               int         `json:"sort"               orm:"sort"                 description:"排序"`      // 排序
	Remark             string      `json:"remark"             orm:"remark"               description:"备注"`      // 备注
	Status             int         `json:"status"             orm:"status"               description:"菜单状态"`    // 菜单状态
	UpdatedAt          *gtime.Time `json:"updatedAt"          orm:"updated_at"           description:"更新时间"`    // 更新时间
	CreatedAt          *gtime.Time `json:"createdAt"          orm:"created_at"           description:"创建时间"`    // 创建时间
	DeletedAt          *gtime.Time `json:"deletedAt"          orm:"deleted_at"           description:"软删除的时间戳"` // 软删除的时间戳
	Target             string      `json:"target"             orm:"target"               description:""`        //
	Access             string      `json:"access"             orm:"access"               description:""`        //
	Key                string      `json:"key"                orm:"key"                  description:""`        //
}
