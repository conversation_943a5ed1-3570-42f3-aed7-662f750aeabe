// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// WithdrawalFeeSettings is the golang structure for table withdrawal_fee_settings.
type WithdrawalFeeSettings struct {
	Id        uint64          `json:"id"        orm:"id"         description:""`                               //
	Currency  string          `json:"currency"  orm:"currency"   description:"币种符号 (如 USDT, BTC)"`             // 币种符号 (如 USDT, BTC)
	Network   string          `json:"network"   orm:"network"    description:"网络类型 (如 TRC20, ERC20)"`          // 网络类型 (如 TRC20, ERC20)
	AmountMin decimal.Decimal `json:"amountMin" orm:"amount_min" description:"单笔提现金额范围最小值"`                    // 单笔提现金额范围最小值
	AmountMax decimal.Decimal `json:"amountMax" orm:"amount_max" description:"单笔提现金额范围最大值"`                    // 单笔提现金额范围最大值
	FeeType   string          `json:"feeType"   orm:"fee_type"   description:"手续费类型: fixed-固定金额, percent-百分比"` // 手续费类型: fixed-固定金额, percent-百分比
	FeeValue  decimal.Decimal `json:"feeValue"  orm:"fee_value"  description:"手续费值 (固定金额或百分比)"`                // 手续费值 (固定金额或百分比)
	Status    int             `json:"status"    orm:"status"     description:"状态: 1-启用, 0-禁用"`                 // 状态: 1-启用, 0-禁用
	CreatedAt *gtime.Time     `json:"createdAt" orm:"created_at" description:"创建时间"`                           // 创建时间
	UpdatedAt *gtime.Time     `json:"updatedAt" orm:"updated_at" description:"更新时间"`                           // 更新时间
	DeletedAt *gtime.Time     `json:"deletedAt" orm:"deleted_at" description:"软删除时间"`                          // 软删除时间
}
