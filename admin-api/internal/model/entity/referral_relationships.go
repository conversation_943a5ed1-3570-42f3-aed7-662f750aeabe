// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ReferralRelationships is the golang structure for table referral_relationships.
type ReferralRelationships struct {
	Type           string      `json:"type"           orm:"type"            description:"上级类型 代理，用户"`                       // 上级类型 代理，用户
	RelationshipId int64       `json:"relationshipId" orm:"relationship_id" description:"关系 ID (主键)"`                       // 关系 ID (主键)
	UserId         int64       `json:"userId"         orm:"user_id"         description:"被推荐人用户 ID (外键, 指向 users.user_id)"` // 被推荐人用户 ID (外键, 指向 users.user_id)
	ReferrerId     int64       `json:"referrerId"     orm:"referrer_id"     description:"推荐人用户 ID (外键, 指向 users.user_id)"`  // 推荐人用户 ID (外键, 指向 users.user_id)
	Level          int         `json:"level"          orm:"level"           description:"推荐层级 (1 表示直接推荐, 2 表示间接推荐, 以此类推)"`  // 推荐层级 (1 表示直接推荐, 2 表示间接推荐, 以此类推)
	CreatedAt      *gtime.Time `json:"createdAt"      orm:"created_at"      description:"关系创建时间"`                           // 关系创建时间
	DeletedAt      *gtime.Time `json:"deletedAt"      orm:"deleted_at"      description:"软删除的时间戳"`                          // 软删除的时间戳
}
