// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Agents is the golang structure for table agents.
type Agents struct {
	AgentId                   uint        `json:"agentId"                   orm:"agent_id"                    description:"代理唯一ID"`                           // 代理唯一ID
	Username                  string      `json:"username"                  orm:"username"                    description:"代理登录用户名"`                          // 代理登录用户名
	PasswordHash              string      `json:"passwordHash"              orm:"password_hash"               description:"加密后的登录密码"`                         // 加密后的登录密码
	AgentName                 string      `json:"agentName"                 orm:"agent_name"                  description:"代理真实姓名或昵称"`                        // 代理真实姓名或昵称
	Email                     string      `json:"email"                     orm:"email"                       description:"电子邮箱"`                             // 电子邮箱
	Level                     uint        `json:"level"                     orm:"level"                       description:"代理级别 (1: 一级代理, 2: 二级代理, 3: 三级代理)"` // 代理级别 (1: 一级代理, 2: 二级代理, 3: 三级代理)
	ParentAgentId             uint        `json:"parentAgentId"             orm:"parent_agent_id"             description:"上级代理ID (一级代理此字段为NULL)"`            // 上级代理ID (一级代理此字段为NULL)
	Status                    int         `json:"status"                    orm:"status"                      description:"账户状态 (0-禁用, 1-启用)"`                // 账户状态 (0-禁用, 1-启用)
	InvitationCode            string      `json:"invitationCode"            orm:"invitation_code"             description:"专属邀请码 (用于下级注册)"`                   // 专属邀请码 (用于下级注册)
	GoogleAuthenticatorSecret string      `json:"googleAuthenticatorSecret" orm:"google_authenticator_secret" description:"Google Authenticator 的秘钥 (用于2FA)"` // Google Authenticator 的秘钥 (用于2FA)
	Relationship              string      `json:"relationship"              orm:"relationship"                description:"代理商关系 (例如层级路径或其他关系标识)"`            // 代理商关系 (例如层级路径或其他关系标识)
	CreatedAt                 *gtime.Time `json:"createdAt"                 orm:"created_at"                  description:"创建时间"`                             // 创建时间
	UpdatedAt                 *gtime.Time `json:"updatedAt"                 orm:"updated_at"                  description:"最后更新时间"`                           // 最后更新时间
	DeletedAt                 *gtime.Time `json:"deletedAt"                 orm:"deleted_at"                  description:"软删除时间"`                            // 软删除时间
}
