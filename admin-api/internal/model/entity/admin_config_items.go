// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdminConfigItems is the golang structure for table admin_config_items.
type AdminConfigItems struct {
	Id          int         `json:"id"          orm:"id"          description:"配置项唯一标识符 (例如 UUID)"`                       // 配置项唯一标识符 (例如 UUID)
	CategoryId  string      `json:"categoryId"  orm:"category_id" description:"所属分类 ID"`                                  // 所属分类 ID
	Key         string      `json:"key"         orm:"key"         description:"配置键 (全局唯一, 包含分类前缀)"`                       // 配置键 (全局唯一, 包含分类前缀)
	Value       string      `json:"value"       orm:"value"       description:"配置值 (以字符串存储)"`                             // 配置值 (以字符串存储)
	ValueType   string      `json:"valueType"   orm:"value_type"  description:"配置值类型 (text, number, boolean, json etc.)"` // 配置值类型 (text, number, boolean, json etc.)
	Description string      `json:"description" orm:"description" description:"描述 (可选)"`                                  // 描述 (可选)
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"  description:"创建时间"`                                     // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"  description:"更新时间"`                                     // 更新时间
}
