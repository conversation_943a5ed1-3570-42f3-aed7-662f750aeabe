// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// MerchantDeposits is the golang structure for table merchant_deposits.
type MerchantDeposits struct {
	RechargesId          uint            `json:"rechargesId"          orm:"recharges_id"           description:"主键ID"`                                          // 主键ID
	MerchantId           uint64          `json:"merchantId"           orm:"merchant_id"            description:"用户ID (Foreign key to users table recommended)"` // 用户ID (Foreign key to users table recommended)
	TokenId              uint            `json:"tokenId"              orm:"token_id"               description:"币种ID"`                                          // 币种ID
	Name                 string          `json:"name"                 orm:"name"                   description:"币种ID"`                                          // 币种ID
	Chan                 string          `json:"chan"                 orm:"chan"                   description:""`                                              //
	TokenContractAddress string          `json:"tokenContractAddress" orm:"token_contract_address" description:"代币合约地址 (for non-native assets)"`                // 代币合约地址 (for non-native assets)
	FromAddress          string          `json:"fromAddress"          orm:"from_address"           description:"来源地址 (发送方地址)"`                                  // 来源地址 (发送方地址)
	ToAddress            string          `json:"toAddress"            orm:"to_address"             description:"目标地址 (平台分配的充值地址)"`                              // 目标地址 (平台分配的充值地址)
	TxHash               string          `json:"txHash"               orm:"tx_hash"                description:"链上交易哈希/ID (Should be unique)"`                  // 链上交易哈希/ID (Should be unique)
	Error                string          `json:"error"                orm:"error"                  description:"失败原因"`                                          // 失败原因
	Amount               decimal.Decimal `json:"amount"               orm:"amount"                 description:"充值数量"`                                          // 充值数量
	State                uint            `json:"state"                orm:"state"                  description:"状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)"`  // 状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)
	FailureReason        string          `json:"failureReason"        orm:"failure_reason"         description:"失败原因"`                                          // 失败原因
	Confirmations        uint            `json:"confirmations"        orm:"confirmations"          description:"状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)"`  // 状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)
	CreatedAt            *gtime.Time     `json:"createdAt"            orm:"created_at"             description:"记录创建时间 (e.g., 链上发现时间)"`                         // 记录创建时间 (e.g., 链上发现时间)
	CompletedAt          *gtime.Time     `json:"completedAt"          orm:"completed_at"           description:"完成时间 (状态变为Completed的时间)"`                       // 完成时间 (状态变为Completed的时间)
	NotificationSent     uint            `json:"notificationSent"     orm:"notification_sent"      description:"是否已发送通知: 0-未发送, 1-已发送"`                         // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt   *gtime.Time     `json:"notificationSentAt"   orm:"notification_sent_at"   description:"通知发送时间"`                                        // 通知发送时间
	UpdatedAt            *gtime.Time     `json:"updatedAt"            orm:"updated_at"             description:"最后更新时间"`                                        // 最后更新时间
}
