// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// RedPacketClaims is the golang structure for table red_packet_claims.
type RedPacketClaims struct {
	ClaimId            int64           `json:"claimId"            orm:"claim_id"             description:"领取记录 ID (主键)"`                                      // 领取记录 ID (主键)
	RedPacketId        int64           `json:"redPacketId"        orm:"red_packet_id"        description:"红包 ID (外键, 指向 red_packets.red_packet_id)"`          // 红包 ID (外键, 指向 red_packets.red_packet_id)
	ClaimerUserId      int64           `json:"claimerUserId"      orm:"claimer_user_id"      description:"领取者用户 ID (外键, 指向 users.user_id)"`                   // 领取者用户 ID (外键, 指向 users.user_id)
	Amount             decimal.Decimal `json:"amount"             orm:"amount"               description:"领取金额"`                                              // 领取金额
	TransactionId      int64           `json:"transactionId"      orm:"transaction_id"       description:"关联的资金入账交易 ID (外键, 指向 transactions.transaction_id)"` // 关联的资金入账交易 ID (外键, 指向 transactions.transaction_id)
	ClaimedAt          *gtime.Time     `json:"claimedAt"          orm:"claimed_at"           description:"领取时间"`                                              // 领取时间
	DeletedAt          *gtime.Time     `json:"deletedAt"          orm:"deleted_at"           description:"软删除的时间戳"`                                           // 软删除的时间戳
	SenderUserId       uint64          `json:"senderUserId"       orm:"sender_user_id"       description:"发送方用户 ID (外键, 指向 users.user_id)"`                   // 发送方用户 ID (外键, 指向 users.user_id)
	ReceiverUserId     uint64          `json:"receiverUserId"     orm:"receiver_user_id"     description:"接收方用户 ID (外键, 指向 users.user_id)"`                   // 接收方用户 ID (外键, 指向 users.user_id)
	SenderUsername     string          `json:"senderUsername"     orm:"sender_username"      description:"发送方用户名"`                                            // 发送方用户名
	ReceiverUsername   string          `json:"receiverUsername"   orm:"receiver_username"    description:"接收方用户名"`                                            // 接收方用户名
	Status             string          `json:"status"             orm:"status"               description:"红包状态 (pending: 待领取, claimed: 已领取, cancelled: 已取消)"` // 红包状态 (pending: 待领取, claimed: 已领取, cancelled: 已取消)
	Symbol             string          `json:"symbol"             orm:"symbol"               description:"代币符号 (例如: USDT, BTC, ETH)"`                         // 代币符号 (例如: USDT, BTC, ETH)
	NotificationSent   uint            `json:"notificationSent"   orm:"notification_sent"    description:"是否已发送通知: 0-未发送, 1-已发送"`                             // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt *gtime.Time     `json:"notificationSentAt" orm:"notification_sent_at" description:"通知发送时间"`                                            // 通知发送时间
}
