package model

import (
	"admin-api/internal/model/entity"
)

// TransactionAdminInfo 用于后台交易列表展示，包含关联信息
type TransactionAdminInfo struct {
	entity.Transactions        // 嵌入基础交易实体
	Username            string `json:"username"`    // 关联的用户名称 (来自 users 表)
	TokenSymbol         string `json:"tokenSymbol"` // 关联的代币符号 (来自 tokens 表)

	// 新增：三级代理信息
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称"`

	// 新增：主备份账户telegram信息
	TelegramId       string `json:"telegramId" dc:"主Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"主Telegram用户名"`
	FirstName        string `json:"firstName" dc:"主备份账户名字"`
}
