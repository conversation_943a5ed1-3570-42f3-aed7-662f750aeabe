{"ADMIN_API_SERVER_ADDRESS": ":7999", "ADMIN_API_SERVER_OPENAPIPATH": "/api.json", "ADMIN_API_SERVER_SWAGGERPATH": "/swagger", "ADMIN_API_SERVER_LOGPATH": "logs", "ADMIN_API_SERVER_DUMPROUTERMAP": false, "ADMIN_API_DEV_MODE": true, "ADMIN_API_I18N_PATH": "manifest/i18n", "ADMIN_API_I18N_LANGUAGE": "zh-CN", "ADMIN_API_DATABASE_LOGGER_PATH": "logs/database", "ADMIN_API_DATABASE_LOGGER_LEVEL": "all", "ADMIN_API_DATABASE_LOGGER_STDOUT": true, "ADMIN_API_DATABASE_DEFAULT_LINK": "mysql:root:root@tcp(mysql:3306)/xpayapi?loc=Local&parseTime=true&charset=utf8mb4&timeout=30s&readTimeout=30s&writeTimeout=30s", "ADMIN_API_DATABASE_DEFAULT_DEBUG": true, "ADMIN_API_REDIS_DEFAULT_ADDRESS": "valkey:6379", "ADMIN_API_REDIS_DEFAULT_DB": 0, "ADMIN_API_REDIS_DEFAULT_PASS": "valkey_password", "ADMIN_API_REDIS_DEFAULT_IDLETIMEOUT": "20s", "ADMIN_API_LOGGER_PATH": "logs", "ADMIN_API_LOGGER_LEVEL": "all", "ADMIN_API_LOGGER_STDOUT": true, "ADMIN_API_LOGGER_ROTATESIZE": "100M", "ADMIN_API_LOGGER_ROTATEEXPIRE": "7d", "ADMIN_API_LOGGER_FORMAT": "json", "ADMIN_API_HEALTH_CHECK_PORT": 8081, "ADMIN_API_MINIO_ENDPOINT": "*************:9000", "ADMIN_API_MINIO_ACCESSKEYID": "minio_Ri46Yi", "ADMIN_API_MINIO_SECRETACCESSKEY": "minio_2cbfCm", "ADMIN_API_MINIO_BUCKETNAME": "xpay-uploads", "ADMIN_API_MINIO_USESSL": false, "ADMIN_API_MINIO_PUBLICURLPREFIX": "http://*************:9000/xpay-uploads/", "ADMIN_API_WALLETSAPI_BASEURL": "http://wallets:8080", "ADMIN_API_CASDOOR_SERVER_ENDPOINT": "http://casdoor:8000", "ADMIN_API_CASDOOR_SERVER_CLIENT_ID": "80756163da773c15fa03", "ADMIN_API_CASDOOR_SERVER_CLIENT_SECRET": "0f319ddd842a7bdb25834aa85b6268f43f5ff686", "ADMIN_API_CASDOOR_SERVER_ORGANIZATION": "organization_xpay", "ADMIN_API_CASDOOR_SERVER_OWNER": "organization_xpay", "ADMIN_API_CASDOOR_SERVER_APPLICATION": "application_xpay", "ADMIN_API_CASDOOR_SERVER_FRONTEND_URL": "http://127.0.0.1", "ADMIN_API_CASDOOR_SERVER_USER": "organization_xpay/xpay", "ADMIN_API_CASDOOR_SERVER_ROLE": "organization_xpay/role_75sgli", "ADMIN_API_CASDOOR_SERVER_MODEL": "model_srgv6d", "ADMIN_API_AGENT_CASDOOR_SERVER_ENDPOINT": "http://casdoor:8000", "ADMIN_API_AGENT_CASDOOR_SERVER_CLIENT_ID": "c4994b2c1e44f27ce1b5", "ADMIN_API_AGENT_CASDOOR_SERVER_CLIENT_SECRET": "383d7198c217e4924765cbcb8d586980a321f7bc", "ADMIN_API_AGENT_CASDOOR_SERVER_ORGANIZATION": "organization_agent", "ADMIN_API_AGENT_CASDOOR_SERVER_OWNER": "organization_agent", "ADMIN_API_AGENT_CASDOOR_SERVER_APPLICATION": "application_agent", "ADMIN_API_AGENT_CASDOOR_SERVER_FRONTEND_URL": "http://127.0.0.1", "ADMIN_API_AGENT_CASDOOR_SERVER_USER": "organization_agent/agent", "ADMIN_API_AGENT_CASDOOR_SERVER_ROLE": "organization_agent/role_agent", "ADMIN_API_AGENT_CASDOOR_SERVER_MODEL": "model_srgv6d", "ADMIN_API_AGENT_CASDOOR_SERVER_DEFAULT_AVATAR": "https://cdn.casbin.org/img/casbin.svg", "ADMIN_API_MERCHANT_CASDOOR_SERVER_ENDPOINT": "http://casdoor:8000", "ADMIN_API_MERCHANT_CASDOOR_SERVER_CLIENT_ID": "f0a8b6891ae4f6d2abff", "ADMIN_API_MERCHANT_CASDOOR_SERVER_CLIENT_SECRET": "9557c5f346bf7d37e0e611dd76e333cf4605a17b", "ADMIN_API_MERCHANT_CASDOOR_SERVER_ORGANIZATION": "organization_merchant", "ADMIN_API_MERCHANT_CASDOOR_SERVER_OWNER": "organization_merchant", "ADMIN_API_MERCHANT_CASDOOR_SERVER_APPLICATION": "application_merchant", "ADMIN_API_MERCHANT_CASDOOR_SERVER_FRONTEND_URL": "http://127.0.0.1", "ADMIN_API_MERCHANT_CASDOOR_SERVER_USER": "organization_merchant/merchant", "ADMIN_API_MERCHANT_CASDOOR_SERVER_ROLE": "organization_merchant/role_merchant", "ADMIN_API_MERCHANT_CASDOOR_SERVER_MODEL": "model_srgv6d", "ADMIN_API_MERCHANT_CASDOOR_SERVER_DEFAULT_AVATAR": "https://cdn.casbin.org/img/casbin.svg", "ADMIN_API_MERCHANT_CASDOOR_SERVER_DELETE_STRATEGY": "hard", "ADMIN_API_CONSUL_ADDRESS": "consul:8500", "ADMIN_API_CONSUL_TOKEN": "af8c827b-0bfd-f3cd-f276-c2b7f4e6e874", "ADMIN_API_CONSUL_CONFIG_PREFIX": "xpay/config", "ADMIN_API_CORS_ALLOWORIGIN": "*", "ADMIN_API_CORS_ALLOWCREDENTIALS": "true", "ADMIN_API_CORS_EXPOSEHEADERS": "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type", "ADMIN_API_CORS_MAXAGE": 86400, "ADMIN_API_CORS_ALLOWMETHODS": "GET, POST, PUT, DELETE, OPTIONS, PATCH", "ADMIN_API_CORS_ALLOWHEADERS": "Origin, X-Requested-With, Content-Type, Accept, Authorization, X-Token", "ADMIN_API_DATABASE_MERCHANT_LINK": "mysql:root:root@tcp(mysql:3306)/merchant?loc=Local&parseTime=true&charset=utf8mb4&timeout=30s&readTimeout=30s&writeTimeout=30s"}