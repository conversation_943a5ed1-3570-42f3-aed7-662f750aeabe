# CLAUDE.md - XPay Admin API Codebase Documentation

## Project Overview

This is the XPay Admin API, a GoFrame-based REST API service that provides administrative functionality for the XPay payment system. The service handles user management, wallet operations, payment processing, exchanges, and various administrative functions.

## Technology Stack

- **Language**: Go 1.24.3
- **Framework**: GoFrame v2.9.0 (https://goframe.org/)
- **Database**: MySQL with GoFrame ORM
- **Cache**: Redis
- **Authentication**: Casdoor + JWT
- **Storage**: MinIO for object storage
- **Configuration**: Consul for distributed config
- **Container**: Docker with Alpine Linux

## Project Structure

```
admin-api/
├── api/                    # API definitions and request/response models
│   ├── common/            # Common API structures
│   └── system/            # System module APIs
│       └── v1/            # Version 1 API definitions
├── cmd/                   # Application entry point
│   └── main.go           # Main entry point
├── internal/              # Private application code
│   ├── boot/             # Application bootstrap
│   ├── cache/            # Cache management (permissions)
│   ├── casdoor/          # Casdoor integration
│   ├── cmd/              # Command definitions
│   ├── codes/            # Business error codes
│   ├── config/           # Configuration management
│   ├── consts/           # Constants definitions
│   ├── controller/       # HTTP controllers
│   ├── dao/              # Data Access Objects (generated)
│   ├── library/          # Shared libraries (captcha, minio)
│   ├── logic/            # Business logic layer
│   ├── middleware/       # HTTP middlewares
│   ├── model/            # Data models
│   │   ├── do/          # Domain Objects (generated)
│   │   └── entity/      # Database entities (generated)
│   ├── router/           # Route definitions
│   └── service/          # Service interfaces
├── manifest/              # Deployment and configuration
│   ├── config/          # Application config files
│   ├── deploy/          # Kubernetes manifests
│   └── docker/          # Docker related files
├── hack/                 # Build scripts and tools
│   ├── config.yaml      # GoFrame CLI configuration
│   ├── hack.mk          # Main build targets
│   └── hack-cli.mk      # CLI tool management
├── utility/              # Utility packages
├── logs/                 # Application logs
└── docs/                # Documentation

```

## Key Components

### 1. API Layer (`api/`)
- Defines request/response structures
- Organized by module (system) and version (v1)
- Uses GoFrame's validation tags

### 2. Controller Layer (`internal/controller/`)
- Handles HTTP requests
- Implements API endpoints defined in `api/`
- Minimal business logic, delegates to logic layer

### 3. Logic Layer (`internal/logic/`)
- Contains all business logic
- Organized by feature/module
- Each feature has its own package with interfaces

### 4. Data Access Layer (`internal/dao/`)
- Generated by GoFrame CLI from database
- Provides type-safe database operations
- Uses GoFrame's ORM

### 5. Service Layer (`internal/service/`)
- Defines service interfaces
- Provides dependency injection points
- Abstracts business operations

### 6. Model Layer (`internal/model/`)
- `entity/`: Database table structures (generated)
- `do/`: Domain objects for queries (generated)
- Custom model definitions

## Development Commands

### Prerequisites
```bash
# Install GoFrame CLI (required for all make commands)
make cli
```

### Database Operations
```bash
# Generate DAO/DO/Entity from database
make dao

# Configuration in hack/config.yaml:
# - Database connection: mysql:root:root@tcp(127.0.0.1:3306)/xpayapi
# - JSON field naming: CamelLower
# - Decimal type mapping to shopspring/decimal
```

### Build Commands
```bash
# Build the application (default target)
make build

# Build Docker image
make image

# Build and push Docker image
make image.push

# Deploy to Kubernetes
make deploy
```

### Code Generation
```bash
# Generate controller from API definitions
make ctrl

# Generate service interfaces
make service

# Generate enums from Go files
make enums

# Parse and generate protobuf files
make pb

# Generate protobuf entities from database
make pbentity
```

### Update GoFrame
```bash
# Update GoFrame and CLI to latest version
make up
```

## Configuration

### Main Configuration (`manifest/config/config.yaml`)
- **Server**: Port 7999, with OpenAPI/Swagger support
- **Database**: MySQL connection with debug logging
- **Redis**: Default database with password auth
- **MinIO**: Object storage for file uploads
- **Casdoor**: SSO and permission management
- **Consul**: Distributed configuration management

### Build Configuration (`hack/config.yaml`)
- Database code generation settings
- Docker build configuration
- Type mappings for code generation

## Key Features

### 1. Authentication & Authorization
- Casdoor integration for SSO
- JWT token-based authentication
- Role-based access control (RBAC)
- Permission caching for performance

### 2. User Management
- User CRUD operations
- Google 2FA support
- Backup accounts
- Agent management

### 3. Wallet & Finance
- Wallet balance management
- Transaction tracking
- User withdrawals with review process
- Recharge operations

### 4. Payment Processing
- Payment request management
- Transaction history
- Transfer operations

### 5. Exchange Module
- Product management
- Order processing
- Exchange rate handling

### 6. Red Packet System
- Red packet creation and distribution
- Claim tracking
- Image verification

### 7. Administrative Features
- Operation logging
- Login tracking
- IP access control
- Notice/announcement system
- Menu and permission management

## Middleware Stack

1. **CORS**: Cross-origin resource sharing
2. **Auth**: JWT authentication validation
3. **Permission**: Route-based access control
4. **Operation Logger**: Audit trail for admin actions
5. **Request Logger**: HTTP request/response logging

## Database Schema

The application uses a comprehensive MySQL schema with tables for:
- Users and authentication
- Wallets and transactions
- Payments and transfers
- Exchange products and orders
- Administrative functions
- Audit logs

All database operations are type-safe through generated DAO layer.

## Running the Application

### Local Development
```bash
# Install dependencies
go mod download

# Generate database code
make dao

# Build and run
make build
./admin-api
```

### Docker
```bash
# Build Docker image
docker build -t admin-api:latest .

# Run with configuration
docker run -d -p 7999:7999 --name my-admin-api \
  -e PATH_TO_SECRET_FILE="/path/to/config_variables.json" \
  -v /path/to/config_variables.json:/path/to/config_variables.json \
  admin-api:latest
```

### Environment Variables
The application uses `config_variables.json` for sensitive configuration values, loaded via the `entrypoint.sh` script.

## API Documentation

- **OpenAPI JSON**: http://localhost:7999/api.json
- **Swagger UI**: http://localhost:7999/swagger

## Health Check

Health check endpoint is available on port 8081 (configured in config.yaml).

## Logging

- **Application logs**: `./logs/`
- **Database query logs**: `./logs/database/`
- Log rotation configured with 100MB size limit and 7-day retention

## Recent Development Activity

Based on git status and recent commits:
- Enhanced user queries with agent and Telegram information
- Added inline message ID support for Telegram integration
- Improved withdrawal review process
- Database schema updates for new features

## Best Practices

1. **Code Generation**: Always use `make dao` after database schema changes
2. **API Design**: Define APIs in `api/` before implementing controllers
3. **Business Logic**: Keep controllers thin, implement logic in `logic/` layer
4. **Error Handling**: Use defined error codes in `codes/` package
5. **Configuration**: Use config management for all environment-specific values
6. **Testing**: Write tests for logic layer components
7. **Logging**: Use structured logging with appropriate levels

## Troubleshooting

1. **Build Issues**: Ensure GoFrame CLI is installed (`make cli`)
2. **Database Connection**: Check MySQL connection string in config.yaml
3. **Permission Errors**: Verify Casdoor configuration and permissions
4. **Cache Issues**: Check Redis connection and clear cache if needed