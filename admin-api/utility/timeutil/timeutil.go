package timeutil

import "strings"

// ParseTimeRange parses date range strings into start and end time strings.
// Handles formats like "YYYY-MM-DD,YYYY-MM-DD" or single "YYYY-MM-DD".
// Also handles "undefined" or "null" inputs.
// Appends " 23:59:59" to the end time if only the date is provided.
func ParseTimeRange(dateRange, startTime, endTime string) (string, string) {
	// Ensure parameters are not nil and handle special string values
	if dateRange != "" && dateRange != "undefined" && dateRange != "null" {
		// Handle date range format
		timeParts := strings.Split(dateRange, ",")
		if len(timeParts) == 2 {
			startTime = strings.TrimSpace(timeParts[0])
			endTime = strings.TrimSpace(timeParts[1])
		} else if len(timeParts) == 1 && timeParts[0] != "" {
			// Single date
			startTime = strings.TrimSpace(timeParts[0])
			endTime = startTime
		}
	}

	// Handle special values
	if startTime == "undefined" || startTime == "null" {
		startTime = ""
	}
	if endTime == "undefined" || endTime == "null" {
		endTime = ""
	}

	// Handle end time, add time part if only date is provided
	if endTime != "" {
		if !strings.Contains(endTime, " ") {
			endTime = endTime + " 23:59:59"
		}
	}

	return startTime, endTime
}
