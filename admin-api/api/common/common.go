package common

// 分页结构体
type PageResponse struct {
	//总条数
	TotalSize int `json:"totalSize" dc:"总条数"`
	//总页数
	TotalPage int `json:"totalPage" dc:"总页数"`
	//当前页
	CurrentPage int `json:"currentPage" dc:"当前页"`
	//每页条数
	PageSize int `json:"pageSize" dc:"每页条数"`
}

// 请求参数
type PageRequest struct {
	Page     int `p:"page" v:"required#页码不能为空,min:1#页码不能小于1,integer#页码必须为整数" d:"1" dc:"页码"`
	PageSize int `p:"pageSize" v:"required#每页数量不能为空,min:1#每页数量不能小于1,integer#每页数量必须为整数" d:"20" dc:"每页数量"`
}

// ListRes 通用列表响应结构
type ListRes struct {
	List interface{}  `json:"list" dc:"列表数据"`
	Page PageResponse `json:"page" dc:"分页信息"`
}

// CalculateTotalPage 计算总页数
func CalculateTotalPage(totalSize, pageSize int) int {
	if pageSize <= 0 {
		return 0 // 防止除零错误
	}
	totalPage := totalSize / pageSize
	if totalSize%pageSize > 0 {
		totalPage++
	}
	return totalPage
}
