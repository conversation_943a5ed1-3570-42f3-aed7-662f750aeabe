// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package system

import (
	"context"

	"admin-api/api/system/v1"
)

type ISystemV1 interface {
	GetAddressStatistics(ctx context.Context, req *v1.GetAddressStatisticsReq) (res *v1.GetAddressStatisticsRes, err error)
	ImportAddresses(ctx context.Context, req *v1.ImportAddressesReq) (res *v1.ImportAddressesRes, err error)
	GetImportProgress(ctx context.Context, req *v1.GetImportProgressReq) (res *v1.GetImportProgressRes, err error)
	AssignRolesToAdminMember(ctx context.Context, req *v1.AssignRolesToAdminMemberReq) (res *v1.AssignRolesToAdminMemberRes, err error)
	GetAdminMemberAssignedRoles(ctx context.Context, req *v1.GetAdminMemberAssignedRolesReq) (res *v1.GetAdminMemberAssignedRolesRes, err error)
	AddAgent(ctx context.Context, req *v1.AddAgentReq) (res *v1.AddAgentRes, err error)
	GetAgentList(ctx context.Context, req *v1.GetAgentListReq) (res *v1.GetAgentListRes, err error)
	GetAgent(ctx context.Context, req *v1.GetAgentReq) (res *v1.GetAgentRes, err error)
	EditAgent(ctx context.Context, req *v1.EditAgentReq) (res *v1.EditAgentRes, err error)
	DeleteAgent(ctx context.Context, req *v1.DeleteAgentReq) (res *v1.DeleteAgentRes, err error)
	UpdateAgentStatus(ctx context.Context, req *v1.UpdateAgentStatusReq) (res *v1.UpdateAgentStatusRes, err error)
	UpdateAgentPassword(ctx context.Context, req *v1.UpdateAgentPasswordReq) (res *v1.UpdateAgentPasswordRes, err error)
	ResetAgent2FA(ctx context.Context, req *v1.ResetAgent2FAReq) (res *v1.ResetAgent2FARes, err error)
	GetAgentWhitelist(ctx context.Context, req *v1.GetAgentWhitelistReq) (res *v1.GetAgentWhitelistRes, err error)
	AddAgentWhitelist(ctx context.Context, req *v1.AddAgentWhitelistReq) (res *v1.AddAgentWhitelistRes, err error)
	DeleteAgentWhitelist(ctx context.Context, req *v1.DeleteAgentWhitelistReq) (res *v1.DeleteAgentWhitelistRes, err error)
	GetApiKeyList(ctx context.Context, req *v1.GetApiKeyListReq) (res *v1.GetApiKeyListRes, err error)
	UpdateApiKey(ctx context.Context, req *v1.UpdateApiKeyReq) (res *v1.UpdateApiKeyRes, err error)
	DeleteApiKey(ctx context.Context, req *v1.DeleteApiKeyReq) (res *v1.DeleteApiKeyRes, err error)
	UpdateApiKeyStatus(ctx context.Context, req *v1.UpdateApiKeyStatusReq) (res *v1.UpdateApiKeyStatusRes, err error)
	Login(ctx context.Context, req *v1.LoginReq) (res *v1.LoginRes, err error)
	GetAdminInfo(ctx context.Context, req *v1.GetAdminInfoReq) (res *v1.GetAdminInfoRes, err error)
	UpdateAdminInfo(ctx context.Context, req *v1.UpdateAdminInfoReq) (res *v1.UpdateAdminInfoRes, err error)
	CasdoorSignin(ctx context.Context, req *v1.CasdoorSigninReq) (res *v1.CasdoorSigninRes, err error)
	GetCasdoorUserInfo(ctx context.Context, req *v1.GetCasdoorUserInfoReq) (res *v1.GetCasdoorUserInfoRes, err error)
	GetBackupAccounts(ctx context.Context, req *v1.GetBackupAccountsReq) (res *v1.GetBackupAccountsRes, err error)
	GetUserBackupAccounts(ctx context.Context, req *v1.GetUserBackupAccountsReq) (res *v1.GetUserBackupAccountsRes, err error)
	AddUserBackupAccount(ctx context.Context, req *v1.AddUserBackupAccountReq) (res *v1.AddUserBackupAccountRes, err error)
	DeleteUserBackupAccount(ctx context.Context, req *v1.DeleteUserBackupAccountReq) (res *v1.DeleteUserBackupAccountRes, err error)
	SetUserBackupAccountVerification(ctx context.Context, req *v1.SetUserBackupAccountVerificationReq) (res *v1.SetUserBackupAccountVerificationRes, err error)
	GetTokenSymbols(ctx context.Context, req *v1.GetTokenSymbolsReq) (res *v1.GetTokenSymbolsRes, err error)
	ListConfigCategory(ctx context.Context, req *v1.ListConfigCategoryReq) (res *v1.ListConfigCategoryRes, err error)
	CreateConfigCategory(ctx context.Context, req *v1.CreateConfigCategoryReq) (res *v1.CreateConfigCategoryRes, err error)
	UpdateConfigCategory(ctx context.Context, req *v1.UpdateConfigCategoryReq) (res *v1.UpdateConfigCategoryRes, err error)
	DeleteConfigCategory(ctx context.Context, req *v1.DeleteConfigCategoryReq) (res *v1.DeleteConfigCategoryRes, err error)
	ListConfigItem(ctx context.Context, req *v1.ListConfigItemReq) (res *v1.ListConfigItemRes, err error)
	CreateConfigItem(ctx context.Context, req *v1.CreateConfigItemReq) (res *v1.CreateConfigItemRes, err error)
	UpdateConfigItem(ctx context.Context, req *v1.UpdateConfigItemReq) (res *v1.UpdateConfigItemRes, err error)
	DeleteConfigItem(ctx context.Context, req *v1.DeleteConfigItemReq) (res *v1.DeleteConfigItemRes, err error)
	GetDashboardStats(ctx context.Context, req *v1.GetDashboardStatsReq) (res *v1.GetDashboardStatsRes, err error)
	ExchangeProductList(ctx context.Context, req *v1.ExchangeProductListReq) (res *v1.ExchangeProductListRes, err error)
	ExchangeProductDetail(ctx context.Context, req *v1.ExchangeProductDetailReq) (res *v1.ExchangeProductDetailRes, err error)
	ExchangeProductCreate(ctx context.Context, req *v1.ExchangeProductCreateReq) (res *v1.ExchangeProductCreateRes, err error)
	ExchangeProductUpdate(ctx context.Context, req *v1.ExchangeProductUpdateReq) (res *v1.ExchangeProductUpdateRes, err error)
	ExchangeProductDelete(ctx context.Context, req *v1.ExchangeProductDeleteReq) (res *v1.ExchangeProductDeleteRes, err error)
	ExchangeProductStatus(ctx context.Context, req *v1.ExchangeProductStatusReq) (res *v1.ExchangeProductStatusRes, err error)
	ExchangeProductVolume(ctx context.Context, req *v1.ExchangeProductVolumeReq) (res *v1.ExchangeProductVolumeRes, err error)
	ExchangeOrderList(ctx context.Context, req *v1.ExchangeOrderListReq) (res *v1.ExchangeOrderListRes, err error)
	ExchangeOrderDetail(ctx context.Context, req *v1.ExchangeOrderDetailReq) (res *v1.ExchangeOrderDetailRes, err error)
	GetIpAccessList(ctx context.Context, req *v1.GetIpAccessListReq) (res *v1.GetIpAccessListRes, err error)
	AddIpAccessList(ctx context.Context, req *v1.AddIpAccessListReq) (res *v1.AddIpAccessListRes, err error)
	DeleteIpAccessList(ctx context.Context, req *v1.DeleteIpAccessListReq) (res *v1.DeleteIpAccessListRes, err error)
	PatchIpAccessList(ctx context.Context, req *v1.PatchIpAccessListReq) (res *v1.PatchIpAccessListRes, err error)
	GetLoginLogList(ctx context.Context, req *v1.GetLoginLogListReq) (res *v1.GetLoginLogListRes, err error)
	GetLoginLogDetail(ctx context.Context, req *v1.GetLoginLogDetailReq) (res *v1.GetLoginLogDetailRes, err error)
	GetMemberList(ctx context.Context, req *v1.GetMemberListReq) (res *v1.GetMemberListRes, err error)
	GetMember(ctx context.Context, req *v1.GetMemberReq) (res *v1.GetMemberRes, err error)
	AddMember(ctx context.Context, req *v1.AddMemberReq) (res *v1.AddMemberRes, err error)
	EditMember(ctx context.Context, req *v1.EditMemberReq) (res *v1.EditMemberRes, err error)
	DeleteMember(ctx context.Context, req *v1.DeleteMemberReq) (res *v1.DeleteMemberRes, err error)
	UpdateMemberStatus(ctx context.Context, req *v1.UpdateMemberStatusReq) (res *v1.UpdateMemberStatusRes, err error)
	ResetMemberPassword(ctx context.Context, req *v1.ResetMemberPasswordReq) (res *v1.ResetMemberPasswordRes, err error)
	AssignRolesToUser(ctx context.Context, req *v1.AssignRolesToUserReq) (res *v1.AssignRolesToUserRes, err error)
	GetMenuList(ctx context.Context, req *v1.GetMenuListReq) (res *v1.GetMenuListRes, err error)
	GetAllMenuList(ctx context.Context, req *v1.GetAllMenuListReq) (res *v1.GetAllMenuListRes, err error)
	GetUserAccessibleMenus(ctx context.Context, req *v1.GetUserAccessibleMenusReq) (res *v1.GetUserAccessibleMenusRes, err error)
	AddMenu(ctx context.Context, req *v1.AddMenuReq) (res *v1.AddMenuRes, err error)
	EditMenu(ctx context.Context, req *v1.EditMenuReq) (res *v1.EditMenuRes, err error)
	DeleteMenu(ctx context.Context, req *v1.DeleteMenuReq) (res *v1.DeleteMenuRes, err error)
	GetMerchantList(ctx context.Context, req *v1.GetMerchantListReq) (res *v1.GetMerchantListRes, err error)
	GetMerchant(ctx context.Context, req *v1.GetMerchantReq) (res *v1.GetMerchantRes, err error)
	AddMerchant(ctx context.Context, req *v1.AddMerchantReq) (res *v1.AddMerchantRes, err error)
	EditMerchant(ctx context.Context, req *v1.EditMerchantReq) (res *v1.EditMerchantRes, err error)
	DeleteMerchant(ctx context.Context, req *v1.DeleteMerchantReq) (res *v1.DeleteMerchantRes, err error)
	UpdateMerchantStatus(ctx context.Context, req *v1.UpdateMerchantStatusReq) (res *v1.UpdateMerchantStatusRes, err error)
	ResetMerchantGoogle2FA(ctx context.Context, req *v1.ResetMerchantGoogle2FAReq) (res *v1.ResetMerchantGoogle2FARes, err error)
	ResetMerchantPassword(ctx context.Context, req *v1.ResetMerchantPasswordReq) (res *v1.ResetMerchantPasswordRes, err error)
	GenerateMerchantApiKey(ctx context.Context, req *v1.GenerateMerchantApiKeyReq) (res *v1.GenerateMerchantApiKeyRes, err error)
	GetMerchantApiKeyList(ctx context.Context, req *v1.GetMerchantApiKeyListReq) (res *v1.GetMerchantApiKeyListRes, err error)
	UpdateMerchantApiKey(ctx context.Context, req *v1.UpdateMerchantApiKeyReq) (res *v1.UpdateMerchantApiKeyRes, err error)
	RevokeMerchantApiKey(ctx context.Context, req *v1.RevokeMerchantApiKeyReq) (res *v1.RevokeMerchantApiKeyRes, err error)
	GetMyCallbacks(ctx context.Context, req *v1.GetMyCallbacksReq) (res *v1.GetMyCallbacksRes, err error)
	GetMyCallbackDetail(ctx context.Context, req *v1.GetMyCallbackDetailReq) (res *v1.GetMyCallbackDetailRes, err error)
	RetryCallback(ctx context.Context, req *v1.RetryCallbackReq) (res *v1.RetryCallbackRes, err error)
	GetMyDeposits(ctx context.Context, req *v1.GetMyDepositsReq) (res *v1.GetMyDepositsRes, err error)
	GetMyDepositDetail(ctx context.Context, req *v1.GetMyDepositDetailReq) (res *v1.GetMyDepositDetailRes, err error)
	GetMyTransactions(ctx context.Context, req *v1.GetMyTransactionsReq) (res *v1.GetMyTransactionsRes, err error)
	GetMyTransactionDetail(ctx context.Context, req *v1.GetMyTransactionDetailReq) (res *v1.GetMyTransactionDetailRes, err error)
	GetMyTransactionStats(ctx context.Context, req *v1.GetMyTransactionStatsReq) (res *v1.GetMyTransactionStatsRes, err error)
	GetMerchantAssets(ctx context.Context, req *v1.GetMerchantAssetsReq) (res *v1.GetMerchantAssetsRes, err error)
	GetMerchantWallets(ctx context.Context, req *v1.GetMerchantWalletsReq) (res *v1.GetMerchantWalletsRes, err error)
	AdjustMerchantBalance(ctx context.Context, req *v1.AdjustMerchantBalanceReq) (res *v1.AdjustMerchantBalanceRes, err error)
	GetMerchantWalletDetail(ctx context.Context, req *v1.GetMerchantWalletDetailReq) (res *v1.GetMerchantWalletDetailRes, err error)
	BatchAdjustMerchantBalance(ctx context.Context, req *v1.BatchAdjustMerchantBalanceReq) (res *v1.BatchAdjustMerchantBalanceRes, err error)
	GetMyWithdraws(ctx context.Context, req *v1.GetMyWithdrawsReq) (res *v1.GetMyWithdrawsRes, err error)
	GetMyWithdrawDetail(ctx context.Context, req *v1.GetMyWithdrawDetailReq) (res *v1.GetMyWithdrawDetailRes, err error)
	CancelMyWithdraw(ctx context.Context, req *v1.CancelMyWithdrawReq) (res *v1.CancelMyWithdrawRes, err error)
	CreateMyWithdraw(ctx context.Context, req *v1.CreateMyWithdrawReq) (res *v1.CreateMyWithdrawRes, err error)
	GetMyWithdrawFee(ctx context.Context, req *v1.GetMyWithdrawFeeReq) (res *v1.GetMyWithdrawFeeRes, err error)
	ApproveWithdraw(ctx context.Context, req *v1.ApproveWithdrawReq) (res *v1.ApproveWithdrawRes, err error)
	RejectWithdraw(ctx context.Context, req *v1.RejectWithdrawReq) (res *v1.RejectWithdrawRes, err error)
	GetAdminNoticeList(ctx context.Context, req *v1.GetAdminNoticeListReq) (res *v1.GetAdminNoticeListRes, err error)
	GetAdminNotice(ctx context.Context, req *v1.GetAdminNoticeReq) (res *v1.GetAdminNoticeRes, err error)
	AddAdminNotice(ctx context.Context, req *v1.AddAdminNoticeReq) (res *v1.AddAdminNoticeRes, err error)
	EditAdminNotice(ctx context.Context, req *v1.EditAdminNoticeReq) (res *v1.EditAdminNoticeRes, err error)
	DeleteAdminNotice(ctx context.Context, req *v1.DeleteAdminNoticeReq) (res *v1.DeleteAdminNoticeRes, err error)
	GetAdminNoticeReadStatus(ctx context.Context, req *v1.GetAdminNoticeReadStatusReq) (res *v1.GetAdminNoticeReadStatusRes, err error)
	GetMemberListForNotice(ctx context.Context, req *v1.GetMemberListForNoticeReq) (res *v1.GetMemberListForNoticeRes, err error)
	GetMyNoticeList(ctx context.Context, req *v1.GetMyNoticeListReq) (res *v1.GetMyNoticeListRes, err error)
	MarkNoticeRead(ctx context.Context, req *v1.MarkNoticeReadReq) (res *v1.MarkNoticeReadRes, err error)
	GetMyUnreadNoticeCount(ctx context.Context, req *v1.GetMyUnreadNoticeCountReq) (res *v1.GetMyUnreadNoticeCountRes, err error)
	GetOperationLogList(ctx context.Context, req *v1.GetOperationLogListReq) (res *v1.GetOperationLogListRes, err error)
	GetOperationLogDetail(ctx context.Context, req *v1.GetOperationLogDetailReq) (res *v1.GetOperationLogDetailRes, err error)
	ListPaymentRequest(ctx context.Context, req *v1.ListPaymentRequestReq) (res *v1.ListPaymentRequestRes, err error)
	GetPaymentRequestDetail(ctx context.Context, req *v1.GetPaymentRequestDetailReq) (res *v1.GetPaymentRequestDetailRes, err error)
	UpdatePaymentRequestStatus(ctx context.Context, req *v1.UpdatePaymentRequestStatusReq) (res *v1.UpdatePaymentRequestStatusRes, err error)
	GetPermissionList(ctx context.Context, req *v1.GetPermissionListReq) (res *v1.GetPermissionListRes, err error)
	GetAllPermissionList(ctx context.Context, req *v1.GetAllPermissionListReq) (res *v1.GetAllPermissionListRes, err error)
	AddPermission(ctx context.Context, req *v1.AddPermissionReq) (res *v1.AddPermissionRes, err error)
	EditPermission(ctx context.Context, req *v1.EditPermissionReq) (res *v1.EditPermissionRes, err error)
	DeletePermission(ctx context.Context, req *v1.DeletePermissionReq) (res *v1.DeletePermissionRes, err error)
	SyncMenuPermissions(ctx context.Context, req *v1.SyncMenuPermissionsReq) (res *v1.SyncMenuPermissionsRes, err error)
	SyncApiPermissions(ctx context.Context, req *v1.SyncApiPermissionsReq) (res *v1.SyncApiPermissionsRes, err error)
	ListAdminRedPackets(ctx context.Context, req *v1.ListAdminRedPacketsReq) (res *v1.ListAdminRedPacketsRes, err error)
	GetAdminRedPacketDetail(ctx context.Context, req *v1.GetAdminRedPacketDetailReq) (res *v1.GetAdminRedPacketDetailRes, err error)
	CancelRedPacket(ctx context.Context, req *v1.CancelRedPacketReq) (res *v1.CancelRedPacketRes, err error)
	ListAdminRedPacketClaims(ctx context.Context, req *v1.ListAdminRedPacketClaimsReq) (res *v1.ListAdminRedPacketClaimsRes, err error)
	ReviewRedPacketImage(ctx context.Context, req *v1.ReviewRedPacketImageReq) (res *v1.ReviewRedPacketImageRes, err error)
	ListRedPacketImages(ctx context.Context, req *v1.ListRedPacketImagesReq) (res *v1.ListRedPacketImagesRes, err error)
	GetReferralCommissionList(ctx context.Context, req *v1.GetReferralCommissionListReq) (res *v1.GetReferralCommissionListRes, err error)
	GetRoleList(ctx context.Context, req *v1.GetRoleListReq) (res *v1.GetRoleListRes, err error)
	GetRole(ctx context.Context, req *v1.GetRoleReq) (res *v1.GetRoleRes, err error)
	AddRole(ctx context.Context, req *v1.AddRoleReq) (res *v1.AddRoleRes, err error)
	EditRole(ctx context.Context, req *v1.EditRoleReq) (res *v1.EditRoleRes, err error)
	DeleteRole(ctx context.Context, req *v1.DeleteRoleReq) (res *v1.DeleteRoleRes, err error)
	GetRoleMenuIds(ctx context.Context, req *v1.GetRoleMenuIdsReq) (res *v1.GetRoleMenuIdsRes, err error)
	AssignRoleMenus(ctx context.Context, req *v1.AssignRoleMenusReq) (res *v1.AssignRoleMenusRes, err error)
	UpdateRoleDataScope(ctx context.Context, req *v1.UpdateRoleDataScopeReq) (res *v1.UpdateRoleDataScopeRes, err error)
	AssignPermissionsToRole(ctx context.Context, req *v1.AssignPermissionsToRoleReq) (res *v1.AssignPermissionsToRoleRes, err error)
	GetTokenList(ctx context.Context, req *v1.GetTokenListReq) (res *v1.GetTokenListRes, err error)
	GetTokenDetail(ctx context.Context, req *v1.GetTokenDetailReq) (res *v1.GetTokenDetailRes, err error)
	CreateToken(ctx context.Context, req *v1.CreateTokenReq) (res *v1.CreateTokenRes, err error)
	UpdateToken(ctx context.Context, req *v1.UpdateTokenReq) (res *v1.UpdateTokenRes, err error)
	DeleteToken(ctx context.Context, req *v1.DeleteTokenReq) (res *v1.DeleteTokenRes, err error)
	ListAdminTransactions(ctx context.Context, req *v1.ListAdminTransactionsReq) (res *v1.ListAdminTransactionsRes, err error)
	ListAdminTransfers(ctx context.Context, req *v1.ListAdminTransfersReq) (res *v1.ListAdminTransfersRes, err error)
	GetAdminTransferDetail(ctx context.Context, req *v1.GetAdminTransferDetailReq) (res *v1.GetAdminTransferDetailRes, err error)
	UploadAvatar(ctx context.Context, req *v1.UploadAvatarReq) (res *v1.UploadAvatarRes, err error)
	GetUserList(ctx context.Context, req *v1.GetUserListReq) (res *v1.GetUserListRes, err error)
	GetUser(ctx context.Context, req *v1.GetUserReq) (res *v1.GetUserRes, err error)
	AddUser(ctx context.Context, req *v1.AddUserReq) (res *v1.AddUserRes, err error)
	EditUser(ctx context.Context, req *v1.EditUserReq) (res *v1.EditUserRes, err error)
	DeleteUser(ctx context.Context, req *v1.DeleteUserReq) (res *v1.DeleteUserRes, err error)
	UpdateUserStatus(ctx context.Context, req *v1.UpdateUserStatusReq) (res *v1.UpdateUserStatusRes, err error)
	ResetUserPassword(ctx context.Context, req *v1.ResetUserPasswordReq) (res *v1.ResetUserPasswordRes, err error)
	ResetUserGoogle2FA(ctx context.Context, req *v1.ResetUserGoogle2FAReq) (res *v1.ResetUserGoogle2FARes, err error)
	ListUserAddresses(ctx context.Context, req *v1.ListUserAddressesReq) (res *v1.ListUserAddressesRes, err error)
	ListUserRecharges(ctx context.Context, req *v1.ListUserRechargesReq) (res *v1.ListUserRechargesRes, err error)
	ListUserWithdraws(ctx context.Context, req *v1.ListUserWithdrawsReq) (res *v1.ListUserWithdrawsRes, err error)
	GetUserWithdrawDetail(ctx context.Context, req *v1.GetUserWithdrawDetailReq) (res *v1.GetUserWithdrawDetailRes, err error)
	ReviewUserWithdraw(ctx context.Context, req *v1.ReviewUserWithdrawReq) (res *v1.ReviewUserWithdrawRes, err error)
	UpdateUserWithdrawStatus(ctx context.Context, req *v1.UpdateUserWithdrawStatusReq) (res *v1.UpdateUserWithdrawStatusRes, err error)
	GetWalletBalance(ctx context.Context, req *v1.GetWalletBalanceReq) (res *v1.GetWalletBalanceRes, err error)
	ListWallets(ctx context.Context, req *v1.ListWalletsReq) (res *v1.ListWalletsRes, err error)
	AdjustBalance(ctx context.Context, req *v1.AdjustBalanceReq) (res *v1.AdjustBalanceRes, err error)
}
