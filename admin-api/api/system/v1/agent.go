package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity" // Import the entity package

	"github.com/gogf/gf/v2/frame/g"
)

const (
	// AgentTag defines the tag for agent management APIs in Swagger.
	AgentTag = "Agent Management"
)

// --- 添加代理 ---

// AddAgentReq defines the request structure for adding an agent.
type AddAgentReq struct {
	g.Meta        `path:"/agents" method:"post" tags:"Agent Management" summary:"添加代理"`
	ParentAgentId int64  `json:"parentAgentId" dc:"上级代理ID (0表示一级代理)"`
	Username      string `json:"username" v:"required|length:4,30#请输入用户名|用户名长度为4到30位" dc:"用户名"`
	Password      string `json:"password" v:"required|length:6,30#请输入密码|密码长度为6到30位" dc:"密码"`
	AgentName     string `json:"agentName" v:"required|length:2,50#请输入代理名称|代理名称长度为2到50位" dc:"代理名称"`
	Email         string `json:"email" v:"email#邮箱格式不正确" dc:"邮箱"`
	PhoneNumber   string `json:"phoneNumber" v:"phone#手机号格式不正确" dc:"手机号"`
	Status        *int   `json:"status" v:"required|in:0,1#请选择状态|状态值无效" dc:"状态 (0:禁用, 1:启用)"`
}

// AddAgentRes defines the response structure for adding an agent.
type AddAgentRes struct{}

// --- 获取代理列表 ---

// GetAgentListReq defines the request structure for getting the agent list.
type GetAgentListReq struct {
	g.Meta `path:"/agents" method:"get" tags:"Agent Management" summary:"获取代理列表"`
	common.PageRequest
	Username    string `json:"username" dc:"用户名"`
	AgentName   string `json:"agentName" dc:"代理名称"`
	PhoneNumber string `json:"phoneNumber" dc:"手机号"`
	Email       string `json:"email" dc:"邮箱"`
	Level       *int   `json:"level" dc:"代理层级"`
	Status      *int   `json:"status" dc:"状态 (0:禁用, 1:启用)"`
	DateRange   string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	Export      int    `json:"export" d:"0" dc:"是否导出:0不导出,1导出"`
}

// AgentListItem defines the structure for an agent item in a tree list.
type AgentListItem struct {
	*entity.Agents
	InvitationUrl string           `json:"invitationUrl" dc:"邀请链接 (Telegram机器人邀请URL)"`
	Children      []*AgentListItem `json:"children,omitempty" dc:"下级代理列表"`

	// Telegram information
	TelegramUsername string `json:"telegramUsername" dc:"Telegram用户名"`
	TelegramId       int64  `json:"telegramId" dc:"Telegram ID"`

	// Timestamps (if not already in entity.Agents)
	CreatedAt string `json:"createdAt" dc:"创建时间"`
	UpdatedAt string `json:"updatedAt" dc:"更新时间"`
}

// GetAgentListRes defines the response structure for getting the agent list.
type GetAgentListRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"` // 分页信息对于树状顶层仍然有效
	Data []*AgentListItem    `json:"data" dc:"代理列表 (树状结构)"`
}

// --- 获取单个代理详情 ---

// GetAgentReq defines the request structure for getting a single agent's details.
type GetAgentReq struct {
	g.Meta  `path:"/agents/{agentId}" method:"get" tags:"Agent Management" summary:"获取单个代理详情"`
	AgentId int64 `json:"agentId" v:"required#请输入代理ID" dc:"代理ID"`
}

// GetAgentRes defines the response structure for getting a single agent's details.
type GetAgentRes struct {
	*entity.Agents
	InvitationUrl string `json:"invitationUrl" dc:"邀请链接 (Telegram机器人邀请URL)"`
}

// --- 编辑代理基础信息 ---

// EditAgentReq defines the request structure for editing an agent's basic information.
type EditAgentReq struct {
	g.Meta      `path:"/agents/{agentId}" method:"put" tags:"Agent Management" summary:"编辑代理基础信息"`
	AgentId     int64  `json:"agentId" v:"required#请输入代理ID" dc:"代理ID"`
	AgentName   string `json:"agentName" v:"required|length:2,50#请输入代理名称|代理名称长度为2到50位" dc:"代理名称"`
	Email       string `json:"email" v:"email#邮箱格式不正确" dc:"邮箱"`
	PhoneNumber string `json:"phoneNumber" v:"phone#手机号格式不正确" dc:"手机号"`
	Status      *int   `json:"status" v:"required|in:0,1#请选择状态|状态值无效" dc:"状态 (0:禁用, 1:启用)"`
}

// EditAgentRes defines the response structure for editing an agent's basic information.
type EditAgentRes struct{}

// --- 批量软删除代理 ---

// DeleteAgentReq defines the request structure for batch soft deleting agents.
type DeleteAgentReq struct {
	// g.Meta   `path:"/agents" method:"delete" tags:"Agent Management" summary:"批量软删除代理"`
	AgentIds []int64 `json:"agentIds" v:"required|min-length:1#请选择要删除的代理" dc:"代理ID列表"`
}

// DeleteAgentRes defines the response structure for batch soft deleting agents.
type DeleteAgentRes struct{}

// --- 批量更新代理状态 ---

// UpdateAgentStatusReq defines the request structure for batch updating agent statuses.
// Consider using PATCH /agents/{agentId} for single updates.
type UpdateAgentStatusReq struct {
	g.Meta   `path:"/agents/batch/status" method:"put" tags:"Agent Management" summary:"批量更新代理状态"`
	AgentIds []int64 `json:"agentIds" v:"required|min-length:1#请选择要更新状态的代理" dc:"代理ID列表"`
	Status   int     `json:"status" v:"required|in:0,1#请选择状态|状态值无效" dc:"状态 (0:禁用, 1:启用)"`
}

// UpdateAgentStatusRes defines the response structure for batch updating agent statuses.
type UpdateAgentStatusRes struct{}

// --- 修改指定代理密码 ---

// UpdateAgentPasswordReq defines the request structure for updating a specific agent's password.
type UpdateAgentPasswordReq struct {
	g.Meta      `path:"/agents/{agentId}/password" method:"put" tags:"Agent Management" summary:"修改指定代理密码"`
	AgentId     int64  `json:"agentId" v:"required#请输入代理ID" dc:"代理ID"`
	NewPassword string `json:"newPassword" v:"required|length:6,30#请输入新密码|新密码长度为6到30位" dc:"新密码"`
}

// UpdateAgentPasswordRes defines the response structure for updating a specific agent's password.
type UpdateAgentPasswordRes struct{}

// --- 重置指定代理的 Google Authenticator ---

// ResetAgent2FAReq defines the request structure for resetting a specific agent's Google Authenticator.
type ResetAgent2FAReq struct {
	g.Meta  `path:"/agents/{agentId}/reset-2fa" method:"put" tags:"Agent Management" summary:"重置指定代理的 Google Authenticator"`
	AgentId int64 `json:"agentId" v:"required#请输入代理ID" dc:"代理ID"`
}

// ResetAgent2FARes defines the response structure for resetting a specific agent's Google Authenticator.
type ResetAgent2FARes struct{}

// --- 获取指定代理的 IP 白名单 ---

// GetAgentWhitelistReq defines the request structure for getting a specific agent's IP whitelist.
type GetAgentWhitelistReq struct {
	g.Meta    `path:"/agents/{agentId}/whitelist" method:"get" tags:"Agent Management" summary:"获取指定代理的 IP 白名单"`
	AgentId   int64  `json:"agentId" v:"required#请输入代理ID" dc:"代理ID"`
	DateRange string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	common.PageRequest
}

// GetAgentWhitelistRes defines the response structure for getting a specific agent's IP whitelist.
type GetAgentWhitelistRes struct {
	Page common.PageResponse    `json:"page" dc:"分页信息"`
	Data []*entity.IpAccessList `json:"data" dc:"IP白名单列表"`
}

// --- 为指定代理添加 IP 白名单 ---

// AddAgentWhitelistReq defines the request structure for adding an IP to a specific agent's whitelist.
type AddAgentWhitelistReq struct {
	g.Meta      `path:"/agents/{agentId}/whitelist" method:"post" tags:"Agent Management" summary:"为指定代理添加 IP 白名单"`
	AgentId     int64  `json:"agentId" v:"required#请输入代理ID" dc:"代理ID"`
	IpAddress   string `json:"ipAddress" v:"required|ip#请输入有效的IP地址" dc:"IP地址"`
	Description string `json:"description" dc:"描述"`
}

// AddAgentWhitelistRes defines the response structure for adding an IP to a specific agent's whitelist.
type AddAgentWhitelistRes struct{}

// --- 删除指定代理的 IP 白名单 ---

// DeleteAgentWhitelistReq defines the request structure for deleting an IP from an agent's whitelist.
// Assumes ipWhitelistId is globally unique or context is handled in logic.
// Consider path:"/agents/{agentId}/whitelist/{ipWhitelistId}" for more explicitness if needed.
type DeleteAgentWhitelistReq struct {
	g.Meta        `path:"/agents/whitelist/{ipWhitelistId}" method:"delete" tags:"Agent Management" summary:"删除指定代理的 IP 白名单"`
	IpWhitelistId int64 `json:"ipWhitelistId" v:"required#请输入要删除的白名单记录ID" dc:"IP白名单记录ID"`
	// 或者根据 agent_id 和 ip_address 删除
	// AgentId   int64  `json:"agentId" v:"required-without:IpWhitelistId#请输入代理ID"`
	// IpAddress string `json:"ipAddress" v:"required-without:IpWhitelistId|ip#请输入IP地址"`
}

// DeleteAgentWhitelistRes defines the response structure for deleting an IP from an agent's whitelist.
type DeleteAgentWhitelistRes struct{}
