package v1

import (
	"admin-api/api/common"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

// --- Withdrawal Amount Settings DTOs ---

// WithdrawalAmountSettingInfo 提现金额设置信息
type WithdrawalAmountSettingInfo struct {
	Id        uint64    `json:"id"`        // 主键ID
	Currency  string    `json:"currency"`  // 币种符号
	Network   string    `json:"network"`   // 网络类型
	MinAmount string    `json:"minAmount"` // 单笔最小提现金额
	MaxAmount string    `json:"maxAmount"` // 单笔最大提现金额
	Status    int       `json:"status"`    // 状态: 1-启用, 0-禁用
	CreatedAt time.Time `json:"createdAt"` // 创建时间
	UpdatedAt time.Time `json:"updatedAt"` // 更新时间
}

// ListWithdrawalAmountSettingsReq 获取提现金额设置列表请求
type ListWithdrawalAmountSettingsReq struct {
	g.Meta           `path:"/withdrawal-amount-settings" method:"get" tags:"WithdrawalConfig" summary:"获取提现金额设置列表"`
	common.PageRequest
	Currency *string `json:"currency,omitempty"` // 按币种筛选
	Network  *string `json:"network,omitempty"`  // 按网络筛选
	Status   *int    `json:"status,omitempty"`   // 按状态筛选
}

// ListWithdrawalAmountSettingsRes 获取提现金额设置列表响应
type ListWithdrawalAmountSettingsRes struct {
	common.PageResponse
	Data []*WithdrawalAmountSettingInfo `json:"data"`
}

// CreateWithdrawalAmountSettingReq 创建提现金额设置请求
type CreateWithdrawalAmountSettingReq struct {
	g.Meta    `path:"/withdrawal-amount-settings" method:"post" tags:"WithdrawalConfig" summary:"创建提现金额设置"`
	Currency  string `json:"currency" v:"required#币种不能为空"`
	Network   string `json:"network" v:"required#网络不能为空"`
	MinAmount string `json:"minAmount" v:"required#最小金额不能为空"`
	MaxAmount string `json:"maxAmount" v:"required#最大金额不能为空"`
	Status    int    `json:"status" v:"in:0,1#状态值必须为0或1"`
}

// CreateWithdrawalAmountSettingRes 创建提现金额设置响应
type CreateWithdrawalAmountSettingRes struct {
	Id uint64 `json:"id"`
}

// UpdateWithdrawalAmountSettingReq 更新提现金额设置请求
type UpdateWithdrawalAmountSettingReq struct {
	g.Meta    `path:"/withdrawal-amount-settings/{id}" method:"put" tags:"WithdrawalConfig" summary:"更新提现金额设置"`
	Id        uint64 `json:"id" v:"required|min:1#ID不能为空|ID必须大于0" in:"path"`
	Currency  string `json:"currency" v:"required#币种不能为空"`
	Network   string `json:"network" v:"required#网络不能为空"`
	MinAmount string `json:"minAmount" v:"required#最小金额不能为空"`
	MaxAmount string `json:"maxAmount" v:"required#最大金额不能为空"`
	Status    int    `json:"status" v:"in:0,1#状态值必须为0或1"`
}

// UpdateWithdrawalAmountSettingRes 更新提现金额设置响应
type UpdateWithdrawalAmountSettingRes struct {
	Id uint64 `json:"id"`
}

// DeleteWithdrawalAmountSettingReq 删除提现金额设置请求
type DeleteWithdrawalAmountSettingReq struct {
	g.Meta `path:"/withdrawal-amount-settings" method:"delete" tags:"WithdrawalConfig" summary:"删除提现金额设置"`
	Ids    []uint64 `json:"ids" v:"required|min-length:1#请选择要删除的设置ID"`
}

// DeleteWithdrawalAmountSettingRes 删除提现金额设置响应
type DeleteWithdrawalAmountSettingRes struct{}

// --- Withdrawal Approval Settings DTOs ---

// WithdrawalApprovalSettingInfo 提现审核设置信息
type WithdrawalApprovalSettingInfo struct {
	Id                uint64    `json:"id"`                // 主键ID
	Currency          string    `json:"currency"`          // 币种符号
	Network           string    `json:"network"`           // 网络类型
	AutoReleaseMin    string    `json:"autoReleaseMin"`    // 无需审核自动放币最小金额
	AutoReleaseMax    string    `json:"autoReleaseMax"`    // 无需审核自动放币最大金额
	ApprovalAutoMin   string    `json:"approvalAutoMin"`   // 审核确定后自动放币最小金额
	ApprovalAutoMax   string    `json:"approvalAutoMax"`   // 审核确定后自动放币最大金额
	ApprovalManualMin string    `json:"approvalManualMin"` // 审核确定后手动放币最小金额
	ApprovalManualMax string    `json:"approvalManualMax"` // 审核确定后手动放币最大金额
	Status            int       `json:"status"`            // 状态: 1-启用, 0-禁用
	CreatedAt         time.Time `json:"createdAt"`         // 创建时间
	UpdatedAt         time.Time `json:"updatedAt"`         // 更新时间
}

// ListWithdrawalApprovalSettingsReq 获取提现审核设置列表请求
type ListWithdrawalApprovalSettingsReq struct {
	g.Meta           `path:"/withdrawal-approval-settings" method:"get" tags:"WithdrawalConfig" summary:"获取提现审核设置列表"`
	common.PageRequest
	Currency *string `json:"currency,omitempty"` // 按币种筛选
	Network  *string `json:"network,omitempty"`  // 按网络筛选
	Status   *int    `json:"status,omitempty"`   // 按状态筛选
}

// ListWithdrawalApprovalSettingsRes 获取提现审核设置列表响应
type ListWithdrawalApprovalSettingsRes struct {
	common.PageResponse
	Data []*WithdrawalApprovalSettingInfo `json:"data"`
}

// CreateWithdrawalApprovalSettingReq 创建提现审核设置请求
type CreateWithdrawalApprovalSettingReq struct {
	g.Meta            `path:"/withdrawal-approval-settings" method:"post" tags:"WithdrawalConfig" summary:"创建提现审核设置"`
	Currency          string `json:"currency" v:"required#币种不能为空"`
	Network           string `json:"network" v:"required#网络不能为空"`
	AutoReleaseMin    string `json:"autoReleaseMin" v:"required#无需审核自动放币最小金额不能为空"`
	AutoReleaseMax    string `json:"autoReleaseMax" v:"required#无需审核自动放币最大金额不能为空"`
	ApprovalAutoMin   string `json:"approvalAutoMin" v:"required#审核确定后自动放币最小金额不能为空"`
	ApprovalAutoMax   string `json:"approvalAutoMax" v:"required#审核确定后自动放币最大金额不能为空"`
	ApprovalManualMin string `json:"approvalManualMin" v:"required#审核确定后手动放币最小金额不能为空"`
	ApprovalManualMax string `json:"approvalManualMax" v:"required#审核确定后手动放币最大金额不能为空"`
	Status            int    `json:"status" v:"in:0,1#状态值必须为0或1"`
}

// CreateWithdrawalApprovalSettingRes 创建提现审核设置响应
type CreateWithdrawalApprovalSettingRes struct {
	Id uint64 `json:"id"`
}

// UpdateWithdrawalApprovalSettingReq 更新提现审核设置请求
type UpdateWithdrawalApprovalSettingReq struct {
	g.Meta            `path:"/withdrawal-approval-settings/{id}" method:"put" tags:"WithdrawalConfig" summary:"更新提现审核设置"`
	Id                uint64 `json:"id" v:"required|min:1#ID不能为空|ID必须大于0" in:"path"`
	Currency          string `json:"currency" v:"required#币种不能为空"`
	Network           string `json:"network" v:"required#网络不能为空"`
	AutoReleaseMin    string `json:"autoReleaseMin" v:"required#无需审核自动放币最小金额不能为空"`
	AutoReleaseMax    string `json:"autoReleaseMax" v:"required#无需审核自动放币最大金额不能为空"`
	ApprovalAutoMin   string `json:"approvalAutoMin" v:"required#审核确定后自动放币最小金额不能为空"`
	ApprovalAutoMax   string `json:"approvalAutoMax" v:"required#审核确定后自动放币最大金额不能为空"`
	ApprovalManualMin string `json:"approvalManualMin" v:"required#审核确定后手动放币最小金额不能为空"`
	ApprovalManualMax string `json:"approvalManualMax" v:"required#审核确定后手动放币最大金额不能为空"`
	Status            int    `json:"status" v:"in:0,1#状态值必须为0或1"`
}

// UpdateWithdrawalApprovalSettingRes 更新提现审核设置响应
type UpdateWithdrawalApprovalSettingRes struct {
	Id uint64 `json:"id"`
}

// DeleteWithdrawalApprovalSettingReq 删除提现审核设置请求
type DeleteWithdrawalApprovalSettingReq struct {
	g.Meta `path:"/withdrawal-approval-settings" method:"delete" tags:"WithdrawalConfig" summary:"删除提现审核设置"`
	Ids    []uint64 `json:"ids" v:"required|min-length:1#请选择要删除的设置ID"`
}

// DeleteWithdrawalApprovalSettingRes 删除提现审核设置响应
type DeleteWithdrawalApprovalSettingRes struct{}

// --- Withdrawal Fee Settings DTOs ---

// WithdrawalFeeSettingInfo 提现手续费设置信息
type WithdrawalFeeSettingInfo struct {
	Id        uint64    `json:"id"`        // 主键ID
	Currency  string    `json:"currency"`  // 币种符号
	Network   string    `json:"network"`   // 网络类型
	AmountMin string    `json:"amountMin"` // 单笔提现金额范围最小值
	AmountMax string    `json:"amountMax"` // 单笔提现金额范围最大值
	FeeType   string    `json:"feeType"`   // 手续费类型: fixed-固定金额, percent-百分比
	FeeValue  string    `json:"feeValue"`  // 手续费值
	Status    int       `json:"status"`    // 状态: 1-启用, 0-禁用
	CreatedAt time.Time `json:"createdAt"` // 创建时间
	UpdatedAt time.Time `json:"updatedAt"` // 更新时间
}

// ListWithdrawalFeeSettingsReq 获取提现手续费设置列表请求
type ListWithdrawalFeeSettingsReq struct {
	g.Meta           `path:"/withdrawal-fee-settings" method:"get" tags:"WithdrawalConfig" summary:"获取提现手续费设置列表"`
	common.PageRequest
	Currency *string `json:"currency,omitempty"` // 按币种筛选
	Network  *string `json:"network,omitempty"`  // 按网络筛选
	FeeType  *string `json:"feeType,omitempty"`  // 按手续费类型筛选
	Status   *int    `json:"status,omitempty"`   // 按状态筛选
}

// ListWithdrawalFeeSettingsRes 获取提现手续费设置列表响应
type ListWithdrawalFeeSettingsRes struct {
	common.PageResponse
	Data []*WithdrawalFeeSettingInfo `json:"data"`
}

// CreateWithdrawalFeeSettingReq 创建提现手续费设置请求
type CreateWithdrawalFeeSettingReq struct {
	g.Meta    `path:"/withdrawal-fee-settings" method:"post" tags:"WithdrawalConfig" summary:"创建提现手续费设置"`
	Currency  string `json:"currency" v:"required#币种不能为空"`
	Network   string `json:"network" v:"required#网络不能为空"`
	AmountMin string `json:"amountMin" v:"required#金额范围最小值不能为空"`
	AmountMax string `json:"amountMax" v:"required#金额范围最大值不能为空"`
	FeeType   string `json:"feeType" v:"required|in:fixed,percent#手续费类型不能为空|手续费类型必须为fixed或percent"`
	FeeValue  string `json:"feeValue" v:"required#手续费值不能为空"`
	Status    int    `json:"status" v:"in:0,1#状态值必须为0或1"`
}

// CreateWithdrawalFeeSettingRes 创建提现手续费设置响应
type CreateWithdrawalFeeSettingRes struct {
	Id uint64 `json:"id"`
}

// UpdateWithdrawalFeeSettingReq 更新提现手续费设置请求
type UpdateWithdrawalFeeSettingReq struct {
	g.Meta    `path:"/withdrawal-fee-settings/{id}" method:"put" tags:"WithdrawalConfig" summary:"更新提现手续费设置"`
	Id        uint64 `json:"id" v:"required|min:1#ID不能为空|ID必须大于0" in:"path"`
	Currency  string `json:"currency" v:"required#币种不能为空"`
	Network   string `json:"network" v:"required#网络不能为空"`
	AmountMin string `json:"amountMin" v:"required#金额范围最小值不能为空"`
	AmountMax string `json:"amountMax" v:"required#金额范围最大值不能为空"`
	FeeType   string `json:"feeType" v:"required|in:fixed,percent#手续费类型不能为空|手续费类型必须为fixed或percent"`
	FeeValue  string `json:"feeValue" v:"required#手续费值不能为空"`
	Status    int    `json:"status" v:"in:0,1#状态值必须为0或1"`
}

// UpdateWithdrawalFeeSettingRes 更新提现手续费设置响应
type UpdateWithdrawalFeeSettingRes struct {
	Id uint64 `json:"id"`
}

// DeleteWithdrawalFeeSettingReq 删除提现手续费设置请求
type DeleteWithdrawalFeeSettingReq struct {
	g.Meta `path:"/withdrawal-fee-settings" method:"delete" tags:"WithdrawalConfig" summary:"删除提现手续费设置"`
	Ids    []uint64 `json:"ids" v:"required|min-length:1#请选择要删除的设置ID"`
}

// DeleteWithdrawalFeeSettingRes 删除提现手续费设置响应
type DeleteWithdrawalFeeSettingRes struct{}
