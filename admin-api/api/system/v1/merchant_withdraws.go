package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// --- 获取我的提现记录列表 ---

// GetMyWithdrawsReq defines the request structure for getting merchant's own withdraw records.
type GetMyWithdrawsReq struct {
	g.Meta `path:"/my/withdraws" method:"get" tags:"MerchantWithdraws" summary:"获取我的提现记录"`
	common.PageRequest
	MerchantId *uint64  `json:"merchantId" dc:"商户ID (可选，不传则查询所有商户)"`
	TokenId    *uint    `json:"tokenId" dc:"币种ID (筛选特定币种)"`
	TokenName  string   `json:"tokenName" dc:"币种名称 (模糊搜索)"`
	State      *uint    `json:"state" dc:"状态 (1-待审核, 2-处理中, 3-已拒绝, 4-已完成, 5-失败, 6-已撤销)"`
	OrderNo    string   `json:"orderNo" dc:"提现订单号 (模糊搜索)"`
	TxHash     string   `json:"txHash" dc:"交易哈希 (模糊搜索)"`
	Address    string   `json:"address" dc:"提币地址 (模糊搜索)"`
	AmountMin  *string  `json:"amountMin" dc:"最小金额"`
	AmountMax  *string  `json:"amountMax" dc:"最大金额"`
	DateRange  string   `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	CreatedAt  []string `json:"createdAt" dc:"创建时间范围数组，格式：[开始时间, 结束时间]"`
	Export     bool     `json:"export" dc:"是否导出 (true-导出, false-不导出)"`
}

// MerchantWithdrawInfoType defines the structure for withdraw information in a list.
type MerchantWithdrawInfoType struct {
	WithdrawsId        uint            `json:"withdrawsId" dc:"提现记录ID"`
	MerchantId         uint64          `json:"merchantId" dc:"商户ID"`
	MerchantName       string          `json:"merchantName" dc:"商户名称"`
	TokenId            uint            `json:"tokenId" dc:"币种ID"`
	TokenName          string          `json:"tokenName" dc:"币种名称"`
	Chan               string          `json:"chan" dc:"提现渠道"`
	OrderNo            string          `json:"orderNo" dc:"提现订单号"`
	Address            string          `json:"address" dc:"提币目标地址"`
	RecipientName      string          `json:"recipientName" dc:"法币收款人姓名"`
	RecipientAccount   string          `json:"recipientAccount" dc:"法币收款账户"`
	Amount             decimal.Decimal `json:"amount" dc:"申请提现金额"`
	HandlingFee        decimal.Decimal `json:"handlingFee" dc:"提现手续费"`
	ActualAmount       decimal.Decimal `json:"actualAmount" dc:"实际到账金额"`
	State              uint            `json:"state" dc:"状态值"`
	StateText          string          `json:"stateText" dc:"状态描述"`
	RefuseReasonZh     string          `json:"refuseReasonZh" dc:"拒绝原因(中文)"`
	RefuseReasonEn     string          `json:"refuseReasonEn" dc:"拒绝原因(英文)"`
	TxHash             string          `json:"txHash" dc:"交易哈希"`
	UserRemark         string          `json:"userRemark" dc:"用户备注"`
	AdminRemark        string          `json:"adminRemark" dc:"管理员备注"`
	FiatType           string          `json:"fiatType" dc:"法币提现类型"`
	CreatedAt          *gtime.Time     `json:"createdAt" dc:"创建时间"`
	CheckedAt          *gtime.Time     `json:"checkedAt" dc:"审核时间"`
	ProcessingAt       *gtime.Time     `json:"processingAt" dc:"处理开始时间"`
	CompletedAt        *gtime.Time     `json:"completedAt" dc:"完成时间"`
	NotificationSent   uint            `json:"notificationSent" dc:"通知状态"`
	NotificationSentAt *gtime.Time     `json:"notificationSentAt" dc:"通知发送时间"`
	CanCancel          bool            `json:"canCancel" dc:"是否可以撤销"`
}

// GetMyWithdrawsRes defines the response structure for getting merchant's own withdraw records.
type GetMyWithdrawsRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of withdraw records.
	Data []*MerchantWithdrawInfoType `json:"data" dc:"提现记录列表数据"`
}

// --- 获取我的提现记录详情 ---

// GetMyWithdrawDetailReq defines the request structure for getting specific withdraw record details.
type GetMyWithdrawDetailReq struct {
	g.Meta      `path:"/my/withdraws/{withdrawsId}" method:"get" tags:"MerchantWithdraws" summary:"获取我的提现记录详情"`
	WithdrawsId uint `json:"withdrawsId" v:"required#提现记录ID不能为空" dc:"提现记录ID"`
}

// MerchantWithdrawDetailType defines the structure for detailed withdraw information.
type MerchantWithdrawDetailType struct {
	entity.MerchantWithdraws
	StateText string `json:"stateText" dc:"状态描述"`
	CanCancel bool   `json:"canCancel" dc:"是否可以撤销"`
}

// GetMyWithdrawDetailRes defines the response structure for getting withdraw record details.
type GetMyWithdrawDetailRes struct {
	// Data contains the withdraw record details.
	Data *MerchantWithdrawDetailType `json:"data" dc:"提现记录详情数据"`
}

// --- 撤销我的提现申请 ---

// CancelMyWithdrawReq defines the request structure for canceling a pending withdraw.
type CancelMyWithdrawReq struct {
	g.Meta      `path:"/my/withdraws/{withdrawsId}/cancel" method:"put" tags:"MerchantWithdraws" summary:"撤销我的提现申请"`
	WithdrawsId uint   `json:"withdrawsId" v:"required#提现记录ID不能为空" dc:"提现记录ID"`
	Reason      string `json:"reason" v:"length:0,200" dc:"撤销原因"`
}

// CancelMyWithdrawRes defines the response structure after canceling a withdraw.
type CancelMyWithdrawRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
	// Message provides additional information about the cancellation.
	Message string `json:"message" dc:"操作结果信息"`
}

// --- 创建我的提现申请 ---

// CreateMyWithdrawReq defines the request structure for creating a new withdraw.
type CreateMyWithdrawReq struct {
	g.Meta     `path:"/my/withdraws" method:"post" tags:"MerchantWithdraws" summary:"创建我的提现申请"`
	MerchantId uint64 `json:"merchantId" v:"required" dc:"商户ID (管理员必须指定商户ID)"`
	TokenName  string `json:"tokenName" v:"required|length:1,50" dc:"币种名称"`
	Chain      string `json:"chain" v:"required|length:1,50" dc:"区块链网络"`
	OrderNo    string `json:"orderNo" v:"required|length:1,100" dc:"商户订单号，唯一标识"`
	Address    string `json:"address" v:"required|length:10,100" dc:"提现目标地址"`
	Amount     string `json:"amount" v:"required|regex:^[0-9]+(\\.[0-9]+)?$" dc:"提现金额"`
	UserRemark string `json:"userRemark" v:"length:0,200" dc:"用户备注"`
	Code       string `json:"code" v:"required|length:6,6" dc:"2FA验证码"`
}

// CreateMyWithdrawRes defines the response structure for creating a withdraw.
type CreateMyWithdrawRes struct {
	// Data contains the created withdraw information.
	Data *CreateMyWithdrawData `json:"data" dc:"创建的提现信息"`
}

// CreateMyWithdrawData defines the data structure returned after creating a withdraw.
type CreateMyWithdrawData struct {
	WithdrawsId   uint   `json:"withdrawsId" dc:"提现记录ID"`
	OrderNo       string `json:"orderNo" dc:"商户订单号"`
	TokenName     string `json:"tokenName" dc:"币种名称"`
	Chain         string `json:"chain" dc:"区块链网络"`
	Address       string `json:"address" dc:"提现目标地址"`
	Amount        string `json:"amount" dc:"提现金额"`
	HandlingFee   string `json:"handlingFee" dc:"手续费"`
	ActualAmount  string `json:"actualAmount" dc:"实际到账金额"`
	Status        string `json:"status" dc:"状态"`
	EstimatedTime string `json:"estimatedTime" dc:"预计到账时间"`
}

// --- 获取我的提现手续费 ---

// GetMyWithdrawFeeReq defines the request structure for getting withdraw fee.
type GetMyWithdrawFeeReq struct {
	g.Meta    `path:"/my/withdraws/fee" method:"get" tags:"MerchantWithdraws" summary:"获取我的提现手续费"`
	TokenName string `json:"tokenName" v:"required|length:1,50" dc:"币种名称"`
	Chain     string `json:"chain" v:"required|length:1,50" dc:"区块链网络"`
	Amount    string `json:"amount" v:"required|regex:^[0-9]+(\\.[0-9]+)?$" dc:"提现金额"`
}

// GetMyWithdrawFeeRes defines the response structure for getting withdraw fee.
type GetMyWithdrawFeeRes struct {
	// Data contains the fee calculation information.
	Data *GetMyWithdrawFeeData `json:"data" dc:"手续费信息"`
}

// GetMyWithdrawFeeData defines the fee calculation data structure.
type GetMyWithdrawFeeData struct {
	TokenName     string `json:"tokenName" dc:"币种名称"`
	Chain         string `json:"chain" dc:"区块链网络"`
	Amount        string `json:"amount" dc:"提现金额"`
	HandlingFee   string `json:"handlingFee" dc:"手续费"`
	ActualAmount  string `json:"actualAmount" dc:"实际到账金额"`
	FeeRate       string `json:"feeRate" dc:"手续费率"`
	MinAmount     string `json:"minAmount" dc:"最小提现金额"`
	MaxAmount     string `json:"maxAmount" dc:"最大提现金额"`
	Available     string `json:"available" dc:"可用余额"`
	EstimatedTime string `json:"estimatedTime" dc:"预计到账时间"`
}

// --- 管理员审批提现申请 ---

// ApproveWithdrawReq defines the request structure for approving a withdraw.
type ApproveWithdrawReq struct {
	g.Meta      `path:"/admin/withdraws/{withdrawsId}/approve" method:"put" tags:"AdminWithdraws" summary:"审批通过提现申请"`
	WithdrawsId uint   `json:"withdrawsId" v:"required#提现记录ID不能为空" dc:"提现记录ID"`
	Notes       string `json:"notes" v:"length:0,200" dc:"审批备注"`
}

// ApproveWithdrawRes defines the response structure after approving a withdraw.
type ApproveWithdrawRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
	// Message provides additional information about the approval.
	Message string `json:"message" dc:"操作结果信息"`
	// TransactionIds contains the IDs of the generated audit transaction records.
	TransactionIds []uint64 `json:"transactionIds" dc:"生成的审计交易记录ID"`
}

// --- 管理员拒绝提现申请 ---

// RejectWithdrawReq defines the request structure for rejecting a withdraw.
type RejectWithdrawReq struct {
	g.Meta      `path:"/admin/withdraws/{withdrawsId}/reject" method:"put" tags:"AdminWithdraws" summary:"拒绝提现申请"`
	WithdrawsId uint   `json:"withdrawsId" v:"required#提现记录ID不能为空" dc:"提现记录ID"`
	Reason      string `json:"reason" v:"required|length:1,200" dc:"拒绝原因"`
}

// RejectWithdrawRes defines the response structure after rejecting a withdraw.
type RejectWithdrawRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
	// Message provides additional information about the rejection.
	Message string `json:"message" dc:"操作结果信息"`
	// TransactionIds contains the IDs of the generated transaction records for unfreezing.
	TransactionIds []uint64 `json:"transactionIds" dc:"解冻交易记录ID"`
}
