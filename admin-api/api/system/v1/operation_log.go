package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// --- 操作日志列表查询 ---

// GetOperationLogListReq defines the request structure for querying the operation log list.
type GetOperationLogListReq struct {
	g.Meta `path:"/operation-logs" method:"get" tags:"SystemOperationLog" summary:"查询操作日志列表"`
	common.PageRequest
	// DateRange filters logs within a specific date range (format: YYYY-MM-DD,YYYY-MM-DD).
	DateRange string `json:"dateRange" dc:"日期范围，格式：2025-01-01,2025-01-31"`
	// StartTime filters logs from this start time (format: YYYY-MM-DD).
	StartTime string `json:"startTime" dc:"开始时间，格式：2025-01-01"`
	// EndTime filters logs up to this end time (format: YYYY-MM-DD).
	EndTime string `json:"endTime" dc:"结束时间，格式：2025-01-31"`
	// OperationIp filters logs by operation IP address (supports fuzzy search).
	OperationIp string `json:"operationIp" dc:"操作IP，支持模糊查询"`
	// Id filters logs by log ID.
	Id int64 `json:"id" dc:"日志ID"`
	// Username filters logs by username (supports fuzzy search).
	Username string `json:"username" dc:"用户名，支持模糊查询"`
	// Module filters logs by operation module (supports fuzzy search).
	Module string `json:"module" dc:"操作模块，支持模糊查询"`
	// Action filters logs by operation action (supports fuzzy search).
	Action string `json:"action" dc:"操作名称，支持模糊查询"`
	// RequestMethod filters logs by HTTP request method.
	RequestMethod string `json:"requestMethod" dc:"请求方法"`
	// Status filters logs by status (1: success, 0: failure, -1: all).
	Status int `json:"status" d:"-1" dc:"状态：1成功，0失败，-1全部"`
	// Export indicates whether to export the results (0: no, 1: yes).
	Export int `json:"export" d:"0" dc:"是否导出：0不导出，1导出"`
}

// GetOperationLogListRes defines the response structure for the operation log list query.
// Note: Directly returning the entity might lead to tight coupling. Consider a DTO.
type GetOperationLogListRes struct {
	// Page contains the pagination information.
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of operation logs.
	Data []*entity.OperationLog `json:"data" dc:"日志列表"`
}

// --- 操作日志详情查询 ---

// GetOperationLogDetailReq defines the request structure for querying operation log details.
type GetOperationLogDetailReq struct {
	g.Meta `path:"/operation-logs/{id}" method:"get" tags:"SystemOperationLog" summary:"查询操作日志详情"`
	// Id is the ID of the operation log entry to retrieve details for (obtained from path).
	Id int64 `json:"id" v:"required#日志ID不能为空" dc:"日志ID"`
}

// GetOperationLogDetailRes defines the response structure for the operation log details query.
// Note: Directly returning the entity might lead to tight coupling. Consider a DTO.
type GetOperationLogDetailRes struct {
	// Info contains the details of the operation log entry.
	Info *entity.OperationLog `json:"info" dc:"日志详情"`
}
