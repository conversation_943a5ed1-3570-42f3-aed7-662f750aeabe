package v1

import "github.com/gogf/gf/v2/frame/g"

// AssignRolesToAdminMemberReq assigns roles to an admin member.
type AssignRolesToAdminMemberReq struct {
	g.Meta        `path:"/system/admin-member/assign-roles" method:"post" summary:"Assign roles to admin member" tags:"System/AdminMember"`
	AdminMemberId int64    `json:"adminMemberId" v:"required#Admin member ID is required"`
	RoleKeys      []string `json:"roleKeys" v:"required#Role keys are required"` // Array of role keys
}

// AssignRolesToAdminMemberRes is the response for assigning roles to an admin member.
type AssignRolesToAdminMemberRes struct {
	// Empty, or a success message/status
}

// GetAdminMemberAssignedRolesReq requests the roles assigned to an admin member.
type GetAdminMemberAssignedRolesReq struct {
	g.Meta        `path:"/system/admin-member/get-roles" method:"get" summary:"Get roles assigned to admin member" tags:"System/AdminMember"`
	AdminMemberId int64 `json:"adminMemberId" v:"required#Admin member ID is required"`
}

// GetAdminMemberAssignedRolesRes is the response for getting roles assigned to an admin member.
type GetAdminMemberAssignedRolesRes struct {
	RoleKeys []string `json:"roleKeys"`
}
