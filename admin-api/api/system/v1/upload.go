package v1

import "github.com/gogf/gf/v2/frame/g"

// UploadAvatarReq defines the request structure for uploading an avatar.
// The actual file is expected via multipart/form-data.
type UploadAvatarReq struct {
	g.Meta `path:"/upload/avatar" method:"post" tags:"SystemUpload" summary:"上传头像"`
	// No specific fields needed here if the file is the only input via form-data and passed implicitly.
	// If you need other form fields along with the file, define them here.
	// Example:
	// UserID string `json:"userId" v:"required#UserID is required"`
}

// UploadAvatarRes defines the response structure after successfully uploading an avatar.
type UploadAvatarRes struct {
	AvatarUrl string `json:"avatarUrl" dc:"头像URL"`
}
