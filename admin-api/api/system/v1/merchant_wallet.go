package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// --- 商户资产管理 API 定义 ---

// --- 获取商户资产概览 ---

// GetMerchantAssetsReq defines the request structure for getting merchant assets overview.
type GetMerchantAssetsReq struct {
	g.Meta     `path:"/merchants/{merchantId}/assets" method:"get" tags:"MerchantWallet" summary:"获取商户资产概览"`
	MerchantId uint64 `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
}

// MerchantAssetItem defines the structure for a merchant asset item.
type MerchantAssetItem struct {
	Symbol                    string          `json:"symbol" dc:"代币符号"`
	AvailableBalance          int64           `json:"availableBalance" dc:"可用余额(原始值)"`
	FrozenBalance             int64           `json:"frozenBalance" dc:"冻结余额(原始值)"`
	FormattedAvailableBalance string          `json:"formattedAvailableBalance" dc:"格式化可用余额"`
	FormattedFrozenBalance    string          `json:"formattedFrozenBalance" dc:"格式化冻结余额"`
	FormattedTotalBalance     string          `json:"formattedTotalBalance" dc:"格式化总余额"`
	DecimalPlaces             uint            `json:"decimalPlaces" dc:"小数位数"`
	LastUpdated               string          `json:"lastUpdated" dc:"最后更新时间"`
}

// GetMerchantAssetsRes defines the response structure for getting merchant assets overview.
type GetMerchantAssetsRes struct {
	MerchantId   uint64               `json:"merchantId" dc:"商户ID"`
	MerchantName string               `json:"merchantName" dc:"商户名称"`
	Assets       []*MerchantAssetItem `json:"assets" dc:"资产列表"`
	TotalAssets  int                  `json:"totalAssets" dc:"资产种类数量"`
}

// --- 获取商户钱包列表 ---

// GetMerchantWalletsReq defines the request structure for getting merchant wallets list.
type GetMerchantWalletsReq struct {
	g.Meta `path:"/merchant-wallets" method:"get" tags:"MerchantWallet" summary:"获取商户钱包列表"`
	common.PageRequest
	MerchantId   *uint64 `json:"merchantId" dc:"商户ID (可选)"`
	MerchantName string  `json:"merchantName" dc:"商户名称 (模糊搜索)"`
	Symbol       string  `json:"symbol" dc:"代币符号 (模糊搜索)"`
	HasBalance   *bool   `json:"hasBalance" dc:"是否有余额 (true-有余额, false-无余额)"`
	DateRange    string  `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	Export       bool    `json:"export" dc:"是否导出 (true-导出, false-不导出)"`
}

// MerchantWalletListItem defines the structure for merchant wallet list item.
type MerchantWalletListItem struct {
	*entity.MerchantWallets
	MerchantName              string `json:"merchantName" dc:"商户名称"`
	FormattedAvailableBalance string `json:"formattedAvailableBalance" dc:"格式化可用余额"`
	FormattedFrozenBalance    string `json:"formattedFrozenBalance" dc:"格式化冻结余额"`
	FormattedTotalBalance     string `json:"formattedTotalBalance" dc:"格式化总余额"`
}

// GetMerchantWalletsRes defines the response structure for getting merchant wallets list.
type GetMerchantWalletsRes struct {
	Page common.PageResponse       `json:"page" dc:"分页信息"`
	Data []*MerchantWalletListItem `json:"data" dc:"商户钱包列表"`
}

// --- 调整商户余额 ---

// AdjustMerchantBalanceReq defines the request structure for adjusting merchant balance.
type AdjustMerchantBalanceReq struct {
	g.Meta     `path:"/merchants/{merchantId}/wallets/adjust" method:"post" tags:"MerchantWallet" summary:"调整商户余额"`
	MerchantId uint64          `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
	Symbol     string          `json:"symbol" v:"required#代币符号不能为空" dc:"代币符号"`
	Amount     decimal.Decimal `json:"amount" v:"required#调整金额不能为空" dc:"调整金额"`
	Type       string          `json:"type" v:"required|in:increase,decrease#调整类型不能为空|调整类型只能是increase或decrease" dc:"调整类型 (increase-增加, decrease-减少)"`
	WalletType string          `json:"walletType" v:"required|in:available,frozen#钱包类型不能为空|钱包类型只能是available或frozen" dc:"钱包类型 (available-可用余额, frozen-冻结余额)"`
	Reason     string          `json:"reason" v:"required|length:1,500#调整原因不能为空|调整原因长度必须在1-500字符之间" dc:"调整原因"`
	Reference  string          `json:"reference" v:"length:0,100" dc:"参考信息 (可选)"`
}

// AdjustMerchantBalanceRes defines the response structure for adjusting merchant balance.
type AdjustMerchantBalanceRes struct {
	Success               bool            `json:"success" dc:"是否成功"`
	TransactionId         uint64          `json:"transactionId" dc:"交易记录ID"`
	BalanceBefore         decimal.Decimal `json:"balanceBefore" dc:"调整前余额"`
	BalanceAfter          decimal.Decimal `json:"balanceAfter" dc:"调整后余额"`
	FormattedBalanceBefore string          `json:"formattedBalanceBefore" dc:"格式化调整前余额"`
	FormattedBalanceAfter  string          `json:"formattedBalanceAfter" dc:"格式化调整后余额"`
}

// --- 获取商户钱包详情 ---

// GetMerchantWalletDetailReq defines the request structure for getting merchant wallet detail.
type GetMerchantWalletDetailReq struct {
	g.Meta     `path:"/merchants/{merchantId}/wallets/{symbol}" method:"get" tags:"MerchantWallet" summary:"获取商户钱包详情"`
	MerchantId uint64 `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
	Symbol     string `json:"symbol" v:"required#代币符号不能为空" dc:"代币符号"`
}

// GetMerchantWalletDetailRes defines the response structure for getting merchant wallet detail.
type GetMerchantWalletDetailRes struct {
	*MerchantAssetItem
	MerchantId   uint64 `json:"merchantId" dc:"商户ID"`
	MerchantName string `json:"merchantName" dc:"商户名称"`
	WalletId     int64  `json:"walletId" dc:"钱包ID"`
	CreatedAt    string `json:"createdAt" dc:"创建时间"`
}

// --- 批量调整商户余额 ---

// BatchAdjustItem defines the structure for batch adjustment item.
type BatchAdjustItem struct {
	MerchantId uint64          `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
	Symbol     string          `json:"symbol" v:"required#代币符号不能为空" dc:"代币符号"`
	Amount     decimal.Decimal `json:"amount" v:"required#调整金额不能为空" dc:"调整金额"`
	Type       string          `json:"type" v:"required|in:increase,decrease#调整类型不能为空|调整类型只能是increase或decrease" dc:"调整类型"`
	WalletType string          `json:"walletType" v:"required|in:available,frozen#钱包类型不能为空|钱包类型只能是available或frozen" dc:"钱包类型"`
	Reason     string          `json:"reason" v:"required|length:1,500#调整原因不能为空|调整原因长度必须在1-500字符之间" dc:"调整原因"`
}

// BatchAdjustMerchantBalanceReq defines the request structure for batch adjusting merchant balance.
type BatchAdjustMerchantBalanceReq struct {
	g.Meta `path:"/merchant-wallets/batch-adjust" method:"post" tags:"MerchantWallet" summary:"批量调整商户余额"`
	Items  []*BatchAdjustItem `json:"items" v:"required|length:1,100#调整项目不能为空|批量调整项目数量限制在1-100之间" dc:"批量调整项目"`
}

// BatchAdjustResult defines the structure for batch adjustment result.
type BatchAdjustResult struct {
	MerchantId    uint64          `json:"merchantId" dc:"商户ID"`
	Symbol        string          `json:"symbol" dc:"代币符号"`
	Success       bool            `json:"success" dc:"是否成功"`
	TransactionId *uint64         `json:"transactionId" dc:"交易记录ID"`
	BalanceBefore *decimal.Decimal `json:"balanceBefore" dc:"调整前余额"`
	BalanceAfter  *decimal.Decimal `json:"balanceAfter" dc:"调整后余额"`
	ErrorMessage  string          `json:"errorMessage" dc:"错误信息"`
}

// BatchAdjustMerchantBalanceRes defines the response structure for batch adjusting merchant balance.
type BatchAdjustMerchantBalanceRes struct {
	TotalCount   int                  `json:"totalCount" dc:"总数量"`
	SuccessCount int                  `json:"successCount" dc:"成功数量"`
	FailedCount  int                  `json:"failedCount" dc:"失败数量"`
	Results      []*BatchAdjustResult `json:"results" dc:"调整结果列表"`
}