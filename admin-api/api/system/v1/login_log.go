package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// --- 登录日志列表查询 ---

// GetLoginLogListReq defines the request structure for querying the login log list.
type GetLoginLogListReq struct {
	g.Meta `path:"/login-logs" method:"get" tags:"SystemLoginLog" summary:"查询登录日志列表"`
	common.PageRequest
	// DateRange filters logs within a specific date range (format: YYYY-MM-DD,YYYY-MM-DD).
	DateRange string `json:"dateRange"  dc:"日期范围，格式：2025-01-01,2025-01-31"`
	// StartTime filters logs from this start time (format: YYYY-MM-DD).
	StartTime string `json:"startTime" dc:"开始时间，格式：2025-01-01"`
	// EndTime filters logs up to this end time (format: YYYY-MM-DD).
	EndTime string `json:"endTime" dc:"结束时间，格式：2025-01-31"`
	// Ip filters logs by IP address (supports fuzzy search).
	Ip string `json:"ip" dc:"IP地址，支持模糊查询"`
	// Id filters logs by log ID.
	Id int64 `json:"id" dc:"日志ID"`
	// Username filters logs by username (supports fuzzy search).
	Username string `json:"username" dc:"用户名，支持模糊查询"`
	// Status filters logs by status (1: success, 0: failure, -1: all).
	Status int `json:"status" d:"-1" dc:"状态：1成功，0失败，-1全部"`
	// Export indicates whether to export the results (0: no, 1: yes).
	Export int `json:"export" d:"0" dc:"是否导出：0不导出，1导出"`

	// 新增：三级代理模糊查询
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称 (模糊搜索)"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称 (模糊搜索)"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称 (模糊搜索)"`

	// 新增：telegram信息查询
	TelegramId       string `json:"telegramId" dc:"Telegram ID (模糊搜索)"`
	TelegramUsername string `json:"telegramUsername" dc:"Telegram用户名 (模糊搜索)"`
	FirstName        string `json:"firstName" dc:"真实姓名 (模糊搜索)"`
}

// LoginLogListItem defines the structure for login log list items with user information.
type LoginLogListItem struct {
	*entity.LoginLog

	// User information
	Username string `json:"username" dc:"用户名"`
	Account  string `json:"account" dc:"用户账号"`

	// Telegram information
	TelegramUsername string `json:"telegramUsername" dc:"Telegram用户名"`
	TelegramId       int64  `json:"telegramId" dc:"Telegram ID"`

	// Agent information
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称"`
}

// GetLoginLogListRes defines the response structure for the login log list query.
// Note: Directly returning the entity might lead to tight coupling. Consider a DTO.
type GetLoginLogListRes struct {
	// Page contains the pagination information.
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of login logs.
	Data []*LoginLogListItem `json:"data" dc:"日志列表"`
}

// --- 登录日志详情查询 ---

// GetLoginLogDetailReq defines the request structure for querying login log details.
type GetLoginLogDetailReq struct {
	g.Meta `path:"/login-logs/{id}" method:"get" tags:"SystemLoginLog" summary:"查询登录日志详情"`
	// Id is the ID of the login log entry to retrieve details for (obtained from path).
	Id int64 `json:"id" v:"required#日志ID不能为空" dc:"日志ID"`
}

// GetLoginLogDetailRes defines the response structure for the login log details query.
// Note: Directly returning the entity might lead to tight coupling. Consider a DTO.
type GetLoginLogDetailRes struct {
	// Info contains the details of the login log entry.
	Info *entity.LoginLog `json:"info" dc:"日志详情"`
}
