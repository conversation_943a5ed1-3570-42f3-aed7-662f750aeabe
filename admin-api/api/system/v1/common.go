package v1

import "github.com/gogf/gf/v2/frame/g"

type CommonStatus int

const (
	StatusDisabled CommonStatus = iota
	StatusEnabled
)

// GetTokenSymbolsReq defines the request structure for getting token symbols for dropdown.
type GetTokenSymbolsReq struct {
	g.Meta `path:"/tokens/symbols" method:"get" tags:"SystemCommon" summary:"获取代币符号列表(用于搜索条件下拉框)"`
}

// GetTokenSymbolsRes defines the response structure for token symbols list.
type GetTokenSymbolsRes struct {
	// Symbols contains the list of token symbols.
	Symbols []string `json:"symbols" dc:"代币符号JSON字符串列表"`
	// TokenIds contains the list of token IDs.
	TokenIds []int `json:"tokenIds" dc:"代币ID列表"`
}
