package v1

import (
	// "admin-api/internal/library/captcha"

	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/gogf/gf/v2/frame/g"

	// "github.com/gogf/gf/v2/os/gtime"
	"golang.org/x/oauth2"
)

// --- 登录 ---

// LoginReq defines the request structure for user login.
type LoginReq struct {
	g.Meta `path:"/login" method:"post" tags:"SystemAuth" summary:"登录"`
	// Username is the user's login name.
	Username string `json:"username" v:"required" dc:"用户名"`
	// Password is the user's login password.
	Password string `json:"password" v:"required" dc:"密码"`
	// CaptchaToken is the token associated with the captcha challenge.
	// CaptchaToken string `json:"captchaToken" dc:"token"`
	// CaptchaCode is the user's response to the captcha challenge.
	// CaptchaCode string `json:"captchaCode" dc:"验证码 {\"x\":123,\"y\":0}"`
}

// LoginRes defines the response structure after successful login.
type LoginRes struct {
	// Token is the authentication token generated for the session.
	Token string `json:"token" dc:"token"`
	// ExpireAt is the timestamp when the token expires.
	ExpireAt int64 `json:"expireAt" dc:"token过期时间"`
}

// --- 获取图形验证码 ---

// // GetCaptchaReq defines the request structure for getting a captcha challenge.
// type GetCaptchaReq struct {
// 	g.Meta `path:"/captcha" method:"get" tags:"SystemAuth" summary:"获取图形验证码 react sdk https://github.com/wenlng/go-captcha-example/tree/master/web/react. 官网 httjson://gocaptcha.wencodes.com/docs/slide-captcha/"`
// }

// // GetCaptchaRes defines the response structure containing the captcha data.
// type GetCaptchaRes struct {
// 	// Embeds the captcha data structure.
// 	*captcha.SlideData
// }

// --- 获取管理员信息 ---

// GetAdminInfoReq defines the request structure for getting admin information.
type GetAdminInfoReq struct {
	g.Meta `path:"/admin-info" method:"get" tags:"SystemAuth" summary:"获取管理员信息"`
}

// GetAdminInfoRes defines the response structure containing the admin information.
type GetAdminInfoRes struct {
	Id string `json:"id"                              dc:"管理员ID"` // 管理员ID
	// RoleId   int64  `json:"roleId"                          dc:"角色ID"`    // 角色ID
	RealName string `json:"realName"                        dc:"真实姓名"` // 真实姓名
	Username string `json:"username"                        dc:"帐号"`   // 帐号
	Avatar   string `json:"avatar"                          dc:"头像"`   // 头像
	Email    string `json:"email"                           dc:"邮箱"`   // 邮箱
	// Mobile   string `json:"mobile"                          dc:"手机号码"`    // 手机号码
	// Pid      int64  `json:"pid"                             dc:"上级管理员ID"` // 上级管理员ID
	// Level        int         `json:"level"                           dc:"关系树等级"`   // 关系树等级
	// Tree         string      `json:"tree"                            dc:"关系树"`     // 关系树
	// InviteCode   string      `json:"inviteCode"                      dc:"邀请码"`    // 邀请码
	// LastActiveAt *gtime.Time `json:"lastActiveAt"                    dc:"最后活跃时间"` // 最后活跃时间
	// Remark       string      `json:"remark"                          dc:"备注"`     // 备注
	// Status       int         `json:"status"                          dc:"状态"`     // 状态
	// CreatedAt    *gtime.Time `json:"createdAt"                       dc:"创建时间"`   // 创建时间
	// UpdatedAt    *gtime.Time `json:"updatedAt"                       dc:"修改时间"`   // 修改时间

	// 新增字段
	// RoleName  *string   `json:"roleName,omitempty"              dc:"角色名称"` // 角色名称
	// PostNames *[]string `json:"postNames,omitempty"             dc:"岗位名称"` // 岗位名称列表
	// Permissions *[]string `json:"permissions,omitempty"           dc:"权限列表"` // 权限列表 todo 下个月实现
	Menus []*MenuTreeNode `json:"menus"` // 顶层菜单项列表
}

// --- 修改个人信息 ---

// UpdateAdminInfoReq defines the request structure for updating admin information.
type UpdateAdminInfoReq struct {
	g.Meta   `path:"/admin-info" method:"put" tags:"SystemAuth" summary:"修改个人信息"`
	RealName string `json:"realName" v:"length:0,50" dc:"真实姓名"`
	Email    string `json:"email" v:"email" dc:"邮箱"`
	Mobile   string `json:"mobile" v:"integer" dc:"手机号码"`
	Avatar   string `json:"avatar" v:"url" dc:"头像"`
	Password string `json:"password" v:"length:6,32" dc:"密码"`
}

// UpdateAdminInfoRes defines the response structure after updating admin information.
type UpdateAdminInfoRes struct {
	// Typically empty on success.
}

type CasdoorSigninReq struct {
	g.Meta `path:"/auth/casdoor/signin" method:"post" tags:"SystemAuth" summary:"Casdoor 单点登录"`
	Code   string `json:"code" v:"required#code不能为空"`
	State  string `json:"state" v:"required#state不能为空"`
}

type CasdoorSigninRes struct {
	oauth2.Token `json:"accessToken"`
}

type GetCasdoorUserInfoReq struct {
	g.Meta `path:"/auth/casdoor/claims" method:"get" tags:"SystemAuth" summary:"Casdoor 用户信息"`
}

type GetCasdoorUserInfoRes struct {
	casdoorsdk.Claims `json:"claims"`
}
