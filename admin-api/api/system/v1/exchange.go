package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// 兑换产品管理相关请求定义

// ExchangeProductListReq 兑换产品列表请求
type ExchangeProductListReq struct {
	g.<PERSON>a     `path:"/exchange/products" method:"get" tags:"ExchangeProduct" summary:"获取兑换产品列表"`
	BaseToken  string `json:"baseToken" dc:"基础代币ID"`
	QuoteToken string `json:"quoteToken" dc:"计价代币ID"`
	Symbol     string `json:"symbol" dc:"交易对符号"`
	IsActive   *int   `json:"isActive" dc:"是否激活"`
	Status     *int   `json:"status" dc:"状态"`
	Page       int    `json:"page" d:"1" dc:"页码"`
	PageSize   int    `json:"pageSize" d:"20" dc:"每页数量"`
}

// ExchangeProductListRes 兑换产品列表响应
type ExchangeProductListRes struct {
	List     []*ExchangeProductItem `json:"list" dc:"产品列表"`
	Total    int                    `json:"total" dc:"总数"`
	Page     int                    `json:"page" dc:"当前页"`
	PageSize int                    `json:"pageSize" dc:"每页数量"`
}

// ExchangeProductItem 兑换产品项
type ExchangeProductItem struct {
	ProductId            uint            `json:"productId" dc:"产品ID"`
	BaseToken            string          `json:"baseToken" dc:"基础代币"`
	QuoteToken           string          `json:"quoteToken" dc:"计价代币"`
	Symbol               string          `json:"symbol" dc:"交易对符号"`
	ProductType          string          `json:"productType" dc:"产品类型"`
	IsActive             int             `json:"isActive" dc:"是否激活"`
	AllowBuy             int             `json:"allowBuy" dc:"允许买入"`
	AllowSell            int             `json:"allowSell" dc:"允许卖出"`
	MinBaseAmountPerTx   decimal.Decimal `json:"minBaseAmountPerTx" dc:"单笔最小基础代币数量"`
	MaxBaseAmountPerTx   decimal.Decimal `json:"maxBaseAmountPerTx" dc:"单笔最大基础代币数量"`
	DailyBaseVolumeLimit decimal.Decimal `json:"dailyBaseVolumeLimit" dc:"每日基础代币限额"`
	TotalBaseVolumeLimit decimal.Decimal `json:"totalBaseVolumeLimit" dc:"累计基础代币限额"`
	PriceSource          string          `json:"priceSource" dc:"价格源"`
	SpreadRate           decimal.Decimal `json:"spreadRate" dc:"价差率"`
	// FeeRate 和 FeeChargedIn 字段在数据库中不存在，已移除
	// 使用 FeeStrategy, OutputFeeRate, MinOutputFeeAmount 代替
	FeeStrategy        string          `json:"feeStrategy" dc:"手续费策略"`
	OutputFeeRate      decimal.Decimal `json:"outputFeeRate" dc:"输出代币手续费率"`
	MinOutputFeeAmount decimal.Decimal `json:"minOutputFeeAmount" dc:"最小手续费金额（以输出代币计价）"`
	Status             int             `json:"status" dc:"状态"`
	DisplayOrder       int             `json:"displayOrder" dc:"显示顺序"`
	Description        string          `json:"description" dc:"描述"`
	CreatedAt          string          `json:"createdAt" dc:"创建时间"`
	UpdatedAt          string          `json:"updatedAt" dc:"更新时间"`
}

// ExchangeProductDetailReq 获取兑换产品详情请求
type ExchangeProductDetailReq struct {
	g.Meta    `path:"/exchange/products/{productId}" method:"get" tags:"ExchangeProduct" summary:"获取兑换产品详情"`
	ProductId uint `json:"productId" in:"path" dc:"产品ID"`
}

// ExchangeProductDetailRes 获取兑换产品详情响应
type ExchangeProductDetailRes struct {
	*ExchangeProductDetail
}

// ExchangeProductDetail 兑换产品详情
type ExchangeProductDetail struct {
	ProductId              uint            `json:"productId" dc:"产品ID"`
	BaseToken              string          `json:"baseToken" dc:"基础代币"`
	QuoteToken             string          `json:"quoteToken" dc:"计价代币"`
	Symbol                 string          `json:"symbol" dc:"交易对符号"`
	ProductType            string          `json:"productType" dc:"产品类型"`
	IsActive               int             `json:"isActive" dc:"是否激活"`
	AllowBuy               int             `json:"allowBuy" dc:"允许买入"`
	AllowSell              int             `json:"allowSell" dc:"允许卖出"`
	MaintenanceMessage     string          `json:"maintenanceMessage" dc:"维护信息"`
	MinBaseAmountPerTx     decimal.Decimal `json:"minBaseAmountPerTx" dc:"单笔最小基础代币数量"`
	MaxBaseAmountPerTx     decimal.Decimal `json:"maxBaseAmountPerTx" dc:"单笔最大基础代币数量"`
	DailyBaseVolumeLimit   decimal.Decimal `json:"dailyBaseVolumeLimit" dc:"每日基础代币限额"`
	TotalBaseVolumeLimit   decimal.Decimal `json:"totalBaseVolumeLimit" dc:"累计基础代币限额"`
	PriceSource            string          `json:"priceSource" dc:"价格源"`
	AllowedSlippagePercent decimal.Decimal `json:"allowedSlippagePercent" dc:"允许滑点百分比"`
	SpreadRate             decimal.Decimal `json:"spreadRate" dc:"价差率"`
	RateRefreshIntervalSec uint            `json:"rateRefreshIntervalSec" dc:"价格刷新间隔(秒)"`
	// FeeRate, FeeChargedIn, MinFeeAmountBaseEquivalent 字段在数据库中不存在，已移除
	// 使用 FeeStrategy, OutputFeeRate, MinOutputFeeAmount 代替
	FeeStrategy        string          `json:"feeStrategy" dc:"手续费策略"`
	OutputFeeRate      decimal.Decimal `json:"outputFeeRate" dc:"输出代币手续费率"`
	MinOutputFeeAmount decimal.Decimal `json:"minOutputFeeAmount" dc:"最小手续费金额（以输出代币计价）"`
	DisplayOrder       int             `json:"displayOrder" dc:"显示顺序"`
	Description        string          `json:"description" dc:"描述"`
	Status             int             `json:"status" dc:"状态"`
	CreatedAt          string          `json:"createdAt" dc:"创建时间"`
	UpdatedAt          string          `json:"updatedAt" dc:"更新时间"`
}

// ExchangeProductCreateReq 创建兑换产品请求
type ExchangeProductCreateReq struct {
	g.Meta                 `path:"/exchange/products" method:"post" tags:"ExchangeProduct" summary:"创建兑换产品"`
	BaseToken              string          `json:"baseToken" v:"required" dc:"基础代币"`
	QuoteToken             string          `json:"quoteToken" v:"required" dc:"计价代币"`
	Symbol                 string          `json:"symbol" v:"required|length:1,50" dc:"交易对符号"`
	ProductType            string          `json:"productType" d:"swap" dc:"产品类型"`
	IsActive               int             `json:"isActive" d:"0" dc:"是否激活"`
	AllowBuy               int             `json:"allowBuy" d:"1" dc:"允许买入"`
	AllowSell              int             `json:"allowSell" d:"1" dc:"允许卖出"`
	MaintenanceMessage     string          `json:"maintenanceMessage" dc:"维护信息"`
	MinBaseAmountPerTx     decimal.Decimal `json:"minBaseAmountPerTx" v:"required" dc:"单笔最小基础代币数量"`
	MaxBaseAmountPerTx     decimal.Decimal `json:"maxBaseAmountPerTx" v:"required" dc:"单笔最大基础代币数量"`
	DailyBaseVolumeLimit   decimal.Decimal `json:"dailyBaseVolumeLimit" dc:"每日基础代币限额"`
	TotalBaseVolumeLimit   decimal.Decimal `json:"totalBaseVolumeLimit" dc:"累计基础代币限额"`
	PriceSource            string          `json:"priceSource" v:"required" dc:"价格源"`
	AllowedSlippagePercent decimal.Decimal `json:"allowedSlippagePercent" dc:"允许滑点百分比"`
	SpreadRate             decimal.Decimal `json:"spreadRate" d:"0.001" dc:"价差率"`
	RateRefreshIntervalSec uint            `json:"rateRefreshIntervalSec" dc:"价格刷新间隔(秒)"`
	// FeeRate, FeeChargedIn, MinFeeAmountBaseEquivalent 字段在数据库中不存在，已移除
	// 使用 FeeStrategy, OutputFeeRate, MinOutputFeeAmount 代替
	FeeStrategy        string          `json:"feeStrategy" d:"output_token_percentage" dc:"手续费策略"`
	OutputFeeRate      decimal.Decimal `json:"outputFeeRate" dc:"输出代币手续费率"`
	MinOutputFeeAmount decimal.Decimal `json:"minOutputFeeAmount" dc:"最小手续费金额（以输出代币计价）"`
	DisplayOrder       int             `json:"displayOrder" d:"0" dc:"显示顺序"`
	Description        string          `json:"description" dc:"描述"`
}

// ExchangeProductCreateRes 创建兑换产品响应
type ExchangeProductCreateRes struct {
	ProductId uint `json:"productId" dc:"产品ID"`
}

// ExchangeProductUpdateReq 更新兑换产品请求
type ExchangeProductUpdateReq struct {
	g.Meta                 `path:"/exchange/products/{productId}" method:"put" tags:"ExchangeProduct" summary:"更新兑换产品"`
	ProductId              uint            `json:"productId" in:"path" dc:"产品ID"`
	BaseToken              string          `json:"baseToken" dc:"基础代币"`
	QuoteToken             string          `json:"quoteToken" dc:"计价代币"`
	Symbol                 string          `json:"symbol" v:"length:1,50" dc:"交易对符号"`
	ProductType            string          `json:"productType" dc:"产品类型"`
	IsActive               *int            `json:"isActive" dc:"是否激活"`
	AllowBuy               *int            `json:"allowBuy" dc:"允许买入"`
	AllowSell              *int            `json:"allowSell" dc:"允许卖出"`
	MaintenanceMessage     string          `json:"maintenanceMessage" dc:"维护信息"`
	MinBaseAmountPerTx     decimal.Decimal `json:"minBaseAmountPerTx" dc:"单笔最小基础代币数量"`
	MaxBaseAmountPerTx     decimal.Decimal `json:"maxBaseAmountPerTx" dc:"单笔最大基础代币数量"`
	DailyBaseVolumeLimit   decimal.Decimal `json:"dailyBaseVolumeLimit" dc:"每日基础代币限额"`
	TotalBaseVolumeLimit   decimal.Decimal `json:"totalBaseVolumeLimit" dc:"累计基础代币限额"`
	PriceSource            string          `json:"priceSource" dc:"价格源"`
	AllowedSlippagePercent decimal.Decimal `json:"allowedSlippagePercent" dc:"允许滑点百分比"`
	SpreadRate             decimal.Decimal `json:"spreadRate" dc:"价差率"`
	RateRefreshIntervalSec *uint           `json:"rateRefreshIntervalSec" dc:"价格刷新间隔(秒)"`
	// FeeRate, FeeChargedIn, MinFeeAmountBaseEquivalent 字段在数据库中不存在，已移除
	// 使用 FeeStrategy, OutputFeeRate, MinOutputFeeAmount 代替
	FeeStrategy        string          `json:"feeStrategy" dc:"手续费策略"`
	OutputFeeRate      decimal.Decimal `json:"outputFeeRate" dc:"输出代币手续费率"`
	MinOutputFeeAmount decimal.Decimal `json:"minOutputFeeAmount" dc:"最小手续费金额（以输出代币计价）"`
	DisplayOrder       *int            `json:"displayOrder" dc:"显示顺序"`
	Description        string          `json:"description" dc:"描述"`
}

// ExchangeProductUpdateRes 更新兑换产品响应
type ExchangeProductUpdateRes struct {
}

// ExchangeProductDeleteReq 删除兑换产品请求
type ExchangeProductDeleteReq struct {
	g.Meta    `path:"/exchange/products/{productId}" method:"delete" tags:"ExchangeProduct" summary:"删除兑换产品"`
	ProductId uint `json:"productId" in:"path" dc:"产品ID"`
}

// ExchangeProductDeleteRes 删除兑换产品响应
type ExchangeProductDeleteRes struct {
}

// ExchangeProductStatusReq 更新兑换产品状态请求
type ExchangeProductStatusReq struct {
	g.Meta    `path:"/exchange/products/{productId}/status" method:"post" tags:"ExchangeProduct" summary:"更新兑换产品状态"`
	ProductId uint `json:"productId" in:"path" dc:"产品ID"`
	IsActive  int  `json:"isActive" v:"required|in:0,1" dc:"是否激活"`
	Status    int  `json:"status" v:"required|in:0,1" dc:"状态"`
}

// ExchangeProductStatusRes 更新兑换产品状态响应
type ExchangeProductStatusRes struct {
}

// ExchangeProductVolumeReq 获取兑换产品交易量请求
type ExchangeProductVolumeReq struct {
	g.Meta    `path:"/exchange/products/{productId}/volume" method:"get" tags:"ExchangeProduct" summary:"获取兑换产品交易量"`
	ProductId uint `json:"productId" in:"path" dc:"产品ID"`
}

// ExchangeProductVolumeRes 获取兑换产品交易量响应
type ExchangeProductVolumeRes struct {
	DailyVolume    decimal.Decimal `json:"dailyVolume" dc:"今日交易量"`
	TotalVolume    decimal.Decimal `json:"totalVolume" dc:"累计交易量"`
	DailyLimit     decimal.Decimal `json:"dailyLimit" dc:"每日限额"`
	TotalLimit     decimal.Decimal `json:"totalLimit" dc:"累计限额"`
	DailyRemaining decimal.Decimal `json:"dailyRemaining" dc:"今日剩余"`
	TotalRemaining decimal.Decimal `json:"totalRemaining" dc:"累计剩余"`
}
