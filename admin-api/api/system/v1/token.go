package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// --- GetTokenList ---

// GetTokenListReq defines the request structure for querying the token list.
type GetTokenListReq struct {
	g.Meta `path:"/tokens" method:"get" tags:"Token Management" summary:"查询代币列表"`
	common.PageRequest
	Network        *string `json:"network" dc:"网络/链"`
	Symbol         *string `json:"symbol" dc:"代币符号"`
	IsActive       *int    `json:"isActive" dc:"是否激活 (1:是, 0:否)"`
	DateRange      string  `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	OrderBy        *string `json:"orderBy" d:"order" dc:"排序字段 (默认 order)"`
	OrderDirection *string `json:"orderDirection" d:"asc" dc:"排序方向 (asc/desc, 默认 asc)" v:"in:asc,desc"`
}

// GetTokenListRes defines the response structure for the token list query.
// Note: Directly returning the entity slice might lead to tight coupling. Consider a DTO.
type GetTokenListRes struct {
	// Page contains the pagination information.
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of tokens.
	Data []*entity.Tokens `json:"data" dc:"代币列表"`
}

// --- GetTokenDetail ---

// GetTokenDetailReq defines the request structure for getting token details.
type GetTokenDetailReq struct {
	g.Meta  `path:"/tokens/{token_id}" method:"get" tags:"Token Management" summary:"获取代币详情"`
	TokenId uint `json:"token_id" v:"required#Token ID不能为空" dc:"代币ID"` // 从路径获取，需要定义并校验
}

// GetTokenDetailRes defines the response structure for getting token details.
// Note: Directly returning the entity might lead to tight coupling. Consider a DTO.
type GetTokenDetailRes struct {
	*entity.Tokens // Embeds the token entity directly.
}

// --- CreateToken ---

// CreateTokenReq defines the request structure for creating a new token.
type CreateTokenReq struct {
	g.Meta `path:"/tokens" method:"post" tags:"Token Management" summary:"创建代币"`
	// Network specifies the blockchain network (e.g., Ethereum, Tron).
	Network string `json:"network"  v:"required#网络/链不能为空" dc:"网络/链"`
	// Symbol is the token symbol (e.g., USDT, ETH).
	Symbol string `json:"symbol" v:"required#代币符号不能为空" dc:"代币符号"`
	// Name is the full name of the token (e.g., Tether USD).
	Name string `json:"name" v:"required#代币名称不能为空" dc:"代币名称"`
	// Decimals indicates the number of decimal places for the token.
	Decimals int `json:"decimals" v:"required|min:0#小数位数不能为空且必须为非负整数" dc:"小数位数"`
	// ContractAddress is the token's contract address (optional).
	ContractAddress *string `json:"contract_address" dc:"合约地址 (可选)"`
	// TokenStandard specifies the token standard (e.g., ERC20, TRC20, optional).
	TokenStandard *string `json:"token_standard" dc:"代币标准 (如 ERC20, TRC20, BEP20, 可选)"`
	// LogoUrl is the URL for the token's logo (optional, must be a valid URL).
	LogoUrl *string `json:"logo_url" v:"url" dc:"Logo URL (可选, 需为有效URL)"`
	// ProjectWebsite is the URL for the token's project website (optional, must be a valid URL).
	ProjectWebsite *string `json:"project_website" v:"url" dc:"项目官网 (可选, 需为有效URL)"`
	// Description provides a description of the token (optional).
	Description *string `json:"description" dc:"描述 (可选)"`
	// Order determines the display order (optional, default 0, non-negative).
	Order *int `json:"order" v:"min:0" d:"0" dc:"排序 (可选, 默认0, 需为非负整数)"`
	// IsActive indicates if the token is active (0: No, 1: Yes, default 1).
	IsActive *int `json:"isActive" v:"in:0,1" d:"1" dc:"是否激活 (0:否, 1:是, 默认1)"`

	// --- Deposit Related ---
	// MinDepositAmount is the minimum deposit amount allowed (-1 for no limit, default -1).
	MinDepositAmount *string `json:"min_deposit_amount" v:"regex:^(\\-1|\\d+(\\.\\d+)?)$#最小充值金额必须是有效数字或-1" d:"-1" dc:"最小充值金额 (-1表示无限制, 默认-1)"`
	// MaxDepositAmount is the maximum deposit amount allowed (-1 for no limit, default -1).
	MaxDepositAmount *string `json:"max_deposit_amount" v:"regex:^(\\-1|\\d+(\\.\\d+)?)$#最大充值金额必须是有效数字或-1" d:"-1" dc:"最大充值金额 (-1表示无限制, 默认-1)"`

	// --- Withdrawal Related ---
	// MinWithdrawalAmount is the minimum withdrawal amount allowed (-1 for no limit, default -1).
	MinWithdrawalAmount *string `json:"min_withdrawal_amount" v:"regex:^(\\-1|\\d+(\\.\\d+)?)$#最小提现金额必须是有效数字或-1" d:"-1" dc:"最小提现金额 (-1表示无限制, 默认-1)"`
	// MaxWithdrawalAmount is the maximum withdrawal amount allowed (-1 for no limit, default -1).
	MaxWithdrawalAmount *string `json:"max_withdrawal_amount" v:"regex:^(\\-1|\\d+(\\.\\d+)?)$#最大提现金额必须是有效数字或-1" d:"-1" dc:"最大提现金额 (-1表示无限制, 默认-1)"`
	// WithdrawalFeeType specifies the type of withdrawal fee (fixed or percent, default fixed).
	WithdrawalFeeType *string `json:"withdrawal_fee_type" v:"in:fixed,percent" d:"fixed" dc:"提现手续费类型 (fixed:固定, percent:百分比, 默认fixed)"`
	// WithdrawalFeeAmount is the withdrawal fee amount or percentage (default 0).
	WithdrawalFeeAmount *string `json:"withdrawal_fee_amount" v:"regex:^\\d+(\\.\\d+)?$#提现手续费金额必须为非负数字" d:"0" dc:"提现手续费金额/百分比 (默认0)"`
}

// CreateTokenRes defines the response structure after creating a new token.
type CreateTokenRes struct {
	// TokenId is the ID of the newly created token.
	TokenId uint `json:"token_id" dc:"新创建的代币ID"`
}

// --- UpdateToken ---

// UpdateTokenReq defines the request structure for updating an existing token.
// Note: Using PUT but fields are pointers, resembling PATCH behavior. Consider using PATCH method.
type UpdateTokenReq struct {
	g.Meta          `path:"/tokens/{token_id}" method:"put" tags:"Token Management" summary:"更新代币"`
	TokenId         uint    `json:"token_id" v:"required#Token ID不能为空" dc:"代币ID"` // 从路径获取，需要定义并校验
	Name            *string `json:"name" dc:"代币名称"`
	ContractAddress *string `json:"contract_address" dc:"合约地址"`
	TokenStandard   *string `json:"token_standard" dc:"代币标准"`
	LogoUrl         *string `json:"logo_url" v:"url" dc:"Logo URL (需为有效URL)"`
	ProjectWebsite  *string `json:"project_website" v:"url" dc:"项目官网 (需为有效URL)"`
	Description     *string `json:"description" dc:"描述"`
	Order           *int    `json:"order" v:"min:0" dc:"排序 (需为非负整数)"`
	IsActive        *int    `json:"isActive" v:"in:0,1" dc:"是否激活 (0:否, 1:是)"`

	// allowDeposit
	AllowDeposit *int `json:"allowDeposit" v:"in:0,1" dc:"是否允许充值 (0:否, 1:是)"`
	// allowWithdraw
	AllowWithdraw *int `json:"allowWithdraw" v:"in:0,1" dc:"是否允许提现 (0:否, 1:是)"`
	// allowTransfer
	AllowTransfer *int `json:"allowTransfer" v:"in:0,1" dc:"是否允许内部转账 (0:否, 1:是)"`
	// allowReceive
	AllowReceive *int `json:"allowReceive" v:"in:0,1" dc:"是否允许内部收款 (0:否, 1:是)"`
	// allowRedPacket
	AllowRedPacket *int `json:"allowRedPacket" v:"in:0,1" dc:"是否允许发红包 (0:否, 1:是)"`
	// allowTrading
	AllowTrading *int `json:"allowTrading" v:"in:0,1" dc:"是否允许在交易对中使用 (0:否, 1:是)"`

	// 充值相关
	MinDepositAmount *string `json:"minDepositAmount" v:"regex:^(\\-1|\\d+(\\.\\d+)?)$#最小充值金额必须是有效数字或-1" dc:"最小充值金额 (-1表示无限制)"`
	MaxDepositAmount *string `json:"maxDepositAmount" v:"regex:^(\\-1|\\d+(\\.\\d+)?)$#最大充值金额必须是有效数字或-1" dc:"最大充值金额 (-1表示无限制)"`
	// DepositConfirmationNum removed as requested.

	// 提现相关
	MinWithdrawalAmount *string `json:"minWithdrawalAmount" v:"regex:^(\\-1|\\d+(\\.\\d+)?)$#最小提现金额必须是有效数字或-1" dc:"最小提现金额 (-1表示无限制)"`
	MaxWithdrawalAmount *string `json:"maxWithdrawalAmount" v:"regex:^(\\-1|\\d+(\\.\\d+)?)$#最大提现金额必须是有效数字或-1" dc:"最大提现金额 (-1表示无限制)"`
	WithdrawalFeeType   *string `json:"withdrawalFeeType" v:"in:fixed,percent" dc:"提现手续费类型 (fixed:固定, percent:百分比)"`
	WithdrawalFeeAmount *string `json:"withdrawalFeeAmount" v:"regex:^\\d+(\\.\\d+)?$#提现手续费金额必须为非负数字" dc:"提现手续费金额/百分比"`
	// MinWithdrawalFee and MaxWithdrawalFee removed as requested.
}

// UpdateTokenRes defines the response structure after updating a token.
type UpdateTokenRes struct {
	// Typically empty on success.
}

// --- DeleteToken ---

// DeleteTokenReq defines the request structure for deleting a token.
type DeleteTokenReq struct {
	g.Meta  `path:"/tokens/{token_id}" method:"delete" tags:"Token Management" summary:"删除代币"`
	TokenId uint `json:"token_id" v:"required#Token ID不能为空" dc:"代币ID"` // 从路径获取，需要定义并校验
}

// DeleteTokenRes defines the response structure after deleting a token.
type DeleteTokenRes struct {
	// Typically empty on success.
}
