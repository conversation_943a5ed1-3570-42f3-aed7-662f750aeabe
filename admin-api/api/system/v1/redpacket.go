package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// --- 查询红包列表 ---

// RedPacketAdminInfoType defines the structure for red packet information in the admin list.
type RedPacketAdminInfoType struct {
	entity.RedPackets // Embeds the base red packet entity.
	// TokenLogo is the URL of the token's logo.
	TokenLogo string `json:"tokenLogo"`

	// 新增：三级代理信息
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称"`

	// 新增：主备份账户telegram信息
	TelegramId       string `json:"telegramId" dc:"主Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"主Telegram用户名"`
	FirstName        string `json:"firstName" dc:"主备份账户名字"`
}

// ListAdminRedPacketsReq defines the request structure for querying the red packet list (admin).
type ListAdminRedPacketsReq struct {
	g.Meta        `path:"/red-packets" method:"get" tags:"SystemRedPacket" summary:"获取红包列表"`
	Page          int   `json:"page" v:"required|min:1#页码不能为空|页码必须大于0"`         // 页码
	PageSize      int   `json:"pageSize" v:"required|min:1#每页数量不能为空|每页数量必须大于0"` // 每页数量
	CreatorUserId int64 `json:"creatorUserId"`                                  // 创建者ID
	TokenId       int   `json:"tokenId"`                                        // 代币ID

	//  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '红包状态: active-可领取, expired-已过期, empty-已领完, cancelled-已取消',

	Status string `json:"status" v:"in:active,expired,empty,cancelled" dc:"红包状态: active-可领取, expired-已过期, empty-已领完, cancelled-已取消"` // 状态
	//  `type` enum('random','fixed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '红包类型: random-随机金额, fixed-固定金额',
	Type           string `json:"type" v:"in:random,fixed" dc:"红包类型: random-随机金额, fixed-固定金额"` // 红包类型
	DateRange      string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`              // 时间范围
	Export         bool   `json:"export"`                                                      // 是否导出
	SenderAccount  string `json:"senderAccount"`                                               // 发送人账号
	SenderNickname string `json:"senderNickname"`                                              // 发送人昵称
	Symbol         string `json:"symbol"`                                                      // 代币符号
	Uuid           string `json:"uuid" dc:"红包UUID (模糊搜索)"`                                     // 红包UUID

	// 新增：三级代理模糊查询
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称 (模糊搜索)"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称 (模糊搜索)"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称 (模糊搜索)"`

	// 新增：telegram信息查询
	TelegramId       string `json:"telegramId" dc:"Telegram ID (模糊搜索)"`
	TelegramUsername string `json:"telegramUsername" dc:"Telegram用户名 (模糊搜索)"`
	FirstName        string `json:"firstName" dc:"真实姓名 (模糊搜索)"`
}

// ListAdminRedPacketsRes defines the response structure for the red packet list query (admin).
type ListAdminRedPacketsRes struct {
	// List contains the red packet data.
	List []*RedPacketAdminInfoType `json:"list"`
	// Page contains the pagination information.
	Page *common.PageResponse `json:"page"`
}

// --- 获取红包详情 ---

// GetAdminRedPacketDetailReq defines the request structure for getting red packet details (admin).
type GetAdminRedPacketDetailReq struct {
	g.Meta      `path:"/red-packets/{redPacketId}" method:"get" tags:"SystemRedPacket" summary:"获取红包详情"`
	RedPacketId int64 `json:"redPacketId" v:"required#红包ID不能为空" dc:"红包ID"`
}

// RedPacketClaimAdminInfoType defines the structure for red packet claim information in the admin view.
type RedPacketClaimAdminInfoType struct {
	entity.RedPacketClaims // Embeds the base red packet claim entity.
	// ClaimerUsername is the username of the user who claimed the red packet portion.
	ClaimerUsername string `json:"claimerUsername"`
	// RedPacketMemo is the memo associated with the original red packet.
	RedPacketMemo string `json:"redPacketMemo"`
	// TokenSymbol is the symbol of the token claimed.
	TokenSymbol string `json:"tokenSymbol"`
	// TokenId is the ID of the token claimed.
	TokenId int `json:"tokenId"`
	// TokenName is the name of the token claimed.
	TokenName string `json:"tokenName"`
	// RedPacketType is the type of the original red packet.
	RedPacketType string `json:"redPacketType"`
	// CreatedAt is the timestamp when the claim was made (overwrites embedded field for clarity).
	CreatedAt *gtime.Time `json:"createdAt"`

	// 新增：领取方三级代理信息
	FirstAgentName  string `json:"firstAgentName" dc:"领取方一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"领取方二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"领取方三级代理名称"`

	// 新增：领取方主备份账户telegram信息
	TelegramId       string `json:"telegramId" dc:"领取方Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"领取方Telegram用户名"`
	FirstName        string `json:"firstName" dc:"领取方真实姓名"`

	// 新增：发送方三级代理信息
	SenderFirstAgentName  string `json:"senderFirstAgentName" dc:"发送方一级代理名称"`
	SenderSecondAgentName string `json:"senderSecondAgentName" dc:"发送方二级代理名称"`
	SenderThirdAgentName  string `json:"senderThirdAgentName" dc:"发送方三级代理名称"`

	// 新增：发送方telegram信息
	SenderTelegramId       string `json:"senderTelegramId" dc:"发送方Telegram ID"`
	SenderTelegramUsername string `json:"senderTelegramUsername" dc:"发送方Telegram用户名"`
	SenderFirstName        string `json:"senderFirstName" dc:"发送方真实姓名"`

	// 新增：红包UUID
	RedPacketUuid string `json:"redPacketUuid" dc:"红包UUID"`
}

// GetAdminRedPacketDetailRes defines the response structure for getting red packet details (admin).
type GetAdminRedPacketDetailRes struct {
	// RedPacket contains the details of the red packet itself.
	RedPacket *RedPacketAdminInfoType `json:"redPacket"`
	// Claims lists the claim records associated with this red packet.
	Claims []*RedPacketClaimAdminInfoType `json:"claims,omitempty"`
}

// --- 取消红包 ---

// CancelRedPacketReq defines the request structure for cancelling a red packet.
type CancelRedPacketReq struct {
	g.Meta      `path:"/red-packets/{redPacketId}/cancel" method:"post" tags:"SystemRedPacket" summary:"取消红包"`
	RedPacketId int64 `json:"redPacketId" v:"required#红包ID不能为空" dc:"红包ID"`
}

// CancelRedPacketRes defines the response structure after cancelling a red packet.
type CancelRedPacketRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success"`
}

// --- 查询红包领取记录列表 ---

// ListAdminRedPacketClaimsReq defines the request structure for querying the red packet claim list (admin).
type ListAdminRedPacketClaimsReq struct {
	g.Meta           `path:"/red-packet-claims" method:"get" tags:"SystemRedPacket" summary:"获取红包领取记录列表"`
	Page             int     `json:"page" v:"required|min:1#页码不能为空|页码必须大于0"`                                                   // 页码
	PageSize         int     `json:"pageSize" v:"required|min:1#每页数量不能为空|每页数量必须大于0"`                                           // 每页数量
	RedPacketId      int64   `json:"redPacketId"`                                                                              // 红包ID
	RedPacketUuid    string  `json:"redPacketUuid" dc:"红包UUID (模糊搜索)"`                                                         // 红包UUID
	ClaimerUserId    int64   `json:"claimerUserId"`                                                                            // 领取者ID
	ClaimerUsername  string  `json:"claimerUsername"`                                                                          // 领取者用户名
	SenderUserId     int64   `json:"senderUserId"`                                                                             // 发送方用户ID
	SenderUsername   string  `json:"senderUsername"`                                                                           // 发送方用户名
	ReceiverUserId   int64   `json:"receiverUserId"`                                                                           // 接收方用户ID
	ReceiverUsername string  `json:"receiverUsername"`                                                                         // 接收方用户名
	Status           string  `json:"status" v:"in:pending,claimed,cancelled" dc:"状态: pending-待领取, claimed-已领取, cancelled-已取消"` // 状态
	Symbol           string  `json:"symbol"`                                                                                   // 代币符号
	RedPacketType    string  `json:"redPacketType" v:"in:random,fixed" dc:"红包类型: random-随机金额, fixed-固定金额"`                     // 红包类型
	MinAmount        float64 `json:"minAmount"`                                                                                // 最小金额
	MaxAmount        float64 `json:"maxAmount"`                                                                                // 最大金额
	DateRange        string  `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`                                           // 时间范围
	Export           bool    `json:"export"`                                                                                   // 是否导出

	// 新增：领取方三级代理模糊查询（与返回字段一致）
	FirstAgentName  string `json:"firstAgentName" dc:"领取方一级代理名称 (模糊搜索)"`
	SecondAgentName string `json:"secondAgentName" dc:"领取方二级代理名称 (模糊搜索)"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"领取方三级代理名称 (模糊搜索)"`

	// 新增：领取方telegram信息查询（与返回字段一致）
	TelegramId       string `json:"telegramId" dc:"领取方Telegram ID (模糊搜索)"`
	TelegramUsername string `json:"telegramUsername" dc:"领取方Telegram用户名 (模糊搜索)"`
	FirstName        string `json:"firstName" dc:"领取方真实姓名 (模糊搜索)"`

	// 新增：发送方三级代理模糊查询（与返回字段一致）
	SenderFirstAgentName  string `json:"senderFirstAgentName" dc:"发送方一级代理名称 (模糊搜索)"`
	SenderSecondAgentName string `json:"senderSecondAgentName" dc:"发送方二级代理名称 (模糊搜索)"`
	SenderThirdAgentName  string `json:"senderThirdAgentName" dc:"发送方三级代理名称 (模糊搜索)"`

	// 新增：发送方telegram信息查询（与返回字段一致）
	SenderTelegramId       string `json:"senderTelegramId" dc:"发送方Telegram ID (模糊搜索)"`
	SenderTelegramUsername string `json:"senderTelegramUsername" dc:"发送方Telegram用户名 (模糊搜索)"`
	SenderFirstName        string `json:"senderFirstName" dc:"发送方真实姓名 (模糊搜索)"`
}

// ListAdminRedPacketClaimsRes defines the response structure for the red packet claim list query (admin).
type ListAdminRedPacketClaimsRes struct {
	// List contains the claim records.
	List []*RedPacketClaimAdminInfoType `json:"list"`
	// Page contains the pagination information.
	Page *common.PageResponse `json:"page"`
}

// --- 审核红包封面图片 ---

// ReviewRedPacketImageReq defines the request structure for reviewing red packet cover images.
type ReviewRedPacketImageReq struct {
	g.Meta            `path:"/red-packet-images/{redPacketImagesId}/review" method:"post" tags:"SystemRedPacket" summary:"审核红包封面图片"`
	RedPacketImagesId int64  `json:"redPacketImagesId" v:"required#红包图片ID不能为空" dc:"红包图片ID"`
	Status            string `json:"status" v:"required|in:success,fail#状态不能为空|状态只能是success或fail" dc:"审核状态: success-通过, fail-拒绝"`
	RefuseReasonZh    string `json:"refuseReasonZh" v:"required-if:status,fail#拒绝时必须填写中文原因" dc:"拒绝原因(中文)"`
	RefuseReasonEn    string `json:"refuseReasonEn" v:"required-if:status,fail#拒绝时必须填写英文原因" dc:"拒绝原因(英文)"`
}

// ReviewRedPacketImageRes defines the response structure for reviewing red packet cover images.
type ReviewRedPacketImageRes struct {
	Success bool `json:"success" dc:"操作是否成功"`
}

// --- 查询红包封面图片列表 ---

// ListRedPacketImagesReq defines the request structure for querying red packet cover images.
type ListRedPacketImagesReq struct {
	g.Meta    `path:"/red-packet-images" method:"get" tags:"SystemRedPacket" summary:"获取红包封面图片列表"`
	Page      int    `json:"page" v:"required|min:1#页码不能为空|页码必须大于0"`         // 页码
	PageSize  int    `json:"pageSize" v:"required|min:1#每页数量不能为空|每页数量必须大于0"` // 每页数量
	Status    string `json:"status" v:"in:pending_review,success,fail" dc:"状态: pending_review-待审核, success-通过, fail-拒绝"`
	UserId    int64  `json:"userId" dc:"用户ID"`
	DateRange string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	Export    bool   `json:"export" dc:"是否导出"` // 是否导出

	// 新增：三级代理模糊查询
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称 (模糊搜索)"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称 (模糊搜索)"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称 (模糊搜索)"`

	// 新增：telegram信息查询
	TelegramId       string `json:"telegramId" dc:"Telegram ID (模糊搜索)"`
	TelegramUsername string `json:"telegramUsername" dc:"Telegram用户名 (模糊搜索)"`
	FirstName        string `json:"firstName" dc:"真实姓名 (模糊搜索)"`
}

// RedPacketImageInfoType defines the structure for red packet image information.
type RedPacketImageInfoType struct {
	entity.RedPacketImages        // Embeds the base red packet images entity.
	Username               string `json:"username" dc:"用户名"`

	// 新增：三级代理信息
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称"`

	// 新增：主备份账户telegram信息
	TelegramId       string `json:"telegramId" dc:"主Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"主Telegram用户名"`
	FirstName        string `json:"firstName" dc:"主备份账户名字"`
}

// ListRedPacketImagesRes defines the response structure for red packet cover images list.
type ListRedPacketImagesRes struct {
	List []*RedPacketImageInfoType `json:"list"`
	Page *common.PageResponse      `json:"page"`
}
