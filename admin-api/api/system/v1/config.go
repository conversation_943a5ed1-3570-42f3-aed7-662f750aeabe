package v1

import (
	"admin-api/api/common" // Import common types
	"encoding/json"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

// --- Value Type Definition ---

// ValueType 定义配置值的类型
type ValueType string

const (
	Text        ValueType = "text"
	Textarea    ValueType = "textarea"
	Number      ValueType = "number"
	NumberRange ValueType = "number_range" // NEW: Number range type for min-max values
	Boolean     ValueType = "boolean"
	JSON        ValueType = "json"
	Password    ValueType = "password"
	Map         ValueType = "map"
	Table       ValueType = "table" // NEW: Table type for dynamic table structures
)

// --- Config Category DTOs ---

// ConfigCategoryInfo 配置分类信息
type ConfigCategoryInfo struct {
	Id          int64     `json:"id"`          // 分类唯一标识符 (int64)
	Name        string    `json:"name"`        // 分类显示名称
	CategoryKey string    `json:"categoryKey"` // 分类键 (唯一, 创建后不可修改)
	SortOrder   int       `json:"sortOrder"`   // 排序顺序
	CreatedAt   time.Time `json:"createdAt"`   // 创建时间
	UpdatedAt   time.Time `json:"updatedAt"`   // 更新时间
}

// ListConfigCategoryReq 获取配置分类列表请求
type ListConfigCategoryReq struct {
	g.Meta      `path:"/config_categories" method:"get" tags:"SystemConfig" summary:"获取配置分类列表"`
	Name        *string `json:"name,omitempty"`        // 按名称筛选 (可选, 模糊匹配)
	CategoryKey *string `json:"categoryKey,omitempty"` // 按 Key 筛选 (可选, 精确匹配)
	// Status      *int    `json:"status,omitempty"`      // 按状态筛选 (可选, 0:禁用, 1:启用) - 暂不添加，根据后续需求
}

// ListConfigCategoryRes 获取配置分类列表响应
type ListConfigCategoryRes struct {
	Data []*ConfigCategoryInfo `json:"data"` // 分类列表
}

// CreateConfigCategoryReq 创建配置分类请求
type CreateConfigCategoryReq struct {
	g.Meta      `path:"/config_categories" method:"post" tags:"SystemConfig" summary:"创建配置分类"`
	Name        string `json:"name" v:"required#分类名称不能为空"`                                     // 分类名称
	CategoryKey string `json:"categoryKey" v:"required|length:1,100#分类Key不能为空|分类Key长度不能超过100"` // 分类 Key (唯一, 创建后不可修改)
	SortOrder   int    `json:"sortOrder"`                                                      // 排序 (默认为0)
}

// CreateConfigCategoryRes 创建配置分类响应
type CreateConfigCategoryRes struct {
	Id int64 `json:"id"` // 新创建的分类 ID
}

// UpdateConfigCategoryReq 更新配置分类请求
type UpdateConfigCategoryReq struct {
	g.Meta    `path:"/config_categories/{id}" method:"put" tags:"SystemConfig" summary:"更新配置分类"`
	Id        int64  `json:"id" v:"required|min:1#分类ID不能为空|分类ID必须大于0" in:"path"` // 要更新的分类 ID
	Name      string `json:"name" v:"required#分类名称不能为空"`                         // 分类名称
	SortOrder int    `json:"sortOrder"`                                          // 排序
}

// UpdateConfigCategoryRes 更新配置分类响应
type UpdateConfigCategoryRes struct {
	Id int64 `json:"id"` // 更新后的分类 ID (与请求中的 ID 相同)
}

// DeleteConfigCategoryReq 删除配置分类请求
type DeleteConfigCategoryReq struct {
	g.Meta `path:"/config_categories" method:"delete" tags:"SystemConfig" summary:"删除配置分类"`
	Ids    []int64 `json:"ids" v:"required|min-length:1#请选择要删除的分类ID"` // 要删除的分类 ID 列表
}

// DeleteConfigCategoryRes 删除配置分类响应 (成功时 data 为空或包含 success:true)
type DeleteConfigCategoryRes struct {
	// No specific fields needed, success indicated by code 0 in wrapper
}

// --- Config Item DTOs ---

// ConfigItemInfo 配置项信息
type ConfigItemInfo struct {
	Id          int64     `json:"id"`                    // 配置项唯一标识符 (int64)
	CategoryId  int64     `json:"categoryId"`            // 所属分类 ID (int64)
	Key         string    `json:"key"`                   // 配置键 (全局唯一, 包含分类前缀)
	Value       string    `json:"value"`                 // 配置值 (以字符串存储)
	ValueType   ValueType `json:"valueType"`             // 配置值类型
	Description string    `json:"description,omitempty"` // 描述 (可选)
	CreatedAt   time.Time `json:"createdAt"`             // 创建时间
	UpdatedAt   time.Time `json:"updatedAt"`             // 更新时间
}

// MarshalJSON 自定义 ConfigItemInfo 的 JSON 序列化方法
// 用于在 ValueType 为 Password 时脱敏 Value 字段
func (c *ConfigItemInfo) MarshalJSON() ([]byte, error) {
	// 使用类型别名以避免 MarshalJSON 无限递归
	type Alias ConfigItemInfo

	// 辅助结构体，用于自定义序列化特定字段
	// 这里我们显式定义 Value 字段，以便在需要时覆盖它
	aux := struct {
		*Alias
		Value string `json:"value"`
	}{
		Alias: (*Alias)(c),
		Value: c.Value,
	}

	if c.ValueType == Password {
		aux.Value = "******"
	}

	return json.Marshal(aux)
}

// ListConfigItemReq 获取配置项列表请求
type ListConfigItemReq struct {
	g.Meta             `path:"/config_items" method:"get" tags:"SystemConfig" summary:"获取配置项列表"`
	common.PageRequest         // 嵌入分页请求
	CategoryId         int64   `json:"categoryId" v:"required|min:1#分类ID不能为空|分类ID必须大于0" in:"query"` // 所属分类 ID (必填)
	Key                *string `json:"key,omitempty"`                                               // 按 Key 筛选 (可选, 模糊匹配)
	DateRange          string  `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	// Add other filters if needed (e.g., ValueType)
}

// ListConfigItemRes 获取配置项列表响应 (支持分页)
type ListConfigItemRes struct {
	common.PageResponse                   // 嵌入分页响应
	Data                []*ConfigItemInfo `json:"data"` // 配置项列表
}

// CreateConfigItemReq 创建配置项请求
type CreateConfigItemReq struct {
	g.Meta      `path:"/config_items" method:"post" tags:"SystemConfig" summary:"创建配置项"`
	CategoryId  int64     `json:"categoryId" v:"required|min:1#分类ID不能为空|分类ID必须大于0"` // 所属分类 ID
	Key         string    `json:"key" v:"required#配置Key不能为空"`                       // 配置 Key (需符合 "categoryKey.itemKey" 格式, 后端校验)
	Value       any       `json:"value"`                                            // 配置值 (根据 valueType 变化)
	ValueType   ValueType `json:"valueType" v:"required#值类型不能为空"`                   // 值类型
	Description string    `json:"description,omitempty"`                            // 描述 (可选)
}

// CreateConfigItemRes 创建配置项响应
type CreateConfigItemRes struct {
	Id int64 `json:"id"` // 新创建的配置项 ID
}

// UpdateConfigItemReq 更新配置项请求
type UpdateConfigItemReq struct {
	g.Meta      `path:"/config_items/{id}" method:"put" tags:"SystemConfig" summary:"更新配置项"`
	Id          int64     `json:"id" v:"required|min:1#配置项ID不能为空|配置项ID必须大于0" in:"path"` // 要更新的配置项 ID
	Value       *string   `json:"value,omitempty"`                                      // 配置值 (为nil时不更新，否则视为意图修改，包括设置为空字符串)
	ValueType   ValueType `json:"valueType" v:"required#值类型不能为空"`                       // 值类型
	Description *string   `json:"description,omitempty"`                                // 描述 (可选, 为nil时不更新)
}

// UpdateConfigItemRes 更新配置项响应
type UpdateConfigItemRes struct {
	Id int64 `json:"id"` // 更新后的配置项 ID (与请求中的 ID 相同)
}

// DeleteConfigItemReq 删除配置项请求
type DeleteConfigItemReq struct {
	g.Meta `path:"/config_items" method:"delete" tags:"SystemConfig" summary:"删除配置项"`
	Ids    []int64 `json:"ids" v:"required|min-length:1#请选择要删除的配置项ID"` // 要删除的配置项 ID 列表
}

// DeleteConfigItemRes 删除配置项响应 (成功时 data 为空或包含 success:true)
type DeleteConfigItemRes struct {
	// No specific fields needed, success indicated by code 0 in wrapper
}
