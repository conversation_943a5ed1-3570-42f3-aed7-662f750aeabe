package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
)

// --- 获取角色列表 ---

// GetRoleListReq defines the request structure for getting the role list.
type GetRoleListReq struct {
	g.Meta `path:"/roles" method:"get" tags:"SystemRole" summary:"获取角色列表"`
	common.PageRequest
	Name      string `json:"name" dc:"角色名称 (模糊查询)"`
	Key       string `json:"key" dc:"权限标识 (模糊查询)"`
	Status    *int   `json:"status" dc:"角色状态 (0:禁用, 1:启用)"`
	DateRange string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
}

// GetRoleListRes defines the response structure for getting the role list.
// Note: Directly returning the entity slice might lead to tight coupling. Consider a DTO.
type GetRoleListRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of roles.
	Data []*entity.AdminRole `json:"data" dc:"角色列表"`
}

// --- 获取角色详情 ---

// GetRoleReq defines the request structure for getting role details.
type GetRoleReq struct {
	g.Meta `path:"/roles/{id}" method:"get" tags:"SystemRole" summary:"获取角色详情"`
	Id     int64 `json:"id" v:"required#角色ID不能为空" dc:"角色ID"`
}

// GetRoleRes defines the response structure for getting role details.
// Note: Directly returning the entity might lead to tight coupling. Consider a DTO.
type GetRoleRes struct {
	// Data contains the role details.
	Data *entity.AdminRole `json:"data" dc:"角色详情"`
}

// --- 新增角色 ---

// AddRoleReq defines the request structure for adding a new role.
type AddRoleReq struct {
	g.Meta `path:"/roles" method:"post" tags:"SystemRole" summary:"新增角色"`
	Name   string `json:"name" v:"required|length:1,50#角色名称不能为空|名称长度限制1-50" dc:"角色名称"`
	Key    string `json:"key" v:"required|length:1,50#权限标识不能为空|标识长度限制1-50" dc:"权限标识 (唯一)"`
	Remark string `json:"remark" v:"length:0,200" dc:"备注"`
	Sort   int    `json:"sort" v:"required|min:0#排序值不能为空|排序值不能为负" d:"100" dc:"排序"`
	Status int    `json:"status" v:"required|in:0,1#状态不能为空|状态值必须是0或1" d:"1" dc:"角色状态(0:禁用, 1:启用)"`
	// Pid, Level, Tree 通常在后端逻辑中处理，不作为API输入
}

// AddRoleRes defines the response structure after adding a new role.
type AddRoleRes struct {
	// Id is the ID of the newly created role.
	Id int64 `json:"id" dc:"新增的角色ID"`
}

// --- 编辑角色 ---

// EditRoleReq defines the request structure for editing an existing role.
type EditRoleReq struct {
	g.Meta `path:"/roles/{id}" method:"put" tags:"SystemRole" summary:"编辑角色"`
	Id     int64  `json:"id" v:"required#角色ID不能为空" dc:"角色ID"` // 从路径获取
	Name   string `json:"name"  v:"required|length:1,50#角色名称不能为空|名称长度限制1-50" dc:"角色名称"`
	Key    string `json:"key" v:"required|length:1,50#权限标识不能为空|标识长度限制1-50" dc:"权限标识 (唯一)"`
	Remark string `json:"remark" v:"length:0,200" dc:"备注"`
	Sort   int    `json:"sort" v:"required|min:0#排序值不能为空|排序值不能为负" d:"100" dc:"排序"`
	Status int    `json:"status" v:"required|in:0,1#状态不能为空|状态值必须是0或1" d:"1" dc:"角色状态(0:禁用, 1:启用)"`
	// Pid, Level, Tree 通常在后端逻辑中处理
}

// EditRoleRes defines the response structure after editing a role.
type EditRoleRes struct {
	// Typically empty on success.
}

// --- 删除角色 ---

// DeleteRoleReq defines the request structure for deleting roles.
type DeleteRoleReq struct {
	g.Meta `path:"/roles" method:"delete" tags:"SystemRole" summary:"删除角色"` // Typically for batch deletion
	Ids    []int64                                                          `json:"ids" v:"required|min-length:1#请选择要删除的角色" dc:"角色ID列表"`
}

// DeleteRoleRes defines the response structure after deleting roles.
type DeleteRoleRes struct {
	// Typically empty on success.
}

// --- 获取角色菜单ID列表 ---

// GetRoleMenuIdsReq defines the request structure for getting the menu IDs associated with a role.
type GetRoleMenuIdsReq struct {
	g.Meta `path:"/roles/{id}/menu-ids" method:"get" tags:"SystemRole" summary:"获取角色菜单ID列表"`
	Id     int64 `json:"id" v:"required#角色ID不能为空" dc:"角色ID"` // 从路径获取
}

// GetRoleMenuIdsRes defines the response structure for getting the menu IDs associated with a role.
type GetRoleMenuIdsRes struct {
	// MenuIds is the list of menu IDs associated with the role.
	MenuIds []int64 `json:"menuIds" dc:"菜单ID列表"`
}

// --- 分配角色菜单 ---

// AssignRoleMenusReq defines the request structure for assigning menus to a role.
type AssignRoleMenusReq struct {
	g.Meta  `path:"/roles/{id}/menus" method:"put" tags:"SystemRole" summary:"分配角色菜单"` // Changed path to standard sub-resource update
	Id      int64                                                                      `json:"id" v:"required#角色ID不能为空" dc:"角色ID"` // 从路径获取
	MenuIds []int64                                                                    `json:"menuIds" dc:"菜单ID列表 (传空数组表示清空权限)"`   // 允许传空数组
}

// AssignRoleMenusRes defines the response structure after assigning menus to a role.
type AssignRoleMenusRes struct {
	// Typically empty on success.
}

// --- 更新角色数据范围 ---

// UpdateRoleDataScopeReq defines the request structure for updating a role's data scope.
type UpdateRoleDataScopeReq struct {
	g.Meta     `path:"/roles/{id}/data-scope" method:"put" tags:"SystemRole" summary:"更新角色数据范围"`
	Id         int64       `json:"id" v:"required#角色ID不能为空" dc:"角色ID"` // 从路径获取
	DataScope  int         `json:"dataScope" v:"required|in:1,2,3,4,5#数据范围值不能为空|数据范围值无效" dc:"数据范围(1:全部, 2:本部门, 3:本部门及以下, 4:仅本人, 5:自定义)"`
	CustomDept *gjson.Json `json:"customDept" dc:"自定义部门ID列表 (当 dataScope=5 时需要)"` // 使用 *gjson.Json 接收 JSON 数组
}

// UpdateRoleDataScopeRes defines the response structure after updating a role's data scope.
type UpdateRoleDataScopeRes struct {
	// Typically empty on success.
}

// --- 角色权限分配 ---

// PermissionAssignment defines the structure for assigning a permission to a role.
type PermissionAssignment struct {
	PermissionKey string `json:"permissionKey" v:"required" dc:"权限标识"`
	Action        string `json:"action"        v:"required" dc:"操作 (e.g., view, GET, click)"`
}

// AssignPermissionsToRoleReq defines the request structure for assigning permissions to a role.
type AssignPermissionsToRoleReq struct {
	g.Meta      `path:"/roles/{roleKey}/permissions" method:"put" tags:"SystemRole" summary:"分配角色权限"`
	RoleKey     string                 `json:"roleKey"     v:"required#角色Key不能为空" dc:"角色Key"` // 从路径获取
	Permissions []PermissionAssignment `json:"permissions" v:"required#权限列表不能为空" dc:"权限列表"`
}

// AssignPermissionsToRoleRes defines the response structure after assigning permissions to a role.
type AssignPermissionsToRoleRes struct {
	// Typically empty on success.
}
