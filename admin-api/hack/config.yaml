
# CLI tool, only in development environment.
# https://goframe.org/docs/cli
gfcli:
  gen:
    dao:
      - link: "mysql:root:root@tcp(127.0.0.1:3306)/xpayapi?loc=Local&parseTime=true&charset=utf8mb4"
        group: "default"  # Explicitly set default group
        descriptionTag: true
        gJsonSupport: true
        jsonCase: "CamelLower"
        typeMapping:
              decimal:
                type:   decimal.Decimal
                import: github.com/shopspring/decimal
      
      # - link: "mysql:root:root@tcp(127.0.0.1:3306)/merchant?loc=Local&parseTime=true&charset=utf8mb4"
      #   tables: "merchant_deposits,merchant_api_keys,merchant_callback_notifications,merchant_callbacks,merchant_transactions,merchant_wallets,merchant_withdraws,merchants"
      #   group: "merchant"  # 自定义组名
      #   descriptionTag: true
      #   gJsonSupport: true
      #   jsonCase: "CamelLower"
      #   typeMapping:
      #         decimal:
      #           type:   decimal.Decimal
      #           import: github.com/shopspring/decimal
  docker:
    build: "-a amd64 -s linux -p temp -ew"
    tagPrefixes:
      - my.image.pub/my-app