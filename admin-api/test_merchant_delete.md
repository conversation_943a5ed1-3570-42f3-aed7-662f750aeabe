# 商户删除功能测试文档

## 功能概述

商户删除功能已经实现了完整的本地数据库唯一验证和Casdoor唯一验证，并支持同步删除Casdoor中的数据。

## 主要改进

### 1. 添加商户时的唯一性验证

#### 本地数据库验证
- ✅ 商户名称唯一性检查
- ✅ 商户邮箱唯一性检查

#### Casdoor唯一性验证
- ✅ 检查商户名称在Casdoor中是否已存在
- ✅ 检查商户邮箱在Casdoor中是否已存在

### 2. 编辑商户时的唯一性验证

#### 本地数据库验证
- ✅ 商户名称唯一性检查（排除当前商户）
- ✅ 商户邮箱唯一性检查（排除当前商户）

#### Casdoor唯一性验证
- ✅ 检查新商户名称在Casdoor中是否已存在（仅当名称发生变化时）
- ✅ 检查新商户邮箱在Casdoor中是否已存在（仅当邮箱发生变化时）

### 3. 删除商户时的Casdoor同步

#### 配置支持
- ✅ 支持软删除（禁用用户）
- ✅ 支持硬删除（真正删除用户）
- ✅ 通过配置文件控制删除策略

#### 删除功能
- ✅ 自动删除用户的MFA设置
- ✅ 删除用户账户
- ✅ 详细的错误日志记录
- ✅ 事务保证数据一致性

## 配置说明

在 `manifest/config/config.yaml` 中添加了删除策略配置：

```yaml
merchant_casdoor_server:
  # ... 其他配置
  # 删除策略配置: "soft" - 软删除(禁用), "hard" - 硬删除(真正删除)
  delete_strategy: "hard"
```

## 错误码

新增了以下错误码：

- `CodeMerchantNameExistsInCasdoor` (4005): 商户名称在Casdoor中已存在
- `CodeMerchantEmailExistsInCasdoor` (4006): 商户邮箱在Casdoor中已存在
- `CodeCasdoorConnectionFailed` (4007): Casdoor连接失败

## API接口

### 添加商户
- **路径**: `POST /merchants`
- **验证**: 本地数据库 + Casdoor唯一性验证
- **错误处理**: 返回具体的错误信息

### 编辑商户
- **路径**: `PUT /merchants/{merchantId}`
- **验证**: 本地数据库 + Casdoor唯一性验证（仅检查变更字段）
- **错误处理**: 返回具体的错误信息

### 删除商户
- **路径**: `DELETE /merchants`
- **功能**: 
  - 本地数据库硬删除（包括关联的API密钥）
  - Casdoor同步删除（根据配置决定软删除或硬删除）
  - 详细的操作日志

## 测试建议

### 1. 添加商户测试
```bash
# 测试重复商户名称
curl -X POST /api/merchants \
  -H "Content-Type: application/json" \
  -d '{"merchantName": "existing_merchant", "email": "<EMAIL>", "password": "password123"}'

# 测试重复邮箱
curl -X POST /api/merchants \
  -H "Content-Type: application/json" \
  -d '{"merchantName": "new_merchant", "email": "<EMAIL>", "password": "password123"}'
```

### 2. 编辑商户测试
```bash
# 测试修改为已存在的商户名称
curl -X PUT /api/merchants/1 \
  -H "Content-Type: application/json" \
  -d '{"merchantName": "existing_merchant", "email": "<EMAIL>"}'
```

### 3. 删除商户测试
```bash
# 删除单个商户
curl -X DELETE /api/merchants \
  -H "Content-Type: application/json" \
  -d '{"merchantIds": [1]}'

# 批量删除商户
curl -X DELETE /api/merchants \
  -H "Content-Type: application/json" \
  -d '{"merchantIds": [1, 2, 3]}'
```

## 日志监控

删除操作会产生详细的日志：

- 成功删除：记录删除的商户信息
- 部分失败：记录Casdoor删除失败的详情
- 完全失败：记录具体的错误原因

## 注意事项

1. **删除策略**: 建议生产环境使用软删除（`soft`），测试环境可以使用硬删除（`hard`）
2. **错误处理**: Casdoor删除失败不会影响本地数据库删除，但会记录警告日志
3. **事务保证**: 本地数据库操作在事务中执行，确保数据一致性
4. **性能考虑**: Casdoor邮箱检查需要获取所有用户，在用户量大时可能影响性能

## 后续优化建议

1. **缓存优化**: 可以考虑缓存Casdoor用户信息以提高验证性能
2. **批量操作**: 优化批量删除时的Casdoor API调用
3. **异步处理**: 考虑将Casdoor同步操作改为异步处理
4. **监控告警**: 添加Casdoor操作失败的监控告警
