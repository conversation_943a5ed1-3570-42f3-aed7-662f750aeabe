#!/bin/bash
set -euo pipefail

# Enable job control for better signal handling
set -m

# =====================================================
# GitLab Incremental Sync Script
# 
# Purpose: Sync local code to remote GitLab repository
#          with incremental updates and independent history
# =====================================================

# Configuration
REMOTE_URL="*************:wallet/admin-api.git"
TARGET_BRANCH="summary_dev"
COMMIT_AUTHOR="Summary Bot <summary@bot>"
COMMIT_PREFIX="Summary update"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Global variables
TEMP_DIR=""
CURRENT_BRANCH=""
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

# Flag to prevent recursive cleanup
CLEANUP_IN_PROGRESS=false

# Cleanup function
cleanup() {
    # Prevent recursive cleanup calls
    if [[ "$CLEANUP_IN_PROGRESS" == "true" ]]; then
        return
    fi
    CLEANUP_IN_PROGRESS=true
    
    log_info "Running cleanup..."
    
    # Remove temporary directory
    if [[ -n "$TEMP_DIR" && -d "$TEMP_DIR" ]]; then
        log_info "Removing temporary directory..."
        rm -rf "$TEMP_DIR"
    fi
    
    # Return to original branch if needed
    if [[ -n "$CURRENT_BRANCH" ]]; then
        cd "$PROJECT_ROOT" 2>/dev/null || true
        if git rev-parse --verify --quiet "$CURRENT_BRANCH" >/dev/null; then
            git checkout "$CURRENT_BRANCH" 2>/dev/null || true
        fi
    fi
    
    log_info "Cleanup completed."
    
    # Exit immediately after cleanup
    exit 130  # 130 is the standard exit code for SIGINT
}

# Set up cleanup trap
trap cleanup EXIT INT TERM

# Handle interrupt more aggressively
handle_interrupt() {
    echo -e "\n${RED}[INTERRUPTED]${NC} Script interrupted by user"
    cleanup
}

trap handle_interrupt INT

# Parse command line arguments
DRY_RUN=false
VERBOSE=false
FORCE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --verbose|-v)
            VERBOSE=true
            shift
            ;;
        --force|-f)
            FORCE=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --dry-run    Show what would be done without making changes"
            echo "  --verbose    Show detailed output"
            echo "  --force      Force sync even if remote has changes"
            echo "  --help       Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Verify we're in a git repository
if ! git -C "$PROJECT_ROOT" rev-parse --is-inside-work-tree > /dev/null 2>&1; then
    log_error "Not inside a git repository: $PROJECT_ROOT"
    exit 1
fi

# Store current branch
CURRENT_BRANCH=$(git -C "$PROJECT_ROOT" rev-parse --abbrev-ref HEAD)
log_info "Current branch: $CURRENT_BRANCH"

# Check for uncommitted changes (warning only)
if ! git -C "$PROJECT_ROOT" diff-index --quiet HEAD -- 2>/dev/null; then
    log_warn "You have uncommitted changes in your working directory"
    if [[ "$FORCE" != "true" ]]; then
        # Use a timeout and handle interrupts properly
        if ! read -t 60 -p "Continue anyway? [y/N] " -n 1 -r; then
            echo -e "\n${RED}[TIMEOUT/INTERRUPTED]${NC} No response received"
            exit 1
        fi
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
fi

# Create temporary directory for remote repository
TEMP_DIR=$(mktemp -d)
log_info "Created temporary directory: $TEMP_DIR"

# Clone or initialize remote repository
log_info "Setting up remote repository..."
if git ls-remote "$REMOTE_URL" "refs/heads/$TARGET_BRANCH" &>/dev/null; then
    log_info "Cloning existing branch '$TARGET_BRANCH' from remote..."
    if ! git clone --single-branch --branch "$TARGET_BRANCH" "$REMOTE_URL" "$TEMP_DIR" 2>/dev/null; then
        log_error "Failed to clone remote repository"
        exit 1
    fi
else
    log_info "Branch '$TARGET_BRANCH' does not exist on remote. Creating new repository..."
    git init "$TEMP_DIR"
    cd "$TEMP_DIR"
    git remote add origin "$REMOTE_URL"
    git checkout -b "$TARGET_BRANCH"
    # Create initial commit
    echo "# Summary Repository" > README.md
    git add README.md
    git commit -m "$COMMIT_PREFIX: Initial commit" --author="$COMMIT_AUTHOR"
    cd - > /dev/null
fi

# Check for required commands
check_dependencies() {
    local missing_deps=()
    
    for cmd in git; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_deps+=("$cmd")
        fi
    done
    
    if [[ ${#missing_deps[@]} -ne 0 ]]; then
        log_error "Missing required dependencies: ${missing_deps[*]}"
        log_error "Please install the missing dependencies and try again."
        exit 1
    fi
}

# Check dependencies before proceeding
check_dependencies

# Function to sync files using git
sync_files() {
    local source_dir="$1"
    local target_dir="$2"
    
    log_info "Synchronizing files from $source_dir to $target_dir..."
    
    # Save current directory
    local original_dir=$(pwd)
    
    # Remove all files in target except .git
    cd "$target_dir"
    find . -mindepth 1 -maxdepth 1 -name '.git' -prune -o -exec rm -rf {} + 2>/dev/null || true
    
    # Copy files from source, respecting .gitignore
    cd "$source_dir"
    
    # Use git ls-files to get list of tracked files and untracked files that aren't ignored
    # This respects .gitignore automatically
    local files_to_copy=()
    
    # Get tracked files
    if git ls-files &>/dev/null; then
        while IFS= read -r file; do
            [[ -n "$file" ]] && files_to_copy+=("$file")
        done < <(git ls-files)
    fi
    
    # Get untracked files that aren't ignored
    if git status --porcelain &>/dev/null; then
        while IFS= read -r line; do
            if [[ "$line" =~ ^\?\?[[:space:]](.+)$ ]]; then
                files_to_copy+=("${BASH_REMATCH[1]}")
            fi
        done < <(git status --porcelain)
    fi
    
    # If not in a git repo, fall back to copying everything except common excludes
    if [[ ${#files_to_copy[@]} -eq 0 ]]; then
        log_warn "Not in a git repository, copying all files with basic exclusions..."
        while IFS= read -r file; do
            files_to_copy+=("$file")
        done < <(find . -type f -not -path "./.git/*" -not -path "./node_modules/*" -not -path "./dist/*" -not -path "./.idea/*" -not -path "./.vscode/*" | sed 's|^\./||')
    fi
    
    # Copy files to target directory
    if [[ "$VERBOSE" == "true" ]]; then
        log_info "Copying ${#files_to_copy[@]} files..."
    fi
    
    for file in "${files_to_copy[@]}"; do
        if [[ -f "$file" ]]; then
            # Create directory structure if needed
            local dir=$(dirname "$file")
            if [[ "$dir" != "." ]]; then
                mkdir -p "$target_dir/$dir"
            fi
            
            # Copy file
            if [[ "$DRY_RUN" == "true" ]]; then
                [[ "$VERBOSE" == "true" ]] && echo "Would copy: $file"
            else
                cp -p "$file" "$target_dir/$file"
            fi
        fi
    done
    
    # Return to original directory
    cd "$original_dir"
}

# Perform incremental sync
sync_files "$PROJECT_ROOT" "$TEMP_DIR"

# Check if there are changes
cd "$TEMP_DIR"
if git diff-index --quiet HEAD -- 2>/dev/null && git ls-files --others --exclude-standard | grep -q .; then
    log_info "No changes detected. Repository is up to date."
    exit 0
fi

# Show changes if verbose
if [[ "$VERBOSE" == "true" || "$DRY_RUN" == "true" ]]; then
    log_info "Changes to be committed:"
    git status --porcelain
fi

# Exit if dry run
if [[ "$DRY_RUN" == "true" ]]; then
    log_info "Dry run completed. No changes were made."
    exit 0
fi

# Stage all changes
git add -A

# Create commit with standardized message
COMMIT_MSG="$COMMIT_PREFIX: $(date +'%Y-%m-%d %H:%M:%S')"
if git diff --staged --quiet; then
    log_info "No changes to commit after staging."
    exit 0
fi

log_info "Creating commit: $COMMIT_MSG"
git commit -m "$COMMIT_MSG" --author="$COMMIT_AUTHOR"

# Push changes
log_info "Pushing changes to remote '$TARGET_BRANCH'..."
if git push origin "$TARGET_BRANCH"; then
    log_success "Successfully pushed changes to remote repository!"
    
    # Show summary
    COMMIT_HASH=$(git rev-parse --short HEAD)
    log_info "Commit: $COMMIT_HASH"
    log_info "Branch: $TARGET_BRANCH"
    log_info "Remote: $REMOTE_URL"
else
    log_error "Failed to push changes to remote repository"
    exit 1
fi

# Script completed successfully
exit 0