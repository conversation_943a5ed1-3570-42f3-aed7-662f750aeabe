import type { ProTableProps } from '@ant-design/pro-components';

/**
 * CommonProTable 组件的属性类型
 * 继承自 ProTableProps，并添加自动滚动相关的配置
 */
export interface CommonProTableProps<T extends Record<string, any> = any>
  extends ProTableProps<T, any> {
  /**
   * 是否启用自动滚动条设置
   * @default true
   */
  autoScroll?: boolean;

  /**
   * 触发自动滚动的列数阈值
   * 当可见列数超过此值时，自动添加水平滚动条
   * @default 8
   */
  scrollThreshold?: number;
}

/**
 * 滚动配置预设
 */
export const SCROLL_PRESETS = {
  SMALL: { x: 1200 },   // 8-10列
  MEDIUM: { x: 1800 },  // 11-15列  
  LARGE: { x: 2000 },   // 15列以上
} as const;

/**
 * 默认配置
 */
export const DEFAULT_CONFIG = {
  autoScroll: true,
  scrollThreshold: 8,
} as const;