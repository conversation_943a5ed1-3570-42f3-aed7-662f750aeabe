import type { ProTableProps } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import React, { useMemo } from 'react';
import type { CommonProTableProps } from './types';

/**
 * 通用 ProTable 组件
 * 自动根据列数设置水平滚动条
 * 
 * @param props - ProTable 的所有属性，会自动计算 scroll 配置
 * @returns ProTable 组件实例
 */
const CommonProTable = <T extends Record<string, any> = any>(
  props: CommonProTableProps<T>,
) => {
  const { columns = [], scroll, autoScroll = true, scrollThreshold = 8, ...restProps } = props;

  // 计算实际展示的列数（过滤掉 hideInTable 的列）
  const visibleColumnsCount = useMemo(() => {
    return columns.filter((col) => !col.hideInTable).length;
  }, [columns]);

  // 自动计算滚动宽度
  const autoScrollConfig = useMemo(() => {
    if (!autoScroll || scroll) {
      return scroll;
    }

    // 根据列数自动设置滚动宽度
    if (visibleColumnsCount < scrollThreshold) {
      return undefined;
    }

    // 基础宽度 + 每增加一列额外增加的宽度
    let scrollWidth: number;
    if (visibleColumnsCount <= 10) {
      scrollWidth = 1200;
    } else if (visibleColumnsCount <= 15) {
      scrollWidth = 1800;
    } else {
      scrollWidth = 2000 + (visibleColumnsCount - 15) * 100;
    }

    return { x: scrollWidth };
  }, [visibleColumnsCount, autoScroll, scroll, scrollThreshold]);

  return (
    <ProTable<T>
      columns={columns}
      scroll={autoScrollConfig}
      {...restProps}
    />
  );
};

export default CommonProTable;