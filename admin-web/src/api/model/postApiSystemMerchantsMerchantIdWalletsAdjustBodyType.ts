/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

/**
 * 调整类型 (increase-增加, decrease-减少)
 */
export type PostApiSystemMerchantsMerchantIdWalletsAdjustBodyType = typeof PostApiSystemMerchantsMerchantIdWalletsAdjustBodyType[keyof typeof PostApiSystemMerchantsMerchantIdWalletsAdjustBodyType];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiSystemMerchantsMerchantIdWalletsAdjustBodyType = {
  increase: 'increase',
  decrease: 'decrease',
} as const;
