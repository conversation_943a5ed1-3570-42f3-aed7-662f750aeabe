/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { AdminApiApiSystemV1UpdateWithdrawalApprovalSettingReqStatus } from './adminApiApiSystemV1UpdateWithdrawalApprovalSettingReqStatus';

export interface AdminApiApiSystemV1UpdateWithdrawalApprovalSettingReq {
  /** @minimum 1 */
  id: number;
  currency: string;
  network: string;
  autoReleaseMin: string;
  autoReleaseMax: string;
  approvalAutoMin: string;
  approvalAutoMax: string;
  approvalManualMin: string;
  approvalManualMax: string;
  status?: AdminApiApiSystemV1UpdateWithdrawalApprovalSettingReqStatus;
}
