/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComShopspringDecimalDecimal } from './githubComShopspringDecimalDecimal';

export interface AdminApiApiSystemV1AdjustMerchantBalanceRes {
  /** 是否成功 */
  success?: boolean;
  /** 交易记录ID */
  transactionId?: number;
  /** 调整前余额 */
  balanceBefore?: GithubComShopspringDecimalDecimal;
  /** 调整后余额 */
  balanceAfter?: GithubComShopspringDecimalDecimal;
  /** 格式化调整前余额 */
  formattedBalanceBefore?: string;
  /** 格式化调整后余额 */
  formattedBalanceAfter?: string;
}
