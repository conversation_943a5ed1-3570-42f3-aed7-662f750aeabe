/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { AdminApiApiSystemV1MerchantAssetItem } from './adminApiApiSystemV1MerchantAssetItem';

export interface AdminApiApiSystemV1GetMerchantAssetsRes {
  /** 商户ID */
  merchantId?: number;
  /** 商户名称 */
  merchantName?: string;
  /** 资产列表 */
  assets?: AdminApiApiSystemV1MerchantAssetItem[];
  /** 资产种类数量 */
  totalAssets?: number;
}
