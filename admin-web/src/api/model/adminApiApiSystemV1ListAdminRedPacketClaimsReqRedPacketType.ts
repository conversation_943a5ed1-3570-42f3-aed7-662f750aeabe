/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

/**
 * 红包类型: random-随机金额, fixed-固定金额
 */
export type AdminApiApiSystemV1ListAdminRedPacketClaimsReqRedPacketType = typeof AdminApiApiSystemV1ListAdminRedPacketClaimsReqRedPacketType[keyof typeof AdminApiApiSystemV1ListAdminRedPacketClaimsReqRedPacketType];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const AdminApiApiSystemV1ListAdminRedPacketClaimsReqRedPacketType = {
  random: 'random',
  fixed: 'fixed',
} as const;
