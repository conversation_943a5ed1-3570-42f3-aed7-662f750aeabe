/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

/**
 * 回调类型 (deposit_confirmed-充值确认, withdraw_created-提现创建, withdraw_cancelled-提现撤销, withdraw_approved-提现通过, withdraw_rejected-提现拒绝, withdraw_completed-提现完成, auth_payment-授权支付, add_completed-授权加款完成, deduct_completed-授权扣款完成)
 */
export type AdminApiApiSystemV1GetMyCallbacksReqCallbackType = typeof AdminApiApiSystemV1GetMyCallbacksReqCallbackType[keyof typeof AdminApiApiSystemV1GetMyCallbacksReqCallbackType];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const AdminApiApiSystemV1GetMyCallbacksReqCallbackType = {
  deposit_confirmed: 'deposit_confirmed',
  withdraw_created: 'withdraw_created',
  withdraw_cancelled: 'withdraw_cancelled',
  withdraw_approved: 'withdraw_approved',
  withdraw_rejected: 'withdraw_rejected',
  withdraw_completed: 'withdraw_completed',
  auth_payment: 'auth_payment',
  add_completed: 'add_completed',
  deduct_completed: 'deduct_completed',
} as const;
