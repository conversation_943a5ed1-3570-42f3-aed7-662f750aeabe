/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PostApiSystemMerchantsMerchantIdWalletsAdjustBodyWalletType } from './postApiSystemMerchantsMerchantIdWalletsAdjustBodyWalletType';
import type { GithubComShopspringDecimalDecimal } from './githubComShopspringDecimalDecimal';
import type { PostApiSystemMerchantsMerchantIdWalletsAdjustBodyType } from './postApiSystemMerchantsMerchantIdWalletsAdjustBodyType';

export type PostApiSystemMerchantsMerchantIdWalletsAdjustBody = {
  /** 钱包类型 (available-可用余额, frozen-冻结余额) */
  walletType: PostApiSystemMerchantsMerchantIdWalletsAdjustBodyWalletType;
  /**
   * 调整原因
   * @minLength 1
   * @maxLength 500
   */
  reason: string;
  /**
   * 参考信息 (可选)
   * @maxLength 100
   */
  reference?: string;
  /** 代币符号 */
  symbol: string;
  /** 调整金额 */
  amount: GithubComShopspringDecimalDecimal;
  /** 调整类型 (increase-增加, decrease-减少) */
  type: PostApiSystemMerchantsMerchantIdWalletsAdjustBodyType;
};
