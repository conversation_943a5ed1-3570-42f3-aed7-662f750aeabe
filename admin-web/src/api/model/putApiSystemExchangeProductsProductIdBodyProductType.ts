/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

/**
 * 产品类型 (swap, spot_limit)
 */
export type PutApiSystemExchangeProductsProductIdBodyProductType =
  (typeof PutApiSystemExchangeProductsProductIdBodyProductType)[keyof typeof PutApiSystemExchangeProductsProductIdBodyProductType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PutApiSystemExchangeProductsProductIdBodyProductType = {
  swap: 'swap',
  spot_limit: 'spot_limit',
} as const;
