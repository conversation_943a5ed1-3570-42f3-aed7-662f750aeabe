/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type GetApiSystemMyCallbacksCallbackType = typeof GetApiSystemMyCallbacksCallbackType[keyof typeof GetApiSystemMyCallbacksCallbackType];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiSystemMyCallbacksCallbackType = {
  deposit_confirmed: 'deposit_confirmed',
  withdraw_created: 'withdraw_created',
  withdraw_cancelled: 'withdraw_cancelled',
  withdraw_approved: 'withdraw_approved',
  withdraw_rejected: 'withdraw_rejected',
  withdraw_completed: 'withdraw_completed',
  auth_payment: 'auth_payment',
  add_completed: 'add_completed',
  deduct_completed: 'deduct_completed',
} as const;
