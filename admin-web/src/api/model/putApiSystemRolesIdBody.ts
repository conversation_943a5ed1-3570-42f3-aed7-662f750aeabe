/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PutApiSystemRolesIdBodyStatus } from './putApiSystemRolesIdBodyStatus';

export type PutApiSystemRolesIdBody = {
  /**
   * 排序
   * @minimum 0
   */
  sort: number;
  /** 角色状态(0:禁用, 1:启用) */
  status: PutApiSystemRolesIdBodyStatus;
  /**
   * 角色名称
   * @minLength 1
   * @maxLength 50
   */
  name: string;
  /**
   * 权限标识 (唯一)
   * @minLength 1
   * @maxLength 50
   */
  key: string;
  /**
   * 备注
   * @maxLength 200
   */
  remark?: string;
};
