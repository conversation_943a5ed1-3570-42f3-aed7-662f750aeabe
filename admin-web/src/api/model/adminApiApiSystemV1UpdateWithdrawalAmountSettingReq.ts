/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { AdminApiApiSystemV1UpdateWithdrawalAmountSettingReqStatus } from './adminApiApiSystemV1UpdateWithdrawalAmountSettingReqStatus';

export interface AdminApiApiSystemV1UpdateWithdrawalAmountSettingReq {
  /** @minimum 1 */
  id: number;
  currency: string;
  network: string;
  minAmount: string;
  maxAmount: string;
  status?: AdminApiApiSystemV1UpdateWithdrawalAmountSettingReqStatus;
}
