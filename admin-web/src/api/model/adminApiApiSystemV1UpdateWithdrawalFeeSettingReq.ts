/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { AdminApiApiSystemV1UpdateWithdrawalFeeSettingReqFeeType } from './adminApiApiSystemV1UpdateWithdrawalFeeSettingReqFeeType';
import type { AdminApiApiSystemV1UpdateWithdrawalFeeSettingReqStatus } from './adminApiApiSystemV1UpdateWithdrawalFeeSettingReqStatus';

export interface AdminApiApiSystemV1UpdateWithdrawalFeeSettingReq {
  /** @minimum 1 */
  id: number;
  currency: string;
  network: string;
  amountMin: string;
  amountMax: string;
  feeType: AdminApiApiSystemV1UpdateWithdrawalFeeSettingReqFeeType;
  feeValue: string;
  status?: AdminApiApiSystemV1UpdateWithdrawalFeeSettingReqStatus;
}
