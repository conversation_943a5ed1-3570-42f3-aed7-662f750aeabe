/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

/**
 * 数据范围(1:全部, 2:本部门, 3:本部门及以下, 4:仅本人, 5:自定义)
 */
export type PutApiSystemRolesIdDataScopeBodyDataScope = typeof PutApiSystemRolesIdDataScopeBodyDataScope[keyof typeof PutApiSystemRolesIdDataScopeBodyDataScope];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PutApiSystemRolesIdDataScopeBodyDataScope = {
  NUMBER_1: 1,
  NUMBER_2: 2,
  NUMBER_3: 3,
  NUMBER_4: 4,
  NUMBER_5: 5,
} as const;
