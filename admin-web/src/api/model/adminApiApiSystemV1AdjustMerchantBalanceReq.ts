/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComShopspringDecimalDecimal } from './githubComShopspringDecimalDecimal';
import type { AdminApiApiSystemV1AdjustMerchantBalanceReqType } from './adminApiApiSystemV1AdjustMerchantBalanceReqType';
import type { AdminApiApiSystemV1AdjustMerchantBalanceReqWalletType } from './adminApiApiSystemV1AdjustMerchantBalanceReqWalletType';

export interface AdminApiApiSystemV1AdjustMerchantBalanceReq {
  /** 商户ID */
  merchantId: number;
  /** 代币符号 */
  symbol: string;
  /** 调整金额 */
  amount: GithubComShopspringDecimalDecimal;
  /** 调整类型 (increase-增加, decrease-减少) */
  type: AdminApiApiSystemV1AdjustMerchantBalanceReqType;
  /** 钱包类型 (available-可用余额, frozen-冻结余额) */
  walletType: AdminApiApiSystemV1AdjustMerchantBalanceReqWalletType;
  /**
   * 调整原因
   * @minLength 1
   * @maxLength 500
   */
  reason: string;
  /**
   * 参考信息 (可选)
   * @maxLength 100
   */
  reference?: string;
}
