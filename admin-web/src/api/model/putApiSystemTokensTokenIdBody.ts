/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PutApiSystemTokensTokenIdBodyAllowTransfer } from './putApiSystemTokensTokenIdBodyAllowTransfer';
import type { PutApiSystemTokensTokenIdBodyWithdrawalFeeType } from './putApiSystemTokensTokenIdBodyWithdrawalFeeType';
import type { PutApiSystemTokensTokenIdBodyAllowReceive } from './putApiSystemTokensTokenIdBodyAllowReceive';
import type { PutApiSystemTokensTokenIdBodyIsActive } from './putApiSystemTokensTokenIdBodyIsActive';
import type { PutApiSystemTokensTokenIdBodyAllowWithdraw } from './putApiSystemTokensTokenIdBodyAllowWithdraw';
import type { PutApiSystemTokensTokenIdBodyAllowTrading } from './putApiSystemTokensTokenIdBodyAllowTrading';
import type { PutApiSystemTokensTokenIdBodyAllowRedPacket } from './putApiSystemTokensTokenIdBodyAllowRedPacket';
import type { PutApiSystemTokensTokenIdBodyAllowDeposit } from './putApiSystemTokensTokenIdBodyAllowDeposit';

export type PutApiSystemTokensTokenIdBody = {
  /** 是否允许内部转账 (0:否, 1:是) */
  allowTransfer?: PutApiSystemTokensTokenIdBodyAllowTransfer;
  /** 提现手续费类型 (fixed:固定, percent:百分比) */
  withdrawalFeeType?: PutApiSystemTokensTokenIdBodyWithdrawalFeeType;
  /** 提现手续费金额/百分比 */
  withdrawalFeeAmount?: string;
  /** 是否允许内部收款 (0:否, 1:是) */
  allowReceive?: PutApiSystemTokensTokenIdBodyAllowReceive;
  /** 代币名称 */
  name?: string;
  /** 合约地址 */
  contract_address?: string;
  /** 项目官网 (需为有效URL) */
  project_website?: string;
  /** 是否激活 (0:否, 1:是) */
  isActive?: PutApiSystemTokensTokenIdBodyIsActive;
  /** 是否允许提现 (0:否, 1:是) */
  allowWithdraw?: PutApiSystemTokensTokenIdBodyAllowWithdraw;
  /** 是否允许在交易对中使用 (0:否, 1:是) */
  allowTrading?: PutApiSystemTokensTokenIdBodyAllowTrading;
  /** 最小提现金额 (-1表示无限制) */
  minWithdrawalAmount?: string;
  /** 代币标准 */
  token_standard?: string;
  /** Logo URL (需为有效URL) */
  logo_url?: string;
  /** 是否允许发红包 (0:否, 1:是) */
  allowRedPacket?: PutApiSystemTokensTokenIdBodyAllowRedPacket;
  /** 最小充值金额 (-1表示无限制) */
  minDepositAmount?: string;
  /** 最大充值金额 (-1表示无限制) */
  maxDepositAmount?: string;
  /** 最大提现金额 (-1表示无限制) */
  maxWithdrawalAmount?: string;
  /** 描述 */
  description?: string;
  /**
   * 排序 (需为非负整数)
   * @minimum 0
   */
  order?: number;
  /** 是否允许充值 (0:否, 1:是) */
  allowDeposit?: PutApiSystemTokensTokenIdBodyAllowDeposit;
};
