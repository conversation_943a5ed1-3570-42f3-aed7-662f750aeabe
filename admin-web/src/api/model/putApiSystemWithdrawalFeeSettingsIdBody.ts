/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PutApiSystemWithdrawalFeeSettingsIdBodyFeeType } from './putApiSystemWithdrawalFeeSettingsIdBodyFeeType';
import type { PutApiSystemWithdrawalFeeSettingsIdBodyStatus } from './putApiSystemWithdrawalFeeSettingsIdBodyStatus';

export type PutApiSystemWithdrawalFeeSettingsIdBody = {
  currency: string;
  network: string;
  amountMin: string;
  amountMax: string;
  feeType: PutApiSystemWithdrawalFeeSettingsIdBodyFeeType;
  feeValue: string;
  status?: PutApiSystemWithdrawalFeeSettingsIdBodyStatus;
};
