/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PutApiSystemPermissionsIdBodyType } from './putApiSystemPermissionsIdBodyType';
import type { PutApiSystemPermissionsIdBodyStatus } from './putApiSystemPermissionsIdBodyStatus';

export type PutApiSystemPermissionsIdBody = {
  /**
   * 排序
   * @minimum 0
   */
  sort: number;
  /** 父权限ID (0为根权限) */
  pid: number;
  /**
   * 权限名称
   * @minLength 1
   * @maxLength 100
   */
  name: string;
  /**
   * 权限标识Key (全局唯一)
   * @minLength 1
   * @maxLength 100
   */
  key: string;
  /** 权限类型 (1:目录, 2:菜单, 3:按钮, 4:API) */
  type: PutApiSystemPermissionsIdBodyType;
  /**
   * 备注
   * @maxLength 200
   */
  remark?: string;
  /** 权限状态(0:禁用, 1:启用) */
  status: PutApiSystemPermissionsIdBodyStatus;
};
