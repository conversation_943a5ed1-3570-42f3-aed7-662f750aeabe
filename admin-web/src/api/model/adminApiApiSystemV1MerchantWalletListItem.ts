/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComShopspringDecimalDecimal } from './githubComShopspringDecimalDecimal';

export interface AdminApiApiSystemV1MerchantWalletListItem {
  /** 钱包记录唯一 ID */
  walletId?: number;
  /** ledger 钱包id */
  ledgerId?: string;
  /** 用户 ID (外键, 关联 users.user_id) */
  merchantId?: number;
  /** 可用余额 */
  availableBalance?: GithubComShopspringDecimalDecimal;
  /** 冻结余额 (例如: 挂单中, 提现处理中) */
  frozenBalance?: GithubComShopspringDecimalDecimal;
  /** 精度 */
  decimalPlaces?: number;
  /** 钱包记录创建时间 */
  createdAt?: string;
  /** 余额最后更新时间 */
  updatedAt?: string;
  /** 软删除的时间戳 */
  deletedAt?: string;
  telegramId?: number;
  /** 类型 */
  type?: string;
  /** 代币符号 (例如: USDT, BTC, ETH) */
  symbol?: string;
  /** 商户名称 */
  merchantName?: string;
  /** 格式化可用余额 */
  formattedAvailableBalance?: string;
  /** 格式化冻结余额 */
  formattedFrozenBalance?: string;
  /** 格式化总余额 */
  formattedTotalBalance?: string;
}
