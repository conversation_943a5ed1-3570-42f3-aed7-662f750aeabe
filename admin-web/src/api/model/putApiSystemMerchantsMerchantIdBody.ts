/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type PutApiSystemMerchantsMerchantIdBody = {
  /**
   * 公司/业务注册名称 (可选)
   * @maxLength 255
   */
  businessName?: string;
  /**
   * 联系电话
   * @maxLength 50
   */
  phone?: string;
  /**
   * 关联用户ID (可选)
   * @minimum 1
   */
  user_id?: number;
  /**
   * 商户名称（只能包含字母、数字、下划线或连字符）
   * @minLength 2
   * @maxLength 150
   */
  merchantName: string;
  /** 商户邮箱 */
  email: string;
  /**
   * 商户网站URL (可选)
   * @maxLength 255
   */
  websiteUrl?: string;
  /** 备用联系邮箱 */
  contactEmail?: string;
  /**
   * 回调URL
   * @maxLength 512
   */
  callbackUrl?: string;
  /**
   * 备注
   * @maxLength 200
   */
  notes?: string;
};
