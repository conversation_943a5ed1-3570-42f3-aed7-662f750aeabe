/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

/**
 * 钱包类型 (available-可用余额, frozen-冻结余额)
 */
export type PostApiSystemMerchantsMerchantIdWalletsAdjustBodyWalletType = typeof PostApiSystemMerchantsMerchantIdWalletsAdjustBodyWalletType[keyof typeof PostApiSystemMerchantsMerchantIdWalletsAdjustBodyWalletType];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiSystemMerchantsMerchantIdWalletsAdjustBodyWalletType = {
  available: 'available',
  frozen: 'frozen',
} as const;
