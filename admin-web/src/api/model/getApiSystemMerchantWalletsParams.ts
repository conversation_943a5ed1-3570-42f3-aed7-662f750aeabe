/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type GetApiSystemMerchantWalletsParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * 商户ID (可选)
 */
merchantId?: number;
/**
 * 商户名称 (模糊搜索)
 */
merchantName?: string;
/**
 * 代币符号 (模糊搜索)
 */
symbol?: string;
/**
 * 是否有余额 (true-有余额, false-无余额)
 */
hasBalance?: boolean;
/**
 * 创建时间范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
/**
 * 是否导出 (true-导出, false-不导出)
 */
export?: boolean;
};
