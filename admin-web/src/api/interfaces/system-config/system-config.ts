/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1CreateConfigCategoryReq,
  AdminApiApiSystemV1CreateConfigCategoryRes,
  AdminApiApiSystemV1CreateConfigItemReq,
  AdminApiApiSystemV1CreateConfigItemRes,
  AdminApiApiSystemV1DeleteConfigCategoryRes,
  AdminApiApiSystemV1DeleteConfigItemRes,
  AdminApiApiSystemV1ListConfigCategoryRes,
  AdminApiApiSystemV1ListConfigItemRes,
  AdminApiApiSystemV1UpdateConfigCategoryRes,
  AdminApiApiSystemV1UpdateConfigItemRes,
  DeleteApiSystemConfigCategoriesParams,
  DeleteApiSystemConfigItemsParams,
  GetApiSystemConfigCategoriesParams,
  GetApiSystemConfigItemsParams,
  PutApiSystemConfigCategoriesIdBody,
  PutApiSystemConfigItemsIdBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemConfig = () => {
/**
 * @summary 删除配置分类
 */
const deleteApiSystemConfigCategories = (
    params: DeleteApiSystemConfigCategoriesParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteConfigCategoryRes>(
      {url: `/api/system/config_categories`, method: 'DELETE',
        params
    },
      );
    }
  /**
 * @summary 获取配置分类列表
 */
const getApiSystemConfigCategories = (
    params?: GetApiSystemConfigCategoriesParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ListConfigCategoryRes>(
      {url: `/api/system/config_categories`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 创建配置分类
 */
const postApiSystemConfigCategories = (
    adminApiApiSystemV1CreateConfigCategoryReq: AdminApiApiSystemV1CreateConfigCategoryReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1CreateConfigCategoryRes>(
      {url: `/api/system/config_categories`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1CreateConfigCategoryReq
    },
      );
    }
  /**
 * @summary 更新配置分类
 */
const putApiSystemConfigCategoriesId = (
    id: number,
    putApiSystemConfigCategoriesIdBody: PutApiSystemConfigCategoriesIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateConfigCategoryRes>(
      {url: `/api/system/config_categories/${id}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemConfigCategoriesIdBody
    },
      );
    }
  /**
 * @summary 删除配置项
 */
const deleteApiSystemConfigItems = (
    params: DeleteApiSystemConfigItemsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteConfigItemRes>(
      {url: `/api/system/config_items`, method: 'DELETE',
        params
    },
      );
    }
  /**
 * @summary 获取配置项列表
 */
const getApiSystemConfigItems = (
    params: GetApiSystemConfigItemsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ListConfigItemRes>(
      {url: `/api/system/config_items`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 创建配置项
 */
const postApiSystemConfigItems = (
    adminApiApiSystemV1CreateConfigItemReq: AdminApiApiSystemV1CreateConfigItemReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1CreateConfigItemRes>(
      {url: `/api/system/config_items`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1CreateConfigItemReq
    },
      );
    }
  /**
 * @summary 更新配置项
 */
const putApiSystemConfigItemsId = (
    id: number,
    putApiSystemConfigItemsIdBody: PutApiSystemConfigItemsIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateConfigItemRes>(
      {url: `/api/system/config_items/${id}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemConfigItemsIdBody
    },
      );
    }
  return {deleteApiSystemConfigCategories,getApiSystemConfigCategories,postApiSystemConfigCategories,putApiSystemConfigCategoriesId,deleteApiSystemConfigItems,getApiSystemConfigItems,postApiSystemConfigItems,putApiSystemConfigItemsId}};
export type DeleteApiSystemConfigCategoriesResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemConfig>['deleteApiSystemConfigCategories']>>>
export type GetApiSystemConfigCategoriesResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemConfig>['getApiSystemConfigCategories']>>>
export type PostApiSystemConfigCategoriesResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemConfig>['postApiSystemConfigCategories']>>>
export type PutApiSystemConfigCategoriesIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemConfig>['putApiSystemConfigCategoriesId']>>>
export type DeleteApiSystemConfigItemsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemConfig>['deleteApiSystemConfigItems']>>>
export type GetApiSystemConfigItemsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemConfig>['getApiSystemConfigItems']>>>
export type PostApiSystemConfigItemsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemConfig>['postApiSystemConfigItems']>>>
export type PutApiSystemConfigItemsIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemConfig>['putApiSystemConfigItemsId']>>>
