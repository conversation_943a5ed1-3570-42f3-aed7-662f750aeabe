/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1AdjustBalanceReq,
  AdminApiApiSystemV1AdjustBalanceRes,
  AdminApiApiSystemV1GetWalletBalanceRes,
  AdminApiApiSystemV1ListWalletsRes,
  GetApiSystemWalletsBalanceParams,
  GetApiSystemWalletsParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemWallet = () => {
/**
 * @summary 获取钱包列表
 */
const getApiSystemWallets = (
    params: GetApiSystemWalletsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ListWalletsRes>(
      {url: `/api/system/wallets`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 调整钱包余额
 */
const postApiSystemWalletsAdjustBalance = (
    adminApiApiSystemV1AdjustBalanceReq: AdminApiApiSystemV1AdjustBalanceReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AdjustBalanceRes>(
      {url: `/api/system/wallets/adjust-balance`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1AdjustBalanceReq
    },
      );
    }
  /**
 * @summary 获取钱包余额
 */
const getApiSystemWalletsBalance = (
    params: GetApiSystemWalletsBalanceParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetWalletBalanceRes>(
      {url: `/api/system/wallets/balance`, method: 'GET',
        params
    },
      );
    }
  return {getApiSystemWallets,postApiSystemWalletsAdjustBalance,getApiSystemWalletsBalance}};
export type GetApiSystemWalletsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemWallet>['getApiSystemWallets']>>>
export type PostApiSystemWalletsAdjustBalanceResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemWallet>['postApiSystemWalletsAdjustBalance']>>>
export type GetApiSystemWalletsBalanceResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemWallet>['getApiSystemWalletsBalance']>>>
