/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1GetOperationLogDetailRes,
  AdminApiApiSystemV1GetOperationLogListRes,
  GetApiSystemOperationLogsParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemOperationLog = () => {
/**
 * @summary 查询操作日志列表
 */
const getApiSystemOperationLogs = (
    params: GetApiSystemOperationLogsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetOperationLogListRes>(
      {url: `/api/system/operation-logs`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 查询操作日志详情
 */
const getApiSystemOperationLogsId = (
    id: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetOperationLogDetailRes>(
      {url: `/api/system/operation-logs/${id}`, method: 'GET'
    },
      );
    }
  return {getApiSystemOperationLogs,getApiSystemOperationLogsId}};
export type GetApiSystemOperationLogsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemOperationLog>['getApiSystemOperationLogs']>>>
export type GetApiSystemOperationLogsIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemOperationLog>['getApiSystemOperationLogsId']>>>
