/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1GetLoginLogDetailRes,
  AdminApiApiSystemV1GetLoginLogListRes,
  GetApiSystemLoginLogsParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemLoginLog = () => {
/**
 * @summary 查询登录日志列表
 */
const getApiSystemLoginLogs = (
    params: GetApiSystemLoginLogsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetLoginLogListRes>(
      {url: `/api/system/login-logs`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 查询登录日志详情
 */
const getApiSystemLoginLogsId = (
    id: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetLoginLogDetailRes>(
      {url: `/api/system/login-logs/${id}`, method: 'GET'
    },
      );
    }
  return {getApiSystemLoginLogs,getApiSystemLoginLogsId}};
export type GetApiSystemLoginLogsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemLoginLog>['getApiSystemLoginLogs']>>>
export type GetApiSystemLoginLogsIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemLoginLog>['getApiSystemLoginLogsId']>>>
