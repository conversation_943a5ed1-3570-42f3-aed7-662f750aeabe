/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1AddRoleReq,
  AdminApiApiSystemV1AddRoleRes,
  AdminApiApiSystemV1AssignPermissionsToRoleRes,
  AdminApiApiSystemV1AssignRoleMenusRes,
  AdminApiApiSystemV1DeleteRoleRes,
  AdminApiApiSystemV1EditRoleRes,
  AdminApiApiSystemV1GetRoleListRes,
  AdminApiApiSystemV1GetRoleMenuIdsRes,
  AdminApiApiSystemV1GetRoleRes,
  AdminApiApiSystemV1UpdateRoleDataScopeRes,
  DeleteApiSystemRolesParams,
  GetApiSystemRolesParams,
  PutApiSystemRolesIdBody,
  PutApiSystemRolesIdDataScopeBody,
  PutApiSystemRolesIdMenusBody,
  PutApiSystemRolesRoleKeyPermissionsBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemRole = () => {
/**
 * @summary 删除角色
 */
const deleteApiSystemRoles = (
    params: DeleteApiSystemRolesParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteRoleRes>(
      {url: `/api/system/roles`, method: 'DELETE',
        params
    },
      );
    }
  /**
 * @summary 获取角色列表
 */
const getApiSystemRoles = (
    params: GetApiSystemRolesParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetRoleListRes>(
      {url: `/api/system/roles`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 新增角色
 */
const postApiSystemRoles = (
    adminApiApiSystemV1AddRoleReq: AdminApiApiSystemV1AddRoleReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AddRoleRes>(
      {url: `/api/system/roles`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1AddRoleReq
    },
      );
    }
  /**
 * @summary 获取角色详情
 */
const getApiSystemRolesId = (
    id: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetRoleRes>(
      {url: `/api/system/roles/${id}`, method: 'GET'
    },
      );
    }
  /**
 * @summary 编辑角色
 */
const putApiSystemRolesId = (
    id: number,
    putApiSystemRolesIdBody: PutApiSystemRolesIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1EditRoleRes>(
      {url: `/api/system/roles/${id}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemRolesIdBody
    },
      );
    }
  /**
 * @summary 更新角色数据范围
 */
const putApiSystemRolesIdDataScope = (
    id: number,
    putApiSystemRolesIdDataScopeBody: PutApiSystemRolesIdDataScopeBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateRoleDataScopeRes>(
      {url: `/api/system/roles/${id}/data-scope`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemRolesIdDataScopeBody
    },
      );
    }
  /**
 * @summary 获取角色菜单ID列表
 */
const getApiSystemRolesIdMenuIds = (
    id: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetRoleMenuIdsRes>(
      {url: `/api/system/roles/${id}/menu-ids`, method: 'GET'
    },
      );
    }
  /**
 * @summary 分配角色菜单
 */
const putApiSystemRolesIdMenus = (
    id: number,
    putApiSystemRolesIdMenusBody: PutApiSystemRolesIdMenusBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AssignRoleMenusRes>(
      {url: `/api/system/roles/${id}/menus`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemRolesIdMenusBody
    },
      );
    }
  /**
 * @summary 分配角色权限
 */
const putApiSystemRolesRoleKeyPermissions = (
    roleKey: string,
    putApiSystemRolesRoleKeyPermissionsBody: PutApiSystemRolesRoleKeyPermissionsBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AssignPermissionsToRoleRes>(
      {url: `/api/system/roles/${roleKey}/permissions`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemRolesRoleKeyPermissionsBody
    },
      );
    }
  return {deleteApiSystemRoles,getApiSystemRoles,postApiSystemRoles,getApiSystemRolesId,putApiSystemRolesId,putApiSystemRolesIdDataScope,getApiSystemRolesIdMenuIds,putApiSystemRolesIdMenus,putApiSystemRolesRoleKeyPermissions}};
export type DeleteApiSystemRolesResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemRole>['deleteApiSystemRoles']>>>
export type GetApiSystemRolesResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemRole>['getApiSystemRoles']>>>
export type PostApiSystemRolesResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemRole>['postApiSystemRoles']>>>
export type GetApiSystemRolesIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemRole>['getApiSystemRolesId']>>>
export type PutApiSystemRolesIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemRole>['putApiSystemRolesId']>>>
export type PutApiSystemRolesIdDataScopeResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemRole>['putApiSystemRolesIdDataScope']>>>
export type GetApiSystemRolesIdMenuIdsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemRole>['getApiSystemRolesIdMenuIds']>>>
export type PutApiSystemRolesIdMenusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemRole>['putApiSystemRolesIdMenus']>>>
export type PutApiSystemRolesRoleKeyPermissionsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemRole>['putApiSystemRolesRoleKeyPermissions']>>>
