/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1ApproveWithdrawRes,
  AdminApiApiSystemV1RejectWithdrawRes,
  PutApiSystemAdminWithdrawsWithdrawsIdApproveBody,
  PutApiSystemAdminWithdrawsWithdrawsIdRejectBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getAdminWithdraws = () => {
/**
 * @summary 审批通过提现申请
 */
const putApiSystemAdminWithdrawsWithdrawsIdApprove = (
    withdrawsId: number,
    putApiSystemAdminWithdrawsWithdrawsIdApproveBody: PutApiSystemAdminWithdrawsWithdrawsIdApproveBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ApproveWithdrawRes>(
      {url: `/api/system/admin/withdraws/${withdrawsId}/approve`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemAdminWithdrawsWithdrawsIdApproveBody
    },
      );
    }
  /**
 * @summary 拒绝提现申请
 */
const putApiSystemAdminWithdrawsWithdrawsIdReject = (
    withdrawsId: number,
    putApiSystemAdminWithdrawsWithdrawsIdRejectBody: PutApiSystemAdminWithdrawsWithdrawsIdRejectBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1RejectWithdrawRes>(
      {url: `/api/system/admin/withdraws/${withdrawsId}/reject`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemAdminWithdrawsWithdrawsIdRejectBody
    },
      );
    }
  return {putApiSystemAdminWithdrawsWithdrawsIdApprove,putApiSystemAdminWithdrawsWithdrawsIdReject}};
export type PutApiSystemAdminWithdrawsWithdrawsIdApproveResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAdminWithdraws>['putApiSystemAdminWithdrawsWithdrawsIdApprove']>>>
export type PutApiSystemAdminWithdrawsWithdrawsIdRejectResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAdminWithdraws>['putApiSystemAdminWithdrawsWithdrawsIdReject']>>>
