/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1AddAgentReq,
  AdminApiApiSystemV1AddAgentRes,
  AdminApiApiSystemV1AddAgentWhitelistRes,
  AdminApiApiSystemV1DeleteAgentWhitelistRes,
  AdminApiApiSystemV1EditAgentRes,
  AdminApiApiSystemV1GetAgentListRes,
  AdminApiApiSystemV1GetAgentRes,
  AdminApiApiSystemV1GetAgentWhitelistRes,
  AdminApiApiSystemV1ResetAgent2FARes,
  AdminApiApiSystemV1UpdateAgentPasswordRes,
  AdminApiApiSystemV1UpdateAgentStatusReq,
  AdminApiApiSystemV1UpdateAgentStatusRes,
  GetApiSystemAgentsAgentIdWhitelistParams,
  GetApiSystemAgentsParams,
  PostApiSystemAgentsAgentIdWhitelistBody,
  PutApiSystemAgentsAgentIdBody,
  PutApiSystemAgentsAgentIdPasswordBody,
  PutApiSystemAgentsAgentIdReset2faBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getAgentManagement = () => {
/**
 * @summary 获取代理列表
 */
const getApiSystemAgents = (
    params: GetApiSystemAgentsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetAgentListRes>(
      {url: `/api/system/agents`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 添加代理
 */
const postApiSystemAgents = (
    adminApiApiSystemV1AddAgentReq: AdminApiApiSystemV1AddAgentReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AddAgentRes>(
      {url: `/api/system/agents`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1AddAgentReq
    },
      );
    }
  /**
 * @summary 批量更新代理状态
 */
const putApiSystemAgentsBatchStatus = (
    adminApiApiSystemV1UpdateAgentStatusReq: AdminApiApiSystemV1UpdateAgentStatusReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateAgentStatusRes>(
      {url: `/api/system/agents/batch/status`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1UpdateAgentStatusReq
    },
      );
    }
  /**
 * @summary 删除指定代理的 IP 白名单
 */
const deleteApiSystemAgentsWhitelistIpWhitelistId = (
    ipWhitelistId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteAgentWhitelistRes>(
      {url: `/api/system/agents/whitelist/${ipWhitelistId}`, method: 'DELETE'
    },
      );
    }
  /**
 * @summary 获取单个代理详情
 */
const getApiSystemAgentsAgentId = (
    agentId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetAgentRes>(
      {url: `/api/system/agents/${agentId}`, method: 'GET'
    },
      );
    }
  /**
 * @summary 编辑代理基础信息
 */
const putApiSystemAgentsAgentId = (
    agentId: number,
    putApiSystemAgentsAgentIdBody: PutApiSystemAgentsAgentIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1EditAgentRes>(
      {url: `/api/system/agents/${agentId}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemAgentsAgentIdBody
    },
      );
    }
  /**
 * @summary 修改指定代理密码
 */
const putApiSystemAgentsAgentIdPassword = (
    agentId: number,
    putApiSystemAgentsAgentIdPasswordBody: PutApiSystemAgentsAgentIdPasswordBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateAgentPasswordRes>(
      {url: `/api/system/agents/${agentId}/password`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemAgentsAgentIdPasswordBody
    },
      );
    }
  /**
 * @summary 重置指定代理的 Google Authenticator
 */
const putApiSystemAgentsAgentIdReset2fa = (
    agentId: number,
    putApiSystemAgentsAgentIdReset2faBody: PutApiSystemAgentsAgentIdReset2faBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ResetAgent2FARes>(
      {url: `/api/system/agents/${agentId}/reset-2fa`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemAgentsAgentIdReset2faBody
    },
      );
    }
  /**
 * @summary 获取指定代理的 IP 白名单
 */
const getApiSystemAgentsAgentIdWhitelist = (
    agentId: number,
    params: GetApiSystemAgentsAgentIdWhitelistParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetAgentWhitelistRes>(
      {url: `/api/system/agents/${agentId}/whitelist`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 为指定代理添加 IP 白名单
 */
const postApiSystemAgentsAgentIdWhitelist = (
    agentId: number,
    postApiSystemAgentsAgentIdWhitelistBody: PostApiSystemAgentsAgentIdWhitelistBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AddAgentWhitelistRes>(
      {url: `/api/system/agents/${agentId}/whitelist`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: postApiSystemAgentsAgentIdWhitelistBody
    },
      );
    }
  return {getApiSystemAgents,postApiSystemAgents,putApiSystemAgentsBatchStatus,deleteApiSystemAgentsWhitelistIpWhitelistId,getApiSystemAgentsAgentId,putApiSystemAgentsAgentId,putApiSystemAgentsAgentIdPassword,putApiSystemAgentsAgentIdReset2fa,getApiSystemAgentsAgentIdWhitelist,postApiSystemAgentsAgentIdWhitelist}};
export type GetApiSystemAgentsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAgentManagement>['getApiSystemAgents']>>>
export type PostApiSystemAgentsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAgentManagement>['postApiSystemAgents']>>>
export type PutApiSystemAgentsBatchStatusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAgentManagement>['putApiSystemAgentsBatchStatus']>>>
export type DeleteApiSystemAgentsWhitelistIpWhitelistIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAgentManagement>['deleteApiSystemAgentsWhitelistIpWhitelistId']>>>
export type GetApiSystemAgentsAgentIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAgentManagement>['getApiSystemAgentsAgentId']>>>
export type PutApiSystemAgentsAgentIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAgentManagement>['putApiSystemAgentsAgentId']>>>
export type PutApiSystemAgentsAgentIdPasswordResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAgentManagement>['putApiSystemAgentsAgentIdPassword']>>>
export type PutApiSystemAgentsAgentIdReset2faResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAgentManagement>['putApiSystemAgentsAgentIdReset2fa']>>>
export type GetApiSystemAgentsAgentIdWhitelistResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAgentManagement>['getApiSystemAgentsAgentIdWhitelist']>>>
export type PostApiSystemAgentsAgentIdWhitelistResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAgentManagement>['postApiSystemAgentsAgentIdWhitelist']>>>
