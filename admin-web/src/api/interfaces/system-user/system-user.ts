/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1AddUserReq,
  AdminApiApiSystemV1AddUserRes,
  AdminApiApiSystemV1DeleteUserRes,
  AdminApiApiSystemV1EditUserRes,
  AdminApiApiSystemV1GetUserListRes,
  AdminApiApiSystemV1GetUserRes,
  AdminApiApiSystemV1ResetUserGoogle2FARes,
  AdminApiApiSystemV1ResetUserPasswordRes,
  AdminApiApiSystemV1UpdateUserStatusRes,
  DeleteApiSystemUsersParams,
  GetApiSystemUsersParams,
  PutApiSystemUsersIdBody,
  PutApiSystemUsersIdGoogle2faBody,
  PutApiSystemUsersIdPasswordBody,
  PutApiSystemUsersIdStatusBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemUser = () => {
/**
 * @summary 删除用户
 */
const deleteApiSystemUsers = (
    params: DeleteApiSystemUsersParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteUserRes>(
      {url: `/api/system/users`, method: 'DELETE',
        params
    },
      );
    }
  /**
 * @summary 获取用户列表
 */
const getApiSystemUsers = (
    params: GetApiSystemUsersParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetUserListRes>(
      {url: `/api/system/users`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 添加用户
 */
const postApiSystemUsers = (
    adminApiApiSystemV1AddUserReq: AdminApiApiSystemV1AddUserReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AddUserRes>(
      {url: `/api/system/users`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1AddUserReq
    },
      );
    }
  /**
 * @summary 获取用户详情
 */
const getApiSystemUsersId = (
    id: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetUserRes>(
      {url: `/api/system/users/${id}`, method: 'GET'
    },
      );
    }
  /**
 * @summary 编辑用户
 */
const putApiSystemUsersId = (
    id: number,
    putApiSystemUsersIdBody: PutApiSystemUsersIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1EditUserRes>(
      {url: `/api/system/users/${id}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemUsersIdBody
    },
      );
    }
  /**
 * @summary 重置用户Google 2FA
 */
const putApiSystemUsersIdGoogle2fa = (
    id: number,
    putApiSystemUsersIdGoogle2faBody: PutApiSystemUsersIdGoogle2faBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ResetUserGoogle2FARes>(
      {url: `/api/system/users/${id}/google2fa`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemUsersIdGoogle2faBody
    },
      );
    }
  /**
 * @summary 重置用户密码
 */
const putApiSystemUsersIdPassword = (
    id: number,
    putApiSystemUsersIdPasswordBody: PutApiSystemUsersIdPasswordBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ResetUserPasswordRes>(
      {url: `/api/system/users/${id}/password`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemUsersIdPasswordBody
    },
      );
    }
  /**
 * @summary 更新用户状态
 */
const putApiSystemUsersIdStatus = (
    id: number,
    putApiSystemUsersIdStatusBody: PutApiSystemUsersIdStatusBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateUserStatusRes>(
      {url: `/api/system/users/${id}/status`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemUsersIdStatusBody
    },
      );
    }
  return {deleteApiSystemUsers,getApiSystemUsers,postApiSystemUsers,getApiSystemUsersId,putApiSystemUsersId,putApiSystemUsersIdGoogle2fa,putApiSystemUsersIdPassword,putApiSystemUsersIdStatus}};
export type DeleteApiSystemUsersResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUser>['deleteApiSystemUsers']>>>
export type GetApiSystemUsersResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUser>['getApiSystemUsers']>>>
export type PostApiSystemUsersResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUser>['postApiSystemUsers']>>>
export type GetApiSystemUsersIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUser>['getApiSystemUsersId']>>>
export type PutApiSystemUsersIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUser>['putApiSystemUsersId']>>>
export type PutApiSystemUsersIdGoogle2faResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUser>['putApiSystemUsersIdGoogle2fa']>>>
export type PutApiSystemUsersIdPasswordResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUser>['putApiSystemUsersIdPassword']>>>
export type PutApiSystemUsersIdStatusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUser>['putApiSystemUsersIdStatus']>>>
