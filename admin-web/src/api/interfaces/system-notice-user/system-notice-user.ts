/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1GetMyNoticeListRes,
  AdminApiApiSystemV1GetMyUnreadNoticeCountRes,
  AdminApiApiSystemV1MarkNoticeReadReq,
  AdminApiApiSystemV1MarkNoticeReadRes,
  GetApiSystemMyNoticesParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemNoticeUser = () => {
/**
 * @summary 获取我的公告列表
 */
const getApiSystemMyNotices = (
    params: GetApiSystemMyNoticesParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMyNoticeListRes>(
      {url: `/api/system/my/notices`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 标记公告已读
 */
const postApiSystemMyNoticesReadStatus = (
    adminApiApiSystemV1MarkNoticeReadReq: AdminApiApiSystemV1MarkNoticeReadReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1MarkNoticeReadRes>(
      {url: `/api/system/my/notices/read-status`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1MarkNoticeReadReq
    },
      );
    }
  /**
 * @summary 获取我的未读公告数
 */
const getApiSystemMyNoticesUnreadCount = (
    
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMyUnreadNoticeCountRes>(
      {url: `/api/system/my/notices/unread-count`, method: 'GET'
    },
      );
    }
  return {getApiSystemMyNotices,postApiSystemMyNoticesReadStatus,getApiSystemMyNoticesUnreadCount}};
export type GetApiSystemMyNoticesResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemNoticeUser>['getApiSystemMyNotices']>>>
export type PostApiSystemMyNoticesReadStatusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemNoticeUser>['postApiSystemMyNoticesReadStatus']>>>
export type GetApiSystemMyNoticesUnreadCountResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemNoticeUser>['getApiSystemMyNoticesUnreadCount']>>>
