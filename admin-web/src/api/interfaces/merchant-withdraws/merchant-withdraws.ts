/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1CancelMyWithdrawRes,
  AdminApiApiSystemV1CreateMyWithdrawReq,
  AdminApiApiSystemV1CreateMyWithdrawRes,
  AdminApiApiSystemV1GetMyWithdrawDetailRes,
  AdminApiApiSystemV1GetMyWithdrawFeeRes,
  AdminApiApiSystemV1GetMyWithdrawsRes,
  GetApiSystemMyWithdrawsFeeParams,
  GetApiSystemMyWithdrawsParams,
  PutApiSystemMyWithdrawsWithdrawsIdCancelBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getMerchantWithdraws = () => {
/**
 * @summary 获取我的提现记录
 */
const getApiSystemMyWithdraws = (
    params: GetApiSystemMyWithdrawsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMyWithdrawsRes>(
      {url: `/api/system/my/withdraws`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 创建我的提现申请
 */
const postApiSystemMyWithdraws = (
    adminApiApiSystemV1CreateMyWithdrawReq: AdminApiApiSystemV1CreateMyWithdrawReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1CreateMyWithdrawRes>(
      {url: `/api/system/my/withdraws`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1CreateMyWithdrawReq
    },
      );
    }
  /**
 * @summary 获取我的提现手续费
 */
const getApiSystemMyWithdrawsFee = (
    params: GetApiSystemMyWithdrawsFeeParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMyWithdrawFeeRes>(
      {url: `/api/system/my/withdraws/fee`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 获取我的提现记录详情
 */
const getApiSystemMyWithdrawsWithdrawsId = (
    withdrawsId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMyWithdrawDetailRes>(
      {url: `/api/system/my/withdraws/${withdrawsId}`, method: 'GET'
    },
      );
    }
  /**
 * @summary 撤销我的提现申请
 */
const putApiSystemMyWithdrawsWithdrawsIdCancel = (
    withdrawsId: number,
    putApiSystemMyWithdrawsWithdrawsIdCancelBody: PutApiSystemMyWithdrawsWithdrawsIdCancelBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1CancelMyWithdrawRes>(
      {url: `/api/system/my/withdraws/${withdrawsId}/cancel`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemMyWithdrawsWithdrawsIdCancelBody
    },
      );
    }
  return {getApiSystemMyWithdraws,postApiSystemMyWithdraws,getApiSystemMyWithdrawsFee,getApiSystemMyWithdrawsWithdrawsId,putApiSystemMyWithdrawsWithdrawsIdCancel}};
export type GetApiSystemMyWithdrawsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getMerchantWithdraws>['getApiSystemMyWithdraws']>>>
export type PostApiSystemMyWithdrawsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getMerchantWithdraws>['postApiSystemMyWithdraws']>>>
export type GetApiSystemMyWithdrawsFeeResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getMerchantWithdraws>['getApiSystemMyWithdrawsFee']>>>
export type GetApiSystemMyWithdrawsWithdrawsIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getMerchantWithdraws>['getApiSystemMyWithdrawsWithdrawsId']>>>
export type PutApiSystemMyWithdrawsWithdrawsIdCancelResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getMerchantWithdraws>['putApiSystemMyWithdrawsWithdrawsIdCancel']>>>
