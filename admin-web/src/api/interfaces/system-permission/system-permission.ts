/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1AddPermissionReq,
  AdminApiApiSystemV1AddPermissionRes,
  AdminApiApiSystemV1DeletePermissionRes,
  AdminApiApiSystemV1EditPermissionRes,
  AdminApiApiSystemV1GetAllPermissionListRes,
  AdminApiApiSystemV1GetPermissionListRes,
  AdminApiApiSystemV1SyncApiPermissionsReq,
  AdminApiApiSystemV1SyncApiPermissionsRes,
  AdminApiApiSystemV1SyncMenuPermissionsReq,
  AdminApiApiSystemV1SyncMenuPermissionsRes,
  GetApiSystemPermissionsParams,
  PutApiSystemPermissionsIdBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemPermission = () => {
/**
 * @summary 一键同步API到权限表
 */
const postApiSystemApisSyncPermissions = (
    adminApiApiSystemV1SyncApiPermissionsReq: AdminApiApiSystemV1SyncApiPermissionsReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1SyncApiPermissionsRes>(
      {url: `/api/system/apis/sync-permissions`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1SyncApiPermissionsReq
    },
      );
    }
  /**
 * @summary 一键同步菜单到权限表
 */
const postApiSystemMenusSyncPermissions = (
    adminApiApiSystemV1SyncMenuPermissionsReq: AdminApiApiSystemV1SyncMenuPermissionsReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1SyncMenuPermissionsRes>(
      {url: `/api/system/menus/sync-permissions`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1SyncMenuPermissionsReq
    },
      );
    }
  /**
 * @summary 获取权限列表
 */
const getApiSystemPermissions = (
    params: GetApiSystemPermissionsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetPermissionListRes>(
      {url: `/api/system/permissions`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 新增权限
 */
const postApiSystemPermissions = (
    adminApiApiSystemV1AddPermissionReq: AdminApiApiSystemV1AddPermissionReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AddPermissionRes>(
      {url: `/api/system/permissions`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1AddPermissionReq
    },
      );
    }
  /**
 * @summary 获取全部权限
 */
const getApiSystemPermissionsAll = (
    
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetAllPermissionListRes>(
      {url: `/api/system/permissions/all`, method: 'GET'
    },
      );
    }
  /**
 * @summary 删除权限
 */
const deleteApiSystemPermissionsId = (
    id: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeletePermissionRes>(
      {url: `/api/system/permissions/${id}`, method: 'DELETE'
    },
      );
    }
  /**
 * @summary 编辑权限
 */
const putApiSystemPermissionsId = (
    id: number,
    putApiSystemPermissionsIdBody: PutApiSystemPermissionsIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1EditPermissionRes>(
      {url: `/api/system/permissions/${id}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemPermissionsIdBody
    },
      );
    }
  return {postApiSystemApisSyncPermissions,postApiSystemMenusSyncPermissions,getApiSystemPermissions,postApiSystemPermissions,getApiSystemPermissionsAll,deleteApiSystemPermissionsId,putApiSystemPermissionsId}};
export type PostApiSystemApisSyncPermissionsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemPermission>['postApiSystemApisSyncPermissions']>>>
export type PostApiSystemMenusSyncPermissionsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemPermission>['postApiSystemMenusSyncPermissions']>>>
export type GetApiSystemPermissionsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemPermission>['getApiSystemPermissions']>>>
export type PostApiSystemPermissionsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemPermission>['postApiSystemPermissions']>>>
export type GetApiSystemPermissionsAllResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemPermission>['getApiSystemPermissionsAll']>>>
export type DeleteApiSystemPermissionsIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemPermission>['deleteApiSystemPermissionsId']>>>
export type PutApiSystemPermissionsIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemPermission>['putApiSystemPermissionsId']>>>
