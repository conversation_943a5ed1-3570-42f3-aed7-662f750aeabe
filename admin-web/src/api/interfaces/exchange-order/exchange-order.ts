/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1ExchangeOrderDetailRes,
  AdminApiApiSystemV1ExchangeOrderListRes,
  GetApiSystemExchangeOrdersParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getExchangeOrder = () => {
/**
 * @summary 获取兑换订单列表
 */
const getApiSystemExchangeOrders = (
    params?: GetApiSystemExchangeOrdersParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ExchangeOrderListRes>(
      {url: `/api/system/exchange/orders`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 获取兑换订单详情
 */
const getApiSystemExchangeOrdersOrderId = (
    orderId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ExchangeOrderDetailRes>(
      {url: `/api/system/exchange/orders/${orderId}`, method: 'GET'
    },
      );
    }
  return {getApiSystemExchangeOrders,getApiSystemExchangeOrdersOrderId}};
export type GetApiSystemExchangeOrdersResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getExchangeOrder>['getApiSystemExchangeOrders']>>>
export type GetApiSystemExchangeOrdersOrderIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getExchangeOrder>['getApiSystemExchangeOrdersOrderId']>>>
