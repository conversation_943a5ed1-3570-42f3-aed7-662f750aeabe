/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1AddMerchantReq,
  AdminApiApiSystemV1AddMerchantRes,
  AdminApiApiSystemV1DeleteMerchantRes,
  AdminApiApiSystemV1EditMerchantRes,
  AdminApiApiSystemV1GenerateMerchantApiKeyRes,
  AdminApiApiSystemV1GetMerchantApiKeyListRes,
  AdminApiApiSystemV1GetMerchantListRes,
  AdminApiApiSystemV1GetMerchantRes,
  AdminApiApiSystemV1ResetMerchantGoogle2FARes,
  AdminApiApiSystemV1ResetMerchantPasswordRes,
  AdminApiApiSystemV1RevokeMerchantApiKeyRes,
  AdminApiApiSystemV1UpdateMerchantApiKeyRes,
  AdminApiApiSystemV1UpdateMerchantStatusRes,
  DeleteApiSystemMerchantsParams,
  GetApiSystemMerchantsMerchantIdApikeysParams,
  GetApiSystemMerchantsParams,
  PostApiSystemMerchantsMerchantIdApikeysBody,
  PutApiSystemMerchantsMerchantIdApikeysApiKeyIdBody,
  PutApiSystemMerchantsMerchantIdBody,
  PutApiSystemMerchantsMerchantIdGoogle2faBody,
  PutApiSystemMerchantsMerchantIdPasswordBody,
  PutApiSystemMerchantsMerchantIdStatusBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemMerchant = () => {
/**
 * @summary 删除商户
 */
const deleteApiSystemMerchants = (
    params: DeleteApiSystemMerchantsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteMerchantRes>(
      {url: `/api/system/merchants`, method: 'DELETE',
        params
    },
      );
    }
  /**
 * @summary 获取商户列表
 */
const getApiSystemMerchants = (
    params: GetApiSystemMerchantsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMerchantListRes>(
      {url: `/api/system/merchants`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 添加商户
 */
const postApiSystemMerchants = (
    adminApiApiSystemV1AddMerchantReq: AdminApiApiSystemV1AddMerchantReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AddMerchantRes>(
      {url: `/api/system/merchants`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1AddMerchantReq
    },
      );
    }
  /**
 * @summary 撤销商户API密钥
 */
const deleteApiSystemMerchantsApikeysApiKeyId = (
    apiKeyId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1RevokeMerchantApiKeyRes>(
      {url: `/api/system/merchants/apikeys/${apiKeyId}`, method: 'DELETE'
    },
      );
    }
  /**
 * @summary 获取商户详情
 */
const getApiSystemMerchantsMerchantId = (
    merchantId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMerchantRes>(
      {url: `/api/system/merchants/${merchantId}`, method: 'GET'
    },
      );
    }
  /**
 * @summary 编辑商户
 */
const putApiSystemMerchantsMerchantId = (
    merchantId: number,
    putApiSystemMerchantsMerchantIdBody: PutApiSystemMerchantsMerchantIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1EditMerchantRes>(
      {url: `/api/system/merchants/${merchantId}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemMerchantsMerchantIdBody
    },
      );
    }
  /**
 * @summary 获取商户API密钥列表
 */
const getApiSystemMerchantsMerchantIdApikeys = (
    merchantId: number,
    params: GetApiSystemMerchantsMerchantIdApikeysParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMerchantApiKeyListRes>(
      {url: `/api/system/merchants/${merchantId}/apikeys`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 生成商户API密钥
 */
const postApiSystemMerchantsMerchantIdApikeys = (
    merchantId: number,
    postApiSystemMerchantsMerchantIdApikeysBody: PostApiSystemMerchantsMerchantIdApikeysBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GenerateMerchantApiKeyRes>(
      {url: `/api/system/merchants/${merchantId}/apikeys`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: postApiSystemMerchantsMerchantIdApikeysBody
    },
      );
    }
  /**
 * @summary 更新商户API密钥
 */
const putApiSystemMerchantsMerchantIdApikeysApiKeyId = (
    merchantId: number,
    apiKeyId: number,
    putApiSystemMerchantsMerchantIdApikeysApiKeyIdBody: PutApiSystemMerchantsMerchantIdApikeysApiKeyIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateMerchantApiKeyRes>(
      {url: `/api/system/merchants/${merchantId}/apikeys/${apiKeyId}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemMerchantsMerchantIdApikeysApiKeyIdBody
    },
      );
    }
  /**
 * @summary 重置商户Google 2FA
 */
const putApiSystemMerchantsMerchantIdGoogle2fa = (
    merchantId: number,
    putApiSystemMerchantsMerchantIdGoogle2faBody: PutApiSystemMerchantsMerchantIdGoogle2faBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ResetMerchantGoogle2FARes>(
      {url: `/api/system/merchants/${merchantId}/google2fa`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemMerchantsMerchantIdGoogle2faBody
    },
      );
    }
  /**
 * @summary 重置商户密码
 */
const putApiSystemMerchantsMerchantIdPassword = (
    merchantId: number,
    putApiSystemMerchantsMerchantIdPasswordBody: PutApiSystemMerchantsMerchantIdPasswordBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ResetMerchantPasswordRes>(
      {url: `/api/system/merchants/${merchantId}/password`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemMerchantsMerchantIdPasswordBody
    },
      );
    }
  /**
 * @summary 更新商户状态
 */
const putApiSystemMerchantsMerchantIdStatus = (
    merchantId: number,
    putApiSystemMerchantsMerchantIdStatusBody: PutApiSystemMerchantsMerchantIdStatusBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateMerchantStatusRes>(
      {url: `/api/system/merchants/${merchantId}/status`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemMerchantsMerchantIdStatusBody
    },
      );
    }
  return {deleteApiSystemMerchants,getApiSystemMerchants,postApiSystemMerchants,deleteApiSystemMerchantsApikeysApiKeyId,getApiSystemMerchantsMerchantId,putApiSystemMerchantsMerchantId,getApiSystemMerchantsMerchantIdApikeys,postApiSystemMerchantsMerchantIdApikeys,putApiSystemMerchantsMerchantIdApikeysApiKeyId,putApiSystemMerchantsMerchantIdGoogle2fa,putApiSystemMerchantsMerchantIdPassword,putApiSystemMerchantsMerchantIdStatus}};
export type DeleteApiSystemMerchantsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMerchant>['deleteApiSystemMerchants']>>>
export type GetApiSystemMerchantsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMerchant>['getApiSystemMerchants']>>>
export type PostApiSystemMerchantsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMerchant>['postApiSystemMerchants']>>>
export type DeleteApiSystemMerchantsApikeysApiKeyIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMerchant>['deleteApiSystemMerchantsApikeysApiKeyId']>>>
export type GetApiSystemMerchantsMerchantIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMerchant>['getApiSystemMerchantsMerchantId']>>>
export type PutApiSystemMerchantsMerchantIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMerchant>['putApiSystemMerchantsMerchantId']>>>
export type GetApiSystemMerchantsMerchantIdApikeysResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMerchant>['getApiSystemMerchantsMerchantIdApikeys']>>>
export type PostApiSystemMerchantsMerchantIdApikeysResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMerchant>['postApiSystemMerchantsMerchantIdApikeys']>>>
export type PutApiSystemMerchantsMerchantIdApikeysApiKeyIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMerchant>['putApiSystemMerchantsMerchantIdApikeysApiKeyId']>>>
export type PutApiSystemMerchantsMerchantIdGoogle2faResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMerchant>['putApiSystemMerchantsMerchantIdGoogle2fa']>>>
export type PutApiSystemMerchantsMerchantIdPasswordResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMerchant>['putApiSystemMerchantsMerchantIdPassword']>>>
export type PutApiSystemMerchantsMerchantIdStatusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMerchant>['putApiSystemMerchantsMerchantIdStatus']>>>
