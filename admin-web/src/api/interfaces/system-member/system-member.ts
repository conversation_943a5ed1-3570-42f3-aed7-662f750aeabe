/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1AddMemberReq,
  AdminApiApiSystemV1AddMemberRes,
  AdminApiApiSystemV1AssignRolesToUserRes,
  AdminApiApiSystemV1DeleteMemberRes,
  AdminApiApiSystemV1EditMemberRes,
  AdminApiApiSystemV1GetMemberListRes,
  AdminApiApiSystemV1GetMemberRes,
  AdminApiApiSystemV1ResetMemberPasswordRes,
  AdminApiApiSystemV1UpdateMemberStatusRes,
  DeleteApiSystemMembersParams,
  GetApiSystemMembersParams,
  PutApiSystemMembersIdBody,
  PutApiSystemMembersIdPasswordBody,
  PutApiSystemMembersIdRolesBody,
  PutApiSystemMembersIdStatusBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemMember = () => {
/**
 * @summary 删除用户
 */
const deleteApiSystemMembers = (
    params: DeleteApiSystemMembersParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteMemberRes>(
      {url: `/api/system/members`, method: 'DELETE',
        params
    },
      );
    }
  /**
 * @summary 获取用户列表
 */
const getApiSystemMembers = (
    params: GetApiSystemMembersParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMemberListRes>(
      {url: `/api/system/members`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 新增用户
 */
const postApiSystemMembers = (
    adminApiApiSystemV1AddMemberReq: AdminApiApiSystemV1AddMemberReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AddMemberRes>(
      {url: `/api/system/members`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1AddMemberReq
    },
      );
    }
  /**
 * @summary 获取用户详情
 */
const getApiSystemMembersId = (
    id: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMemberRes>(
      {url: `/api/system/members/${id}`, method: 'GET'
    },
      );
    }
  /**
 * @summary 编辑用户
 */
const putApiSystemMembersId = (
    id: number,
    putApiSystemMembersIdBody: PutApiSystemMembersIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1EditMemberRes>(
      {url: `/api/system/members/${id}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemMembersIdBody
    },
      );
    }
  /**
 * @summary 重置用户密码
 */
const putApiSystemMembersIdPassword = (
    id: number,
    putApiSystemMembersIdPasswordBody: PutApiSystemMembersIdPasswordBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ResetMemberPasswordRes>(
      {url: `/api/system/members/${id}/password`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemMembersIdPasswordBody
    },
      );
    }
  /**
 * @summary 分配用户角色
 */
const putApiSystemMembersIdRoles = (
    id: number,
    putApiSystemMembersIdRolesBody: PutApiSystemMembersIdRolesBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AssignRolesToUserRes>(
      {url: `/api/system/members/${id}/roles`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemMembersIdRolesBody
    },
      );
    }
  /**
 * @summary 更新用户状态
 */
const putApiSystemMembersIdStatus = (
    id: number,
    putApiSystemMembersIdStatusBody: PutApiSystemMembersIdStatusBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateMemberStatusRes>(
      {url: `/api/system/members/${id}/status`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemMembersIdStatusBody
    },
      );
    }
  return {deleteApiSystemMembers,getApiSystemMembers,postApiSystemMembers,getApiSystemMembersId,putApiSystemMembersId,putApiSystemMembersIdPassword,putApiSystemMembersIdRoles,putApiSystemMembersIdStatus}};
export type DeleteApiSystemMembersResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMember>['deleteApiSystemMembers']>>>
export type GetApiSystemMembersResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMember>['getApiSystemMembers']>>>
export type PostApiSystemMembersResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMember>['postApiSystemMembers']>>>
export type GetApiSystemMembersIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMember>['getApiSystemMembersId']>>>
export type PutApiSystemMembersIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMember>['putApiSystemMembersId']>>>
export type PutApiSystemMembersIdPasswordResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMember>['putApiSystemMembersIdPassword']>>>
export type PutApiSystemMembersIdRolesResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMember>['putApiSystemMembersIdRoles']>>>
export type PutApiSystemMembersIdStatusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMember>['putApiSystemMembersIdStatus']>>>
