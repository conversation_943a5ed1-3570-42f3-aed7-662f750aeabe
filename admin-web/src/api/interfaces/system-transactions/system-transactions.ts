/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1ListAdminTransactionsRes,
  GetApiSystemTransactionsParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemTransactions = () => {
/**
 * @summary 查询交易记录列表(后台)
 */
const getApiSystemTransactions = (
    params: GetApiSystemTransactionsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ListAdminTransactionsRes>(
      {url: `/api/system/transactions`, method: 'GET',
        params
    },
      );
    }
  return {getApiSystemTransactions}};
export type GetApiSystemTransactionsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemTransactions>['getApiSystemTransactions']>>>
