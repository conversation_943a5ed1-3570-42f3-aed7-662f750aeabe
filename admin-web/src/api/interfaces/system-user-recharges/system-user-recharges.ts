/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1ListUserRechargesRes,
  GetApiSystemUserRechargesParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemUserRecharges = () => {
/**
 * @summary 查询用户充值记录列表
 */
const getApiSystemUserRecharges = (
    params: GetApiSystemUserRechargesParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ListUserRechargesRes>(
      {url: `/api/system/user-recharges`, method: 'GET',
        params
    },
      );
    }
  return {getApiSystemUserRecharges}};
export type GetApiSystemUserRechargesResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserRecharges>['getApiSystemUserRecharges']>>>
