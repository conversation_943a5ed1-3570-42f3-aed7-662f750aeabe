/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1CreateWithdrawalAmountSettingReq,
  AdminApiApiSystemV1CreateWithdrawalAmountSettingRes,
  AdminApiApiSystemV1CreateWithdrawalApprovalSettingReq,
  AdminApiApiSystemV1CreateWithdrawalApprovalSettingRes,
  AdminApiApiSystemV1CreateWithdrawalFeeSettingReq,
  AdminApiApiSystemV1CreateWithdrawalFeeSettingRes,
  AdminApiApiSystemV1DeleteWithdrawalAmountSettingRes,
  AdminApiApiSystemV1DeleteWithdrawalApprovalSettingRes,
  AdminApiApiSystemV1DeleteWithdrawalFeeSettingRes,
  AdminApiApiSystemV1ListWithdrawalAmountSettingsRes,
  AdminApiApiSystemV1ListWithdrawalApprovalSettingsRes,
  AdminApiApiSystemV1ListWithdrawalFeeSettingsRes,
  AdminApiApiSystemV1UpdateWithdrawalAmountSettingRes,
  AdminApiApiSystemV1UpdateWithdrawalApprovalSettingRes,
  AdminApiApiSystemV1UpdateWithdrawalFeeSettingRes,
  DeleteApiSystemWithdrawalAmountSettingsParams,
  DeleteApiSystemWithdrawalApprovalSettingsParams,
  DeleteApiSystemWithdrawalFeeSettingsParams,
  GetApiSystemWithdrawalAmountSettingsParams,
  GetApiSystemWithdrawalApprovalSettingsParams,
  GetApiSystemWithdrawalFeeSettingsParams,
  PutApiSystemWithdrawalAmountSettingsIdBody,
  PutApiSystemWithdrawalApprovalSettingsIdBody,
  PutApiSystemWithdrawalFeeSettingsIdBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getWithdrawalConfig = () => {
/**
 * @summary 删除提现金额设置
 */
const deleteApiSystemWithdrawalAmountSettings = (
    params: DeleteApiSystemWithdrawalAmountSettingsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteWithdrawalAmountSettingRes>(
      {url: `/api/system/withdrawal-amount-settings`, method: 'DELETE',
        params
    },
      );
    }
  /**
 * @summary 获取提现金额设置列表
 */
const getApiSystemWithdrawalAmountSettings = (
    params: GetApiSystemWithdrawalAmountSettingsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ListWithdrawalAmountSettingsRes>(
      {url: `/api/system/withdrawal-amount-settings`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 创建提现金额设置
 */
const postApiSystemWithdrawalAmountSettings = (
    adminApiApiSystemV1CreateWithdrawalAmountSettingReq: AdminApiApiSystemV1CreateWithdrawalAmountSettingReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1CreateWithdrawalAmountSettingRes>(
      {url: `/api/system/withdrawal-amount-settings`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1CreateWithdrawalAmountSettingReq
    },
      );
    }
  /**
 * @summary 更新提现金额设置
 */
const putApiSystemWithdrawalAmountSettingsId = (
    id: number,
    putApiSystemWithdrawalAmountSettingsIdBody: PutApiSystemWithdrawalAmountSettingsIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateWithdrawalAmountSettingRes>(
      {url: `/api/system/withdrawal-amount-settings/${id}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemWithdrawalAmountSettingsIdBody
    },
      );
    }
  /**
 * @summary 删除提现审核设置
 */
const deleteApiSystemWithdrawalApprovalSettings = (
    params: DeleteApiSystemWithdrawalApprovalSettingsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteWithdrawalApprovalSettingRes>(
      {url: `/api/system/withdrawal-approval-settings`, method: 'DELETE',
        params
    },
      );
    }
  /**
 * @summary 获取提现审核设置列表
 */
const getApiSystemWithdrawalApprovalSettings = (
    params: GetApiSystemWithdrawalApprovalSettingsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ListWithdrawalApprovalSettingsRes>(
      {url: `/api/system/withdrawal-approval-settings`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 创建提现审核设置
 */
const postApiSystemWithdrawalApprovalSettings = (
    adminApiApiSystemV1CreateWithdrawalApprovalSettingReq: AdminApiApiSystemV1CreateWithdrawalApprovalSettingReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1CreateWithdrawalApprovalSettingRes>(
      {url: `/api/system/withdrawal-approval-settings`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1CreateWithdrawalApprovalSettingReq
    },
      );
    }
  /**
 * @summary 更新提现审核设置
 */
const putApiSystemWithdrawalApprovalSettingsId = (
    id: number,
    putApiSystemWithdrawalApprovalSettingsIdBody: PutApiSystemWithdrawalApprovalSettingsIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateWithdrawalApprovalSettingRes>(
      {url: `/api/system/withdrawal-approval-settings/${id}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemWithdrawalApprovalSettingsIdBody
    },
      );
    }
  /**
 * @summary 删除提现手续费设置
 */
const deleteApiSystemWithdrawalFeeSettings = (
    params: DeleteApiSystemWithdrawalFeeSettingsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteWithdrawalFeeSettingRes>(
      {url: `/api/system/withdrawal-fee-settings`, method: 'DELETE',
        params
    },
      );
    }
  /**
 * @summary 获取提现手续费设置列表
 */
const getApiSystemWithdrawalFeeSettings = (
    params: GetApiSystemWithdrawalFeeSettingsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ListWithdrawalFeeSettingsRes>(
      {url: `/api/system/withdrawal-fee-settings`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 创建提现手续费设置
 */
const postApiSystemWithdrawalFeeSettings = (
    adminApiApiSystemV1CreateWithdrawalFeeSettingReq: AdminApiApiSystemV1CreateWithdrawalFeeSettingReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1CreateWithdrawalFeeSettingRes>(
      {url: `/api/system/withdrawal-fee-settings`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1CreateWithdrawalFeeSettingReq
    },
      );
    }
  /**
 * @summary 更新提现手续费设置
 */
const putApiSystemWithdrawalFeeSettingsId = (
    id: number,
    putApiSystemWithdrawalFeeSettingsIdBody: PutApiSystemWithdrawalFeeSettingsIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateWithdrawalFeeSettingRes>(
      {url: `/api/system/withdrawal-fee-settings/${id}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemWithdrawalFeeSettingsIdBody
    },
      );
    }
  return {deleteApiSystemWithdrawalAmountSettings,getApiSystemWithdrawalAmountSettings,postApiSystemWithdrawalAmountSettings,putApiSystemWithdrawalAmountSettingsId,deleteApiSystemWithdrawalApprovalSettings,getApiSystemWithdrawalApprovalSettings,postApiSystemWithdrawalApprovalSettings,putApiSystemWithdrawalApprovalSettingsId,deleteApiSystemWithdrawalFeeSettings,getApiSystemWithdrawalFeeSettings,postApiSystemWithdrawalFeeSettings,putApiSystemWithdrawalFeeSettingsId}};
export type DeleteApiSystemWithdrawalAmountSettingsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdrawalConfig>['deleteApiSystemWithdrawalAmountSettings']>>>
export type GetApiSystemWithdrawalAmountSettingsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdrawalConfig>['getApiSystemWithdrawalAmountSettings']>>>
export type PostApiSystemWithdrawalAmountSettingsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdrawalConfig>['postApiSystemWithdrawalAmountSettings']>>>
export type PutApiSystemWithdrawalAmountSettingsIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdrawalConfig>['putApiSystemWithdrawalAmountSettingsId']>>>
export type DeleteApiSystemWithdrawalApprovalSettingsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdrawalConfig>['deleteApiSystemWithdrawalApprovalSettings']>>>
export type GetApiSystemWithdrawalApprovalSettingsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdrawalConfig>['getApiSystemWithdrawalApprovalSettings']>>>
export type PostApiSystemWithdrawalApprovalSettingsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdrawalConfig>['postApiSystemWithdrawalApprovalSettings']>>>
export type PutApiSystemWithdrawalApprovalSettingsIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdrawalConfig>['putApiSystemWithdrawalApprovalSettingsId']>>>
export type DeleteApiSystemWithdrawalFeeSettingsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdrawalConfig>['deleteApiSystemWithdrawalFeeSettings']>>>
export type GetApiSystemWithdrawalFeeSettingsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdrawalConfig>['getApiSystemWithdrawalFeeSettings']>>>
export type PostApiSystemWithdrawalFeeSettingsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdrawalConfig>['postApiSystemWithdrawalFeeSettings']>>>
export type PutApiSystemWithdrawalFeeSettingsIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdrawalConfig>['putApiSystemWithdrawalFeeSettingsId']>>>
