/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1ExchangeProductCreateReq,
  AdminApiApiSystemV1ExchangeProductCreateRes,
  AdminApiApiSystemV1ExchangeProductDeleteRes,
  AdminApiApiSystemV1ExchangeProductDetailRes,
  AdminApiApiSystemV1ExchangeProductListRes,
  AdminApiApiSystemV1ExchangeProductStatusRes,
  AdminApiApiSystemV1ExchangeProductUpdateRes,
  AdminApiApiSystemV1ExchangeProductVolumeRes,
  GetApiSystemExchangeProductsParams,
  PostApiSystemExchangeProductsProductIdStatusBody,
  PutApiSystemExchangeProductsProductIdBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getExchangeProduct = () => {
/**
 * @summary 获取兑换产品列表
 */
const getApiSystemExchangeProducts = (
    params?: GetApiSystemExchangeProductsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ExchangeProductListRes>(
      {url: `/api/system/exchange/products`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 创建兑换产品
 */
const postApiSystemExchangeProducts = (
    adminApiApiSystemV1ExchangeProductCreateReq: AdminApiApiSystemV1ExchangeProductCreateReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ExchangeProductCreateRes>(
      {url: `/api/system/exchange/products`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1ExchangeProductCreateReq
    },
      );
    }
  /**
 * @summary 删除兑换产品
 */
const deleteApiSystemExchangeProductsProductId = (
    productId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ExchangeProductDeleteRes>(
      {url: `/api/system/exchange/products/${productId}`, method: 'DELETE'
    },
      );
    }
  /**
 * @summary 获取兑换产品详情
 */
const getApiSystemExchangeProductsProductId = (
    productId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ExchangeProductDetailRes>(
      {url: `/api/system/exchange/products/${productId}`, method: 'GET'
    },
      );
    }
  /**
 * @summary 更新兑换产品
 */
const putApiSystemExchangeProductsProductId = (
    productId: number,
    putApiSystemExchangeProductsProductIdBody: PutApiSystemExchangeProductsProductIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ExchangeProductUpdateRes>(
      {url: `/api/system/exchange/products/${productId}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemExchangeProductsProductIdBody
    },
      );
    }
  /**
 * @summary 更新兑换产品状态
 */
const postApiSystemExchangeProductsProductIdStatus = (
    productId: number,
    postApiSystemExchangeProductsProductIdStatusBody: PostApiSystemExchangeProductsProductIdStatusBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ExchangeProductStatusRes>(
      {url: `/api/system/exchange/products/${productId}/status`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: postApiSystemExchangeProductsProductIdStatusBody
    },
      );
    }
  /**
 * @summary 获取兑换产品交易量
 */
const getApiSystemExchangeProductsProductIdVolume = (
    productId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ExchangeProductVolumeRes>(
      {url: `/api/system/exchange/products/${productId}/volume`, method: 'GET'
    },
      );
    }
  return {getApiSystemExchangeProducts,postApiSystemExchangeProducts,deleteApiSystemExchangeProductsProductId,getApiSystemExchangeProductsProductId,putApiSystemExchangeProductsProductId,postApiSystemExchangeProductsProductIdStatus,getApiSystemExchangeProductsProductIdVolume}};
export type GetApiSystemExchangeProductsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getExchangeProduct>['getApiSystemExchangeProducts']>>>
export type PostApiSystemExchangeProductsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getExchangeProduct>['postApiSystemExchangeProducts']>>>
export type DeleteApiSystemExchangeProductsProductIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getExchangeProduct>['deleteApiSystemExchangeProductsProductId']>>>
export type GetApiSystemExchangeProductsProductIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getExchangeProduct>['getApiSystemExchangeProductsProductId']>>>
export type PutApiSystemExchangeProductsProductIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getExchangeProduct>['putApiSystemExchangeProductsProductId']>>>
export type PostApiSystemExchangeProductsProductIdStatusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getExchangeProduct>['postApiSystemExchangeProductsProductIdStatus']>>>
export type GetApiSystemExchangeProductsProductIdVolumeResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getExchangeProduct>['getApiSystemExchangeProductsProductIdVolume']>>>
