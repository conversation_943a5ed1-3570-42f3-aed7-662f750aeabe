/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1GetMyCallbackDetailRes,
  AdminApiApiSystemV1GetMyCallbacksRes,
  AdminApiApiSystemV1RetryCallbackRes,
  GetApiSystemMyCallbacksParams,
  PostApiSystemMyCallbacksIdRetryBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getMerchantCallbacks = () => {
/**
 * @summary 获取我的回调记录
 */
const getApiSystemMyCallbacks = (
    params: GetApiSystemMyCallbacksParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMyCallbacksRes>(
      {url: `/api/system/my/callbacks`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 获取我的回调记录详情
 */
const getApiSystemMyCallbacksId = (
    id: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMyCallbackDetailRes>(
      {url: `/api/system/my/callbacks/${id}`, method: 'GET'
    },
      );
    }
  /**
 * @summary 重试失败的回调
 */
const postApiSystemMyCallbacksIdRetry = (
    id: number,
    postApiSystemMyCallbacksIdRetryBody: PostApiSystemMyCallbacksIdRetryBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1RetryCallbackRes>(
      {url: `/api/system/my/callbacks/${id}/retry`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: postApiSystemMyCallbacksIdRetryBody
    },
      );
    }
  return {getApiSystemMyCallbacks,getApiSystemMyCallbacksId,postApiSystemMyCallbacksIdRetry}};
export type GetApiSystemMyCallbacksResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getMerchantCallbacks>['getApiSystemMyCallbacks']>>>
export type GetApiSystemMyCallbacksIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getMerchantCallbacks>['getApiSystemMyCallbacksId']>>>
export type PostApiSystemMyCallbacksIdRetryResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getMerchantCallbacks>['postApiSystemMyCallbacksIdRetry']>>>
