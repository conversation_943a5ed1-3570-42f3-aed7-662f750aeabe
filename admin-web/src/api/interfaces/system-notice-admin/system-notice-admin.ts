/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1AddAdminNoticeReq,
  AdminApiApiSystemV1AddAdminNoticeRes,
  AdminApiApiSystemV1DeleteAdminNoticeRes,
  AdminApiApiSystemV1EditAdminNoticeRes,
  AdminApiApiSystemV1GetAdminNoticeListRes,
  AdminApiApiSystemV1GetAdminNoticeReadStatusRes,
  AdminApiApiSystemV1GetAdminNoticeRes,
  AdminApiApiSystemV1GetMemberListForNoticeRes,
  DeleteApiSystemAdminNoticesParams,
  GetApiSystemAdminNoticesIdReadStatusParams,
  GetApiSystemAdminNoticesMembersSelectorParams,
  GetApiSystemAdminNoticesParams,
  PutApiSystemAdminNoticesIdBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemNoticeAdmin = () => {
/**
 * @summary 删除公告(管理端)
 */
const deleteApiSystemAdminNotices = (
    params: DeleteApiSystemAdminNoticesParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteAdminNoticeRes>(
      {url: `/api/system/admin/notices`, method: 'DELETE',
        params
    },
      );
    }
  /**
 * @summary 获取公告列表(管理端)
 */
const getApiSystemAdminNotices = (
    params: GetApiSystemAdminNoticesParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetAdminNoticeListRes>(
      {url: `/api/system/admin/notices`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 新增公告(管理端)
 */
const postApiSystemAdminNotices = (
    adminApiApiSystemV1AddAdminNoticeReq: AdminApiApiSystemV1AddAdminNoticeReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AddAdminNoticeRes>(
      {url: `/api/system/admin/notices`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1AddAdminNoticeReq
    },
      );
    }
  /**
 * @summary 获取用户列表(公告选择器)
 */
const getApiSystemAdminNoticesMembersSelector = (
    params: GetApiSystemAdminNoticesMembersSelectorParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMemberListForNoticeRes>(
      {url: `/api/system/admin/notices/members/selector`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 获取公告详情(管理端)
 */
const getApiSystemAdminNoticesId = (
    id: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetAdminNoticeRes>(
      {url: `/api/system/admin/notices/${id}`, method: 'GET'
    },
      );
    }
  /**
 * @summary 编辑公告(管理端)
 */
const putApiSystemAdminNoticesId = (
    id: number,
    putApiSystemAdminNoticesIdBody: PutApiSystemAdminNoticesIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1EditAdminNoticeRes>(
      {url: `/api/system/admin/notices/${id}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemAdminNoticesIdBody
    },
      );
    }
  /**
 * @summary 获取公告已读未读用户列表
 */
const getApiSystemAdminNoticesIdReadStatus = (
    id: number,
    params: GetApiSystemAdminNoticesIdReadStatusParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetAdminNoticeReadStatusRes>(
      {url: `/api/system/admin/notices/${id}/read-status`, method: 'GET',
        params
    },
      );
    }
  return {deleteApiSystemAdminNotices,getApiSystemAdminNotices,postApiSystemAdminNotices,getApiSystemAdminNoticesMembersSelector,getApiSystemAdminNoticesId,putApiSystemAdminNoticesId,getApiSystemAdminNoticesIdReadStatus}};
export type DeleteApiSystemAdminNoticesResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemNoticeAdmin>['deleteApiSystemAdminNotices']>>>
export type GetApiSystemAdminNoticesResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemNoticeAdmin>['getApiSystemAdminNotices']>>>
export type PostApiSystemAdminNoticesResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemNoticeAdmin>['postApiSystemAdminNotices']>>>
export type GetApiSystemAdminNoticesMembersSelectorResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemNoticeAdmin>['getApiSystemAdminNoticesMembersSelector']>>>
export type GetApiSystemAdminNoticesIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemNoticeAdmin>['getApiSystemAdminNoticesId']>>>
export type PutApiSystemAdminNoticesIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemNoticeAdmin>['putApiSystemAdminNoticesId']>>>
export type GetApiSystemAdminNoticesIdReadStatusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemNoticeAdmin>['getApiSystemAdminNoticesIdReadStatus']>>>
