/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1AssignRolesToAdminMemberReq,
  AdminApiApiSystemV1AssignRolesToAdminMemberRes,
  AdminApiApiSystemV1GetAdminMemberAssignedRolesRes,
  GetApiSystemSystemAdminMemberGetRolesParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemAdminmember = () => {
/**
 * @summary Assign roles to admin member
 */
const postApiSystemSystemAdminMemberAssignRoles = (
    adminApiApiSystemV1AssignRolesToAdminMemberReq: AdminApiApiSystemV1AssignRolesToAdminMemberReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AssignRolesToAdminMemberRes>(
      {url: `/api/system/system/admin-member/assign-roles`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1AssignRolesToAdminMemberReq
    },
      );
    }
  /**
 * @summary Get roles assigned to admin member
 */
const getApiSystemSystemAdminMemberGetRoles = (
    params: GetApiSystemSystemAdminMemberGetRolesParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetAdminMemberAssignedRolesRes>(
      {url: `/api/system/system/admin-member/get-roles`, method: 'GET',
        params
    },
      );
    }
  return {postApiSystemSystemAdminMemberAssignRoles,getApiSystemSystemAdminMemberGetRoles}};
export type PostApiSystemSystemAdminMemberAssignRolesResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemAdminmember>['postApiSystemSystemAdminMemberAssignRoles']>>>
export type GetApiSystemSystemAdminMemberGetRolesResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemAdminmember>['getApiSystemSystemAdminMemberGetRoles']>>>
