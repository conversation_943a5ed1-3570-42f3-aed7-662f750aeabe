/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1UploadAvatarReq,
  AdminApiApiSystemV1UploadAvatarRes
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemUpload = () => {
/**
 * @summary 上传头像
 */
const postApiSystemUploadAvatar = (
    adminApiApiSystemV1UploadAvatarReq: AdminApiApiSystemV1UploadAvatarReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UploadAvatarRes>(
      {url: `/api/system/upload/avatar`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1UploadAvatarReq
    },
      );
    }
  return {postApiSystemUploadAvatar}};
export type PostApiSystemUploadAvatarResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUpload>['postApiSystemUploadAvatar']>>>
