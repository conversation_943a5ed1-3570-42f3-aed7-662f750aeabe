/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1AdjustMerchantBalanceRes,
  AdminApiApiSystemV1GetMerchantAssetsRes,
  AdminApiApiSystemV1GetMerchantWalletDetailRes,
  AdminApiApiSystemV1GetMerchantWalletsRes,
  GetApiSystemMerchantWalletsParams,
  PostApiSystemMerchantsMerchantIdWalletsAdjustBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getMerchantWallet = () => {
/**
 * @summary 获取商户钱包列表
 */
const getApiSystemMerchantWallets = (
    params: GetApiSystemMerchantWalletsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMerchantWalletsRes>(
      {url: `/api/system/merchant-wallets`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 获取商户资产概览
 */
const getApiSystemMerchantsMerchantIdAssets = (
    merchantId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMerchantAssetsRes>(
      {url: `/api/system/merchants/${merchantId}/assets`, method: 'GET'
    },
      );
    }
  /**
 * @summary 调整商户余额
 */
const postApiSystemMerchantsMerchantIdWalletsAdjust = (
    merchantId: number,
    postApiSystemMerchantsMerchantIdWalletsAdjustBody: PostApiSystemMerchantsMerchantIdWalletsAdjustBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AdjustMerchantBalanceRes>(
      {url: `/api/system/merchants/${merchantId}/wallets/adjust`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: postApiSystemMerchantsMerchantIdWalletsAdjustBody
    },
      );
    }
  /**
 * @summary 获取商户钱包详情
 */
const getApiSystemMerchantsMerchantIdWalletsSymbol = (
    merchantId: number,
    symbol: string,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMerchantWalletDetailRes>(
      {url: `/api/system/merchants/${merchantId}/wallets/${symbol}`, method: 'GET'
    },
      );
    }
  return {getApiSystemMerchantWallets,getApiSystemMerchantsMerchantIdAssets,postApiSystemMerchantsMerchantIdWalletsAdjust,getApiSystemMerchantsMerchantIdWalletsSymbol}};
export type GetApiSystemMerchantWalletsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getMerchantWallet>['getApiSystemMerchantWallets']>>>
export type GetApiSystemMerchantsMerchantIdAssetsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getMerchantWallet>['getApiSystemMerchantsMerchantIdAssets']>>>
export type PostApiSystemMerchantsMerchantIdWalletsAdjustResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getMerchantWallet>['postApiSystemMerchantsMerchantIdWalletsAdjust']>>>
export type GetApiSystemMerchantsMerchantIdWalletsSymbolResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getMerchantWallet>['getApiSystemMerchantsMerchantIdWalletsSymbol']>>>
