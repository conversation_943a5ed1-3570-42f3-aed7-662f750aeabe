/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1CancelRedPacketRes,
  AdminApiApiSystemV1GetAdminRedPacketDetailRes,
  AdminApiApiSystemV1ListAdminRedPacketClaimsRes,
  AdminApiApiSystemV1ListAdminRedPacketsRes,
  AdminApiApiSystemV1ListRedPacketImagesRes,
  AdminApiApiSystemV1ReviewRedPacketImageRes,
  GetApiSystemRedPacketClaimsParams,
  GetApiSystemRedPacketImagesParams,
  GetApiSystemRedPacketsParams,
  PostApiSystemRedPacketImagesRedPacketImagesIdReviewBody,
  PostApiSystemRedPacketsRedPacketIdCancelBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemRedPacket = () => {
/**
 * @summary 获取红包领取记录列表
 */
const getApiSystemRedPacketClaims = (
    params: GetApiSystemRedPacketClaimsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ListAdminRedPacketClaimsRes>(
      {url: `/api/system/red-packet-claims`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 获取红包封面图片列表
 */
const getApiSystemRedPacketImages = (
    params: GetApiSystemRedPacketImagesParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ListRedPacketImagesRes>(
      {url: `/api/system/red-packet-images`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 审核红包封面图片
 */
const postApiSystemRedPacketImagesRedPacketImagesIdReview = (
    redPacketImagesId: number,
    postApiSystemRedPacketImagesRedPacketImagesIdReviewBody: PostApiSystemRedPacketImagesRedPacketImagesIdReviewBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ReviewRedPacketImageRes>(
      {url: `/api/system/red-packet-images/${redPacketImagesId}/review`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: postApiSystemRedPacketImagesRedPacketImagesIdReviewBody
    },
      );
    }
  /**
 * @summary 获取红包列表
 */
const getApiSystemRedPackets = (
    params: GetApiSystemRedPacketsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ListAdminRedPacketsRes>(
      {url: `/api/system/red-packets`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 获取红包详情
 */
const getApiSystemRedPacketsRedPacketId = (
    redPacketId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetAdminRedPacketDetailRes>(
      {url: `/api/system/red-packets/${redPacketId}`, method: 'GET'
    },
      );
    }
  /**
 * @summary 取消红包
 */
const postApiSystemRedPacketsRedPacketIdCancel = (
    redPacketId: number,
    postApiSystemRedPacketsRedPacketIdCancelBody: PostApiSystemRedPacketsRedPacketIdCancelBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1CancelRedPacketRes>(
      {url: `/api/system/red-packets/${redPacketId}/cancel`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: postApiSystemRedPacketsRedPacketIdCancelBody
    },
      );
    }
  return {getApiSystemRedPacketClaims,getApiSystemRedPacketImages,postApiSystemRedPacketImagesRedPacketImagesIdReview,getApiSystemRedPackets,getApiSystemRedPacketsRedPacketId,postApiSystemRedPacketsRedPacketIdCancel}};
export type GetApiSystemRedPacketClaimsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemRedPacket>['getApiSystemRedPacketClaims']>>>
export type GetApiSystemRedPacketImagesResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemRedPacket>['getApiSystemRedPacketImages']>>>
export type PostApiSystemRedPacketImagesRedPacketImagesIdReviewResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemRedPacket>['postApiSystemRedPacketImagesRedPacketImagesIdReview']>>>
export type GetApiSystemRedPacketsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemRedPacket>['getApiSystemRedPackets']>>>
export type GetApiSystemRedPacketsRedPacketIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemRedPacket>['getApiSystemRedPacketsRedPacketId']>>>
export type PostApiSystemRedPacketsRedPacketIdCancelResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemRedPacket>['postApiSystemRedPacketsRedPacketIdCancel']>>>
