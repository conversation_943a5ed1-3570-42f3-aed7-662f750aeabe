/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1AddMenuReq,
  AdminApiApiSystemV1AddMenuRes,
  AdminApiApiSystemV1DeleteMenuRes,
  AdminApiApiSystemV1EditMenuRes,
  AdminApiApiSystemV1GetAllMenuListRes,
  AdminApiApiSystemV1GetMenuListRes,
  AdminApiApiSystemV1GetUserAccessibleMenusRes,
  GetApiSystemMenusParams,
  PutApiSystemMenusIdBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemMenu = () => {
/**
 * @summary 获取菜单列表
 */
const getApiSystemMenus = (
    params: GetApiSystemMenusParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMenuListRes>(
      {url: `/api/system/menus`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 新增菜单
 */
const postApiSystemMenus = (
    adminApiApiSystemV1AddMenuReq: AdminApiApiSystemV1AddMenuReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AddMenuRes>(
      {url: `/api/system/menus`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1AddMenuReq
    },
      );
    }
  /**
 * @summary 获取当前用户可访问的菜单树
 */
const getApiSystemMenusAccessible = (
    
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetUserAccessibleMenusRes>(
      {url: `/api/system/menus/accessible`, method: 'GET'
    },
      );
    }
  /**
 * @summary 获取全部菜单
 */
const getApiSystemMenusAll = (
    
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetAllMenuListRes>(
      {url: `/api/system/menus/all`, method: 'GET'
    },
      );
    }
  /**
 * @summary 删除菜单
 */
const deleteApiSystemMenusId = (
    id: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteMenuRes>(
      {url: `/api/system/menus/${id}`, method: 'DELETE'
    },
      );
    }
  /**
 * @summary 编辑菜单
 */
const putApiSystemMenusId = (
    id: number,
    putApiSystemMenusIdBody: PutApiSystemMenusIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1EditMenuRes>(
      {url: `/api/system/menus/${id}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemMenusIdBody
    },
      );
    }
  return {getApiSystemMenus,postApiSystemMenus,getApiSystemMenusAccessible,getApiSystemMenusAll,deleteApiSystemMenusId,putApiSystemMenusId}};
export type GetApiSystemMenusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMenu>['getApiSystemMenus']>>>
export type PostApiSystemMenusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMenu>['postApiSystemMenus']>>>
export type GetApiSystemMenusAccessibleResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMenu>['getApiSystemMenusAccessible']>>>
export type GetApiSystemMenusAllResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMenu>['getApiSystemMenusAll']>>>
export type DeleteApiSystemMenusIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMenu>['deleteApiSystemMenusId']>>>
export type PutApiSystemMenusIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemMenu>['putApiSystemMenusId']>>>
