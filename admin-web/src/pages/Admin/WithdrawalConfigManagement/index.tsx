import { PageContainer } from '@ant-design/pro-components';
import { Tabs } from 'antd';
import React, { useState } from 'react';
import WithdrawalAmountSettings from './components/WithdrawalAmountSettings';
import WithdrawalApprovalSettings from './components/WithdrawalApprovalSettings';
import WithdrawalFeeSettings from './components/WithdrawalFeeSettings';

const { TabPane } = Tabs;

/**
 * 提现配置管理页面
 */
const WithdrawalConfigManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('amount');

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  return (
    <PageContainer title="提现配置管理">
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        type="card"
        size="large"
      >
        <TabPane tab="提现金额设置" key="amount">
          <WithdrawalAmountSettings />
        </TabPane>
        <TabPane tab="提现审核设置" key="approval">
          <WithdrawalApprovalSettings />
        </TabPane>
        <TabPane tab="提现手续费设置" key="fee">
          <WithdrawalFeeSettings />
        </TabPane>
      </Tabs>
    </PageContainer>
  );
};

export default WithdrawalConfigManagement;
