import { Form, Input, Modal, Select, Switch, message } from 'antd';
import React, { useEffect } from 'react';
import { WithdrawalAmountSetting, WithdrawalAmountFormValues, CURRENCY_OPTIONS, NETWORK_OPTIONS } from '../types';

const { Option } = Select;

interface WithdrawalAmountModalProps {
  visible: boolean;
  editingRecord: WithdrawalAmountSetting | null;
  onCancel: () => void;
  onSuccess: () => void;
}

/**
 * 提现金额设置模态框
 */
const WithdrawalAmountModal: React.FC<WithdrawalAmountModalProps> = ({
  visible,
  editingRecord,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm<WithdrawalAmountFormValues>();
  const [loading, setLoading] = React.useState(false);

  // 重置表单
  useEffect(() => {
    if (visible) {
      if (editingRecord) {
        form.setFieldsValue({
          currency: editingRecord.currency,
          network: editingRecord.network,
          minAmount: editingRecord.minAmount,
          maxAmount: editingRecord.maxAmount,
          status: editingRecord.status,
        });
      } else {
        form.resetFields();
        form.setFieldsValue({
          status: 1, // 默认启用
        });
      }
    }
  }, [visible, editingRecord, form]);

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (editingRecord) {
        // 编辑
        // TODO: 调用编辑API
        // await updateWithdrawalAmountSetting(editingRecord.id!, values);
        message.success('编辑成功');
      } else {
        // 新增
        // TODO: 调用新增API
        // await createWithdrawalAmountSetting(values);
        message.success('新增成功');
      }

      onSuccess();
    } catch (error) {
      if (error?.errorFields) {
        // 表单验证错误
        return;
      }
      message.error(editingRecord ? '编辑失败' : '新增失败');
    } finally {
      setLoading(false);
    }
  };

  // 验证最大金额大于最小金额
  const validateMaxAmount = (_: any, value: string) => {
    const minAmount = form.getFieldValue('minAmount');
    if (minAmount && value && parseFloat(value) <= parseFloat(minAmount)) {
      return Promise.reject(new Error('最大金额必须大于最小金额'));
    }
    return Promise.resolve();
  };

  return (
    <Modal
      title={editingRecord ? '编辑提现金额设置' : '新增提现金额设置'}
      open={visible}
      onOk={handleSubmit}
      onCancel={onCancel}
      confirmLoading={loading}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        preserve={false}
      >
        <Form.Item
          name="currency"
          label="币种"
          rules={[{ required: true, message: '请选择币种' }]}
        >
          <Select placeholder="请选择币种" showSearch>
            {CURRENCY_OPTIONS.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="network"
          label="网络"
          rules={[{ required: true, message: '请选择网络' }]}
        >
          <Select placeholder="请选择网络" showSearch>
            {NETWORK_OPTIONS.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="minAmount"
          label="单笔最小提现金额"
          rules={[
            { required: true, message: '请输入最小金额' },
            { pattern: /^\d+(\.\d{1,8})?$/, message: '请输入有效的金额（最多8位小数）' },
          ]}
        >
          <Input
            placeholder="请输入最小金额"
            suffix="单位：币种"
            addonBefore="≥"
          />
        </Form.Item>

        <Form.Item
          name="maxAmount"
          label="单笔最大提现金额"
          rules={[
            { required: true, message: '请输入最大金额' },
            { pattern: /^\d+(\.\d{1,8})?$/, message: '请输入有效的金额（最多8位小数）' },
            { validator: validateMaxAmount },
          ]}
        >
          <Input
            placeholder="请输入最大金额"
            suffix="单位：币种"
            addonBefore="≤"
          />
        </Form.Item>

        <Form.Item
          name="status"
          label="状态"
          valuePropName="checked"
          getValueFromEvent={(checked) => checked ? 1 : 0}
          getValueProps={(value) => ({ checked: value === 1 })}
        >
          <Switch
            checkedChildren="启用"
            unCheckedChildren="禁用"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default WithdrawalAmountModal;
