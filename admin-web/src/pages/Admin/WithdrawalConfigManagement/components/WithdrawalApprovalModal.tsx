import { Form, Input, Modal, Select, Switch, message, Row, Col, Divider } from 'antd';
import React, { useEffect } from 'react';
import { WithdrawalApprovalSetting, WithdrawalApprovalFormValues, CURRENCY_OPTIONS, NETWORK_OPTIONS } from '../types';

const { Option } = Select;

interface WithdrawalApprovalModalProps {
  visible: boolean;
  editingRecord: WithdrawalApprovalSetting | null;
  onCancel: () => void;
  onSuccess: () => void;
}

/**
 * 提现审核设置模态框
 */
const WithdrawalApprovalModal: React.FC<WithdrawalApprovalModalProps> = ({
  visible,
  editingRecord,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm<WithdrawalApprovalFormValues>();
  const [loading, setLoading] = React.useState(false);

  // 重置表单
  useEffect(() => {
    if (visible) {
      if (editingRecord) {
        form.setFieldsValue({
          currency: editingRecord.currency,
          network: editingRecord.network,
          autoReleaseMin: editingRecord.autoReleaseMin,
          autoReleaseMax: editingRecord.autoReleaseMax,
          approvalAutoMin: editingRecord.approvalAutoMin,
          approvalAutoMax: editingRecord.approvalAutoMax,
          approvalManualMin: editingRecord.approvalManualMin,
          approvalManualMax: editingRecord.approvalManualMax,
          status: editingRecord.status,
        });
      } else {
        form.resetFields();
        form.setFieldsValue({
          status: 1, // 默认启用
        });
      }
    }
  }, [visible, editingRecord, form]);

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (editingRecord) {
        // 编辑
        // TODO: 调用编辑API
        // await updateWithdrawalApprovalSetting(editingRecord.id!, values);
        message.success('编辑成功');
      } else {
        // 新增
        // TODO: 调用新增API
        // await createWithdrawalApprovalSetting(values);
        message.success('新增成功');
      }

      onSuccess();
    } catch (error) {
      if (error?.errorFields) {
        // 表单验证错误
        return;
      }
      message.error(editingRecord ? '编辑失败' : '新增失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={editingRecord ? '编辑提现审核设置' : '新增提现审核设置'}
      open={visible}
      onOk={handleSubmit}
      onCancel={onCancel}
      confirmLoading={loading}
      width={800}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        preserve={false}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="currency"
              label="币种"
              rules={[{ required: true, message: '请选择币种' }]}
            >
              <Select placeholder="请选择币种" showSearch>
                {CURRENCY_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="network"
              label="网络"
              rules={[{ required: true, message: '请选择网络' }]}
            >
              <Select placeholder="请选择网络" showSearch>
                {NETWORK_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Divider orientation="left">无需审核自动放币金额</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="autoReleaseMin"
              label="最小金额"
              rules={[
                { required: true, message: '请输入最小金额' },
                { pattern: /^\d+(\.\d{1,8})?$/, message: '请输入有效的金额（最多8位小数）' },
              ]}
            >
              <Input placeholder="请输入最小金额" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="autoReleaseMax"
              label="最大金额"
              rules={[
                { required: true, message: '请输入最大金额' },
                { pattern: /^\d+(\.\d{1,8})?$/, message: '请输入有效的金额（最多8位小数）' },
              ]}
            >
              <Input placeholder="请输入最大金额" />
            </Form.Item>
          </Col>
        </Row>

        <Divider orientation="left">审核确定后自动放币金额</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="approvalAutoMin"
              label="最小金额"
              rules={[
                { required: true, message: '请输入最小金额' },
                { pattern: /^\d+(\.\d{1,8})?$/, message: '请输入有效的金额（最多8位小数）' },
              ]}
            >
              <Input placeholder="请输入最小金额" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="approvalAutoMax"
              label="最大金额"
              rules={[
                { required: true, message: '请输入最大金额' },
                { pattern: /^\d+(\.\d{1,8})?$/, message: '请输入有效的金额（最多8位小数）' },
              ]}
            >
              <Input placeholder="请输入最大金额" />
            </Form.Item>
          </Col>
        </Row>

        <Divider orientation="left">审核确定后手动放币金额</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="approvalManualMin"
              label="最小金额"
              rules={[
                { required: true, message: '请输入最小金额' },
                { pattern: /^\d+(\.\d{1,8})?$/, message: '请输入有效的金额（最多8位小数）' },
              ]}
            >
              <Input placeholder="请输入最小金额" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="approvalManualMax"
              label="最大金额"
              rules={[
                { required: true, message: '请输入最大金额' },
                { pattern: /^\d+(\.\d{1,8})?$/, message: '请输入有效的金额（最多8位小数）' },
              ]}
            >
              <Input placeholder="请输入最大金额" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="status"
          label="状态"
          valuePropName="checked"
          getValueFromEvent={(checked) => checked ? 1 : 0}
          getValueProps={(value) => ({ checked: value === 1 })}
        >
          <Switch
            checkedChildren="启用"
            unCheckedChildren="禁用"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default WithdrawalApprovalModal;
