import { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { Button, message, Popconfirm, Space, Tag } from 'antd';
import React, { useRef, useState } from 'react';
import { WithdrawalFeeSetting } from '../types';
import WithdrawalFeeModal from './WithdrawalFeeModal';

/**
 * 提现手续费设置组件
 */
const WithdrawalFeeSettings: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<WithdrawalFeeSetting | null>(null);
  const [loading, setLoading] = useState(false);

  // 获取数据
  const fetchData = async (params: any) => {
    try {
      // TODO: 调用API获取数据
      // const response = await getWithdrawalFeeSettings(params);
      // return response;
      
      // 模拟数据
      return {
        data: [
          {
            id: 1,
            currency: 'USDT',
            network: 'TRC20',
            amountMin: '0',
            amountMax: '1000',
            feeType: 'fixed' as const,
            feeValue: '1',
            status: 1,
            createdAt: '2025-08-03 10:00:00',
            updatedAt: '2025-08-03 10:00:00',
          },
        ],
        success: true,
        total: 1,
      };
    } catch (error) {
      message.error('获取数据失败');
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };

  // 新增
  const handleAdd = () => {
    setEditingRecord(null);
    setModalVisible(true);
  };

  // 编辑
  const handleEdit = (record: WithdrawalFeeSetting) => {
    setEditingRecord(record);
    setModalVisible(true);
  };

  // 删除
  const handleDelete = async (id: number) => {
    try {
      setLoading(true);
      // TODO: 调用删除API
      // await deleteWithdrawalFeeSetting([id]);
      message.success('删除成功');
      actionRef.current?.reload();
    } catch (error) {
      message.error('删除失败');
    } finally {
      setLoading(false);
    }
  };

  // 批量删除
  const handleBatchDelete = async (selectedRows: WithdrawalFeeSetting[]) => {
    try {
      setLoading(true);
      const ids = selectedRows.map(row => row.id!);
      // TODO: 调用批量删除API
      // await deleteWithdrawalFeeSetting(ids);
      message.success('批量删除成功');
      actionRef.current?.reload();
    } catch (error) {
      message.error('批量删除失败');
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      hideInSearch: true,
    },
    {
      title: '币种',
      dataIndex: 'currency',
      width: 100,
    },
    {
      title: '网络',
      dataIndex: 'network',
      width: 100,
    },
    {
      title: '单笔提现金额',
      dataIndex: 'amountRange',
      width: 150,
      hideInSearch: true,
      render: (_, record: WithdrawalFeeSetting) => (
        <span>{record.amountMin} - {record.amountMax}</span>
      ),
    },
    {
      title: '手续费类型',
      dataIndex: 'feeType',
      width: 120,
      valueType: 'select',
      valueEnum: {
        fixed: { text: '固定金额' },
        percent: { text: '百分比' },
      },
      render: (_, record: WithdrawalFeeSetting) => (
        <Tag color={record.feeType === 'fixed' ? 'blue' : 'green'}>
          {record.feeType === 'fixed' ? '固定金额' : '百分比'}
        </Tag>
      ),
    },
    {
      title: '手续费',
      dataIndex: 'feeValue',
      width: 120,
      hideInSearch: true,
      render: (_, record: WithdrawalFeeSetting) => (
        <span>
          {record.feeValue}
          {record.feeType === 'percent' ? '%' : ''}
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      valueType: 'select',
      valueEnum: {
        1: { text: '启用', status: 'Success' },
        0: { text: '禁用', status: 'Error' },
      },
      render: (_, record: WithdrawalFeeSetting) => (
        <Tag color={record.status === 1 ? 'green' : 'red'}>
          {record.status === 1 ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 180,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 150,
      render: (_, record: WithdrawalFeeSetting) => [
        <Button
          key="edit"
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => handleEdit(record)}
        >
          编辑
        </Button>,
        <Popconfirm
          key="delete"
          title="确定要删除这条记录吗？"
          onConfirm={() => handleDelete(record.id!)}
          okText="确定"
          cancelText="取消"
        >
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
          >
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <>
      <ProTable<WithdrawalFeeSetting>
        headerTitle="提现手续费设置"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            新增设置
          </Button>,
        ]}
        request={fetchData}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            // 可以在这里处理选中的行
          },
        }}
        tableAlertRender={({ selectedRowKeys, selectedRows, onCleanSelected }) => (
          <Space size={24}>
            <span>
              已选择 {selectedRowKeys.length} 项
              <Button type="link" style={{ marginLeft: 8 }} onClick={onCleanSelected}>
                取消选择
              </Button>
            </span>
          </Space>
        )}
        tableAlertOptionRender={({ selectedRows }) => (
          <Space size={16}>
            <Popconfirm
              title="确定要批量删除选中的记录吗？"
              onConfirm={() => handleBatchDelete(selectedRows)}
              okText="确定"
              cancelText="取消"
            >
              <Button size="small" danger>
                批量删除
              </Button>
            </Popconfirm>
          </Space>
        )}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
      />

      <WithdrawalFeeModal
        visible={modalVisible}
        editingRecord={editingRecord}
        onCancel={() => setModalVisible(false)}
        onSuccess={() => {
          setModalVisible(false);
          actionRef.current?.reload();
        }}
      />
    </>
  );
};

export default WithdrawalFeeSettings;
