import { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { Button, message, Popconfirm, Space, Tag } from 'antd';
import React, { useRef, useState } from 'react';
import { WithdrawalApprovalSetting } from '../types';
import WithdrawalApprovalModal from './WithdrawalApprovalModal';

/**
 * 提现审核设置组件
 */
const WithdrawalApprovalSettings: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<WithdrawalApprovalSetting | null>(null);
  const [loading, setLoading] = useState(false);

  // 获取数据
  const fetchData = async (params: any) => {
    try {
      // TODO: 调用API获取数据
      // const response = await getWithdrawalApprovalSettings(params);
      // return response;
      
      // 模拟数据
      return {
        data: [
          {
            id: 1,
            currency: 'USDT',
            network: 'TRC20',
            autoReleaseMin: '0',
            autoReleaseMax: '1000',
            approvalAutoMin: '1000',
            approvalAutoMax: '50000',
            approvalManualMin: '50000',
            approvalManualMax: '0',
            status: 1,
            createdAt: '2025-08-03 10:00:00',
            updatedAt: '2025-08-03 10:00:00',
          },
        ],
        success: true,
        total: 1,
      };
    } catch (error) {
      message.error('获取数据失败');
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };

  // 新增
  const handleAdd = () => {
    setEditingRecord(null);
    setModalVisible(true);
  };

  // 编辑
  const handleEdit = (record: WithdrawalApprovalSetting) => {
    setEditingRecord(record);
    setModalVisible(true);
  };

  // 删除
  const handleDelete = async (id: number) => {
    try {
      setLoading(true);
      // TODO: 调用删除API
      // await deleteWithdrawalApprovalSetting([id]);
      message.success('删除成功');
      actionRef.current?.reload();
    } catch (error) {
      message.error('删除失败');
    } finally {
      setLoading(false);
    }
  };

  // 批量删除
  const handleBatchDelete = async (selectedRows: WithdrawalApprovalSetting[]) => {
    try {
      setLoading(true);
      const ids = selectedRows.map(row => row.id!);
      // TODO: 调用批量删除API
      // await deleteWithdrawalApprovalSetting(ids);
      message.success('批量删除成功');
      actionRef.current?.reload();
    } catch (error) {
      message.error('批量删除失败');
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      hideInSearch: true,
    },
    {
      title: '币种',
      dataIndex: 'currency',
      width: 100,
    },
    {
      title: '网络',
      dataIndex: 'network',
      width: 100,
    },
    {
      title: '无需审核自动放币金额',
      dataIndex: 'autoRelease',
      width: 180,
      hideInSearch: true,
      render: (_, record: WithdrawalApprovalSetting) => (
        <span>{record.autoReleaseMin} - {record.autoReleaseMax}</span>
      ),
    },
    {
      title: '审核确定后自动放币金额',
      dataIndex: 'approvalAuto',
      width: 180,
      hideInSearch: true,
      render: (_, record: WithdrawalApprovalSetting) => (
        <span>{record.approvalAutoMin} - {record.approvalAutoMax}</span>
      ),
    },
    {
      title: '审核确定后手动放币金额',
      dataIndex: 'approvalManual',
      width: 180,
      hideInSearch: true,
      render: (_, record: WithdrawalApprovalSetting) => (
        <span>{record.approvalManualMin} - {record.approvalManualMax}</span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      valueType: 'select',
      valueEnum: {
        1: { text: '启用', status: 'Success' },
        0: { text: '禁用', status: 'Error' },
      },
      render: (_, record: WithdrawalApprovalSetting) => (
        <Tag color={record.status === 1 ? 'green' : 'red'}>
          {record.status === 1 ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 180,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 150,
      render: (_, record: WithdrawalApprovalSetting) => [
        <Button
          key="edit"
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => handleEdit(record)}
        >
          编辑
        </Button>,
        <Popconfirm
          key="delete"
          title="确定要删除这条记录吗？"
          onConfirm={() => handleDelete(record.id!)}
          okText="确定"
          cancelText="取消"
        >
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
          >
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <>
      <ProTable<WithdrawalApprovalSetting>
        headerTitle="提现审核设置"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            新增设置
          </Button>,
        ]}
        request={fetchData}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            // 可以在这里处理选中的行
          },
        }}
        tableAlertRender={({ selectedRowKeys, selectedRows, onCleanSelected }) => (
          <Space size={24}>
            <span>
              已选择 {selectedRowKeys.length} 项
              <Button type="link" style={{ marginLeft: 8 }} onClick={onCleanSelected}>
                取消选择
              </Button>
            </span>
          </Space>
        )}
        tableAlertOptionRender={({ selectedRows }) => (
          <Space size={16}>
            <Popconfirm
              title="确定要批量删除选中的记录吗？"
              onConfirm={() => handleBatchDelete(selectedRows)}
              okText="确定"
              cancelText="取消"
            >
              <Button size="small" danger>
                批量删除
              </Button>
            </Popconfirm>
          </Space>
        )}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        scroll={{ x: 1200 }}
      />

      <WithdrawalApprovalModal
        visible={modalVisible}
        editingRecord={editingRecord}
        onCancel={() => setModalVisible(false)}
        onSuccess={() => {
          setModalVisible(false);
          actionRef.current?.reload();
        }}
      />
    </>
  );
};

export default WithdrawalApprovalSettings;
