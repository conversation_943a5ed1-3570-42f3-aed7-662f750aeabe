import { Form, Input, Modal, Select, Switch, message, Row, Col } from 'antd';
import React, { useEffect } from 'react';
import { WithdrawalFeeSetting, WithdrawalFeeFormValues, CURRENCY_OPTIONS, NETWORK_OPTIONS, FEE_TYPE_OPTIONS } from '../types';

const { Option } = Select;

interface WithdrawalFeeModalProps {
  visible: boolean;
  editingRecord: WithdrawalFeeSetting | null;
  onCancel: () => void;
  onSuccess: () => void;
}

/**
 * 提现手续费设置模态框
 */
const WithdrawalFeeModal: React.FC<WithdrawalFeeModalProps> = ({
  visible,
  editingRecord,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm<WithdrawalFeeFormValues>();
  const [loading, setLoading] = React.useState(false);

  // 重置表单
  useEffect(() => {
    if (visible) {
      if (editingRecord) {
        form.setFieldsValue({
          currency: editingRecord.currency,
          network: editingRecord.network,
          amountMin: editingRecord.amountMin,
          amountMax: editingRecord.amountMax,
          feeType: editingRecord.feeType,
          feeValue: editingRecord.feeValue,
          status: editingRecord.status,
        });
      } else {
        form.resetFields();
        form.setFieldsValue({
          feeType: 'fixed', // 默认固定金额
          status: 1, // 默认启用
        });
      }
    }
  }, [visible, editingRecord, form]);

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (editingRecord) {
        // 编辑
        // TODO: 调用编辑API
        // await updateWithdrawalFeeSetting(editingRecord.id!, values);
        message.success('编辑成功');
      } else {
        // 新增
        // TODO: 调用新增API
        // await createWithdrawalFeeSetting(values);
        message.success('新增成功');
      }

      onSuccess();
    } catch (error) {
      if (error?.errorFields) {
        // 表单验证错误
        return;
      }
      message.error(editingRecord ? '编辑失败' : '新增失败');
    } finally {
      setLoading(false);
    }
  };

  // 验证最大金额大于最小金额
  const validateMaxAmount = (_: any, value: string) => {
    const minAmount = form.getFieldValue('amountMin');
    if (minAmount && value && parseFloat(value) <= parseFloat(minAmount)) {
      return Promise.reject(new Error('最大金额必须大于最小金额'));
    }
    return Promise.resolve();
  };

  // 验证手续费值
  const validateFeeValue = (_: any, value: string) => {
    const feeType = form.getFieldValue('feeType');
    if (feeType === 'percent' && value && parseFloat(value) > 100) {
      return Promise.reject(new Error('百分比手续费不能超过100%'));
    }
    return Promise.resolve();
  };

  // 监听手续费类型变化
  const handleFeeTypeChange = (value: string) => {
    // 清空手续费值，让用户重新输入
    form.setFieldsValue({ feeValue: undefined });
  };

  const feeType = Form.useWatch('feeType', form);

  return (
    <Modal
      title={editingRecord ? '编辑提现手续费设置' : '新增提现手续费设置'}
      open={visible}
      onOk={handleSubmit}
      onCancel={onCancel}
      confirmLoading={loading}
      width={700}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        preserve={false}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="currency"
              label="币种"
              rules={[{ required: true, message: '请选择币种' }]}
            >
              <Select placeholder="请选择币种" showSearch>
                {CURRENCY_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="network"
              label="网络"
              rules={[{ required: true, message: '请选择网络' }]}
            >
              <Select placeholder="请选择网络" showSearch>
                {NETWORK_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="amountMin"
              label="单笔提现金额范围最小值"
              rules={[
                { required: true, message: '请输入最小金额' },
                { pattern: /^\d+(\.\d{1,8})?$/, message: '请输入有效的金额（最多8位小数）' },
              ]}
            >
              <Input
                placeholder="请输入最小金额"
                addonBefore="≥"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="amountMax"
              label="单笔提现金额范围最大值"
              rules={[
                { required: true, message: '请输入最大金额' },
                { pattern: /^\d+(\.\d{1,8})?$/, message: '请输入有效的金额（最多8位小数）' },
                { validator: validateMaxAmount },
              ]}
            >
              <Input
                placeholder="请输入最大金额"
                addonBefore="≤"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="feeType"
              label="手续费类型"
              rules={[{ required: true, message: '请选择手续费类型' }]}
            >
              <Select placeholder="请选择手续费类型" onChange={handleFeeTypeChange}>
                {FEE_TYPE_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="feeValue"
              label="手续费"
              rules={[
                { required: true, message: '请输入手续费' },
                { pattern: /^\d+(\.\d{1,8})?$/, message: '请输入有效的数值（最多8位小数）' },
                { validator: validateFeeValue },
              ]}
            >
              <Input
                placeholder={feeType === 'percent' ? '请输入百分比（0-100）' : '请输入固定金额'}
                suffix={feeType === 'percent' ? '%' : ''}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="status"
          label="状态"
          valuePropName="checked"
          getValueFromEvent={(checked) => checked ? 1 : 0}
          getValueProps={(value) => ({ checked: value === 1 })}
        >
          <Switch
            checkedChildren="启用"
            unCheckedChildren="禁用"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default WithdrawalFeeModal;
