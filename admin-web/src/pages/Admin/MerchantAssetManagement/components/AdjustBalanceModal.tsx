import {
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Alert,
  Space,
  Typography,
  Divider,
  Row,
  Col,
} from 'antd';
import React, { useEffect } from 'react';
import type { AdjustBalanceFormData } from '../types';
import {
  ADJUST_TYPE_OPTIONS,
  WALLET_TYPE_OPTIONS,
  COMMON_SYMBOLS,
  AMOUNT_RULES,
  REASON_RULES,
} from '../constants';

const { Option } = Select;
const { TextArea } = Input;
const { Text } = Typography;

interface AdjustBalanceModalProps {
  visible: boolean;
  merchantId: number;
  merchantName: string;
  symbol?: string; // 预设的代币符号
  onCancel: () => void;
  onSubmit: (values: AdjustBalanceFormData) => Promise<void>;
  loading?: boolean;
}

const AdjustBalanceModal: React.FC<AdjustBalanceModalProps> = ({
  visible,
  merchantId,
  merchantName,
  symbol,
  onCancel,
  onSubmit,
  loading = false,
}) => {
  const [form] = Form.useForm<AdjustBalanceFormData>();

  useEffect(() => {
    if (visible) {
      form.resetFields();
      if (symbol) {
        form.setFieldsValue({ symbol });
      }
    }
  }, [visible, symbol, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      await onSubmit(values);
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const watchType = Form.useWatch('type', form);
  const watchAmount = Form.useWatch('amount', form);
  const watchSymbol = Form.useWatch('symbol', form);
  const watchWalletType = Form.useWatch('walletType', form);

  return (
    <Modal
      title="调整商户余额"
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={600}
      destroyOnClose
    >
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        {/* 商户信息展示 */}
        <Alert
          message={
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>商户名称: </Text>
                <Text>{merchantName}</Text>
              </Col>
              <Col span={12}>
                <Text strong>商户ID: </Text>
                <Text>{merchantId}</Text>
              </Col>
            </Row>
          }
          type="info"
          showIcon
        />

        <Form
          form={form}
          layout="vertical"
          requiredMark={false}
          initialValues={{
            type: 'increase',
            walletType: 'available',
            symbol: symbol || undefined,
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="代币符号"
                name="symbol"
                rules={[{ required: true, message: '请选择代币符号' }]}
              >
                <Select
                  placeholder="请选择代币符号"
                  showSearch
                  allowClear
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  options={COMMON_SYMBOLS}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="调整类型"
                name="type"
                rules={[{ required: true, message: '请选择调整类型' }]}
              >
                <Select
                  placeholder="请选择调整类型"
                  options={ADJUST_TYPE_OPTIONS}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="钱包类型"
                name="walletType"
                rules={[{ required: true, message: '请选择钱包类型' }]}
              >
                <Select
                  placeholder="请选择钱包类型"
                  options={WALLET_TYPE_OPTIONS}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="调整金额"
                name="amount"
                rules={AMOUNT_RULES}
              >
                <Input
                  placeholder="请输入调整金额"
                  suffix={watchSymbol || ''}
                  style={{ fontFamily: 'monospace' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="调整原因"
            name="reason"
            rules={REASON_RULES}
          >
            <TextArea
              placeholder="请详细说明调整原因，至少5个字符"
              rows={3}
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item
            label="参考信息"
            name="reference"
            rules={[{ max: 100, message: '参考信息最多100个字符' }]}
          >
            <Input
              placeholder="可选，如相关工单号、申请单号等"
              maxLength={100}
            />
          </Form.Item>
        </Form>

        {/* 操作预览 */}
        {watchType && watchAmount && watchSymbol && watchWalletType && (
          <>
            <Divider orientation="left" orientationMargin="0">
              操作预览
            </Divider>
            <Alert
              message={
                <Space direction="vertical" size="small">
                  <Text>
                    将对商户 <Text strong>{merchantName}</Text> 的{' '}
                    <Text strong style={{ color: '#1890ff' }}>{watchSymbol}</Text>{' '}
                    <Text strong>
                      {watchWalletType === 'available' ? '可用余额' : '冻结余额'}
                    </Text>{' '}
                    进行{' '}
                    <Text
                      strong
                      style={{
                        color: watchType === 'increase' ? '#52c41a' : '#ff4d4f',
                      }}
                    >
                      {watchType === 'increase' ? '增加' : '减少'}
                    </Text>{' '}
                    <Text strong style={{ fontFamily: 'monospace' }}>
                      {watchAmount} {watchSymbol}
                    </Text>
                  </Text>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    操作完成后将生成相应的交易记录，请确认无误后提交
                  </Text>
                </Space>
              }
              type={watchType === 'increase' ? 'success' : 'warning'}
              showIcon
            />
          </>
        )}

        {/* 安全提示 */}
        <Alert
          message="安全提示"
          description={
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              <li>余额调整操作将被完整记录，包括操作人、时间、原因等信息</li>
              <li>减少余额时会验证当前余额是否充足</li>
              <li>操作完成后无法撤销，请谨慎操作</li>
            </ul>
          }
          type="warning"
          showIcon
        />
      </Space>
    </Modal>
  );
};

export default AdjustBalanceModal;