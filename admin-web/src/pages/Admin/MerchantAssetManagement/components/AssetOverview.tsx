import { Card, Col, Row, Statistic, Tag, Typography, Button, Space, Tooltip } from 'antd';
import { ReloadOutlined, PlusOutlined, HistoryOutlined, DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import React from 'react';
import type { MerchantAssetsOverview } from '../types';

const { Title, Text } = Typography;

interface AssetOverviewProps {
  data: MerchantAssetsOverview | null;
  loading: boolean;
  onRefresh: () => void;
  onAdjustBalance: (symbol: string) => void;
  onViewTransactions: () => void;
}

const AssetOverview: React.FC<AssetOverviewProps> = ({
  data,
  loading,
  onRefresh,
  onAdjustBalance,
  onViewTransactions,
}) => {
  if (!data) {
    return (
      <Card loading={loading}>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Text type="secondary">暂无资产数据</Text>
        </div>
      </Card>
    );
  }

  return (
    <div>
      {/* 商户信息头部 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space size="large">
              <div>
                <Title level={4} style={{ margin: 0 }}>
                  {data.merchantName}
                </Title>
                <Text type="secondary">商户ID: {data.merchantId}</Text>
              </div>
              <div>
                <Statistic
                  title="资产种类"
                  value={data.totalAssets}
                  suffix="种"
                  valueStyle={{ fontSize: '24px' }}
                />
              </div>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<DownloadOutlined />}
                onClick={() => history.push('/admin/deposit-records')}
              >
                商户内充记录
              </Button>
              <Button
                icon={<UploadOutlined />}
                onClick={() => history.push('/admin/withdraw-records')}
              >
                商户下发记录
              </Button>
              <Button
                icon={<HistoryOutlined />}
                onClick={onViewTransactions}
              >
                交易记录
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={onRefresh}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 资产列表 */}
      <Row gutter={[16, 16]}>
        {data.assets.map((asset) => (
          <Col xs={24} sm={12} lg={8} xl={6} key={asset.symbol}>
            <Card
              hoverable
              actions={[
                <Tooltip title="调整余额" key="adjust">
                  <Button
                    type="text"
                    icon={<PlusOutlined />}
                    onClick={() => onAdjustBalance(asset.symbol)}
                  >
                    调整
                  </Button>
                </Tooltip>,
              ]}
            >
              <div style={{ textAlign: 'center' }}>
                {/* 代币符号 */}
                <div style={{ marginBottom: 16 }}>
                  <Tag color="blue" style={{ fontSize: '16px', padding: '4px 12px' }}>
                    {asset.symbol}
                  </Tag>
                </div>

                {/* 总余额 */}
                <div style={{ marginBottom: 16 }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    总余额
                  </Text>
                  <div
                    style={{
                      fontSize: '20px',
                      fontWeight: 'bold',
                      fontFamily: 'monospace',
                      color: '#1890ff',
                    }}
                  >
                    {asset.formattedTotalBalance}
                  </div>
                </div>

                {/* 详细余额 */}
                <Row gutter={8}>
                  <Col span={12}>
                    <div style={{ textAlign: 'center' }}>
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        可用
                      </Text>
                      <div
                        style={{
                          fontSize: '14px',
                          fontFamily: 'monospace',
                          color: '#52c41a',
                        }}
                      >
                        {asset.formattedAvailableBalance}
                      </div>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div style={{ textAlign: 'center' }}>
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        冻结
                      </Text>
                      <div
                        style={{
                          fontSize: '14px',
                          fontFamily: 'monospace',
                          color: '#fa8c16',
                        }}
                      >
                        {asset.formattedFrozenBalance}
                      </div>
                    </div>
                  </Col>
                </Row>

                {/* 小数位数和更新时间 */}
                <div style={{ marginTop: 12, fontSize: '11px' }}>
                  <Text type="secondary">
                    精度: {asset.decimalPlaces}位
                  </Text>
                  <br />
                  <Text type="secondary">
                    更新: {asset.lastUpdated}
                  </Text>
                </div>
              </div>
            </Card>
          </Col>
        ))}

        {/* 空状态提示 */}
        {data.assets.length === 0 && (
          <Col span={24}>
            <Card>
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <Text type="secondary">该商户暂无资产记录</Text>
              </div>
            </Card>
          </Col>
        )}
      </Row>
    </div>
  );
};

export default AssetOverview;