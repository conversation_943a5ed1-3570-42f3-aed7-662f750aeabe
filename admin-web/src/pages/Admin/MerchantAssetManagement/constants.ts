import type { ProColumns } from '@ant-design/pro-components';
import { Tag } from 'antd';
import React from 'react';
import type { MerchantWalletItem, AdjustType, WalletType } from './types';

/**
 * 调整类型映射
 */
export const ADJUST_TYPE_MAP: Record<AdjustType, { text: string; color: string }> = {
  increase: { text: '增加', color: 'green' },
  decrease: { text: '减少', color: 'red' },
};

/**
 * 钱包类型映射
 */
export const WALLET_TYPE_MAP: Record<WalletType, { text: string; color: string }> = {
  available: { text: '可用余额', color: 'blue' },
  frozen: { text: '冻结余额', color: 'orange' },
};

/**
 * 商户钱包列表表格列定义
 */
export const merchantWalletColumns: ProColumns<MerchantWalletItem>[] = [
  {
    title: '钱包ID',
    dataIndex: 'walletId',
    key: 'walletId',
    search: false,
    width: 100,
    align: 'center',
    copyable: true,
  },
  {
    title: '商户ID',
    dataIndex: 'merchantId',
    key: 'merchantId',
    search: false,
    width: 100,
    align: 'center',
    copyable: true,
  },
  {
    title: '商户名称',
    dataIndex: 'merchantName',
    key: 'merchantName',
    ellipsis: true,
    width: 150,
    align: 'center',
    hideInSearch: false,
    fieldProps: {
      placeholder: '请输入商户名称',
    },
  },
  {
    title: '代币符号',
    dataIndex: 'symbol',
    key: 'symbol',
    width: 120,
    align: 'center',
    hideInSearch: false,
    fieldProps: {
      placeholder: '请输入代币符号',
    },
    render: (_, record) => (
      React.createElement(Tag, { color: 'blue' }, record.symbol)
    ),
  },
  {
    title: '可用余额',
    dataIndex: 'formattedAvailableBalance',
    key: 'formattedAvailableBalance',
    search: false,
    width: 150,
    align: 'right',
    render: (_, record) => (
      React.createElement('span', { 
        style: { fontFamily: 'monospace', fontWeight: 'bold' } 
      }, `${record.formattedAvailableBalance} ${record.symbol}`)
    ),
  },
  {
    title: '冻结余额',
    dataIndex: 'formattedFrozenBalance',
    key: 'formattedFrozenBalance',
    search: false,
    width: 150,
    align: 'right',
    render: (_, record) => (
      React.createElement('span', { 
        style: { fontFamily: 'monospace', fontWeight: 'bold' } 
      }, `${record.formattedFrozenBalance} ${record.symbol}`)
    ),
  },
  {
    title: '总余额',
    dataIndex: 'formattedTotalBalance',
    key: 'formattedTotalBalance',
    search: false,
    width: 150,
    align: 'right',
    render: (_, record) => (
      React.createElement('span', { 
        style: { fontFamily: 'monospace', fontWeight: 'bold', color: '#1890ff' } 
      }, `${record.formattedTotalBalance} ${record.symbol}`)
    ),
  },
  {
    title: '小数位数',
    dataIndex: 'decimalPlaces',
    key: 'decimalPlaces',
    search: false,
    width: 100,
    align: 'center',
    render: (_, record) => (
      React.createElement(Tag, { color: 'default' }, record.decimalPlaces.toString())
    ),
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    search: false,
    width: 180,
    align: 'center',
  },
  {
    title: '更新时间',
    dataIndex: 'updatedAt',
    key: 'updatedAt',
    search: false,
    width: 180,
    align: 'center',
  },
];

/**
 * 表格滚动配置
 */
export const TABLE_SCROLL = {
  x: 1400,
  y: 600,
};

/**
 * 默认分页配置
 */
export const DEFAULT_PAGE_SIZE = 10;

/**
 * 常用代币符号选项
 */
export const COMMON_SYMBOLS = [
  { label: 'USDT', value: 'USDT' },
  { label: 'BTC', value: 'BTC' },
  { label: 'ETH', value: 'ETH' },
  { label: 'TRX', value: 'TRX' },
];

/**
 * 调整类型选项
 */
export const ADJUST_TYPE_OPTIONS = [
  { label: '增加', value: 'increase' },
  { label: '减少', value: 'decrease' },
];

/**
 * 钱包类型选项
 */
export const WALLET_TYPE_OPTIONS = [
  { label: '可用余额', value: 'available' },
  { label: '冻结余额', value: 'frozen' },
];

/**
 * 金额输入规则
 */
export const AMOUNT_RULES = [
  { required: true, message: '请输入调整金额' },
  { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的数字金额' },
  { 
    validator: (_: any, value: string) => {
      if (value && parseFloat(value) <= 0) {
        return Promise.reject(new Error('金额必须大于0'));
      }
      return Promise.resolve();
    }
  },
];

/**
 * 原因输入规则
 */
export const REASON_RULES = [
  { required: true, message: '请输入调整原因' },
  { min: 5, message: '调整原因至少5个字符' },
  { max: 500, message: '调整原因最多500个字符' },
];