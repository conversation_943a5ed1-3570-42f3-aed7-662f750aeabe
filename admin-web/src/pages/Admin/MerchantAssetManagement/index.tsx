import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, Space, Tabs, message } from 'antd';
import { ExportOutlined, ReloadOutlined } from '@ant-design/icons';
import React, { useRef, useState, useEffect } from 'react';
import type { ActionType } from '@ant-design/pro-components';
import { useNavigate, useLocation } from 'react-router-dom';
import AssetOverview from './components/AssetOverview';
import AdjustBalanceModal from './components/AdjustBalanceModal';
import { merchantWalletColumns, TABLE_SCROLL, DEFAULT_PAGE_SIZE } from './constants';
import { useMerchantAssetManagement, useMerchantWalletsList, useAdjustBalanceForm } from './hooks';
import type { 
  MerchantWalletItem, 
  MerchantWalletsQueryParams, 
  AdjustBalanceFormData,
} from './types';

const { TabPane } = Tabs;

const MerchantAssetManagement: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const actionRef = useRef<ActionType>();

  // 获取商户ID（从URL参数或查询参数）
  const getMerchantIdFromUrl = () => {
    const searchParams = new URLSearchParams(location.search);
    return searchParams.get('merchantId') ? parseInt(searchParams.get('merchantId')!, 10) : null;
  };

  const [currentMerchantId, setCurrentMerchantId] = useState<number | null>(getMerchantIdFromUrl());
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [adjustModalVisible, setAdjustModalVisible] = useState(false);
  const [selectedSymbol, setSelectedSymbol] = useState<string | undefined>();

  // 自定义 Hooks
  const {
    loading: assetsLoading,
    assetsOverview,
    fetchMerchantAssets,
    adjustMerchantBalance,
  } = useMerchantAssetManagement();

  const { loading: walletsLoading, fetchMerchantWallets } = useMerchantWalletsList();
  const { adjusting, submitAdjustment } = useAdjustBalanceForm();

  // 页面初始化
  useEffect(() => {
    if (currentMerchantId) {
      fetchMerchantAssets(currentMerchantId);
    }
  }, [currentMerchantId, fetchMerchantAssets]);

  // 处理资产概览刷新
  const handleRefreshAssets = () => {
    if (currentMerchantId) {
      fetchMerchantAssets(currentMerchantId);
    }
  };

  // 处理打开调整余额弹窗
  const handleOpenAdjustModal = (symbol?: string) => {
    setSelectedSymbol(symbol);
    setAdjustModalVisible(true);
  };

  // 处理调整余额提交
  const handleAdjustSubmit = async (values: AdjustBalanceFormData) => {
    if (!currentMerchantId) {
      message.error('商户ID不能为空');
      return;
    }

    const request = {
      ...values,
    };

    const result = await submitAdjustment(currentMerchantId, request);
    if (result.success) {
      setAdjustModalVisible(false);
      setSelectedSymbol(undefined);
      // 刷新数据
      handleRefreshAssets();
      if (activeTab === 'wallets') {
        actionRef.current?.reload();
      }
    }
  };

  // 处理查看交易记录
  const handleViewTransactions = () => {
    if (currentMerchantId) {
      navigate(`/admin/merchant-transaction-management?merchantId=${currentMerchantId}`);
    }
  };

  // 如果没有商户ID，显示选择提示
  if (!currentMerchantId) {
    return (
      <PageContainer
        header={{
          title: '商户资产管理',
          subTitle: '管理商户的资产余额和资金调整',
        }}
      >
        <div style={{ textAlign: 'center', padding: '100px 0' }}>
          <h3>请先选择要管理的商户</h3>
          <p>
            您可以从{' '}
            <a 
              onClick={() => navigate('/admin/merchant-list-management')}
              style={{ cursor: 'pointer' }}
            >
              商户列表
            </a>{' '}
            进入资产管理页面
          </p>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer
      header={{
        title: '商户资产管理',
        subTitle: `管理商户 ${assetsOverview?.merchantName || currentMerchantId} 的资产余额`,
        extra: [
          <Button
            key="back"
            onClick={() => navigate('/admin/merchant-list-management')}
          >
            返回商户列表
          </Button>,
        ],
      }}
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'overview',
            label: '资产概览',
            children: (
              <AssetOverview
                data={assetsOverview}
                loading={assetsLoading}
                onRefresh={handleRefreshAssets}
                onAdjustBalance={handleOpenAdjustModal}
                onViewTransactions={handleViewTransactions}
              />
            ),
          },
          {
            key: 'wallets',
            label: '钱包列表',
            children: (
              <ProTable<MerchantWalletItem>
                columns={[
                  ...merchantWalletColumns,
                  {
                    title: '操作',
                    key: 'action',
                    valueType: 'option',
                    width: 120,
                    align: 'center',
                    fixed: 'right',
                    render: (_, record) => (
                      <Space size="small">
                        <Button
                          type="link"
                          size="small"
                          onClick={() => handleOpenAdjustModal(record.symbol)}
                        >
                          调整
                        </Button>
                      </Space>
                    ),
                  },
                ]}
                actionRef={actionRef}
                cardBordered
                request={async (params, sorter, filter) => {
                  const queryParams: MerchantWalletsQueryParams = {
                    page: params.current,
                    pageSize: params.pageSize,
                    merchantId: currentMerchantId || undefined,
                    merchantName: params.merchantName,
                    symbol: params.symbol,
                    hasBalance: params.hasBalance,
                    export: params.export,
                  };
                  
                  return fetchMerchantWallets(queryParams);
                }}
                loading={walletsLoading}
                rowKey="walletId"
                search={{
                  labelWidth: 'auto',
                  defaultCollapsed: false,
                  span: {
                    xs: 24,
                    sm: 12,
                    md: 8,
                    lg: 6,
                    xl: 6,
                    xxl: 6,
                  },
                }}
                scroll={TABLE_SCROLL}
                options={{
                  setting: {
                    listsHeight: 400,
                  },
                  density: true,
                  fullScreen: true,
                  reload: true,
                }}
                pagination={{
                  pageSize: DEFAULT_PAGE_SIZE,
                  showSizeChanger: true,
                  showQuickJumper: true,
                }}
                dateFormatter="string"
                headerTitle="商户钱包列表"
                toolBarRender={() => [
                  <Button
                    key="adjust"
                    type="primary"
                    onClick={() => handleOpenAdjustModal()}
                  >
                    调整余额
                  </Button>,
                  <Button
                    key="refresh"
                    icon={<ReloadOutlined />}
                    onClick={() => actionRef.current?.reload()}
                  >
                    刷新
                  </Button>,
                ]}
              />
            ),
          },
        ]}
      />

      {/* 调整余额弹窗 */}
      <AdjustBalanceModal
        visible={adjustModalVisible}
        merchantId={currentMerchantId}
        merchantName={assetsOverview?.merchantName || `商户${currentMerchantId}`}
        symbol={selectedSymbol}
        onCancel={() => {
          setAdjustModalVisible(false);
          setSelectedSymbol(undefined);
        }}
        onSubmit={handleAdjustSubmit}
        loading={adjusting}
      />
    </PageContainer>
  );
};

export default MerchantAssetManagement;