/**
 * 商户资产管理相关类型定义
 */

import type { 
  AdminApiApiSystemV1MerchantAssetItem,
  AdminApiApiSystemV1MerchantWalletListItem,
  AdminApiApiSystemV1AdjustMerchantBalanceReqType,
  AdminApiApiSystemV1AdjustMerchantBalanceReqWalletType,
} from '@/api/model';

// 使用生成的枚举类型
export type AdjustType = AdminApiApiSystemV1AdjustMerchantBalanceReqType;
export const AdjustType = {
  Increase: 'increase' as AdjustType,
  Decrease: 'decrease' as AdjustType,
};

export type WalletType = AdminApiApiSystemV1AdjustMerchantBalanceReqWalletType;
export const WalletType = {
  Available: 'available' as WalletType,
  Frozen: 'frozen' as WalletType,
};

// 商户资产项目 - 使用生成的类型
export type MerchantAssetItem = AdminApiApiSystemV1MerchantAssetItem;

// 商户资产概览
export interface MerchantAssetsOverview {
  merchantId: number;
  merchantName: string;
  assets: MerchantAssetItem[];
  totalAssets: number;
}

// 商户钱包列表项 - 使用生成的类型
export type MerchantWalletItem = AdminApiApiSystemV1MerchantWalletListItem;

// 资金调整请求参数
export interface AdjustBalanceRequest {
  symbol: string;
  amount: string | number;
  type: AdjustType;
  walletType: WalletType;
  reason: string;
  reference?: string;
}

// 资金调整响应
export interface AdjustBalanceResponse {
  success: boolean;
  transactionId: number;
  balanceBefore: string;
  balanceAfter: string;
  formattedBalanceBefore: string;
  formattedBalanceAfter: string;
}

// 商户钱包查询参数
export interface MerchantWalletsQueryParams {
  page?: number;
  pageSize?: number;
  merchantId?: number;
  merchantName?: string;
  symbol?: string;
  hasBalance?: boolean;
  dateRange?: [string, string];
  export?: boolean;
}

// 商户资产管理页面路由参数
export interface AssetManagementRouteParams {
  merchantId?: string;
}

// 调整余额表单数据
export interface AdjustBalanceFormData {
  symbol: string;
  amount: string;
  type: AdjustType;
  walletType: WalletType;
  reason: string;
  reference?: string;
}