import {
  DeleteOutlined,
  ImportOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Input,
  InputNumber,
  Modal,
  Space,
  Switch,
  message,
} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { ValueType, ValueTypes } from '../types';
import TableInputRenderer from './TableInputRenderer';
import NumberRangeInput from './NumberRangeInput';

// MapInputRenderer component for handling key-value pairs
interface MapInputRendererProps {
  value?: any;
  onChange?: (newValue: any) => void;
  disabled?: boolean;
}

interface KeyValuePair {
  key: string;
  value: string;
  id: string; // For React key prop
}

const MapInputRenderer: React.FC<MapInputRendererProps> = ({
  value,
  onChange,
  disabled = false,
}) => {
  const [pairs, setPairs] = useState<KeyValuePair[]>([]);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [jsonInput, setJsonInput] = useState('');
  const isInternalUpdate = useRef(false);
  const lastExternalValue = useRef<any>(undefined);

  // Parse the input value and convert to key-value pairs
  useEffect(() => {
    // Skip if this is an internal update
    if (isInternalUpdate.current) {
      isInternalUpdate.current = false;
      return;
    }

    // Skip if the value hasn't actually changed from external source
    if (lastExternalValue.current === value) {
      return;
    }

    lastExternalValue.current = value;

    if (!value) {
      setPairs([{ key: '', value: '', id: `pair-${Date.now()}-0` }]);
      return;
    }

    try {
      let parsedValue: Record<string, any>;

      if (typeof value === 'string') {
        // Try to parse as JSON first
        try {
          parsedValue = JSON.parse(value);
        } catch {
          // If JSON parsing fails, treat as simple key=value format
          parsedValue = {};
          const lines = value.split('\n').filter((line) => line.trim());
          for (const line of lines) {
            const [k, ...vParts] = line.split('=');
            if (k && vParts.length > 0) {
              parsedValue[k.trim()] = vParts.join('=').trim();
            }
          }
        }
      } else if (typeof value === 'object' && value !== null) {
        parsedValue = value;
      } else {
        parsedValue = {};
      }

      const newPairs = Object.entries(parsedValue).map(([k, v], index) => ({
        key: k,
        value: String(v),
        id: `pair-${Date.now()}-${index}`,
      }));

      // Always have at least one empty pair for adding new entries
      if (newPairs.length === 0) {
        newPairs.push({ key: '', value: '', id: `pair-${Date.now()}-0` });
      }

      setPairs(newPairs);
    } catch (error) {
      console.error('Error parsing map value:', error);
      setPairs([{ key: '', value: '', id: `pair-${Date.now()}-0` }]);
    }
  }, [value]);

  // Convert pairs back to object and notify parent
  const updateValue = (newPairs: KeyValuePair[]) => {
    setPairs(newPairs);

    if (onChange) {
      // Filter out empty pairs and convert to object
      const validPairs = newPairs.filter((pair) => pair.key.trim() !== '');
      const mapObject: Record<string, string> = {};

      validPairs.forEach((pair) => {
        if (pair.key.trim()) {
          mapObject[pair.key.trim()] = pair.value;
        }
      });

      // Convert to JSON string for storage
      const newValue = JSON.stringify(mapObject);

      // Only call onChange if the value actually changed
      if (newValue !== value) {
        isInternalUpdate.current = true;
        lastExternalValue.current = newValue;
        onChange(newValue);
      }
    }
  };

  const addPair = () => {
    const newPairs = [
      ...pairs,
      { key: '', value: '', id: `pair-${Date.now()}-${pairs.length}` },
    ];
    updateValue(newPairs);
  };

  const removePair = (id: string) => {
    if (pairs.length <= 1) return; // Keep at least one pair
    const newPairs = pairs.filter((pair) => pair.id !== id);
    updateValue(newPairs);
  };

  const updatePair = (id: string, field: 'key' | 'value', newValue: string) => {
    const newPairs = pairs.map((pair) =>
      pair.id === id ? { ...pair, [field]: newValue } : pair,
    );
    updateValue(newPairs);
  };

  const handleJsonImport = () => {
    try {
      if (!jsonInput.trim()) {
        message.error('请输入 JSON 内容');
        return;
      }

      const jsonData = JSON.parse(jsonInput);

      if (
        typeof jsonData !== 'object' ||
        jsonData === null ||
        Array.isArray(jsonData)
      ) {
        message.error('JSON 内容必须是一个对象格式的键值对');
        return;
      }

      // Convert JSON object to pairs
      const newPairs = Object.entries(jsonData).map(([k, v], index) => ({
        key: k,
        value: String(v),
        id: `pair-${Date.now()}-${index}`,
      }));

      // Add an empty pair for new entries if needed
      if (
        newPairs.length === 0 ||
        newPairs.every((pair) => pair.key.trim() !== '')
      ) {
        newPairs.push({
          key: '',
          value: '',
          id: `pair-${Date.now()}-${newPairs.length}`,
        });
      }

      updateValue(newPairs);
      message.success(`成功导入 ${Object.keys(jsonData).length} 个键值对`);

      // Close modal and clear input
      setImportModalVisible(false);
      setJsonInput('');
    } catch (error) {
      message.error('JSON 格式错误，请检查输入内容');
      console.error('JSON import error:', error);
    }
  };

  const showImportModal = () => {
    setImportModalVisible(true);
    setJsonInput('');
  };

  const handleModalCancel = () => {
    setImportModalVisible(false);
    setJsonInput('');
  };

  return (
    <Card size="small" style={{ backgroundColor: '#fafafa' }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 8,
        }}
      >
        <div style={{ color: '#666', fontSize: '12px' }}>
          键值对映射 (类似 Go 语言的 map)
        </div>
        <Button
          size="small"
          icon={<ImportOutlined />}
          type="link"
          disabled={disabled}
          onClick={showImportModal}
          style={{ padding: '0 4px', fontSize: '12px' }}
        >
          导入JSON
        </Button>
      </div>
      <Space direction="vertical" style={{ width: '100%' }}>
        {pairs.map((pair) => (
          <div
            key={pair.id}
            style={{ display: 'flex', gap: 8, alignItems: 'center' }}
          >
            <Input
              placeholder="键名 (如: 客服1)"
              value={pair.key}
              onChange={(e) => updatePair(pair.id, 'key', e.target.value)}
              disabled={disabled}
              style={{ flex: 1 }}
            />
            <span style={{ color: '#666' }}>:</span>
            <Input
              placeholder="值 (如: 1)"
              value={pair.value}
              onChange={(e) => updatePair(pair.id, 'value', e.target.value)}
              disabled={disabled}
              style={{ flex: 1 }}
            />
            {pairs.length > 1 && (
              <Button
                type="text"
                icon={<DeleteOutlined />}
                onClick={() => removePair(pair.id)}
                disabled={disabled}
                size="small"
                danger
              />
            )}
          </div>
        ))}
        <Button
          type="dashed"
          icon={<PlusOutlined />}
          onClick={addPair}
          disabled={disabled}
          size="small"
          style={{ width: '100%' }}
        >
          添加键值对
        </Button>
      </Space>

      {/* JSON Import Modal */}
      <Modal
        title="导入 JSON 键值对"
        open={importModalVisible}
        onOk={handleJsonImport}
        onCancel={handleModalCancel}
        okText="导入"
        cancelText="取消"
        width={600}
      >
        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8, color: '#666' }}>
            请输入有效的 JSON 对象格式，例如：
          </div>
          <div
            style={{
              backgroundColor: '#f5f5f5',
              padding: 8,
              borderRadius: 4,
              fontFamily: 'monospace',
              fontSize: '12px',
              color: '#666',
            }}
          >
            {`{
  "key1": "value1",
  "key2": "value2",
  "config_enabled": "true"
}`}
          </div>
        </div>
        <Input.TextArea
          value={jsonInput}
          onChange={(e) => setJsonInput(e.target.value)}
          placeholder="请粘贴 JSON 内容..."
          rows={8}
          style={{ fontFamily: 'monospace' }}
        />
      </Modal>
    </Card>
  );
};

interface ValueInputRendererProps {
  valueType: ValueType;
  value?: any; // Made optional - Form will inject this
  onChange?: (newValue: any) => void; // Made optional - Form will inject this
  disabled?: boolean;
}

const ValueInputRenderer: React.FC<ValueInputRendererProps> = ({
  valueType,
  value, // Receive value (might be undefined initially if Form hasn't injected)
  onChange, // Receive onChange (might be undefined initially)
  disabled = false,
}) => {
  // Internal handler to ensure onChange is called if provided
  const handleChange = (newValue: any) => {
    if (onChange) {
      onChange(newValue);
    }
  };

  switch (valueType) {
    case ValueTypes.NUMBER:
      return (
        <InputNumber
          style={{ width: '100%' }}
          value={typeof value === 'number' ? value : Number(value || 0)}
          onChange={(num) => handleChange(num ?? 0)} // Use internal handler
          disabled={disabled}
        />
      );
    case ValueTypes.BOOLEAN: {
      // Handle numeric boolean ('0'/'1') as per requirements
      // Also handle string boolean ('true'/'false') for backward compatibility
      const isChecked =
        typeof value === 'boolean'
          ? value
          : value === '1' || String(value).toLowerCase() === 'true';
      return (
        <Switch
          checked={isChecked}
          onChange={handleChange} // Use internal handler
          checkedChildren="是"
          unCheckedChildren="否"
          disabled={disabled}
        />
      );
    } // Added block scope
    case ValueTypes.TEXTAREA:
      return (
        <Input.TextArea
          rows={4}
          value={String(value ?? '')}
          onChange={(e) => handleChange(e.target.value)} // Use internal handler
          disabled={disabled}
          placeholder="请输入多行文本值"
        />
      );
    case ValueTypes.JSON: {
      // Added block scope
      let jsonString = value;
      if (typeof value === 'object' && value !== null) {
        try {
          jsonString = JSON.stringify(value, null, 2);
        } catch (e) {
          jsonString = String(value);
        }
      } else {
        jsonString = String(value ?? '');
      }
      return (
        <Input.TextArea
          rows={6}
          value={jsonString}
          onChange={(e) => handleChange(e.target.value)} // Use internal handler
          disabled={disabled}
          placeholder="请输入 JSON 格式字符串"
          style={{ fontFamily: 'monospace' }}
        />
      );
    } // Added block scope
    case ValueTypes.PASSWORD: // Add PASSWORD case
      return (
        <Input.Password
          value={String(value ?? '')}
          onChange={(e) => handleChange(e.target.value)}
          disabled={disabled}
          placeholder="输入密码值"
        />
      );
    case ValueTypes.MAP: // Add MAP case for key-value pairs
      return (
        <MapInputRenderer
          value={value}
          onChange={handleChange}
          disabled={disabled}
        />
      );
    case ValueTypes.TABLE: // NEW: Add TABLE case for dynamic table structures
      return (
        <TableInputRenderer
          value={String(value ?? '')}
          onChange={handleChange}
          disabled={disabled}
        />
      );
    case ValueTypes.NUMBER_RANGE: // NEW: Add NUMBER_RANGE case for min-max number values
      return (
        <NumberRangeInput
          value={String(value ?? '')}
          onChange={handleChange}
          disabled={disabled}
        />
      );
    case ValueTypes.TEXT:
    default:
      return (
        <Input
          value={String(value ?? '')}
          onChange={(e) => handleChange(e.target.value)} // Use internal handler
          disabled={disabled}
          placeholder="请输入文本值"
        />
      );
  }
};

export default ValueInputRenderer;
