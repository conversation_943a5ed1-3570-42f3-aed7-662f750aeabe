import { Form, Input, Modal, Select, Typography } from 'antd';
import type { FormInstance } from 'antd/es/form';
import React, { useEffect, useState } from 'react';
import {
  ConfigCategory,
  ConfigItem,
  ConfigItemFormData,
  ValueTypes,
} from '../types';
import ValueInputRenderer from './ValueInputRenderer'; // Assuming ValueInputRenderer is in the same directory

const { Option } = Select;

interface ConfigItemModalProps {
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
  confirmLoading: boolean;
  editingRecord: ConfigItem | null;
  itemForm: FormInstance<ConfigItemFormData>;
  activeCategoryId: string | undefined;
  categories: ConfigCategory[];
}

const ConfigItemModal: React.FC<ConfigItemModalProps> = ({
  visible,
  onOk,
  onCancel,
  confirmLoading,
  editingRecord,
  itemForm,
  activeCategoryId,
  categories,
}) => {
  const currentCategory = categories.find((c) => c.id === activeCategoryId);
  const [isPasswordPlaceholder, setIsPasswordPlaceholder] = useState(false);
  const [initialPasswordValue, setInitialPasswordValue] = useState<
    string | undefined
  >(undefined);
  const [selectedValueType, setSelectedValueType] = useState<string>(
    editingRecord?.valueType || ValueTypes.TEXT
  );

  // Effect to handle password placeholder for editing
  useEffect(() => {
    if (editingRecord && editingRecord.valueType === ValueTypes.PASSWORD) {
      const maskedPasswordPlaceholder = '******'; // Standard placeholder
      setInitialPasswordValue(editingRecord.value); // Store original value from API

      if (editingRecord.value === maskedPasswordPlaceholder) {
        itemForm.setFieldsValue({ value: '' }); // Set to empty for user input
        setIsPasswordPlaceholder(true);
      } else {
        // If it's not the placeholder, it's a real value (e.g. during creation before save)
        itemForm.setFieldsValue({ value: editingRecord.value });
        setIsPasswordPlaceholder(false);
      }
    } else if (editingRecord) {
      // For other types or if not editing password, set value normally
      itemForm.setFieldsValue({ value: editingRecord.value });
      setIsPasswordPlaceholder(false);
      setInitialPasswordValue(undefined);
      setSelectedValueType(editingRecord.valueType); // Update selected type for editing
    } else {
      // For new records
      setIsPasswordPlaceholder(false);
      setInitialPasswordValue(undefined);
    }
  }, [editingRecord, itemForm, visible]); // Rerun when modal becomes visible or editingRecord changes

  // Reset states when modal is not visible or editingRecord is null
  useEffect(() => {
    if (!visible) {
      setIsPasswordPlaceholder(false);
      setInitialPasswordValue(undefined);
      setSelectedValueType(ValueTypes.TEXT); // Reset to default value type
      // itemForm.resetFields(['value']); // Optionally reset value field specifically
    }
  }, [visible]);

  // Calculate modal width based on selected value type
  const getModalWidth = () => {
    if (selectedValueType === ValueTypes.TABLE) {
      return 1600; // Larger width for table type
    } else if (selectedValueType === ValueTypes.JSON || selectedValueType === ValueTypes.MAP) {
      return 800; // Medium width for JSON/MAP types
    }
    return 600; // Default width for other types
  };

  return (
    <Modal
      title={editingRecord ? '编辑配置项' : '新建配置项'}
      open={visible}
      onOk={onOk}
      onCancel={onCancel}
      confirmLoading={confirmLoading}
      afterClose={() => {
        // Clean up modal state after it's fully closed
        itemForm.resetFields();
        setSelectedValueType(ValueTypes.TEXT);
      }}
      width={getModalWidth()}
    >
      <Form
        form={itemForm}
        layout="vertical"
        name="config_item_form"
        initialValues={{ valueType: ValueTypes.TEXT }}
      >
        {/* 显示当前分类的前缀信息 */}
        {currentCategory && !editingRecord && (
          <div
            style={{
              marginBottom: 16,
              padding: 12,
              backgroundColor: '#f6f6f6',
              borderRadius: 6,
            }}
          >
            <div style={{ color: '#666', fontSize: '14px', marginBottom: 4 }}>
              当前分类前缀（自动添加）:
            </div>
            <div
              style={{ color: '#1890ff', fontSize: '16px', fontWeight: 'bold' }}
            >
              {currentCategory.categoryKey}.
            </div>
          </div>
        )}

        <Form.Item
          name="key"
          label={editingRecord ? '配置键 (Key)' : '配置键名称'}
          rules={[
            { required: true, message: '请输入配置键名称!' },
            () => ({
              validator(_, value) {
                if (editingRecord) {
                  return Promise.resolve();
                }
                if (!value || value.trim() === '') {
                  return Promise.reject(new Error('请输入配置键名称!'));
                }
                // For new items, validate that the suffix doesn't contain dots or invalid characters
                if (value.includes('.')) {
                  return Promise.reject(
                    new Error('配置键名称不能包含点号(.)!'),
                  );
                }
                // 验证是否为有效的标识符格式
                if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(value)) {
                  return Promise.reject(
                    new Error(
                      '配置键名称只能包含字母、数字和下划线，且必须以字母或下划线开头!',
                    ),
                  );
                }
                return Promise.resolve();
              },
            }),
          ]}
          tooltip={
            editingRecord
              ? '配置键不可修改'
              : '输入配置键名称，将自动与分类前缀组合'
          }
          extra={
            !editingRecord && currentCategory
              ? `完整配置键将为: ${currentCategory.categoryKey}.您输入的名称`
              : undefined
          }
        >
          {editingRecord ? (
            // For editing, show the full key as disabled input
            <Input
              value={editingRecord.key}
              disabled={true}
              style={{ color: '#666' }}
            />
          ) : (
            // For new items, only show input for the suffix part
            <Input
              placeholder="例如：state, enabled, max_count"
              autoComplete="off"
            />
          )}
        </Form.Item>

        <Form.Item
          name="valueType"
          label="值类型"
          rules={[{ required: true, message: '请选择值类型!' }]}
        >
          <Select
            disabled={!!editingRecord}
            onChange={(newType) => {
              // Added newType parameter
              setSelectedValueType(newType); // Update selected type state
              // Reset value when type changes
              // For password, if editing and it was a placeholder, keep it empty
              if (
                newType === ValueTypes.PASSWORD &&
                editingRecord &&
                initialPasswordValue === '******'
              ) {
                itemForm.setFieldsValue({ value: '' });
                setIsPasswordPlaceholder(true); // Ensure this is set if type changes to password
              } else {
                itemForm.setFieldsValue({ value: undefined });
                setIsPasswordPlaceholder(false); // Reset for other types
              }
            }}
          >
            {Object.entries(ValueTypes).map(([key, value]) => (
              <Option key={value} value={value}>
                {key}
              </Option>
            ))}
          </Select>
        </Form.Item>

        {/* Dynamic Value Input using Form.Item render props */}
        <Form.Item
          noStyle // Important: prevents Form.Item from rendering its own wrapper
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.valueType !== currentValues.valueType ||
            // Also update if the placeholder status changes for password type
            (currentValues.valueType === ValueTypes.PASSWORD &&
              prevValues.value !== currentValues.value)
          }
        >
          {({ getFieldValue }) => {
            const currentType = getFieldValue('valueType') || ValueTypes.TEXT;
            const currentValue = getFieldValue('value');

            // Determine if the "leave blank" message should show for password
            const showPasswordPlaceholderMessage =
              currentType === ValueTypes.PASSWORD &&
              editingRecord &&
              isPasswordPlaceholder;

            return (
              <>
                {showPasswordPlaceholderMessage && (
                  <Typography.Text
                    type="secondary"
                    style={{ display: 'block', marginBottom: '8px' }}
                  >
                    当前密码已设置。如需修改，请输入新密码。若不修改，请留空。
                  </Typography.Text>
                )}
                <Form.Item
                  name="value"
                  label="配置值"
                  rules={[
                    {
                      // Password can be empty if it was a placeholder and user hasn't typed
                      required: !(
                        showPasswordPlaceholderMessage && currentValue === ''
                      ),
                      message: '请输入配置值!',
                    },
                    // Type-specific validation
                    () => ({
                      validator(_, val) {
                        // Renamed value to val
                        const type = getFieldValue('valueType');
                        if (type === ValueTypes.JSON) {
                          try {
                            if (val && typeof val === 'string') JSON.parse(val);
                            else if (val && typeof val !== 'object') {
                              // return Promise.reject(new Error('无效的 JSON 输入!'));
                            }
                          } catch (e) {
                            return Promise.reject(
                              new Error('请输入有效的 JSON 格式字符串!'),
                            );
                          }
                        }
                        if (
                          type === ValueTypes.NUMBER &&
                          val !== null &&
                          val !== undefined &&
                          isNaN(Number(val))
                        ) {
                          return Promise.reject(new Error('请输入有效的数字!'));
                        }
                        if (type === ValueTypes.MAP) {
                          try {
                            if (val && typeof val === 'string') {
                              const parsed = JSON.parse(val);
                              if (
                                typeof parsed !== 'object' ||
                                parsed === null ||
                                Array.isArray(parsed)
                              ) {
                                return Promise.reject(
                                  new Error('MAP 类型必须是有效的键值对对象!'),
                                );
                              }
                            } else if (val && typeof val !== 'object') {
                              return Promise.reject(
                                new Error('MAP 类型必须是有效的键值对对象!'),
                              );
                            }
                          } catch (e) {
                            return Promise.reject(
                              new Error('请输入有效的键值对格式!'),
                            );
                          }
                        }
                        // No specific validation for PASSWORD here, just that it's a string
                        return Promise.resolve();
                      },
                    }),
                  ]}
                >
                  <ValueInputRenderer valueType={currentType} />
                </Form.Item>
              </>
            );
          }}
        </Form.Item>

        <Form.Item name="description" label="描述">
          <Input placeholder="对此配置项的简短说明" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ConfigItemModal;
