import { InputN<PERSON>ber, Space } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';

interface NumberRangeInputProps {
  value?: string; // JSON string with {min: number, max: number}
  onChange?: (value: string) => void;
  disabled?: boolean;
  placeholder?: {
    min?: string;
    max?: string;
  };
}

interface NumberRange {
  min: number;
  max: number;
}

const NumberRangeInput: React.FC<NumberRangeInputProps> = ({
  value,
  onChange,
  disabled = false,
  placeholder = {
    min: 'Min',
    max: 'Max',
  },
}) => {
  const [range, setRange] = useState<NumberRange>(() => {
    if (value) {
      try {
        const parsed = JSON.parse(value);
        if (typeof parsed.min === 'number' && typeof parsed.max === 'number') {
          return parsed;
        }
      } catch (e) {
        console.error('Failed to parse number range value:', e);
      }
    }
    return { min: 0, max: 100 };
  });

  // Update internal state when value prop changes
  useEffect(() => {
    if (value) {
      try {
        const parsed = JSON.parse(value);
        if (typeof parsed.min === 'number' && typeof parsed.max === 'number') {
          setRange(parsed);
        }
      } catch (e) {
        console.error('Failed to parse number range value:', e);
      }
    }
  }, [value]);

  const handleMinChange = useCallback(
    (newMin: number | null) => {
      const min = newMin ?? 0;
      const newRange = { ...range, min };
      
      // Ensure min is not greater than max
      if (min > newRange.max) {
        newRange.max = min;
      }
      
      setRange(newRange);
      onChange?.(JSON.stringify(newRange));
    },
    [range, onChange]
  );

  const handleMaxChange = useCallback(
    (newMax: number | null) => {
      const max = newMax ?? 0;
      const newRange = { ...range, max };
      
      // Ensure max is not less than min
      if (max < newRange.min) {
        newRange.min = max;
      }
      
      setRange(newRange);
      onChange?.(JSON.stringify(newRange));
    },
    [range, onChange]
  );

  return (
    <Space.Compact style={{ width: '100%' }}>
      <InputNumber
        value={range.min}
        onChange={handleMinChange}
        disabled={disabled}
        placeholder={placeholder.min}
        style={{ width: '50%' }}
        addonBefore="Min"
      />
      <InputNumber
        value={range.max}
        onChange={handleMaxChange}
        disabled={disabled}
        placeholder={placeholder.max}
        style={{ width: '50%' }}
        addonBefore="Max"
      />
    </Space.Compact>
  );
};

export default NumberRangeInput;