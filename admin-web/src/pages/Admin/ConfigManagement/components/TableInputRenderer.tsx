import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  SettingOutlined,
  DragOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  Modal,
  Row,
  Select,
  Space,
  Switch,
  Table,
  Tabs,
  Tooltip,
  message,
  DatePicker,
  Radio,
  Checkbox,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  TableStructure,
  TableColumn,
  TableRow,
  TableMeta,
  TableColumnType,
  RemoteDataConfig,
  SelectOption,
} from '../types';
import NumberRangeInput from './NumberRangeInput';
// Generate unique IDs without external dependency
const generateUuid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

const { TabPane } = Tabs;
const { Option } = Select;

interface TableInputRendererProps {
  value?: string; // JSON string representation of TableStructure
  onChange?: (value: string) => void;
  disabled?: boolean;
}

// Default table structure for new tables
const createDefaultTableStructure = (): TableStructure => ({
  columns: [
    {
      id: generateUuid(),
      name: 'Name',
      key: 'name',
      dataType: TableColumnType.TEXT,
      required: false,
      sortable: true,
    },
    {
      id: generateUuid(),
      name: 'Value',
      key: 'value',
      dataType: TableColumnType.TEXT,
      required: false,
      sortable: true,
    },
  ],
  rows: [], // Start with empty rows instead of a default empty row
  meta: {
    allowAddRows: true,
    allowDeleteRows: true,
    allowEditRows: true,
    allowAddColumns: true,
    allowDeleteColumns: true,
    allowEditColumns: true,
    showRowNumbers: true,
    paginated: false,
  },
});

// Parse table value from JSON string
const parseTableValue = (value?: string): TableStructure => {
  if (!value) {
    return createDefaultTableStructure();
  }
  
  try {
    const parsed = JSON.parse(value);
    // Validate basic structure
    if (parsed && typeof parsed === 'object' && parsed.columns && parsed.rows && parsed.meta) {
      return parsed as TableStructure;
    }
  } catch (error) {
    console.error('Failed to parse table value:', error);
  }
  
  return createDefaultTableStructure();
};

// Column configuration modal component
interface ColumnConfigModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (column: TableColumn) => void;
  column?: TableColumn;
  existingColumns: TableColumn[];
}

const ColumnConfigModal: React.FC<ColumnConfigModalProps> = ({
  visible,
  onCancel,
  onOk,
  column,
  existingColumns,
}) => {
  const [form] = Form.useForm();
  const [dataType, setDataType] = useState<TableColumnType>(TableColumnType.TEXT);

  useEffect(() => {
    if (visible) {
      if (column) {
        form.setFieldsValue({
          name: column.name,
          key: column.key,
          dataType: column.dataType,
          required: column.required,
          sortable: column.sortable,
          options: column.options?.join('\n') || '',
        });
        setDataType(column.dataType);
      } else {
        form.resetFields();
        form.setFieldsValue({
          dataType: TableColumnType.TEXT,
          required: false,
          sortable: true,
        });
        setDataType(TableColumnType.TEXT);
      }
    }
  }, [visible, column, form]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      
      // Check for duplicate column names (exclude current column)
      const existingNames = existingColumns
        .filter(col => col.id !== column?.id)
        .map(col => col.name.toLowerCase());
      
      if (existingNames.includes(values.name.toLowerCase())) {
        message.error('Column name already exists');
        return;
      }

      // Check for duplicate column keys (exclude current column)
      const existingKeys = existingColumns
        .filter(col => col.id !== column?.id)
        .map(col => col.key.toLowerCase());
      
      if (existingKeys.includes(values.key.toLowerCase())) {
        message.error('Column key already exists');
        return;
      }

      const newColumn: TableColumn = {
        id: column?.id || generateUuid(),
        name: values.name,
        key: values.key,
        dataType: values.dataType,
        required: values.required || false,
        sortable: values.sortable !== false,
        options: values.options ? values.options.split('\n').filter((opt: string) => opt.trim()) : undefined,
      };

      onOk(newColumn);
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  const isSelectType = useMemo(() => {
    return [
      TableColumnType.SINGLE_SELECT,
      TableColumnType.MULTI_SELECT,
      TableColumnType.RADIO,
    ].includes(dataType);
  }, [dataType]);

  return (
    <Modal
      title={column ? 'Edit Column' : 'Add Column'}
      open={visible}
      onCancel={onCancel}
      onOk={handleOk}
      width={600}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="name"
          label="Column Name (Display)"
          rules={[{ required: true, message: 'Please enter column name' }]}
          tooltip="This is the display name shown to users"
        >
          <Input placeholder="e.g., Customer Name, Order Status" />
        </Form.Item>

        <Form.Item
          name="key"
          label="Column Key (Data Field)"
          rules={[
            { required: true, message: 'Please enter column key' },
            {
              pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/,
              message: 'Key must start with a letter and contain only letters, numbers, and underscores'
            }
          ]}
          tooltip="This is the actual key used for storing data (e.g., customer_name, order_status)"
        >
          <Input placeholder="e.g., customer_name, order_status" />
        </Form.Item>

        <Form.Item
          name="dataType"
          label="Data Type"
          rules={[{ required: true, message: 'Please select data type' }]}
        >
          <Select onChange={(value) => setDataType(value as TableColumnType)}>
            <Option value={TableColumnType.TEXT}>Text</Option>
            <Option value={TableColumnType.TEXTAREA}>Textarea</Option>
            <Option value={TableColumnType.NUMBER}>Number</Option>
            <Option value={TableColumnType.NUMBER_RANGE}>Number Range</Option>
            <Option value={TableColumnType.BOOLEAN}>Boolean</Option>
            <Option value={TableColumnType.DATE}>Date</Option>
            <Option value={TableColumnType.DATETIME}>DateTime</Option>
            <Option value={TableColumnType.SINGLE_SELECT}>Single Select</Option>
            <Option value={TableColumnType.MULTI_SELECT}>Multi Select</Option>
            <Option value={TableColumnType.RADIO}>Radio</Option>
          </Select>
        </Form.Item>

        {isSelectType && (
          <Form.Item
            name="options"
            label="Options (one per line)"
            rules={[{ required: true, message: 'Please enter options' }]}
          >
            <Input.TextArea
              rows={4}
              placeholder="Option 1&#10;Option 2&#10;Option 3"
            />
          </Form.Item>
        )}

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="required" valuePropName="checked">
              <Checkbox>Required</Checkbox>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="sortable" valuePropName="checked">
              <Checkbox>Sortable</Checkbox>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

// Cell editor component for inline editing
interface CellEditorProps {
  column: TableColumn;
  value: any;
  onChange: (value: any) => void;
  onSave: () => void;
  onCancel: () => void;
}

const CellEditor: React.FC<CellEditorProps> = ({
  column,
  value,
  onChange,
  onSave,
  onCancel,
}) => {
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onCancel();
    }
  };

  const renderEditor = () => {
    switch (column.dataType) {
      case TableColumnType.NUMBER:
        return (
          <InputNumber
            value={value}
            onChange={onChange}
            onPressEnter={onSave}
            autoFocus
            style={{ width: '100%' }}
          />
        );
      
      case TableColumnType.NUMBER_RANGE:
        return (
          <NumberRangeInput
            value={typeof value === 'string' ? value : JSON.stringify(value || {min: 0, max: 100})}
            onChange={onChange}
          />
        );
      
      case TableColumnType.BOOLEAN:
        return (
          <Switch
            checked={Boolean(value)}
            onChange={onChange}
            autoFocus
          />
        );
      
      case TableColumnType.DATE:
        return (
          <DatePicker
            value={value}
            onChange={onChange}
            autoFocus
            style={{ width: '100%' }}
          />
        );
      
      case TableColumnType.TEXTAREA:
        return (
          <Input.TextArea
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyPress}
            autoFocus
            rows={2}
          />
        );
      
      case TableColumnType.SINGLE_SELECT:
      case TableColumnType.RADIO:
        return (
          <Select
            value={value}
            onChange={onChange}
            autoFocus
            style={{ width: '100%' }}
            placeholder="Select option"
          >
            {column.options?.map((option) => (
              <Option key={option} value={option}>
                {option}
              </Option>
            ))}
          </Select>
        );
      
      case TableColumnType.MULTI_SELECT:
        return (
          <Select
            mode="multiple"
            value={Array.isArray(value) ? value : []}
            onChange={onChange}
            autoFocus
            style={{ width: '100%' }}
            placeholder="Select options"
          >
            {column.options?.map((option) => (
              <Option key={option} value={option}>
                {option}
              </Option>
            ))}
          </Select>
        );
      
      case TableColumnType.TEXT:
      default:
        return (
          <Input
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyPress}
            autoFocus
          />
        );
    }
  };

  return (
    <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
      <div style={{ flex: 1 }}>{renderEditor()}</div>
      <Button size="small" type="primary" onClick={onSave}>
        Save
      </Button>
      <Button size="small" onClick={onCancel}>
        Cancel
      </Button>
    </div>
  );
};

// Main table input renderer component
const TableInputRenderer: React.FC<TableInputRendererProps> = ({
  value,
  onChange,
  disabled = false,
}) => {
  const [tableStructure, setTableStructure] = useState<TableStructure>(() =>
    parseTableValue(value)
  );
  const [activeTab, setActiveTab] = useState<'configure' | 'edit' | 'preview'>('edit');
  const [columnModalVisible, setColumnModalVisible] = useState(false);
  const [editingColumn, setEditingColumn] = useState<TableColumn | undefined>();
  const [editingCell, setEditingCell] = useState<{
    rowId: string;
    columnId: string;
    value: any;
  } | null>(null);

  // Update internal state when value prop changes
  useEffect(() => {
    const newStructure = parseTableValue(value);
    setTableStructure(newStructure);
  }, [value]);

  // Notify parent of changes
  const updateTableStructure = useCallback(
    (newStructure: TableStructure) => {
      setTableStructure(newStructure);
      if (onChange) {
        try {
          const jsonString = JSON.stringify(newStructure);
          onChange(jsonString);
        } catch (error) {
          console.error('Failed to serialize table structure:', error);
          message.error('Failed to save table changes');
        }
      }
    },
    [onChange]
  );

  // Column management
  const handleAddColumn = () => {
    setEditingColumn(undefined);
    setColumnModalVisible(true);
  };

  const handleEditColumn = (column: TableColumn) => {
    setEditingColumn(column);
    setColumnModalVisible(true);
  };

  const handleDeleteColumn = (columnId: string) => {
    const newStructure = {
      ...tableStructure,
      columns: tableStructure.columns.filter(col => col.id !== columnId),
      rows: tableStructure.rows.map(row => ({
        ...row,
        data: Object.fromEntries(
          Object.entries(row.data).filter(([key]) => key !== columnId)
        ),
      })),
    };
    updateTableStructure(newStructure);
    message.success('Column deleted successfully');
  };

  const handleColumnModalOk = (column: TableColumn) => {
    let newColumns: TableColumn[];
    
    if (editingColumn) {
      // Editing existing column
      newColumns = tableStructure.columns.map(col =>
        col.id === column.id ? column : col
      );
    } else {
      // Adding new column
      newColumns = [...tableStructure.columns, column];
    }

    const newStructure = {
      ...tableStructure,
      columns: newColumns,
    };

    updateTableStructure(newStructure);
    setColumnModalVisible(false);
    message.success(`Column ${editingColumn ? 'updated' : 'added'} successfully`);
  };

  // Row management
  const handleAddRow = () => {
    // Initialize row data with default values for required columns
    const rowData: Record<string, any> = {};
    
    tableStructure.columns.forEach(column => {
      // Set default value based on column type and requirements
      if (column.defaultValue !== undefined) {
        rowData[column.key] = column.defaultValue;
      } else if (column.required) {
        // Set appropriate default for required fields based on type
        switch (column.dataType) {
          case TableColumnType.NUMBER:
            rowData[column.key] = 0;
            break;
          case TableColumnType.NUMBER_RANGE:
            rowData[column.key] = JSON.stringify({ min: 0, max: 100 });
            break;
          case TableColumnType.BOOLEAN:
            rowData[column.key] = false;
            break;
          case TableColumnType.SINGLE_SELECT:
          case TableColumnType.RADIO:
            // Use first option as default if available
            rowData[column.key] = column.options?.[0] || '';
            break;
          case TableColumnType.MULTI_SELECT:
            rowData[column.key] = [];
            break;
          case TableColumnType.DATE:
          case TableColumnType.DATETIME:
            rowData[column.key] = null; // User must select date
            break;
          default:
            rowData[column.key] = '';
        }
      }
    });

    const newRow: TableRow = {
      id: generateUuid(),
      data: rowData,
    };

    const newStructure = {
      ...tableStructure,
      rows: [...tableStructure.rows, newRow],
    };

    updateTableStructure(newStructure);
  };

  const handleDeleteRow = (rowId: string) => {
    const newStructure = {
      ...tableStructure,
      rows: tableStructure.rows.filter(row => row.id !== rowId),
    };
    updateTableStructure(newStructure);
    message.success('Row deleted successfully');
  };

  // Cell editing
  const startCellEdit = (rowId: string, columnKey: string, currentValue: any) => {
    setEditingCell({
      rowId,
      columnId: columnKey, // Using columnKey but keeping field name for compatibility
      value: currentValue,
    });
  };

  const cancelCellEdit = () => {
    setEditingCell(null);
  };

  const saveCellEdit = () => {
    if (!editingCell) return;

    const newStructure = {
      ...tableStructure,
      rows: tableStructure.rows.map(row =>
        row.id === editingCell.rowId
          ? {
              ...row,
              data: {
                ...row.data,
                [editingCell.columnId]: editingCell.value,
              },
            }
          : row
      ),
    };

    updateTableStructure(newStructure);
    setEditingCell(null);
  };

  // Create table columns for Ant Design Table
  const antdColumns: ColumnsType<TableRow> = useMemo(() => {
    const columns: ColumnsType<TableRow> = [];

    // Row number column
    if (tableStructure.meta.showRowNumbers) {
      columns.push({
        title: '#',
        key: 'rowNumber',
        width: 50,
        render: (_, __, index) => index + 1,
      });
    }

    // Data columns
    tableStructure.columns.forEach((column) => {
      columns.push({
        title: (
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <span>{column.name}</span>
            {column.required && <span style={{ color: 'red' }}>*</span>}
            {activeTab === 'configure' && (
              <Space size="small">
                <Tooltip title="Edit Column">
                  <Button
                    type="text"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => handleEditColumn(column)}
                    disabled={disabled}
                  />
                </Tooltip>
                <Tooltip title="Delete Column">
                  <Button
                    type="text"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleDeleteColumn(column.id)}
                    disabled={disabled || tableStructure.columns.length <= 1}
                  />
                </Tooltip>
              </Space>
            )}
          </div>
        ),
        key: column.id,
        dataIndex: ['data', column.key], // Use column.key for data access
        render: (cellValue: any, row: TableRow) => {
          const isEditing = editingCell?.rowId === row.id && editingCell?.columnId === column.key;

          if (isEditing) {
            return (
              <CellEditor
                column={column}
                value={editingCell.value}
                onChange={(newValue) =>
                  setEditingCell(prev => prev ? { ...prev, value: newValue } : null)
                }
                onSave={saveCellEdit}
                onCancel={cancelCellEdit}
              />
            );
          }

          let displayValue: string;
          
          if (column.dataType === TableColumnType.NUMBER_RANGE && cellValue) {
            try {
              const range = typeof cellValue === 'string' ? JSON.parse(cellValue) : cellValue;
              displayValue = `${range.min} - ${range.max}`;
            } catch {
              displayValue = String(cellValue || '');
            }
          } else if (Array.isArray(cellValue)) {
            displayValue = cellValue.join(', ');
          } else {
            displayValue = String(cellValue || '');
          }
          
          return (
            <div
              style={{ 
                cursor: disabled || activeTab === 'preview' ? 'default' : 'pointer',
                minHeight: 22,
                padding: '4px 0',
              }}
              onClick={() => {
                if (!disabled && activeTab === 'edit') {
                  startCellEdit(row.id, column.key, cellValue);
                }
              }}
            >
              {displayValue || <span style={{ color: '#ccc' }}>Click to edit</span>}
            </div>
          );
        },
      });
    });

    // Actions column
    if (activeTab === 'edit' && !disabled) {
      columns.push({
        title: 'Actions',
        key: 'actions',
        width: 100,
        render: (_, row) => (
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteRow(row.id)}
            disabled={false} // Allow deleting all rows since we start with none
            title="Delete Row"
          />
        ),
      });
    }

    return columns;
  }, [tableStructure, activeTab, editingCell, disabled]);

  // Render table configuration panel
  const renderConfigurePanel = () => (
    <Card>
      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddColumn}
          disabled={disabled}
        >
          Add Column
        </Button>
      </div>
      
      <Table
        dataSource={tableStructure.rows}
        columns={antdColumns}
        rowKey="id"
        pagination={false}
        size="small"
        bordered
      />
    </Card>
  );

  // Render data editing panel
  const renderEditPanel = () => (
    <Card>
      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddRow}
          disabled={disabled}
        >
          Add Row
        </Button>
      </div>
      
      <Table
        dataSource={tableStructure.rows}
        columns={antdColumns}
        rowKey="id"
        pagination={tableStructure.meta.paginated ? { pageSize: tableStructure.meta.pageSize } : false}
        size="small"
        bordered
      />
    </Card>
  );

  // Render preview panel
  const renderPreviewPanel = () => (
    <Card>
      <Table
        dataSource={tableStructure.rows}
        columns={antdColumns}
        rowKey="id"
        pagination={tableStructure.meta.paginated ? { pageSize: tableStructure.meta.pageSize } : false}
        size="small"
        bordered
      />
    </Card>
  );

  return (
    <div className="table-input-renderer">
      <Tabs 
        activeKey={activeTab} 
        onChange={(key) => setActiveTab(key as any)}
        items={[
          {
            key: 'edit',
            label: (
              <span>
                <EditOutlined />
                Edit Data
              </span>
            ),
            children: renderEditPanel(),
          },
          {
            key: 'configure',
            label: (
              <span>
                <SettingOutlined />
                Configure
              </span>
            ),
            children: renderConfigurePanel(),
          },
          {
            key: 'preview',
            label: (
              <span>
                <EyeOutlined />
                Preview
              </span>
            ),
            children: renderPreviewPanel(),
          },
        ]}
      />

      <ColumnConfigModal
        visible={columnModalVisible}
        onCancel={() => setColumnModalVisible(false)}
        onOk={handleColumnModalOk}
        column={editingColumn}
        existingColumns={tableStructure.columns}
      />
    </div>
  );
};

export default TableInputRenderer;