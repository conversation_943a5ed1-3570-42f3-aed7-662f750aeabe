import { CopyOutlined, EyeOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  ProColumns,
  ProTable,
  RequestData, // Import RequestData
} from '@ant-design/pro-components';
import {
  Button,
  message,
  Modal,
  Popconfirm,
  Switch,
  Tag,
  Typography,
} from 'antd';
import type { SortOrder } from 'antd/es/table/interface'; // Import SortOrder
import React, { useState } from 'react';
// Removed unused GetApiSystemConfigItemsParams import as params type is now generic
import type { ConfigItem, ConfigItemFormData } from '../types';
import { ValueType, ValueTypes } from '../types';

// Helper function to parse stored string value based on type
const parseValue = (value: string, type: ValueType) => {
  try {
    switch (type) {
      case ValueTypes.NUMBER: {
        const num = Number(value);
        return isNaN(num) ? null : num;
      }
      case ValueTypes.BOOLEAN:
        // Handle numeric boolean ('0'/'1') as per requirements
        // Also handle string boolean ('true'/'false') for backward compatibility
        return value === '1' || value.toLowerCase() === 'true';
      case ValueTypes.JSON:
        if (!value) return null;
        return JSON.parse(value);
      case ValueTypes.TEXT:
      case ValueTypes.TEXTAREA:
      default:
        return value;
    }
  } catch (e) {
    console.error(`Error parsing value "${value}" as type "${type}":`, e);
    return value;
  }
};

// Component for displaying config values with ellipsis and expand functionality
interface ValueDisplayProps {
  value: string;
  valueType: ValueType;
  maxLength?: number;
}

const ValueDisplay: React.FC<ValueDisplayProps> = ({
  value,
  valueType,
  maxLength = 50,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const shouldTruncate = value && value.length > maxLength;
  const displayValue = shouldTruncate
    ? `${value.substring(0, maxLength)}...`
    : value;

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleModalClose = () => {
    setIsModalVisible(false);
  };

  const renderFullValue = () => {
    switch (valueType) {
      case ValueTypes.TABLE:
        try {
          const tableData = JSON.parse(value);
          return (
            <div>
              <div style={{ marginBottom: 16, fontWeight: 'bold' }}>
                Table Structure ({tableData?.columns?.length || 0} columns, {tableData?.rows?.length || 0} rows)
              </div>
              <pre
                style={{
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-all',
                  backgroundColor: '#f5f5f5',
                  padding: '12px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  fontFamily: 'monospace',
                  maxHeight: '400px',
                  overflow: 'auto',
                }}
              >
                {JSON.stringify(tableData, null, 2)}
              </pre>
            </div>
          );
        } catch {
          return (
            <Typography.Text type="danger">
              Invalid table data format
            </Typography.Text>
          );
        }
      case ValueTypes.JSON:
      case ValueTypes.MAP:
        try {
          const parsed = JSON.parse(value);
          return (
            <pre
              style={{
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-all',
                backgroundColor: '#f5f5f5',
                padding: '12px',
                borderRadius: '4px',
                fontSize: '12px',
                fontFamily: 'monospace',
              }}
            >
              {JSON.stringify(parsed, null, 2)}
            </pre>
          );
        } catch {
          return (
            <Typography.Text
              code
              style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}
            >
              {value}
            </Typography.Text>
          );
        }
      case ValueTypes.TEXTAREA:
        return (
          <Typography.Text
            style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}
          >
            {value}
          </Typography.Text>
        );
      default:
        return (
          <Typography.Text style={{ wordBreak: 'break-all' }}>
            {value}
          </Typography.Text>
        );
    }
  };

  if (!shouldTruncate) {
    return <span>{value}</span>;
  }

  return (
    <>
      <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <span>{displayValue}</span>
        <Button
          type="text"
          size="small"
          icon={<EyeOutlined />}
          onClick={showModal}
          style={{ padding: '0 4px', minWidth: 'auto' }}
          title="查看完整内容"
        />
      </span>

      <Modal
        title="配置值详情"
        open={isModalVisible}
        onCancel={handleModalClose}
        footer={[
          <Button key="close" onClick={handleModalClose}>
            关闭
          </Button>,
        ]}
        width={600}
        style={{ top: 20 }}
      >
        <div style={{ maxHeight: '60vh', overflow: 'auto' }}>
          {renderFullValue()}
        </div>
      </Modal>
    </>
  );
};

interface ConfigItemTableProps {
  actionRef: React.MutableRefObject<ActionType | undefined>;
  // dataSource: ConfigItem[]; // Removed
  // loading: boolean; // Removed
  request: (
    // Make params type more generic to match ProTable's default
    params: Record<string, any> & {
      // Use Record<string, any>
      pageSize?: number;
      current?: number;
      keyword?: string; // Keep known ProTable params
    },
    sort: Record<string, SortOrder>,
    filter: Record<string, (string | number)[] | null>,
  ) => Promise<RequestData<ConfigItem>>; // Return type is correct based on hooks.ts
  actionLoading: boolean;
  headerTitle: string;
  onShowItemModal: (record?: ConfigItem) => void;
  onDeleteItem: (id: string) => void;
  // Add editConfigItem function for inline editing
  editConfigItem: (
    id: string,
    updates: Partial<ConfigItemFormData>,
  ) => Promise<{
    success: boolean;
    error?: string;
  }>;
}

const ConfigItemTable: React.FC<ConfigItemTableProps> = ({
  actionRef,
  // dataSource, // Removed
  // loading, // Removed
  request, // Added
  actionLoading,
  headerTitle,
  onShowItemModal,
  onDeleteItem,
  editConfigItem,
}) => {
  // State to track items being edited (for loading indicators)
  const [editingItemIds, setEditingItemIds] = useState<Set<string>>(new Set());

  // Handle boolean switch toggle
  const handleBooleanToggle = async (record: ConfigItem, checked: boolean) => {
    // Add item to editing state
    setEditingItemIds((prev) => new Set(prev).add(record.id));

    try {
      // Convert boolean to numeric string (0 or 1) as per requirements
      // The checked parameter is the new state of the switch
      const numericValue = checked ? '1' : '0';

      // Call editConfigItem with the new value
      const result = await editConfigItem(record.id, {
        valueType: ValueTypes.BOOLEAN,
        value: numericValue,
      });

      if (result.success) {
        message.success('配置值已更新');
        // Reload the table to reflect changes
        actionRef.current?.reload();
      } else {
        message.error(result.error || '更新失败');
      }
    } catch (error) {
      console.error('Failed to update boolean value:', error);
      message.error('更新失败');
    } finally {
      // Remove item from editing state
      setEditingItemIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(record.id);
        return newSet;
      });
    }
  };
  const columns: ProColumns<ConfigItem>[] = [
    {
      title: '配置键 (Key)',
      dataIndex: 'key',
      key: 'key',
      width: '25%',
      ellipsis: true,
      render: (_, record) => (
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Typography.Text ellipsis style={{ flex: 1 }}>
            {record.key}
          </Typography.Text>
          <Button
            type="text"
            size="small"
            icon={<CopyOutlined />}
            onClick={() => {
              navigator.clipboard.writeText(record.key);
              message.success('已复制到剪贴板');
            }}
            style={{ padding: '0 4px', minWidth: 'auto' }}
            title="复制配置键"
          />
        </span>
      ),
    },
    {
      title: '配置值 (Value)',
      dataIndex: 'value',
      key: 'value',
      width: '35%',
      ellipsis: true,
      render: (_, record) => {
        const displayValue = parseValue(record.value, record.valueType);
        const isEditing = editingItemIds.has(record.id);

        switch (record.valueType) {
          case ValueTypes.TABLE: {
            // For table values, show a compact summary
            try {
              const tableData = JSON.parse(record.value);
              const columnCount = tableData?.columns?.length || 0;
              const rowCount = tableData?.rows?.length || 0;
              return (
                <span style={{ color: '#666', fontStyle: 'italic' }}>
                  Table ({columnCount} columns, {rowCount} rows)
                </span>
              );
            } catch {
              return (
                <span style={{ color: '#ff4d4f', fontStyle: 'italic' }}>
                  Invalid table data
                </span>
              );
            }
          }
          case ValueTypes.BOOLEAN: {
            // For boolean values, show an editable switch
            // Convert string value to boolean for display
            // The API stores boolean values as strings "0"/"1" as per requirements
            const boolValue =
              typeof displayValue === 'boolean'
                ? displayValue
                : record.value === '1' || record.value.toLowerCase() === 'true';

            return (
              <Switch
                checked={boolValue}
                onChange={(checked) => handleBooleanToggle(record, checked)}
                checkedChildren="是"
                unCheckedChildren="否"
                loading={isEditing}
                disabled={actionLoading || isEditing}
              />
            );
          }
          default:
            // Use ValueDisplay component for all other types (including JSON, TEXTAREA, MAP, etc.)
            return (
              <ValueDisplay
                value={record.value}
                valueType={record.valueType}
                maxLength={50}
              />
            );
        }
      },
    },
    {
      title: '值类型',
      dataIndex: 'valueType',
      key: 'valueType',
      width: '10%',
      render: (type) => <Tag>{type}</Tag>,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: '15%',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => [
        <Button
          type="link"
          key="edit"
          onClick={() => onShowItemModal(record)}
          style={{ paddingLeft: 0 }}
          disabled={actionLoading}
        >
          编辑
        </Button>,
        <Popconfirm
          title="确认删除此配置项?"
          onConfirm={() => onDeleteItem(record.id)}
          key="delete"
          okText="确认"
          cancelText="取消"
          placement="topRight"
          disabled={actionLoading}
        >
          <Button type="link" key="delete" danger disabled={actionLoading}>
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <ProTable<ConfigItem>
      columns={columns}
      actionRef={actionRef}
      // dataSource={dataSource} // Removed
      // loading={loading} // Removed
      request={request} // Added
      rowKey="id"
      search={false}
      pagination={{ pageSize: 10 }}
      dateFormatter="string"
      headerTitle={headerTitle}
      toolBarRender={() => [
        <Button
          key="button"
          icon={<PlusOutlined />}
          onClick={() => onShowItemModal()} // Call prop function for adding
          type="primary"
          disabled={actionLoading}
        >
          新建配置项
        </Button>,
      ]}
      options={false}
    />
  );
};

export default ConfigItemTable;
