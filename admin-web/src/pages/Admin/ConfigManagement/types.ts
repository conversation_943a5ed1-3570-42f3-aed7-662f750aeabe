// TypeScript interfaces for ConfigManagement

/**
 * Defines the possible types for configuration values.
 * This allows rendering different input components and performing type-specific validation/parsing.
 */
export const ValueTypes = {
  TEXT: 'text',
  TEXTAREA: 'textarea',
  NUMBER: 'number',
  NUMBER_RANGE: 'number_range', // NEW: Number range type for min-max values
  BOOLEAN: 'boolean',
  JSON: 'json',
  PASSWORD: 'password',
  MAP: 'map', // Add MAP type for key-value pairs like Go's map
  TABLE: 'table', // NEW: Table type for dynamic table structures
  // Add more types here as needed, e.g., 'select', 'color'
} as const; // Use 'as const' for stricter typing

// Create a TypeScript type from the keys of ValueTypes
export type ValueType = (typeof ValueTypes)[keyof typeof ValueTypes];

/**
 * Represents a configuration category (used for Tabs)
 */
export interface ConfigCategory {
  id: string; // Unique identifier for the category
  name: string; // Display name for the category tab
  categoryKey: string; // Unique key for this category, used as prefix for item keys (cannot be changed after creation)
  sortOrder: number; // Order in which the tab should appear
}

/**
 * Represents a single configuration item
 */
export interface ConfigItem {
  id: string; // Unique identifier for the config item
  categoryId: string; // ID of the category this item belongs to
  key: string; // Configuration key (globally unique, includes categoryKey prefix, e.g., 'general.site_name')
  value: string; // Configuration value (stored as string, parsed/interpreted based on valueType)
  valueType: ValueType; // The type of the configuration value (e.g., 'text', 'number', 'boolean')
  description?: string; // Optional description
  createdAt: string; // Timestamp of creation
  updatedAt: string; // Timestamp of last update
}

// --- Form specific types (optional but good practice) ---

/**
 * Form values for adding/editing a ConfigCategory
 */
export interface CategoryFormData {
  name: string;
  categoryKey: string; // Required only on creation
  sortOrder: number;
}

/**
 * Form values for adding/editing a ConfigItem
 */
export interface ConfigItemFormData {
  categoryId: string; // Added: ID of the category this item belongs to
  key: string; // Full key including prefix
  value: any; // Value type depends on the selected valueType in the form
  valueType: ValueType;
  description?: string;
}

// --- Table Field Type Definitions ---

/**
 * Complete structure of a table configuration
 */
export interface TableStructure {
  columns: TableColumn[];
  rows: TableRow[];
  meta: TableMeta;
}

/**
 * Defines a single column in the table
 */
export interface TableColumn {
  id: string; // Unique column identifier
  name: string; // Display name for the column (shown to users)
  key: string; // Actual key for data storage (used in key-value mapping)
  dataType: TableColumnType; // Column data type
  required: boolean; // Whether this column is required
  defaultValue?: any; // Default value for new rows
  options?: string[]; // For select types - predefined options
  remoteConfig?: RemoteDataConfig; // For remote data sources
  validation?: Record<string, any>; // Validation rules
  width?: string; // Column width (optional)
  sortable: boolean; // Whether column is sortable
}

/**
 * Represents a single row of data
 */
export interface TableRow {
  id: string; // Unique row identifier
  data: Record<string, any>; // Column_id -> value mapping
}

/**
 * Contains table configuration and metadata
 */
export interface TableMeta {
  allowAddRows: boolean; // Allow users to add new rows
  allowDeleteRows: boolean; // Allow users to delete rows
  allowEditRows: boolean; // Allow users to edit row data
  allowAddColumns: boolean; // Allow users to add new columns
  allowDeleteColumns: boolean; // Allow users to delete columns
  allowEditColumns: boolean; // Allow users to edit column definitions
  maxRows?: number; // Maximum number of rows (0 = unlimited)
  maxColumns?: number; // Maximum number of columns (0 = unlimited)
  showRowNumbers: boolean; // Display row numbers
  paginated: boolean; // Enable pagination for large tables
  pageSize?: number; // Rows per page when paginated
}

/**
 * Configuration for fetching data from remote APIs
 */
export interface RemoteDataConfig {
  endpoint: string; // Remote API endpoint URL
  method?: 'GET' | 'POST'; // HTTP method - default GET
  headers?: Record<string, string>; // Custom headers for the request
  valueField: string; // Field name to use as option value
  labelField: string; // Field name to use as option label
  searchParam?: string; // Parameter name for search/filter functionality
  cacheTtl?: number; // Cache time-to-live in seconds
  dependsOn?: string[]; // Other column IDs this depends on
}

/**
 * Supported column data types
 */
export enum TableColumnType {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  NUMBER = 'number',
  NUMBER_RANGE = 'number_range', // NEW: Number range type for min-max values
  BOOLEAN = 'boolean',
  DATE = 'date',
  DATETIME = 'datetime',
  SINGLE_SELECT = 'single_select',
  MULTI_SELECT = 'multi_select',
  RADIO = 'radio',
  REMOTE_SELECT = 'remote_select',
  REMOTE_MULTI_SELECT = 'remote_multi_select'
}

/**
 * Option for select-type columns
 */
export interface SelectOption {
  value: string;
  label: string;
}
