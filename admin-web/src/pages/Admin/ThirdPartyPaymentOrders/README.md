# 第三方支付订单管理模块

## 概述

第三方支付订单管理模块提供了统一的界面来管理代收订单和代付订单，整合了原有的充值记录和提现记录功能，并增加了商户相关字段的显示和筛选功能。

## 功能特性

### 1. 代收订单 (DepositOrdersTab)
- 集成了原有充值记录管理的所有功能
- 新增商户信息字段显示和筛选
- 支持按商户ID、商户名称、业务名称筛选

### 2. 代付订单 (WithdrawOrdersTab)  
- 集成了原有提现记录管理的所有功能
- 新增商户信息字段显示和筛选
- 支持按商户ID、商户名称、业务名称筛选
- 保留原有的撤销功能

## 商户字段说明

### 新增字段
- **商户ID** (`merchantId`): 对应 merchants 表的 merchant_id
- **商户名称** (`merchantName`): 对应 merchants 表的 merchant_name

### 字段展示
商户信息以独立列的形式展示：
- **商户ID列**: 显示商户ID数值
- **商户名称列**: 显示商户名称文本

### 筛选功能
在搜索表单中可以分别按以下条件筛选：
- 商户ID (支持精确匹配) ✅
- 商户名称 (前端已支持，⚠️ 需要后端API支持)

## 技术实现

### 类型定义
- `EnhancedDepositRecord`: 扩展的充值记录类型，包含商户字段
- `EnhancedWithdrawRecord`: 扩展的提现记录类型，包含商户字段
- `EnhancedDepositRecordsRequestParams`: 扩展的充值记录请求参数
- `EnhancedWithdrawRecordsRequestParams`: 扩展的提现记录请求参数

### API 集成
模块复用了原有的API钩子函数：
- `useDepositRecords`: 处理充值记录数据
- `useWithdrawRecords`: 处理提现记录数据

### 组件结构
```
ThirdPartyPaymentOrders/
├── components/
│   ├── DepositOrdersTab.tsx      # 代收订单页面
│   ├── WithdrawOrdersTab.tsx     # 代付订单页面
│   └── index.ts                  # 组件导出
├── index.tsx                     # 主模块入口
├── types.ts                      # 类型定义
├── constants.ts                  # 常量定义
└── README.md                     # 说明文档
```

## 路由配置

模块已在 `.umirc.ts` 中注册路由：
```typescript
{
  path: '/admin/third-party-payment-orders',
  component: './Admin/ThirdPartyPaymentOrders',
}
```

## 使用方式

1. 访问 `/admin/third-party-payment-orders` 进入模块
2. 默认显示"代收订单"标签页
3. 点击"代付订单"标签页切换到提现记录管理
4. 使用搜索表单可按商户信息筛选记录
5. 所有原有功能(详情查看、导出、撤销等)保持不变

## 兼容性

- 完全向后兼容原有的充值记录和提现记录功能
- 新增的商户字段为可选字段，不影响现有数据展示
- 保留了所有原有的搜索、筛选、导出功能

## ⚠️ 重要提醒

**商户名称搜索功能需要后端API支持**

当前状态：
- ✅ 商户ID搜索 - 完全可用
- ⚠️ 商户名称搜索 - 需要后端在以下API中添加 `merchantName` 参数支持：
  - `/api/system/my/deposits`
  - `/api/system/my/withdraws`

详细信息请参考：[API_MODIFICATION_REQUIRED.md](./API_MODIFICATION_REQUIRED.md)