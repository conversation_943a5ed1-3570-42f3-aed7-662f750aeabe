/**
 * 第三方支付订单管理相关类型定义
 */

// 重新导出充值记录相关类型
export type {
  DepositRecordsRequestParams,
  DepositRecord,
  DepositRecordDetail,
  DepositDetailDrawerProps,
  DepositRecordsTableRef,
  DepositStatus,
  NotificationStatus as DepositNotificationStatus,
  AmountFormatOptions as DepositAmountFormatOptions,
  CopyCallback as DepositCopyCallback,
  ExportParams as DepositExportParams,
} from '../DepositRecordsManagement/types';

/**
 * 扩展的充值记录类型（包含商户信息）
 */
export interface EnhancedDepositRecord extends DepositRecord {
  merchantId?: number | string;
  merchantName?: string;
}

/**
 * 扩展的充值记录请求参数（包含商户搜索）
 */
export interface EnhancedDepositRecordsRequestParams extends DepositRecordsRequestParams {
  merchantId?: number | string;
  merchantName?: string;
}

// 重新导出提现记录相关类型
export type {
  WithdrawRecordsRequestParams,
  WithdrawRecord,
  WithdrawRecordDetail,
  WithdrawDetailDrawerProps,
  WithdrawRecordsTableRef,
  WithdrawStatus,
  NotificationStatus as WithdrawNotificationStatus,
  EnergyStatus,
  FiatType,
  CancelWithdrawRequest,
  CancelWithdrawModalProps,
  StatusTimelineProps,
  AmountBreakdownProps,
  PaymentMethodCardProps,
  AmountFormatOptions as WithdrawAmountFormatOptions,
  CopyCallback as WithdrawCopyCallback,
  ExportParams as WithdrawExportParams,
  TimelineStep,
  StatusStatistics,
  FilterOption,
  BatchOperation,
  RealtimeUpdateConfig,
  SearchConfig,
  ColumnConfig,
  PageState,
  ApiResponse,
  ErrorInfo,
  OperationHistory,
} from '../WithdrawRecordsManagement/types';

/**
 * 扩展的提现记录类型（包含商户信息）
 */
export interface EnhancedWithdrawRecord extends WithdrawRecord {
  merchantId?: number | string;
  merchantName?: string;
}

/**
 * 扩展的提现记录请求参数（包含商户搜索）
 */
export interface EnhancedWithdrawRecordsRequestParams extends WithdrawRecordsRequestParams {
  merchantId?: number | string;
  merchantName?: string;
}

/**
 * 第三方支付订单标签页类型
 */
export type PaymentOrderTabType = 'deposit' | 'withdraw';

/**
 * 第三方支付订单管理页面状态
 */
export interface PaymentOrdersPageState {
  activeTab: PaymentOrderTabType;
  loading: boolean;
}

/**
 * 统一的订单记录类型
 */
export type PaymentOrderRecord = DepositRecord | WithdrawRecord;

/**
 * 统一的订单详情类型
 */
export type PaymentOrderDetail = DepositRecordDetail | WithdrawRecordDetail;

/**
 * 统一的搜索参数类型
 */
export type PaymentOrderSearchParams = DepositRecordsRequestParams | WithdrawRecordsRequestParams;