import { PageContainer } from '@ant-design/pro-components';
import { Tabs } from 'antd';
import React, { useState } from 'react';
import DepositOrdersTab from './components/DepositOrdersTab';
import WithdrawOrdersTab from './components/WithdrawOrdersTab';

/**
 * 第三方支付订单管理主页面
 */
const ThirdPartyPaymentOrders: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('deposit');

  return (
    <PageContainer
      header={{
        title: '第三方支付订单管理',
        subTitle: '管理代收订单和代付订单',
      }}
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'deposit',
            label: '代收订单',
            children: <DepositOrdersTab />,
          },
          {
            key: 'withdraw',
            label: '代付订单',
            children: <WithdrawOrdersTab />,
          },
        ]}
      />
    </PageContainer>
  );
};

export default ThirdPartyPaymentOrders;