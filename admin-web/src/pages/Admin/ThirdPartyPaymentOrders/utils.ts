/**
 * 第三方支付订单管理工具函数
 */

import { message } from 'antd';

/**
 * 处理商户搜索参数
 * 前端已支持merchantName参数，等待后端API实现
 */
export const processMerchantSearchParams = (params: any) => {
  // 前端现在完全支持merchantName参数传递
  // 如果后端还未实现，会收到相应的API错误响应
  return params;
};

/**
 * 验证商户ID格式
 */
export const validateMerchantId = (merchantId: string | number): boolean => {
  if (!merchantId) return true; // 空值是允许的
  
  const id = typeof merchantId === 'string' ? parseInt(merchantId) : merchantId;
  return !isNaN(id) && id > 0;
};

/**
 * 格式化商户信息显示
 */
export const formatMerchantInfo = (merchantId?: number | string, merchantName?: string) => {
  if (!merchantId && !merchantName) return '-';
  
  const parts = [];
  if (merchantId) parts.push(`ID: ${merchantId}`);
  if (merchantName) parts.push(merchantName);
  
  return parts.join(' - ');
};

/**
 * 检查是否需要显示API限制提示
 */
export const shouldShowApiLimitation = () => {
  // 可以基于环境变量或配置来控制是否显示提示
  return process.env.NODE_ENV === 'development' || true;
};