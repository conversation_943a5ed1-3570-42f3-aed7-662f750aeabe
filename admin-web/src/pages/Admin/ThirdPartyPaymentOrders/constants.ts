/**
 * 第三方支付订单管理相关常量
 */

// 重新导出充值记录相关常量
export {
  DEPOSIT_STATUS_ENUM,
  NOTIFICATION_STATUS_ENUM as DEPOSIT_NOTIFICATION_STATUS_ENUM,
  TABLE_SCROLL as DEPOSIT_TABLE_SCROLL,
} from '../DepositRecordsManagement/constants';

// 重新导出提现记录相关常量
export {
  WITHDRAW_STATUS_ENUM,
  NOTIFICATION_STATUS_ENUM as WITHDRAW_NOTIFICATION_STATUS_ENUM,
  TABLE_SCROLL as WITHDRAW_TABLE_SCROLL,
} from '../WithdrawRecordsManagement/constants';

/**
 * 第三方支付订单标签页配置
 */
export const PAYMENT_ORDER_TABS = [
  {
    key: 'deposit',
    label: '代收订单',
    description: '管理充值记录和代收订单',
  },
  {
    key: 'withdraw',
    label: '代付订单',
    description: '管理提现记录和代付订单',
  },
] as const;

/**
 * 页面默认配置
 */
export const PAGE_CONFIG = {
  DEFAULT_TAB: 'deposit',
  PAGE_TITLE: '第三方支付订单管理',
  PAGE_SUBTITLE: '管理代收订单和代付订单',
} as const;