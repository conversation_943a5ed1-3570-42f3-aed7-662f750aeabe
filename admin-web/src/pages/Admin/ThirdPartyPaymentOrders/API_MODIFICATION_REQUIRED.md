# 🚨 Admin API 修改需求

## 概述
第三方支付订单管理模块需要后端API支持商户名称搜索功能。

## 需要修改的API

### 1. 充值记录API
**接口路径**: `/api/system/my/deposits`  
**参数类型**: `GetApiSystemMyDepositsParams`

**需要添加的参数**:
```typescript
export type GetApiSystemMyDepositsParams = {
  // ... 现有参数
  merchantId?: number;      // ✅ 已支持
  merchantName?: string;    // 🆕 需要添加 - 商户名称模糊搜索
}
```

### 2. 提现记录API  
**接口路径**: `/api/system/my/withdraws`
**参数类型**: `GetApiSystemMyWithdrawsParams`

**需要添加的参数**:
```typescript
export type GetApiSystemMyWithdrawsParams = {
  // ... 现有参数
  merchantId?: number;      // ✅ 已支持
  merchantName?: string;    // 🆕 需要添加 - 商户名称模糊搜索
}
```

## 后端修改说明

### 数据库查询
需要在查询条件中添加商户名称的模糊匹配：
```sql
-- 示例查询条件
WHERE merchant.merchant_name LIKE '%{merchantName}%'
```

### API文档更新
需要在API文档中添加 `merchantName` 参数说明：
- **参数名**: `merchantName`
- **类型**: `string`
- **必填**: 否
- **说明**: 商户名称模糊搜索

## 前端实现状态

### ✅ 已完成
- ✅ API参数类型定义已添加merchantName支持
- ✅ 商户ID和商户名称分离为独立列显示
- ✅ 商户ID和商户名称分别为独立搜索字段
- ✅ 界面商户信息字段显示
- ✅ 搜索表单商户名称输入框
- ✅ 参数格式化和传递逻辑
- ✅ 类型定义扩展

### ⚠️ 待后端支持
- `merchantName` 参数的实际搜索功能

## 测试计划

### 测试用例
1. **商户ID精确搜索** - ✅ 已支持
2. **商户名称模糊搜索** - ⚠️ 需要后端支持
3. **组合搜索条件** - ⚠️ 需要后端支持
4. **导出功能包含商户信息** - ✅ 已支持

### 验证步骤
1. 输入商户名称进行搜索
2. 验证返回结果包含匹配的商户记录
3. 确认模糊匹配功能正常工作

## 时间预估
- **后端API修改**: 1-2小时
- **API类型重新生成**: 30分钟
- **联调测试**: 1小时
- **总计**: 2.5-3.5小时

## 紧急程度
🔴 **高优先级** - 影响用户使用新功能

## 联系人
前端实现已完成，等待后端API支持。