import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Button, Input, Space, Typography, Tooltip } from 'antd';
import { 
  ExportOutlined, 
  ReloadOutlined, 
  CopyOutlined, 
  EyeOutlined,
  CloseOutlined
} from '@ant-design/icons';
import React, { useMemo, useCallback, useRef } from 'react';
import { useUnifiedFields } from '@/components/UnifiedFields';
import type { UnifiedAgentSearchFields } from '@/types/search';
import { formatDateRange } from '@/types/search';
import { 
  WithdrawDetailDrawer, 
  CancelWithdrawModal 
} from '../../WithdrawRecordsManagement/components';
import { 
  WITHDRAW_STATUS_ENUM, 
  NOTIFICATION_STATUS_ENUM,
  TABLE_SCROLL 
} from '../../WithdrawRecordsManagement/constants';
import { useWithdrawRecords } from '../../WithdrawRecordsManagement/hooks';
import type { WithdrawRecordsRequestParams, WithdrawRecord } from '../../WithdrawRecordsManagement/types';
import { 
  getWithdrawStatusTag, 
  getNotificationStatusTag,
  formatDate, 
  formatAmount,
  truncateText,
  copyToClipboard,
  formatOrderNo,
  formatTxHash,
  canCancelWithdraw,
  needLongProcessingAlert,
  getWithdrawTypeText
} from '../../WithdrawRecordsManagement/utils';

const { Text } = Typography;

/**
 * 代付订单页面组件
 */
const WithdrawOrdersTab: React.FC = () => {
  const {
    tableRef,
    detailVisible,
    currentWithdraw,
    detailLoading,
    exporting,
    cancelModalVisible,
    cancelLoading,
    selectedWithdraw,
    fetchWithdrawRecords,
    viewDetail,
    closeDetail,
    openCancelModal,
    closeCancelModal,
    cancelWithdraw,
    exportRecords,
    refreshTable,
  } = useWithdrawRecords();

  // 创建表单引用，用于获取搜索参数
  const formRef = useRef<ProFormInstance>();

  /**
   * 处理撤销提现
   */
  const handleCancelWithdraw = useCallback(async (reason?: string) => {
    if (!selectedWithdraw?.withdrawsId) return;
    
    const success = await cancelWithdraw(selectedWithdraw.withdrawsId, reason);
    if (success) {
      closeCancelModal();
    }
  }, [selectedWithdraw, cancelWithdraw, closeCancelModal]);

  // 定义基础表格列（不包含时间范围搜索）
  const baseColumns: ProColumns<WithdrawRecord & UnifiedAgentSearchFields>[] = useMemo(
    () => [
      {
        title: '记录ID',
        dataIndex: 'withdrawsId',
        key: 'withdrawsId',
        width: 100,
        search: false,
        fixed: 'left',
      },
      {
        title: '商户ID',
        dataIndex: 'merchantId',
        key: 'merchantId',
        width: 120,
        renderFormItem: () => (
          <Input placeholder="商户ID" />
        ),
        render: (_, record) => (
          <Text strong>{record.merchantId || '-'}</Text>
        ),
      },
      {
        title: '商户名称',
        dataIndex: 'merchantName',
        key: 'merchantName',
        width: 150,
        renderFormItem: () => (
          <Input placeholder="商户名称" />
        ),
        render: (_, record) => (
          <Text>{record.merchantName || '-'}</Text>
        ),
      },
      {
        title: '订单号',
        dataIndex: 'orderNo',
        key: 'orderNo',
        width: 180,
        renderFormItem: () => (
          <Input placeholder="订单号" />
        ),
        render: (_, record) => {
          const orderNo = record.orderNo;
          if (!orderNo) return '-';
          return (
            <Tooltip title={orderNo}>
              <Space>
                <Text code style={{ maxWidth: '120px' }} ellipsis>
                  {formatOrderNo(orderNo)}
                </Text>
                <Button 
                  icon={<CopyOutlined />} 
                  size="small" 
                  type="text"
                  onClick={() => copyToClipboard(orderNo, '订单号复制成功')}
                />
              </Space>
            </Tooltip>
          );
        },
      },
      {
        title: '提现类型',
        key: 'withdrawType',
        width: 120,
        search: false,
        render: (_, record) => (
          <Space direction="vertical" size="small">
            <Text>{getWithdrawTypeText(record)}</Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.tokenName}
            </Text>
          </Space>
        ),
      },
      {
        title: '申请金额',
        dataIndex: 'amount',
        key: 'amount',
        width: 150,
        search: false,
        render: (_, record) => (
          <Space direction="vertical" size="small">
            <Text strong style={{ color: '#1890ff' }}>
              {formatAmount(record.amount)}
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.tokenName}
            </Text>
          </Space>
        ),
      },
      {
        title: '实际到账',
        dataIndex: 'actualAmount',
        key: 'actualAmount',
        width: 150,
        search: false,
        render: (_, record) => (
          <Space direction="vertical" size="small">
            <Text strong style={{ color: '#52c41a' }}>
              {formatAmount(record.actualAmount)}
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              手续费: {formatAmount(record.handlingFee)}
            </Text>
          </Space>
        ),
      },
      {
        title: '状态',
        dataIndex: 'state',
        key: 'state',
        width: 120,
        valueEnum: WITHDRAW_STATUS_ENUM,
        render: (_, record) => (
          <Space direction="vertical" size="small">
            {getWithdrawStatusTag(record.state as any)}
            {needLongProcessingAlert(record) && (
              <Text type="warning" style={{ fontSize: '12px' }}>
                ⚠️ 处理中
              </Text>
            )}
            {record.stateText && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {record.stateText}
              </Text>
            )}
          </Space>
        ),
      },
      {
        title: '交易信息',
        key: 'transactionInfo',
        width: 200,
        search: false,
        render: (_, record) => (
          <Space direction="vertical" size="small">
            {record.txHash ? (
              <div>
                <Text type="secondary" style={{ fontSize: '12px' }}>交易哈希:</Text>
                <br />
                <Tooltip title={record.txHash}>
                  <Space>
                    <Text code style={{ fontSize: '11px' }}>
                      {formatTxHash(record.txHash)}
                    </Text>
                    <Button 
                      icon={<CopyOutlined />} 
                      size="small" 
                      type="text"
                      onClick={() => copyToClipboard(record.txHash!, '交易哈希复制成功')}
                    />
                  </Space>
                </Tooltip>
              </div>
            ) : (
              <Text type="secondary" style={{ fontSize: '12px' }}>暂无交易哈希</Text>
            )}
            {record.address && (
              <div>
                <Text type="secondary" style={{ fontSize: '12px' }}>地址:</Text>
                <br />
                <Tooltip title={record.address}>
                  <Text code style={{ fontSize: '11px' }}>
                    {truncateText(record.address, 16)}
                  </Text>
                </Tooltip>
              </div>
            )}
            {record.recipientName && (
              <div>
                <Text type="secondary" style={{ fontSize: '12px' }}>收款人:</Text>
                <br />
                <Text style={{ fontSize: '11px' }}>
                  {record.recipientName}
                </Text>
              </div>
            )}
          </Space>
        ),
      },
      {
        title: '通知状态',
        dataIndex: 'notificationSent',
        key: 'notificationSent',
        width: 100,
        search: false,
        valueEnum: NOTIFICATION_STATUS_ENUM,
        render: (_, record) => getNotificationStatusTag(record.notificationSent as any),
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 160,
        defaultSortOrder: 'descend',
        hideInSearch: true,
        render: (_, record) => (
          <Space direction="vertical" size="small">
            <Text>{formatDate(record.createdAt)}</Text>
            {record.completedAt && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                完成: {formatDate(record.completedAt)}
              </Text>
            )}
          </Space>
        ),
      },
      {
        title: '操作',
        key: 'action',
        valueType: 'option',
        width: 120,
        fixed: 'right',
        render: (_, record) => [
          <Button 
            type="link" 
            key="view" 
            icon={<EyeOutlined />}
            onClick={() => viewDetail(record.withdrawsId!)}
            size="small"
          >
            详情
          </Button>,
          canCancelWithdraw(record) && (
            <Button 
              type="link" 
              key="cancel" 
              icon={<CloseOutlined />}
              danger
              onClick={() => openCancelModal(record)}
              size="small"
            >
              撤销
            </Button>
          ),
        ].filter(Boolean),
      },
    ],
    [viewDetail, openCancelModal],
  );

  // 使用统一字段系统合并时间范围搜索功能
  const columns = useUnifiedFields<WithdrawRecord & UnifiedAgentSearchFields>(
    baseColumns,
    { 
      dateRange: { 
        search: true, 
        display: false 
      } 
    },
    undefined, // no custom renders
    'after' // add unified fields after base columns
  );

  // 处理导出
  const handleExport = async () => {
    // 获取当前搜索表单的参数
    const searchParams = formRef.current?.getFieldsValue() || {};
    
    // 转换日期范围格式以保持与搜索一致，同时保护高级导出功能
    if (searchParams.dateRange && Array.isArray(searchParams.dateRange)) {
      try {
        const formattedDateRange = formatDateRange(searchParams.dateRange);
        if (formattedDateRange) {
          searchParams.dateRange = formattedDateRange;
        } else {
          // 如果格式化失败，移除无效的日期范围参数
          delete searchParams.dateRange;
        }
      } catch (error) {
        console.warn('日期范围格式化失败:', error);
        // 移除无效的日期范围参数，继续导出其他数据
        delete searchParams.dateRange;
      }
    }
    
    // 保持原有的高级导出功能不变
    await exportRecords(searchParams, 'csv');
  };

  return (
    <>
      {/* 主表格 */}
      <ProTable<WithdrawRecord & UnifiedAgentSearchFields, WithdrawRecordsRequestParams>
        headerTitle="代付订单管理"
        actionRef={tableRef}
        formRef={formRef}
        rowKey="withdrawsId"
        search={{
          labelWidth: 120,
          defaultCollapsed: false,
        }}
        request={fetchWithdrawRecords}
        columns={columns}
        scroll={{ x: 1800 }}
        toolBarRender={() => [
          <Button
            key="refresh"
            icon={<ReloadOutlined />}
            onClick={refreshTable}
          >
            刷新
          </Button>,
          <Button
            key="export"
            icon={<ExportOutlined />}
            onClick={handleExport}
            loading={exporting}
          >
            导出
          </Button>,
        ]}
        options={{
          reload: true,
          setting: true,
          density: true,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: false,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
        }}
      />

      {/* 详情抽屉 */}
      <WithdrawDetailDrawer
        visible={detailVisible}
        loading={detailLoading}
        currentWithdraw={currentWithdraw}
        onClose={closeDetail}
        onCancel={async (withdrawId, reason) => {
          const success = await cancelWithdraw(withdrawId, reason);
          if (success) {
            closeDetail();
          }
        }}
      />

      {/* 撤销确认模态框 */}
      <CancelWithdrawModal
        visible={cancelModalVisible}
        loading={cancelLoading}
        withdrawRecord={selectedWithdraw}
        onCancel={handleCancelWithdraw}
        onClose={closeCancelModal}
      />
    </>
  );
};

export default WithdrawOrdersTab;