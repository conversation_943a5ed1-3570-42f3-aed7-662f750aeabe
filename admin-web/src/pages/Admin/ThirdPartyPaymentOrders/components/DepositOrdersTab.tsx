import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Button, Input, Space, Typography, Tooltip } from 'antd';
import { ExportOutlined, ReloadOutlined, CopyOutlined } from '@ant-design/icons';
import React, { useMemo, useRef } from 'react';
import { useUnifiedFields } from '@/components/UnifiedFields';
import type { UnifiedAgentSearchFields } from '@/types/search';
import { formatDateRange } from '@/types/search';
import DepositDetailDrawer from '../../DepositRecordsManagement/components/DepositDetailDrawer';
import { 
  DEPOSIT_STATUS_ENUM, 
  NOTIFICATION_STATUS_ENUM, 
  TABLE_SCROLL 
} from '../../DepositRecordsManagement/constants';
import { useDepositRecords } from '../../DepositRecordsManagement/hooks';
import type { DepositRecordsRequestParams, DepositRecord } from '../../DepositRecordsManagement/types';
import { 
  getDepositStatusTag, 
  getNotificationStatusTag,
  formatDate, 
  formatAmount,
  truncateText,
  copyToClipboard
} from '../../DepositRecordsManagement/utils';

const { Text } = Typography;

/**
 * 代收订单页面组件
 */
const DepositOrdersTab: React.FC = () => {
  const {
    tableRef,
    detailVisible,
    currentDeposit,
    detailLoading,
    exporting,
    fetchDepositRecords,
    viewDetail,
    closeDetail,
    exportRecords,
    refreshTable,
  } = useDepositRecords();

  // 创建表单引用，用于获取搜索参数
  const formRef = useRef<ProFormInstance>();

  // 定义基础表格列（不包含时间范围搜索）
  const baseColumns: ProColumns<DepositRecord & UnifiedAgentSearchFields>[] = useMemo(
    () => [
      {
        title: '记录ID',
        dataIndex: 'rechargesId',
        key: 'rechargesId',
        width: 100,
        search: false,
        fixed: 'left',
      },
      {
        title: '商户ID',
        dataIndex: 'merchantId',
        key: 'merchantId',
        width: 120,
        renderFormItem: () => (
          <Input placeholder="商户ID" />
        ),
        render: (_, record) => (
          <Text strong>{record.merchantId || '-'}</Text>
        ),
      },
      {
        title: '商户名称',
        dataIndex: 'merchantName',
        key: 'merchantName',
        width: 150,
        renderFormItem: () => (
          <Input placeholder="商户名称" />
        ),
        render: (_, record) => (
          <Text>{record.merchantName || '-'}</Text>
        ),
      },
      {
        title: '币种',
        dataIndex: 'tokenName',
        key: 'tokenName',
        width: 100,
        renderFormItem: () => (
          <Input placeholder="币种名称" />
        ),
        render: (text, record) => (
          <Space direction="vertical" size="small">
            <Text strong>{text}</Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              ID: {record.tokenId}
            </Text>
          </Space>
        ),
      },
      {
        title: '充值数量',
        dataIndex: 'amount',
        key: 'amount',
        width: 150,
        search: false,
        render: (_, record) => (
          <Space direction="vertical" size="small">
            <Text strong style={{ color: '#52c41a' }}>
              {formatAmount(record.amount)}
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.tokenName}
            </Text>
          </Space>
        ),
      },
      {
        title: '状态',
        dataIndex: 'state',
        key: 'state',
        width: 100,
        valueEnum: DEPOSIT_STATUS_ENUM,
        render: (_, record) => (
          <Space direction="vertical" size="small">
            {getDepositStatusTag(record.state as any)}
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.stateText}
            </Text>
          </Space>
        ),
      },
      {
        title: '交易哈希',
        dataIndex: 'txHash',
        key: 'txHash',
        width: 200,
        ellipsis: true,
        renderFormItem: () => (
          <Input placeholder="交易哈希" />
        ),
        render: (_, record) => {
          const txHash = record.txHash;
          if (!txHash) return '-';
          return (
            <Tooltip title={txHash}>
              <Space>
                <Text code style={{ maxWidth: '150px' }} ellipsis>
                  {truncateText(txHash, 16)}
                </Text>
                <Button 
                  icon={<CopyOutlined />} 
                  size="small" 
                  type="text"
                  onClick={() => copyToClipboard(txHash, '交易哈希复制成功')}
                />
              </Space>
            </Tooltip>
          );
        },
      },
      {
        title: '地址信息',
        key: 'addresses',
        width: 200,
        search: false,
        render: (_, record) => (
          <Space direction="vertical" size="small">
            <div>
              <Text type="secondary" style={{ fontSize: '12px' }}>来源:</Text>
              <br />
              <Tooltip title={record.fromAddress}>
                <Text code style={{ fontSize: '11px' }}>
                  {truncateText(record.fromAddress, 12)}
                </Text>
              </Tooltip>
            </div>
            <div>
              <Text type="secondary" style={{ fontSize: '12px' }}>目标:</Text>
              <br />
              <Tooltip title={record.toAddress}>
                <Text code style={{ fontSize: '11px' }}>
                  {truncateText(record.toAddress, 12)}
                </Text>
              </Tooltip>
            </div>
          </Space>
        ),
      },
      {
        title: '确认数',
        dataIndex: 'confirmations',
        key: 'confirmations',
        width: 80,
        search: false,
        render: (_, record) => record.confirmations || '-',
      },
      {
        title: '通知状态',
        dataIndex: 'notificationSent',
        key: 'notificationSent',
        width: 100,
        search: false,
        valueEnum: NOTIFICATION_STATUS_ENUM,
        render: (_, record) => getNotificationStatusTag(record.notificationSent as any),
      },
      {
        title: '渠道',
        dataIndex: 'chan',
        key: 'chan',
        width: 100,
        search: false,
        render: (_, record) => record.chan || '-',
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 160,
        defaultSortOrder: 'descend',
        hideInSearch: true,
        render: (_, record) => (
          <Space direction="vertical" size="small">
            <Text>{formatDate(record.createdAt)}</Text>
            {record.completedAt && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                完成: {formatDate(record.completedAt)}
              </Text>
            )}
          </Space>
        ),
      },
      {
        title: '操作',
        key: 'action',
        valueType: 'option',
        width: 120,
        fixed: 'right',
        render: (_, record) => [
          <Button 
            type="link" 
            key="view" 
            onClick={() => viewDetail(record.rechargesId!)}
            size="small"
          >
            详情
          </Button>,
        ],
      },
    ],
    [viewDetail],
  );

  // 使用统一字段系统合并时间范围搜索功能
  const columns = useUnifiedFields<DepositRecord & UnifiedAgentSearchFields>(
    baseColumns,
    { 
      dateRange: { 
        search: true, 
        display: false 
      } 
    },
    undefined, // no custom renders
    'after' // add unified fields after base columns
  );

  // 处理导出
  const handleExport = async () => {
    // 获取当前搜索表单的参数
    const searchParams = formRef.current?.getFieldsValue() || {};
    
    // 转换日期范围格式以保持与搜索一致
    if (searchParams.dateRange && Array.isArray(searchParams.dateRange)) {
      try {
        const formattedDateRange = formatDateRange(searchParams.dateRange);
        if (formattedDateRange) {
          searchParams.dateRange = formattedDateRange;
        } else {
          // 如果格式化失败，移除无效的日期范围参数
          delete searchParams.dateRange;
        }
      } catch (error) {
        console.warn('日期范围格式化失败:', error);
        // 移除无效的日期范围参数，继续导出其他数据
        delete searchParams.dateRange;
      }
    }
    
    await exportRecords(searchParams);
  };

  return (
    <>
      <ProTable<DepositRecord & UnifiedAgentSearchFields, DepositRecordsRequestParams>
        headerTitle="代收订单管理"
        actionRef={tableRef}
        formRef={formRef}
        rowKey="rechargesId"
        search={{
          labelWidth: 120,
          defaultCollapsed: false,
        }}
        request={fetchDepositRecords}
        columns={columns}
        scroll={{ x: 1600 }}
        toolBarRender={() => [
          <Button
            key="refresh"
            icon={<ReloadOutlined />}
            onClick={refreshTable}
          >
            刷新
          </Button>,
          <Button
            key="export"
            icon={<ExportOutlined />}
            onClick={handleExport}
            loading={exporting}
          >
            导出
          </Button>,
        ]}
        options={{
          reload: true,
          setting: true,
          density: true,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: false,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
        }}
      />

      <DepositDetailDrawer
        visible={detailVisible}
        loading={detailLoading}
        currentDeposit={currentDeposit}
        onClose={closeDetail}
      />
    </>
  );
};

export default DepositOrdersTab;