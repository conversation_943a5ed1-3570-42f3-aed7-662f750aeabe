import { CopyOutlined, EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons';
import { Button, Space, Tooltip, Typography } from 'antd';
import React, { useState } from 'react';
import { copyToClipboard, truncateApiKey } from '../utils';

const { Text } = Typography;

interface ApiKeyDisplayProps {
  apiKey: string;
  maxLength?: number;
}

const ApiKeyDisplay: React.FC<ApiKeyDisplayProps> = ({ 
  apiKey, 
  maxLength = 20 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  if (!apiKey) {
    return <Text type="secondary">-</Text>;
  }

  const shouldTruncate = apiKey.length > maxLength;
  const displayText = shouldTruncate && !isExpanded 
    ? truncateApiKey(apiKey, maxLength)
    : apiKey;

  return (
    <Space size="small">
      <Tooltip title={shouldTruncate && !isExpanded ? apiKey : undefined}>
        <Text 
          code 
          style={{ 
            fontFamily: 'monospace',
            fontSize: '12px',
            maxWidth: isExpanded ? 'none' : '200px',
            wordBreak: 'break-all'
          }}
        >
          {displayText}
        </Text>
      </Tooltip>
      
      {shouldTruncate && (
        <Button
          type="text"
          size="small"
          icon={isExpanded ? <EyeInvisibleOutlined /> : <EyeOutlined />}
          onClick={() => setIsExpanded(!isExpanded)}
          title={isExpanded ? '收起' : '展开'}
        />
      )}
      
      <Button
        type="text"
        size="small"
        icon={<CopyOutlined />}
        onClick={() => copyToClipboard(apiKey)}
        title="复制API KEY"
      />
    </Space>
  );
};

export default ApiKeyDisplay;
