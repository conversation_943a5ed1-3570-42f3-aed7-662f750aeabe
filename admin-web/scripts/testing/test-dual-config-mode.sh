#!/bin/bash

echo "=== 测试双模式配置系统 ==="
echo ""

# 测试目录
TEST_DIR="/tmp/admin-web-dual-config-test"
mkdir -p "$TEST_DIR"

# 复制 entrypoint.sh
cp deployment/entrypoint.sh "$TEST_DIR/entrypoint-test.sh"
sed -i 's|/usr/share/nginx/html/runtime-config.json|'"$TEST_DIR"'/runtime-config.json|g' "$TEST_DIR/entrypoint-test.sh"

echo "测试 1: 优先使用 PATH_TO_SECRET_FILE（文件存在）"
echo "================================================"

# 创建测试的密钥文件
cat > "$TEST_DIR/test-secrets.json" << EOF
{
  "UMI_APP_API_URL": "http://file-based-api.example.com/",
  "UMI_APP_SDK_SERVER_URL": "https://file-based-auth.example.com/",
  "UMI_APP_SDK_CLIENT_ID": "file-client-id-123",
  "UMI_APP_SDK_ORGANIZATION_NAME": "file-org",
  "UMI_APP_SDK_APP_NAME": "file-app",
  "UMI_APP_SDK_REDIRECT_PATH": "/file-callback"
}
EOF

# 设置环境变量（这些应该被文件覆盖）
export UMI_APP_API_URL="http://env-api.example.com/"
export UMI_APP_SDK_SERVER_URL="https://env-auth.example.com/"
export PATH_TO_SECRET_FILE="$TEST_DIR/test-secrets.json"

# 运行脚本
echo "运行 entrypoint.sh（有密钥文件）..."
bash "$TEST_DIR/entrypoint-test.sh" echo "Test completed" 2>&1 | grep -E "(INFO|ERROR|Warning)"

# 检查结果
echo ""
echo "生成的配置："
if [ -f "$TEST_DIR/runtime-config.json" ]; then
    cat "$TEST_DIR/runtime-config.json" | python3 -m json.tool
    echo ""
    echo "✅ 验证：配置来自文件（API URL 应该是 file-based-api.example.com）"
fi

# 清理
rm -f "$TEST_DIR/runtime-config.json"

echo ""
echo "测试 2: 使用环境变量（文件不存在）"
echo "==================================="

# 删除密钥文件
rm -f "$TEST_DIR/test-secrets.json"

# 重新设置环境变量
export UMI_APP_API_URL="http://env-api.example.com/"
export UMI_APP_SDK_SERVER_URL="https://env-auth.example.com/"
export UMI_APP_SDK_CLIENT_ID="env-client-id-456"
export UMI_APP_SDK_ORGANIZATION_NAME="env-org"
export UMI_APP_SDK_APP_NAME="env-app"
export UMI_APP_SDK_REDIRECT_PATH="/env-callback"

# 运行脚本
echo "运行 entrypoint.sh（无密钥文件）..."
bash "$TEST_DIR/entrypoint-test.sh" echo "Test completed" 2>&1 | grep -E "(INFO|ERROR|Warning)"

# 检查结果
echo ""
echo "生成的配置："
if [ -f "$TEST_DIR/runtime-config.json" ]; then
    cat "$TEST_DIR/runtime-config.json" | python3 -m json.tool
    echo ""
    echo "✅ 验证：配置来自环境变量（API URL 应该是 env-api.example.com）"
fi

echo ""
echo "测试 3: 错误处理（无文件也无环境变量）"
echo "======================================"

# 清除所有环境变量
unset UMI_APP_API_URL
unset UMI_APP_SDK_SERVER_URL
unset PATH_TO_SECRET_FILE

# 运行脚本（应该失败）
echo "运行 entrypoint.sh（无配置）..."
bash "$TEST_DIR/entrypoint-test.sh" echo "Test completed" 2>&1 | grep -E "(INFO|ERROR)"

echo ""
echo "=== 测试总结 ==="
echo "新的 entrypoint.sh 支持："
echo "1. ✅ 优先从 PATH_TO_SECRET_FILE 读取配置（如果文件存在）"
echo "2. ✅ 回退到环境变量（如果文件不存在）"
echo "3. ✅ 错误处理（两者都没有时报错）"
echo "4. ✅ 完全兼容运维的K8s配置"

# 清理
rm -rf "$TEST_DIR"