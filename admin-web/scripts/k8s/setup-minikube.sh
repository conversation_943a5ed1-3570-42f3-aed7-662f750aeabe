#!/bin/bash

echo "=== 安装 Minikube 本地 Kubernetes 环境 ==="
echo ""

# 检测系统
OS="$(uname -s)"
ARCH="$(uname -m)"

echo "检测到系统: $OS $ARCH"

# 1. 安装 kubectl
echo ""
echo "1. 安装 kubectl..."
if ! command -v kubectl &> /dev/null; then
    if [[ "$OS" == "Linux" ]]; then
        curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
        sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
        rm kubectl
    fi
    echo "✅ kubectl 安装完成"
else
    echo "✅ kubectl 已安装: $(kubectl version --client --short)"
fi

# 2. 安装 Minikube
echo ""
echo "2. 安装 Minikube..."
if ! command -v minikube &> /dev/null; then
    if [[ "$OS" == "Linux" ]]; then
        curl -LO https://storage.googleapis.com/minikube/releases/latest/minikube-linux-amd64
        sudo install minikube-linux-amd64 /usr/local/bin/minikube
        rm minikube-linux-amd64
    fi
    echo "✅ Minikube 安装完成"
else
    echo "✅ Minikube 已安装: $(minikube version --short)"
fi

# 3. 检查 Docker
echo ""
echo "3. 检查 Docker..."
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    echo "安装指南: https://docs.docker.com/engine/install/"
    exit 1
else
    echo "✅ Docker 已安装: $(docker --version)"
fi

# 4. 启动 Minikube
echo ""
echo "4. 启动 Minikube..."
if minikube status | grep -q "Running"; then
    echo "✅ Minikube 已在运行"
else
    echo "启动 Minikube（使用 Docker 驱动）..."
    minikube start --driver=docker --cpus=2 --memory=4096
fi

# 5. 验证安装
echo ""
echo "5. 验证 Kubernetes 集群..."
kubectl cluster-info
echo ""
kubectl get nodes

echo ""
echo "=== Minikube 安装完成 ==="
echo ""
echo "常用命令:"
echo "- 查看状态: minikube status"
echo "- 停止集群: minikube stop"
echo "- 删除集群: minikube delete"
echo "- 访问仪表板: minikube dashboard"
echo ""
echo "下一步："
echo "1. 运行 ./test-k8s-deployment-local.sh 来测试运维的部署脚本"