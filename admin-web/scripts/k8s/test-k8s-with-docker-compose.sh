#!/bin/bash

echo "=== 使用 Docker Compose 模拟 K8s 部署测试 ==="
echo ""

# 创建测试目录
TEST_DIR="./k8s-simulation"
mkdir -p "$TEST_DIR/secrets"

# 1. 创建模拟的 AWS Secrets 文件
echo "1. 创建模拟的 AWS Secrets 文件..."
cat > "$TEST_DIR/secrets/wallet_admin-web" << EOF
{
  "UMI_APP_API_URL": "http://aws-secret-api.example.com/",
  "UMI_APP_SDK_SERVER_URL": "https://aws-secret-auth.example.com/",
  "UMI_APP_SDK_CLIENT_ID": "aws-secret-client-789",
  "UMI_APP_SDK_ORGANIZATION_NAME": "aws-org",
  "UMI_APP_SDK_APP_NAME": "aws-app",
  "UMI_APP_SDK_REDIRECT_PATH": "/aws-callback"
}
EOF

# 2. 创建 docker-compose.yml
echo "2. 创建 docker-compose.yml..."
cat > "$TEST_DIR/docker-compose.yml" << 'EOF'
version: '3.8'

services:
  admin-web:
    build:
      context: ..
      dockerfile: deployment/Dockerfile
    ports:
      - "8080:80"
    environment:
      # 模拟运维的 K8s 环境变量配置
      - PATH_TO_SECRET_FILE=/mnt/secrets-store/wallet_admin-web
      - UMI_APP_API_URL=http://api-admin-dev.jjpay.co/
      - UMI_APP_SDK_SERVER_URL=https://admin-dev.jjpay.co/
      - UMI_APP_SDK_CLIENT_ID=47637dde487603346a77
      - UMI_APP_SDK_ORGANIZATION_NAME=xpay
      - UMI_APP_SDK_APP_NAME=application_xpay
      - UMI_APP_SDK_REDIRECT_PATH=/callback
      - APP_NAME=admin_api
    volumes:
      # 模拟 K8s CSI 挂载的密钥文件
      - ./secrets:/mnt/secrets-store:ro
    command: ["nginx", "-g", "daemon off;"]
EOF

# 3. 构建并启动
echo "3. 构建并启动容器..."
cd "$TEST_DIR"
docker-compose up -d --build

# 4. 等待启动
echo ""
echo "4. 等待容器启动..."
sleep 5

# 5. 查看启动日志
echo ""
echo "5. 容器启动日志："
echo "=================================="
docker-compose logs | grep -E "\[INFO\]|\[ERROR\]" | head -30

# 6. 检查配置加载结果
echo ""
echo "6. 检查生成的配置："
echo "=================================="

# 检查 runtime-config.json
echo "runtime-config.json 内容："
docker-compose exec admin-web cat /usr/share/nginx/html/runtime-config.json 2>/dev/null | python3 -m json.tool || echo "无法读取配置文件"

# 7. 测试访问
echo ""
echo "7. 测试访问："
echo "=================================="
echo "测试主页："
curl -s http://localhost:8080/ | head -n 5

echo ""
echo "测试配置接口："
curl -s http://localhost:8080/runtime-config.json | python3 -m json.tool

# 8. 分析结果
echo ""
echo "8. 结果分析："
echo "=================================="
CONFIG_CONTENT=$(docker-compose exec admin-web cat /usr/share/nginx/html/runtime-config.json 2>/dev/null || echo "{}")
if echo "$CONFIG_CONTENT" | grep -q "aws-secret-api.example.com"; then
    echo "✅ 成功：配置来自 AWS Secrets 文件（优先级更高）"
elif echo "$CONFIG_CONTENT" | grep -q "api-admin-dev.jjpay.co"; then
    echo "⚠️  配置来自环境变量（密钥文件可能未正确加载）"
else
    echo "❌ 错误：无法确定配置来源"
fi

# 9. 清理提示
echo ""
echo "=== 测试完成 ==="
echo ""
echo "清理命令："
echo "- 停止容器: cd $TEST_DIR && docker-compose down"
echo "- 查看日志: cd $TEST_DIR && docker-compose logs -f"
echo "- 进入容器: cd $TEST_DIR && docker-compose exec admin-web /bin/sh"
echo ""
echo "访问应用: http://localhost:8080"