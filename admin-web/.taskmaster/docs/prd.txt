产品需求文档 - 统一搜索条件重构

## 项目概述
本项目旨在重构管理后台系统的搜索功能，将已经在API接口层面统一的搜索条件应用到前端页面组件中。目前系统中有16个模块的API接口已经包含了统一的搜索条件，但前端页面组件还没有使用这些条件。

## 背景
1. 系统已经在API层面统一了用户相关接口的搜索条件
2. 共有16个模块需要添加统一搜索条件
3. 统一的搜索条件包括：dateRange、firstAgentName、secondAgentName、thirdAgentName、telegramId、telegramUsername、firstName

## 需求详细说明

### 1. 创建通用搜索组件
- 创建一个通用的AgentSearchFields组件，包含所有统一搜索字段
- 组件应该支持灵活配置，可以选择性显示某些字段
- 提供良好的UI布局，适配ProTable的搜索表单
- 支持日期范围选择器
- 所有文本字段支持模糊搜索

### 2. 需要改造的模块列表
根据API接口文件，需要改造以下16个模块：
1. 用户管理 (UserManagement) - adminApiApiSystemV1GetUserListReq
2. 用户备份账户管理 (UserBackupManagement) - adminApiApiSystemV1GetUserBackupAccountsReq
3. 备份账户管理 (BackupAccountManagement) - adminApiApiSystemV1GetBackupAccountsReq
4. 登录日志管理 (LoginLogManagement) - adminApiApiSystemV1GetLoginLogListReq
5. 推荐佣金管理 (ReferralCommissionManagement) - adminApiApiSystemV1GetReferralCommissionListReq
6. 红包管理 (RedPacketManagement) - adminApiApiSystemV1ListAdminRedPacketsReq
7. 红包领取记录 (RedPacketClaimManagement) - adminApiApiSystemV1ListAdminRedPacketClaimsReq
8. 红包图片管理 (RedPacketImageManagement) - adminApiApiSystemV1ListRedPacketImagesReq
9. 交易记录管理 (TransactionManagement) - adminApiApiSystemV1ListAdminTransactionsReq
10. 转账记录管理 (TransferManagement) - adminApiApiSystemV1ListAdminTransfersReq
11. 支付请求管理 (PaymentRequestManagement) - adminApiApiSystemV1ListPaymentRequestReq
12. 用户地址管理 (UserAddressManagement) - adminApiApiSystemV1ListUserAddressesReq
13. 用户充值管理 (UserRechargeManagement) - adminApiApiSystemV1ListUserRechargesReq
14. 用户提现管理 (UserWithdrawManagement) - adminApiApiSystemV1ListUserWithdrawsReq
15. 钱包管理 (WalletManagement) - adminApiApiSystemV1ListWalletsReq
16. 兑换订单管理 (ExchangeOrderManagement) - adminApiApiSystemV1ExchangeOrderListReq

### 3. 技术要求
- 使用React和TypeScript开发
- 基于Ant Design Pro组件库
- 与现有的ProTable组件无缝集成
- 保持代码风格一致性
- 确保类型安全

### 4. 实施步骤
1. 创建通用的AgentSearchFields组件
2. 创建搜索条件的类型定义
3. 为每个模块找到对应的页面组件
4. 逐个模块集成统一搜索条件
5. 测试每个模块的搜索功能
6. 优化UI布局和用户体验

### 5. 预期成果
- 所有16个模块都使用统一的搜索条件
- 提高系统的一致性和可维护性
- 改善用户体验，搜索功能更加完善
- 代码复用率提高，减少重复代码

### 6. 注意事项
- 保持向后兼容，不影响现有功能
- 确保搜索参数正确转换为API需要的格式
- 处理好空值和默认值
- 提供良好的错误处理和用户反馈