# X-Pay Admin Web 部署指南

## 📋 概述

本项目现在支持通过 JSON 配置文件在构建时生成 `Conf.ts` 文件，实现更灵活的环境配置管理。

## 🏗️ 新的启动流程

### 1. 配置文件结构

```
deployment/
├── config_variables.json    # 环境变量配置
├── Conf.ts.template        # TypeScript 配置模板
├── Dockerfile             # Docker 构建文件
└── docker-compose.yml     # 容器编排配置
```

### 2. 配置说明

#### `config_variables.json`

包含所有环境变量的 JSON 配置文件：

```json
{
  "UMI_APP_API_URL": "http://**************:7999",
  "UMI_APP_SDK_SERVER_URL": "http://**************:8000",
  "UMI_APP_SDK_CLIENT_ID": "9572007bf31d2ff690b4",
  "UMI_APP_SDK_ORGANIZATION_NAME": "xpay",
  "UMI_APP_SDK_APP_NAME": "application_xpay",
  "UMI_APP_SDK_REDIRECT_PATH": "/callback"
}
```

#### `Conf.ts.template`

TypeScript 配置模板文件，使用 `${VARIABLE_NAME}` 语法：

```typescript
export const envConfig = {
  // API Configuration
  apiUrl: '${UMI_APP_API_URL}',
  umiAppApiUrl: '${UMI_APP_API_URL}',

  // Casdoor SDK Configuration
  serverUrl: '${UMI_APP_SDK_SERVER_URL}',
  clientId: '${UMI_APP_SDK_CLIENT_ID}',
  organizationName: '${UMI_APP_SDK_ORGANIZATION_NAME}',
  appName: '${UMI_APP_SDK_APP_NAME}',
  redirectPath: '${UMI_APP_SDK_REDIRECT_PATH}',
};
```

### 3. 构建流程

1. **Docker 构建开始** → 复制源代码和配置文件
2. **读取配置** → 解析 `config_variables.json`
3. **环境变量优先级处理** →
   - 如果环境中已存在变量（如 CI/CD 设置的），优先使用环境变量
   - 如果环境中不存在，则使用 `config_variables.json` 中的默认值
4. **生成 Conf.ts** → 使用 `envsubst` 替换模板中的变量
5. **构建应用** → 执行 `pnpm build` 构建前端应用
6. **启动 Nginx** → 直接启动 nginx 服务

### 4. 配置优先级

配置的优先级从高到低为：

1. **环境变量** (最高优先级) - 通过 CI/CD、Kubernetes、docker-compose 等设置
2. **config_variables.json** (默认值) - 项目中的配置文件

## 🚀 部署命令

### 构建并启动

```bash
cd deployment
docker-compose up --build -d
```

### 查看日志

```bash
docker-compose logs -f admin-web
```

### 停止服务

```bash
docker-compose down
```

## 🔧 自定义配置

### 方法 1: 环境变量覆盖 (推荐用于生产环境)

在 CI/CD 系统、Kubernetes 或 `docker-compose.yml` 中直接设置环境变量：

```yaml
# docker-compose.yml
environment:
  - UMI_APP_API_URL=https://api-prod.example.com
  - UMI_APP_SDK_SERVER_URL=https://sso-prod.example.com
  - UMI_APP_SDK_CLIENT_ID=your-production-client-id
  - UMI_APP_SDK_ORGANIZATION_NAME=your-org
  - UMI_APP_SDK_APP_NAME=your-app
  - UMI_APP_SDK_REDIRECT_PATH=/callback
```

```yaml
# Kubernetes Deployment
env:
  - name: UMI_APP_API_URL
    value: 'https://api-prod.example.com'
  - name: UMI_APP_SDK_SERVER_URL
    value: 'https://sso-prod.example.com'
  # ... 其他环境变量
```

### 方法 2: 修改 config_variables.json (用于开发环境)

直接编辑 `deployment/config_variables.json` 文件，然后重新构建镜像。

### 方法 3: 挂载外部配置文件

在 `docker-compose.yml` 中添加 volume 挂载：

```yaml
volumes:
  - ./custom_config.json:/app/config_variables.json
```

## 📝 构建日志说明

构建时会看到类似以下日志，显示配置的优先级处理：

```
Loading configuration with environment variable priority...
Using existing UMI_APP_API_URL=https://api-admin-dev.jjpay.co (from environment)
Using existing UMI_APP_SDK_SERVER_URL=https://sso-dev.jjpay.co (from environment)
Using existing UMI_APP_SDK_CLIENT_ID=47637dde487603346a77 (from environment)
Set UMI_APP_SDK_ORGANIZATION_NAME=organization_xpay (from config file)
Set UMI_APP_SDK_APP_NAME=application_xpay (from config file)
Set UMI_APP_SDK_REDIRECT_PATH=/callback (from config file)

Final environment variables for template substitution:
UMI_APP_API_URL=https://api-admin-dev.jjpay.co
UMI_APP_SDK_SERVER_URL=https://sso-dev.jjpay.co
UMI_APP_SDK_CLIENT_ID=47637dde487603346a77
UMI_APP_SDK_ORGANIZATION_NAME=organization_xpay
UMI_APP_SDK_APP_NAME=application_xpay
UMI_APP_SDK_REDIRECT_PATH=/callback

Generated Conf.ts:
export const envConfig = {
  // API Configuration
  apiUrl: "https://api-admin-dev.jjpay.co",
  umiAppApiUrl: "https://api-admin-dev.jjpay.co",
  // Casdoor SDK Configuration
  serverUrl: "https://sso-dev.jjpay.co",
  clientId: "47637dde487603346a77",
  organizationName: "organization_xpay",
  appName: "application_xpay",
  redirectPath: "/callback",
};
```

从日志中可以看到：

- 哪些变量来自环境变量 (from environment)
- 哪些变量来自配置文件 (from config file)
- 最终生成的配置内容

## 🔍 故障排除

### 检查生成的 Conf.ts 文件

由于 Conf.ts 是在构建时生成的，可以通过查看构建日志来确认配置是否正确生成。

### 检查配置文件

```bash
# 查看当前的配置文件
cat deployment/config_variables.json
```

### 重新构建镜像

如果修改了配置文件，需要重新构建镜像：

```bash
cd deployment
docker-compose build --no-cache
docker-compose up -d
```

### 查看详细日志

```bash
docker-compose logs admin-web
```

## ⚠️ 注意事项

1. **JSON 格式**：确保 `config_variables.json` 是有效的 JSON 格式
2. **变量名称**：模板中的变量名必须与 JSON 中的键名完全匹配
3. **权限问题**：entrypoint.sh 脚本已设置为可执行权限
4. **网络配置**：确保 `xpay_app-network` 网络已创建

## 🔄 迁移说明

从旧的静态 `.env` 文件迁移到新的动态配置：

1. 将现有的 `.env` 文件内容转换为 JSON 格式
2. 更新 `.env.template` 文件以匹配所需的变量
3. 重新构建并部署容器

这种新的配置方式提供了更好的灵活性和可维护性，特别适合多环境部署场景。
