# 配置值显示功能说明

## 新增功能

为配置管理表格的"配置值 (Value)"列添加了省略展示和点击查看全部的功能。

## 功能特性

### 1. 自动省略长内容

- 当配置值长度超过 50 个字符时，自动省略显示
- 显示格式：`前50个字符...`
- 在省略内容后显示一个眼睛图标按钮

### 2. 点击查看完整内容

- 点击眼睛图标打开模态框
- 模态框中显示完整的配置值
- 支持不同类型的格式化显示

### 3. 类型化显示

#### JSON 和 MAP 类型

- 自动格式化为缩进的 JSON 格式
- 使用等宽字体和代码背景
- 语法高亮显示

#### TEXTAREA 类型

- 保持原始换行格式
- 支持长文本显示

#### 其他类型 (TEXT, NUMBER, PASSWORD)

- 简单文本显示
- 自动换行处理

### 4. 特殊处理

#### BOOLEAN 类型

- 继续使用开关组件显示
- 不使用省略功能（因为开关本身就很简洁）

## 技术实现

### ValueDisplay 组件

```typescript
interface ValueDisplayProps {
  value: string;
  valueType: ValueType;
  maxLength?: number; // 默认 50
}
```

### 主要功能

1. **长度检测**：自动检测内容是否需要省略
2. **模态框展示**：点击图标打开详情模态框
3. **类型化渲染**：根据值类型进行不同的格式化
4. **响应式设计**：模态框支持滚动和自适应高度

## 使用场景

### 1. 长 JSON 配置

```json
{
  "database": {
    "host": "localhost",
    "port": 3306,
    "username": "admin",
    "password": "secret123",
    "options": {
      "charset": "utf8mb4",
      "timeout": 30000
    }
  }
}
```

### 2. MAP 类型客服信息

```json
{
  "客服1": "1",
  "客服2": "2",
  "客服3": "3",
  "技术支持": "<EMAIL>",
  "投诉建议": "<EMAIL>"
}
```

### 3. 长文本配置

```
这是一段很长的配置说明文本，包含了详细的使用说明和注意事项...
```

## 用户体验

1. **表格简洁**：长内容不会撑破表格布局
2. **快速预览**：可以看到内容的开头部分
3. **完整查看**：需要时可以查看完整内容
4. **格式友好**：不同类型有相应的显示格式

## 注意事项

- 眼睛图标只在内容被省略时显示
- 模态框支持键盘 ESC 键关闭
- 内容过长时模态框内部支持滚动
- 保持了原有的 BOOLEAN 类型开关功能
