# X-Pay 前端开发规范

本文档定义了 X-Pay 收款管理系统前端项目的开发规范，以确保代码质量、一致性和可维护性。请所有开发人员和 AI 辅助工具遵循这些规范。

## 项目概览

X-Pay 是一个收款管理系统，包含三种角色：

- 总后台管理（Admin）
- 代理后台（Agent）
- 商户后台（Merchant）

项目基于以下主要技术栈：

- Ant Design Pro
- UmiJS
- React
- TypeScript
- Tailwind CSS
- Lodash-es

## 目录结构规范

```
src/
  ├── assets/         # 静态资源文件
  ├── components/     # 公共组件
  │   ├── [ComponentName]/   # 组件目录
  │       ├── index.tsx      # 组件主文件
  │       ├── index.less     # 组件样式
  │       └── types.ts       # 组件类型定义（可选）
  ├── constants/      # 常量定义
  ├── hooks/          # 自定义 Hooks
  ├── models/         # 全局数据模型
  ├── pages/          # 页面
  │   ├── Admin/      # 总后台页面
  │       ├── [PageName]/   # 页面目录
  │           ├── index.tsx      # 页面主文件
  │           ├── index.less     # 页面样式
  │           └── types.ts       # 页面类型定义（可选）
  │   ├── Agent/      # 代理后台页面
  │   ├── Merchant/   # 商户后台页面
  │   └── Login/      # 登录页面
  ├── services/       # API 服务
  │   ├── api/        # API 定义与调用
  │   └── typings.d.ts # API 类型定义
  └── utils/          # 工具函数
```

## 命名规范

### 文件命名

- 组件文件夹和组件文件使用 PascalCase（首字母大写）：`UserProfile/index.tsx`
- 工具函数、Hooks 使用 camelCase（首字母小写）：`formatDate.ts`、`useRequest.ts`
- 常量文件使用全大写下划线分隔：`API_CONSTANTS.ts`

### 变量命名

- 组件名使用 PascalCase：`const UserProfile = () => {...}`
- 变量和函数使用 camelCase：`const getUserData = () => {...}`
- 常量使用全大写下划线分隔：`const API_BASE_URL = 'https://api.example.com'`
- 布尔类型变量使用 is/has/should 前缀：`isLoading`、`hasError`、`shouldRefresh`

## 组件开发规范

### 组件结构

- 使用函数式组件和 React Hooks
- 组件应导出默认函数：`export default ComponentName`
- 组件属性类型使用 TypeScript 接口定义并导出

```tsx
import React from 'react';
import styles from './index.less';

export interface UserProfileProps {
  userId: string;
  name: string;
  onEdit?: (id: string) => void;
}

const UserProfile: React.FC<UserProfileProps> = ({ userId, name, onEdit }) => {
  return <div className={styles.container}>{/* 组件内容 */}</div>;
};

export default UserProfile;
```

### 组件最佳实践

1. 组件应专注于单一功能
2. 较大组件应拆分为更小的可复用组件
3. 使用 React.memo() 优化纯展示组件
4. 使用自定义 Hooks 抽离业务逻辑

## Ant Design Pro 规范

### 布局组件

- 使用 Ant Design Pro 的 PageContainer 作为页面容器
- 表单使用 ProForm 系列组件
- 表格使用 ProTable 组件，支持搜索、筛选和分页
- 详情页使用 ProDescriptions 组件

```tsx
import { PageContainer } from '@ant-design/pro-layout';
import { ProTable } from '@ant-design/pro-components';

const UserList = () => {
  return (
    <PageContainer title="用户列表">
      <ProTable
        columns={columns}
        request={fetchUsers}
        rowKey="id"
        pagination={{
          showQuickJumper: true,
        }}
        search={{
          labelWidth: 120,
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <Button key="add" type="primary">
            新增
          </Button>,
        ]}
      />
    </PageContainer>
  );
};
```

### 表单处理

- 使用 ProForm 组件处理表单
- 字段验证应在表单组件中定义
- 提交处理逻辑应清晰且包含错误处理

```tsx
import { ProForm, ProFormText } from '@ant-design/pro-components';
import { message } from 'antd';

const UserForm = () => {
  const handleSubmit = async (values) => {
    try {
      await saveUser(values);
      message.success('保存成功');
    } catch (error) {
      message.error('保存失败');
    }
  };

  return (
    <ProForm onFinish={handleSubmit}>
      <ProFormText
        name="username"
        label="用户名"
        rules={[{ required: true, message: '请输入用户名' }]}
      />
      {/* 其他表单项 */}
    </ProForm>
  );
};
```

## CSS 样式规范

### Tailwind CSS 用法

- 优先使用 Tailwind 原子类样式
- 组合使用多个原子类来实现所需样式
- 对于复杂或重复的样式，创建自定义类
- Tailwind 配置文件位于项目根目录的 `tailwind.config.js`
- 已禁用 Tailwind 的 preflight 样式以避免与 Ant Design 样式冲突

```tsx
// 推荐的 Tailwind 用法
<div className="flex items-center p-4 bg-white rounded-lg shadow-md">
  <img className="w-12 h-12 rounded-full mr-4" src={avatar} alt="avatar" />
  <div>
    <h3 className="text-lg font-medium text-gray-900">{name}</h3>
    <p className="text-sm text-gray-500">{role}</p>
  </div>
</div>
```

### Less 样式

- 当 Tailwind 不能满足需求时，使用 Less 文件
- 使用 CSS Modules 避免样式冲突
- 样式选择器应有明确的层级关系，避免过深嵌套（不超过 3 层）

```less
// index.less
.container {
  padding: 16px;

  .header {
    display: flex;
    align-items: center;

    .title {
      font-size: 18px;
      font-weight: 500;
    }
  }
}
```

## 数据处理规范

### API 服务调用

- 使用封装好的 API 服务函数
- 所有 API 服务应返回 Promise
- 使用 try/catch 处理 API 错误

```tsx
import { getUserData } from '@/services/api/user';

const UserDetail = ({ userId }) => {
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(false);

  const fetchUser = async () => {
    setLoading(true);
    try {
      const data = await getUserData(userId);
      setUserData(data);
    } catch (error) {
      message.error('获取用户数据失败');
    } finally {
      setLoading(false);
    }
  };

  // ...
};
```

### 使用 Lodash-es

- 导入时使用解构引入具体方法，减小打包体积
- 优先使用 Lodash 函数进行数据转换和处理

```tsx
// 推荐的 Lodash 导入方式
import { get, debounce, isEmpty } from 'lodash-es';

// 使用示例
const userName = get(userData, 'profile.name', '未知');
const debouncedSearch = debounce(handleSearch, 300);
```

## 类型定义规范

### TypeScript 类型

- 所有组件 Props 必须定义接口并导出
- 所有 API 响应数据必须定义类型
- 避免使用 any 类型，优先使用泛型或联合类型

```tsx
// API 类型定义示例
export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'agent' | 'merchant';
  status: 'active' | 'inactive';
  createdAt: string;
}

// 组件 Props 定义示例
export interface UserTableProps {
  dataSource: User[];
  loading: boolean;
  onEdit: (user: User) => void;
  onDelete: (userId: string) => void;
}
```

## 状态管理规范

### UmiJS Model 使用

- 全局状态使用 UmiJS Model 管理
- Model 文件结构应包含 state, reducers, effects
- 命名空间应与功能模块对应

```tsx
// models/user.ts
export default {
  namespace: 'user',

  state: {
    currentUser: null,
    userList: [],
    loading: false,
  },

  effects: {
    *fetchUserList({ payload }, { call, put }) {
      yield put({ type: 'setLoading', payload: true });
      try {
        const data = yield call(getUserList, payload);
        yield put({ type: 'saveUserList', payload: data });
      } catch (error) {
        // 错误处理
      } finally {
        yield put({ type: 'setLoading', payload: false });
      }
    },
  },

  reducers: {
    saveUserList(state, { payload }) {
      return { ...state, userList: payload };
    },
    setLoading(state, { payload }) {
      return { ...state, loading: payload };
    },
  },
};
```

## 权限控制规范

### 访问控制

- 使用 UmiJS 的 access 功能控制路由访问权限
- 根据用户角色显示/隐藏相应功能
- 页面组件内部也应进行权限检查

```tsx
// access.ts
export default function access(initialState) {
  const { currentUser } = initialState || {};

  return {
    canAdmin: currentUser && currentUser.userRole === 'admin',
    canAgent: currentUser && ['admin', 'agent'].includes(currentUser.userRole),
    canMerchant:
      currentUser &&
      ['admin', 'agent', 'merchant'].includes(currentUser.userRole),
  };
}

// 在组件中使用
import { useAccess } from '@umijs/max';

const SettingsPage = () => {
  const access = useAccess();

  return (
    <div>
      {access.canAdmin && <AdminSettings />}
      <CommonSettings />
    </div>
  );
};
```

## 性能优化规范

1. 使用 React.memo() 避免不必要的重渲染
2. 使用 useCallback 和 useMemo 缓存函数和计算结果
3. 大列表使用虚拟滚动（如 react-window）
4. 图片使用懒加载
5. 代码分割和动态导入大型组件

```tsx
// 代码分割示例
import { lazy, Suspense } from 'react';
const LargeComponent = lazy(() => import('./LargeComponent'));

const App = () => (
  <Suspense fallback={<div>Loading...</div>}>
    <LargeComponent />
  </Suspense>
);
```

## 错误处理规范

1. 所有异步操作应使用 try/catch 捕获异常
2. 全局错误边界捕获渲染错误
3. 表单校验应提供清晰的错误提示
4. API 错误应向用户展示友好的错误信息

```tsx
// 错误边界示例
import React from 'react';

class ErrorBoundary extends React.Component {
  state = { hasError: false, error: null };

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // 可以将错误日志发送到服务端
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <div className="error-page">出错了，请刷新页面重试</div>;
    }
    return this.props.children;
  }
}
```

## 测试规范

1. 每个组件应有单元测试
2. 使用 Jest 和 React Testing Library 编写测试
3. 模拟 API 请求，避免真实网络调用
4. 测试覆盖率应达到 70% 以上

```tsx
// 测试示例
import { render, screen } from '@testing-library/react';
import UserCard from './UserCard';

test('renders user information correctly', () => {
  const user = { id: '1', name: 'John Doe', role: 'admin' };
  render(<UserCard user={user} />);

  expect(screen.getByText('John Doe')).toBeInTheDocument();
  expect(screen.getByText('admin')).toBeInTheDocument();
});
```

## 文档规范

1. 关键组件应有详细注释
2. 复杂的业务逻辑应有流程说明
3. 公共 API 和函数应有 JSDoc 注释

```tsx
/**
 * 格式化金额，添加千位分隔符并保留小数位
 * @param amount - 要格式化的金额
 * @param digits - 保留的小数位数，默认为 2
 * @returns 格式化后的金额字符串
 * @example
 * // 返回 "1,234.56"
 * formatAmount(1234.56);
 */
export const formatAmount = (amount: number, digits: number = 2): string => {
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: digits,
    maximumFractionDigits: digits,
  });
};
```

## AI 助手使用建议

当使用 Cursor AI 或其他 AI 工具开发时，请遵循以下建议：

1. 提供明确的需求描述，包括页面功能、输入输出和交互方式
2. 指定使用的组件库（Ant Design Pro）和样式方案（Tailwind）
3. 说明数据结构和 API 接口规范
4. 要求生成的代码符合本文档的各项规范
5. 对于复杂功能，先让 AI 提供组件结构和数据流设计，再逐步实现

示例提示：

```
请帮我实现一个商户交易列表页面，要求：
1. 使用 ProTable 组件展示数据
2. 包含搜索功能，可按交易号、商户名搜索
3. 表格字段包括：交易号、商户名、金额、状态、时间
4. 使用 Tailwind CSS 进行样式设计
5. 集成 lodash-es 进行数据处理
6. 遵循项目的 TypeScript 类型规范
```

## 总结

遵循本规范将确保我们的 X-Pay 前端项目代码保持一致性、可维护性和高质量。所有开发人员和 AI 辅助工具都应当参考这些规范进行开发。

规范会随着项目的发展而更新，请定期查看最新版本。
